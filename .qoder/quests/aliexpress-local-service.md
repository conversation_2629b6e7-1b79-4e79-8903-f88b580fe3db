# AliExpress 海外托管服务实现设计

## 概述

本设计文档旨在完成 `AliexpressLocalServiceImpl` 类中所有 TODO 项的实现，重点解决海外托管商品 API 与标准 AliExpress API 之间的数据类型转换问题。海外托管服务采用与标准服务不同的数据格式，需要实现双向的数据转换机制。

## 技术栈

- **语言**: Kotlin
- **框架**: Spring Boot 3.3.5
- **API 客户端**: IopClient (AliExpress 官方 SDK)
- **数据转换**: Jackson + 自定义扩展函数
- **日志**: Blade Logging Framework
- **异常处理**: 自定义业务异常

## 架构设计

### 类关系图

```mermaid
classDiagram
    class AliexpressService {
        <<interface>>
    }
    
    class AliexpressServiceImpl {
        +postProduct()
        +editProduct()
        +queryProduct()
        +pageProduct()
    }
    
    class AliexpressLocalServiceImpl {
        +postProduct()
        +editProduct() 
        +queryProduct()
        +pageProduct()
        +queryCategoryTreeList()
        +queryCategoryAttributes()
        +uploadImageFileToTempDirectory()
        +queryFreightTemplateList()
        +onlineAeProduct()
        +offlineAeProduct()
        +updateProductSkuPrices()
        +updateProductSkuStocks()
    }
    
    AliexpressService <|-- AliexpressServiceImpl
    AliexpressServiceImpl <|-- AliexpressLocalServiceImpl
    
    class LocalServiceConverts {
        +toLocalServiceProductDto()
        +toAliexpressProductQueryResponse()
        +toAliexpressProductPageResponse() 
        +toLocalServiceSearchCondition()
    }
    
    AliexpressLocalServiceImpl --> LocalServiceConverts
```

### 数据转换流程

```mermaid
flowchart TD
    A[标准 AliExpress 请求格式] --> B[类型转换层]
    B --> C[海外托管 API 格式]
    C --> D[IopClient API 调用]
    D --> E[海外托管响应格式]
    E --> F[类型转换层]
    F --> G[标准 AliExpress 响应格式]
```

## 核心实现

### 1. 商品查询类目和属性接口

#### 1.1 查询类目树列表

```kotlin
override fun queryCategoryTreeList(
    accessToken: String?,
    channelSellerId: Long?,
    onlyWithPermission: Boolean,
    channel: String?,
    categoryId: Long?
): AliexpressCategoryTreeListResponse {
    // 海外托管使用相同的类目 API，直接调用父类实现
    return super.queryCategoryTreeList(
        accessToken, 
        channelSellerId, 
        onlyWithPermission, 
        channel ?: "AE_GLOBAL", 
        categoryId
    )
}
```

#### 1.2 查询类目属性

```kotlin
override fun queryCategoryAttributes(
    accessToken: String?,
    categoryId: Long,
    param2: String?,
    locale: String?,
    channel: String?,
    productType: String?
): AliexpressCategoryAttributeResponse {
    // 海外托管支持 productType 参数，需要传递给父类
    return super.queryCategoryAttributes(
        accessToken,
        categoryId,
        param2,
        locale,
        channel ?: "AE_GLOBAL",
        productType
    )
}
```

### 2. 认证和令牌管理

#### 2.1 刷新授权令牌

```kotlin
override fun refreshToken(refreshToken: String): AliexpressAuthTokenRefreshResponse {
    // 海外托管使用相同的认证机制，调用父类实现
    return super.refreshToken(refreshToken)
}
```

#### 2.2 创建授权令牌

```kotlin
override fun createToken(code: String): AliexpressAuthTokenResponse {
    // 海外托管使用相同的认证机制，调用父类实现
    return super.createToken(code)
}
```

### 3. 图片和模板管理

#### 3.1 图片上传到临时目录

```kotlin
override fun uploadImageFileToTempDirectory(
    accessToken: String?,
    file: File
): AliexpressImageUploadResponse {
    // 海外托管使用相同的图片上传接口
    return super.uploadImageFileToTempDirectory(accessToken, file)
}
```

#### 3.2 查询运费模板列表

```kotlin
override fun queryFreightTemplateList(
    accessToken: String,
    channelSellerId: String?
): AliexpressFreightTemplateResponse {
    // 海外托管使用相同的运费模板接口
    return super.queryFreightTemplateList(accessToken, channelSellerId)
}
```

#### 3.3 查询服务模板

```kotlin
override fun queryPromiseTemplates(
    accessToken: String,
    templateId: Long
): AliexpressPromiseTemplateResponse {
    return super.queryPromiseTemplates(accessToken, templateId)
}
```

#### 3.4 获取运费模板详情

```kotlin
override fun getFreightSettingByTemplateQuery(
    accessToken: String,
    templateId: Long
): AliexpressFreightTemplateDetailResponse {
    return super.getFreightSettingByTemplateQuery(accessToken, templateId)
}
```

### 4. 商品管理操作

#### 4.1 商品上架

```kotlin
override fun onlineAeProduct(
    accessToken: String,
    platformProductIds: List<String>
): AliexpressPostproductRedefiningOnlineaeproductResponse {
    // 海外托管使用相同的上架接口
    return super.onlineAeProduct(accessToken, platformProductIds)
}
```

#### 4.2 商品下架

```kotlin  
override fun offlineAeProduct(
    accessToken: String,
    platformProductIds: List<String>
): AeOfflineProductResp {
    return super.offlineAeProduct(accessToken, platformProductIds)
}
```

### 5. 尺码和制造商管理

#### 5.1 设置商品尺码表模板

```kotlin
override fun setSizeChart(
    accessToken: String,
    productId: Long,
    sizeChartId: Long,
    channel: String?,
    channelSellerId: String?
): AliexpressSetSizechartResponse {
    return super.setSizeChart(
        accessToken,
        productId, 
        sizeChartId,
        channel ?: "AE_GLOBAL",
        channelSellerId
    )
}
```

#### 5.2 查询尺码模板列表

```kotlin
override fun querySizeTemplatesByCategory(
    accessToken: String,
    leafCategoryId: Long,
    currentPage: Long,
    channel: String?,
    channelSellerId: String?
): AliexpressSizeTemplatesResponse {
    return super.querySizeTemplatesByCategory(
        accessToken,
        leafCategoryId,
        currentPage,
        channel ?: "AE_GLOBAL",
        channelSellerId
    )
}
```

#### 5.3 查询欧盟责任人列表

```kotlin
override fun queryMsrList(
    accessToken: String,
    channelSellerId: Long?,
    channel: String?
): AliexpressMsrListResponse {
    return super.queryMsrList(
        accessToken,
        channelSellerId,
        channel ?: "AE_GLOBAL"
    )
}
```

#### 5.4 查询制造商信息

```kotlin
override fun queryManufactureDetail(
    accessToken: String,
    manufactureId: Long,
    channelSellerId: Long?,
    channel: String?
): AliexpressManufactureDetailResponse {
    return super.queryManufactureDetail(
        accessToken,
        manufactureId,
        channelSellerId,
        channel ?: "AE_GLOBAL"
    )
}

override fun queryManufactureList(
    accessToken: String,
    channelSellerId: Long?,
    channel: String?
): AliexpressManufactureListResponse {
    return super.queryManufactureList(
        accessToken,
        channelSellerId,
        channel ?: "AE_GLOBAL"
    )
}
```

### 6. 商品状态和分组管理

#### 6.1 查询商品状态

```kotlin
override fun queryProductStatus(
    accessToken: String,
    productId: Long
): AliexpressProductStatusResponse {
    return super.queryProductStatus(accessToken, productId)
}
```

#### 6.2 查询商家账号关系

```kotlin
override fun querySellerRelations(token: String): AliexpressSellerRelationResponse {
    return super.querySellerRelations(token)
}
```

#### 6.3 查询产品分组

```kotlin
override fun queryProductGroups(accessToken: String): AliexpressProductGroupsResponse {
    return super.queryProductGroups(accessToken)
}
```

#### 6.4 设置商品分组

```kotlin
@ExternalLogApiCall(businessIdParamName = "productId")
override fun setProductGroups(
    productId: Long,
    request: AliexpressSetProductGroupsRequest,
    accessToken: String
): AliexpressSetProductGroupsResponse {
    // 海外托管使用相同的分组设置接口
    return super.setProductGroups(productId, request, accessToken)
}
```

### 7. 价格和库存管理

#### 7.1 编辑 SKU 价格

```kotlin
override fun updateProductSkuPrices(
    accessToken: String,
    productId: Long,
    skuPrices: Map<String, String>
): AeUpdateSkuPricesResp {
    // 海外托管商品需要使用专用的价格编辑接口
    ExternalApiExecutor.executeAliexpressApi("updateLocalServiceProductSkuPrices") {
        val skuPriceModelList = skuPrices.map { (skuId, price) ->
            SkuPriceModelDto(
                skuId = skuId.toLong(),
                supplyPrice = price,
                dimSupplyPriceList = null // 分国家定价根据需要设置
            )
        }
        
        val request = AliexpressLocalServiceProductPricesEditRequest(
            channelSellerId = 1, // 从配置获取
            skuPriceModelList = skuPriceModelList,
            productId = productId,
            channel = "AE_GLOBAL"
        ).toRequest()
        
        val response = iopClient.execute(request, accessToken, Protocol.TOP)
        
        saveSyncLog(
            productId,
            request.toJson(),
            response.gopResponseBody!!,
            PlatformOperatorTypeEnum.MODIFY_PRICE,
            response.gopErrorSubMsg
        )
        
        val localServiceResponse = response.parseAliResponse<AliexpressLocalServiceProductPricesEditResponse>()
        
        // 转换为标准格式
        return AeUpdateSkuPricesResp(
            result = AeUpdateSkuPricesResp.Result(
                modifyCount = if (localServiceResponse.success == "true") skuPrices.size else 0,
                errorDetails = if (localServiceResponse.success != "true") 
                    listOf(localServiceResponse.errorCode ?: "未知错误") 
                else emptyList()
            )
        )
    }
}
```

#### 7.2 编辑 SKU 库存

```kotlin
override fun updateProductSkuStocks(
    accessToken: String,
    productId: Long,
    skuStocks: Map<String, Long>
): AeUpdateSkuStocksResp {
    ExternalApiExecutor.executeAliexpressApi("updateLocalServiceProductSkuStocks") {
        val productSkuStockList = skuStocks.map { (skuId, stock) ->
            ProductSkuStockUpdate(
                skuId = skuId.toLong(),
                warehouseStockList = listOf(
                    WarehouseStockDto(
                        warehouseCode = "DEFAULT", // 默认仓库
                        stock = stock
                    )
                )
            )
        }
        
        val request = AliexpressLocalServiceProductStocksUpdateRequest(
            channelSellerId = 1,
            productId = productId,
            channel = "AE_GLOBAL", 
            productSkuStockList = productSkuStockList
        ).toRequest()
        
        val response = iopClient.execute(request, accessToken, Protocol.TOP)
        
        saveSyncLog(
            productId,
            request.toJson(),
            response.gopResponseBody!!,
            PlatformOperatorTypeEnum.MODIFY_STOCK,
            response.gopErrorSubMsg
        )
        
        val localServiceResponse = response.parseAliResponse<BaseAliexpressResponse>()
        
        return AeUpdateSkuStocksResp(
            result = AeUpdateSkuStocksResp.Result(
                modifyCount = if (localServiceResponse.isSuccess()) skuStocks.size else 0,
                errorDetails = if (!localServiceResponse.isSuccess()) 
                    listOf("库存更新失败") 
                else emptyList()
            )
        )
    }
}
```

### 8. 税务和监管属性

#### 8.1 查询监管属性信息

```kotlin
override fun queryRegulatoryAttributesInfo(
    accessToken: String,
    request: AliexpressQueryRegulatoryAttributesInfoRequest
): AliexpressQueryRegulatoryAttributesInfoResponse {
    // 海外托管使用相同的税务接口
    return super.queryRegulatoryAttributesInfo(accessToken, request)
}
```

#### 8.2 批量查询监管属性信息

```kotlin
override fun batchQueryRegulatoryAttributesInfo(
    accessToken: String,
    requests: List<AliexpressQueryRegulatoryAttributesInfoRequest>
): AliexpressQueryRegulatoryAttributesInfoResponse {
    return super.batchQueryRegulatoryAttributesInfo(accessToken, requests)
}
```

#### 8.3 选择监管属性选项

```kotlin
override fun selectRegulatoryAttributesOptions(
    accessToken: String,
    request: AliexpressSelectRegulatoryAttributesOptionsRequest
): AliexpressSelectRegulatoryAttributesOptionsResponse {
    return super.selectRegulatoryAttributesOptions(accessToken, request)
}
```

### 9. 商品字段编辑

#### 9.1 编辑商品单个字段

```kotlin
@ExternalLogApiCall(businessIdParamName = "req.productId")
override fun editSimpleProductFiled(
    req: AliexpressPostproductRedefiningEditsimpleproductfiledRequest,
    accessToken: String
): AliexpressPostproductRedefiningEditsimpleproductfiledResponse {
    // 海外托管商品的字段编辑需要使用标准接口
    // 因为海外托管商品在字段级别编辑时使用相同的 API
    return super.editSimpleProductFiled(req, accessToken)
}
```

## 数据转换实现

### 1. 商品列表查询转换

```kotlin
private fun AliexpressLocalServiceProductsListResponse.toAliexpressProductPageResponse(): AliexpressProductPageResponse {
    return AliexpressProductPageResponse(
        result = AliexpressProductPageResultDto(
            success = result.success,
            currentPage = (result.totalCount / 20) + 1, // 根据实际分页计算
            totalPage = (result.totalCount / 20) + 1,
            productCount = result.totalCount,
            errorMsg = if (!result.success) "查询失败" else null,
            dtoList = result.productList?.map { localProduct ->
                AliexpressProductPageResult(
                    productId = localProduct.productId,
                    productType = "LOCAL_SERVICE", // 海外托管类型
                    currencyCode = localProduct.currencyCode,
                    gmtCreate = localProduct.createTime,
                    gmtModified = localProduct.updateTime,
                    imageURLs = localProduct.mainImageList?.joinToString(";"),
                    productMaxPrice = localProduct.maxPrice,
                    productMinPrice = localProduct.minPrice,
                    productStatusType = localProduct.productStatus,
                    subject = localProduct.subject
                )
            }
        )
    )
}
```

### 2. 搜索条件转换

```kotlin
private fun AliexpressPostproductRedefiningFindproductinfolistqueryAeopAEProductListQuery.toLocalServiceSearchCondition(): SearchCondition {
    return SearchCondition(
        productStatus = productStatusType ?: "ONLINE", // 默认查询在线商品
        leafCategoryId = leafCategoryId,
        productId = productId,
        createAfter = gmtCreateStart?.let { SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(it) },
        createBefore = gmtCreateEnd?.let { SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(it) },
        updateAfter = gmtModifiedStart?.let { SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(it) },
        updateBefore = gmtModifiedEnd?.let { SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(it) },
        auditFailureReason = null, // 标准查询不包含此字段
        msrEuId = null, // 根据需要映射
        manufacturerId = null // 根据需要映射
    )
}
```

## 错误处理策略

### 异常处理流程

```mermaid
flowchart TD
    A[API 调用] --> B{调用成功?}
    B -->|是| C[响应解析]
    B -->|否| D[记录错误日志]
    C --> E{业务成功?}
    E -->|是| F[返回结果]
    E -->|否| G[抛出业务异常]
    D --> H[抛出 API 异常]
    G --> I[记录同步日志]
    H --> I
    F --> J[记录成功日志]
```

### 异常类型映射

| 海外托管错误 | 标准错误类型 | 处理策略 |
|------------|------------|----------|
| `success: false` | `AliexpressApiException` | 记录日志并抛出异常 |
| `error_code` 存在 | `BaseBizException` | 根据错误码分类处理 |
| 网络超时 | `TimeoutException` | 重试机制 |
| 参数验证失败 | `IllegalArgumentException` | 参数校验提前处理 |

## 测试策略

### 单元测试覆盖

```kotlin
@Test
fun `test queryProduct with local service response conversion`() {
    // 测试海外托管商品查询响应转换
    val mockResponse = createMockLocalServiceResponse()
    val convertedResponse = mockResponse.toAliexpressProductQueryResponse()
    
    assertThat(convertedResponse.isSuccess()).isTrue()
    assertThat(convertedResponse.result).isNotNull()
    assertThat(convertedResponse.result!!.categoryId).isEqualTo(12345)
}

@Test  
fun `test pageProduct with search condition conversion`() {
    // 测试搜索条件转换
    val standardQuery = createStandardQuery()
    val localServiceCondition = standardQuery.toLocalServiceSearchCondition()
    
    assertThat(localServiceCondition.productStatus).isEqualTo("ONLINE")
    assertThat(localServiceCondition.leafCategoryId).isEqualTo(standardQuery.leafCategoryId)
}
```

### 集成测试场景

1. **完整商品生命周期测试**
   - 商品发布 → 编辑 → 查询 → 上架 → 下架
   
2. **价格库存管理测试**  
   - 批量价格更新 → 库存调整 → 状态验证
   
3. **异常处理测试**
   - 网络异常 → 业务异常 → 参数异常

## 配置管理

### 海外托管专用配置

```yaml
aliexpress:
  local-service:
    default-channel: "AE_GLOBAL"
    default-channel-seller-id: 1
    retry-count: 3
    timeout: 30000
    enable-country-pricing: true
```

### 环境差异配置

| 环境 | Channel Seller ID | 特殊配置 |
|------|------------------|----------|
| 开发环境 | 1 | 启用调试日志 |  
| 测试环境 | 2 | 模拟响应 |
| 生产环境 | 实际 ID | 完整监控 |




























































































































































































































































































































































