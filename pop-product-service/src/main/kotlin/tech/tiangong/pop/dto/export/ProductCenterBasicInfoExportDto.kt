package tech.tiangong.pop.dto.export

import com.alibaba.excel.annotation.ExcelProperty
import java.time.LocalDateTime

data class ProductCenterBasicInfoExportDto(

    @ExcelProperty(value = ["SPU"])
    var spuCode: String? = null,
    @ExcelProperty(value = ["商品分类"])
    var productType: String? = null,
    @ExcelProperty(value = ["供给方式"])
    var supplyMode: String? = null,
    @ExcelProperty(value = ["商品类型"])
    var goodsType: String? = null,
    @ExcelProperty(value = ["品类"])
    var categoryName: String? = null,
    @ExcelProperty(value = ["品牌"])
    var brandName: String? = null,
    @ExcelProperty(value = ["波次"])
    var waves: String? = null,
    @ExcelProperty(value = ["货盘类型"])
    var goodsRepType: String? = null,
    @ExcelProperty(value = ["款式风格"])
    var clothingStyleName: String? = null,
    @ExcelProperty(value = ["织造方式"])
    var weaveMode: String? = null,
    @ExcelProperty(value = ["市场"])
    var market: String? = null,
    @ExcelProperty(value = ["适用场景"])
    var sceneName: String? = null,
    @ExcelProperty(value = ["选款国家"])
    var countryName: String? = null,
    @ExcelProperty(value = ["选款店铺"])
    var shopName: String? = null,
    @ExcelProperty(value = ["版型号"])
    var prototypeNum: String? = null,
    @ExcelProperty(value = ["印花类型"])
    var printType: String? = null,
    @ExcelProperty(value = ["创建人"])
    var creatorName: String? = null,
    @ExcelProperty(value = ["创建时间"])
    var createdTime: LocalDateTime? = null,
    @ExcelProperty(value = ["更新人"])
    var reviserName: String? = null,
    @ExcelProperty(value = ["更新时间"])
    var revisedTime: LocalDateTime? = null,
    @ExcelProperty(value = ["首次上架人"])
    var publishUserName: String? = null,
    @ExcelProperty(value = ["首次上架时间"])
    var publishTime: LocalDateTime? = null,
    @ExcelProperty(value = ["商品名称(中)"])
    var productTitle: String? = null,
    @ExcelProperty(value = ["商品名称(英)"])
    var productTitleEn: String? = null,
    @ExcelProperty(value = ["买手"])
    var buyerName: String? = null,
    @ExcelProperty(value = ["商品链接"])
    var productLink: String? = null,
)
