package tech.tiangong.pop.dto.export

import com.alibaba.excel.annotation.ExcelProperty
import java.math.BigDecimal

data class ProductCenterAttrExportDto(

    @ExcelProperty(value = ["SPU"])
    var spuCode: String? = null,
    @ExcelProperty(value = ["品类"])
    var category: String? = null,
    @ExcelProperty(value = ["尺码组"])
    var sizeGroup: String? = null,
    @ExcelProperty(value = ["尺码"])
    var size: String? = null,
    @ExcelProperty(value = ["SKC"])
    var skc: String? = null,
    @ExcelProperty(value = ["颜色"])
    var color: String? = null,
    @ExcelProperty(value = ["供货价"])
    var supplyPrice: BigDecimal? = null,
    @ExcelProperty(value = ["采购价"])
    var purchasePrice: BigDecimal? = null,
    @ExcelProperty(value = ["现货类型"])
    var spotType: String? = null,
    @ExcelProperty(value = ["定价类型"])
    var pricingType: String? = null,
    // 动态字段（不带注解，单独处理）
    var dynamicProperties: Map<String, String> = emptyMap(),
)
