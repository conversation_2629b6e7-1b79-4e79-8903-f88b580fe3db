package tech.tiangong.pop.controller.category

import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.*
import team.aikero.blade.core.constant.UrlVersionConstant.WEB
import team.aikero.blade.core.protocol.DataResponse
import team.aikero.blade.core.protocol.ok
import tech.tiangong.pop.req.category.SavePublishChannelReq
import tech.tiangong.pop.resp.category.PublishChannelVo
import tech.tiangong.pop.service.category.PublishChannelService

/**
 * 发布渠道
 */
@RestController
@RequestMapping("$WEB/v1/publish-channel")
class PublishChannelController(
    private val publishChannelService: PublishChannelService
) {
    /**
     * 发布渠道列表
     */
    @PostMapping("/list")
    fun list(): DataResponse<List<PublishChannelVo>> {
        return ok(publishChannelService.list())
    }

    /**
     * 新增发布渠道
     */
    @PostMapping("/add")
    fun add(@RequestBody @Validated req: SavePublishChannelReq): DataResponse<Boolean> {
        return ok(publishChannelService.add(req))
    }

    /**
     * 更新发布渠道
     */
    @PostMapping("/update")
    fun update(@RequestBody @Validated req: SavePublishChannelReq): DataResponse<Boolean> {
        return ok(publishChannelService.update(req))
    }

    /**
     * 逻辑删除发布渠道
     */
    @PostMapping("/delete")
    fun delete(@RequestParam id: Long): DataResponse<Boolean> {
        return ok(publishChannelService.delete(id))
    }
}