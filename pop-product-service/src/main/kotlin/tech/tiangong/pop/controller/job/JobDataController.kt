package tech.tiangong.pop.controller.job

import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import team.aikero.blade.auth.annotation.PreCheckIgnore
import team.aikero.blade.auth.withSystemUser
import team.aikero.blade.core.protocol.DataResponse
import team.aikero.blade.core.protocol.ok
import tech.tiangong.pop.service.CleanDataService

/**
 * 操作数据的任务
 */
@PreCheckIgnore
@RestController
@RequestMapping("/job/v1/data")
class JobDataController(
    private val cleanDataService: CleanDataService,
) {

    /**
     * 定期清理同步任务数据
     * {"method":"POST","url":"/pop-product-service/job/v1/data/clean/sync/task","system":"ola", "env":"dev"}
     */
    @PostMapping("/clean/sync/task")
    fun cleanSyncTask(): DataResponse<Unit> {
        withSystemUser {
            cleanDataService.cleanSyncTask()
        }
        return ok()
    }
}