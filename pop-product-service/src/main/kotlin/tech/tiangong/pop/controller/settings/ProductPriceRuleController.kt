package tech.tiangong.pop.controller.settings

import jakarta.validation.Valid
import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.*
import team.aikero.blade.core.protocol.DataResponse
import team.aikero.blade.core.protocol.PageVo
import team.aikero.blade.core.protocol.ok
import tech.tiangong.pop.bo.ProductPriceCalcBO
import tech.tiangong.pop.bo.ProductPriceCalcResultBO
import tech.tiangong.pop.helper.cache.ProductPriceCalcHelper
import tech.tiangong.pop.req.settings.ProductPriceRuleGroupSaveReq
import tech.tiangong.pop.req.settings.ProductPriceRulePageGroupReq
import tech.tiangong.pop.resp.settings.ProductPriceRuleDetailResp
import tech.tiangong.pop.resp.settings.ProductPriceRuleGroupPageResp
import tech.tiangong.pop.service.settings.ProductPriceCalcService
import tech.tiangong.pop.service.settings.ProductPriceRuleService

/**
 * 基础配置-商品销售定价规则
 */
@RestController
@RequestMapping("/web/v1/product-price-rule")
class ProductPriceRuleController(
    private val productPriceRuleService: ProductPriceRuleService,
    private val productPriceCalcService: ProductPriceCalcService,
    private val productPriceCalcHelper: ProductPriceCalcHelper,
    ) {
    /**
     * 分页查询产品定价规则
     */
    @PostMapping("/page")
    fun page(@Validated @RequestBody req: ProductPriceRulePageGroupReq): DataResponse<PageVo<ProductPriceRuleGroupPageResp>> {
        return ok(productPriceRuleService.page(req))
    }

    /**
     * 查询价格规则详情
     */
    @PostMapping("/query-rules/{groupId}")
    fun batchQueryRuleDetails(@PathVariable groupId: Long): DataResponse<List<ProductPriceRuleDetailResp>> {
        return ok(productPriceRuleService.queryRulesByGroupId(groupId))
    }

    /**
     * 删除单个价格规则
     */
    @PostMapping("/delete/{groupId}")
    fun delete(@PathVariable groupId: Long): DataResponse<Boolean> {
        return ok(productPriceRuleService.deleteRulesByGroupId(groupId))
    }

    /**
     * 保存或更新价格规则组
     */
    @PostMapping("/save-or-update-group")
    fun saveOrUpdate(@Valid @RequestBody req: ProductPriceRuleGroupSaveReq): DataResponse<Boolean> {
        return ok(productPriceRuleService.saveOrUpdateGroup(req))
    }

    /**
     * 计算商品价格
     *
     * @param calcBO 商品价格计算对象
     * @return ProductPriceCalcResultBO 计算结果
     */
    @PostMapping("/calculate")
    fun calculatePrice(@RequestBody calcBO: ProductPriceCalcBO): ProductPriceCalcResultBO {
        return productPriceCalcService.calculate(calcBO)
    }

    /**
     * 手动清除所有价格计算相关缓存
     */
    @PostMapping("/invalidate-cache")
    fun invalidateCache(): DataResponse<Boolean> {
        productPriceCalcHelper.invalidateAllCache()
        return ok()
    }
}
