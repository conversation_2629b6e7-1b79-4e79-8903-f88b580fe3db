package tech.tiangong.pop.controller.product

import jakarta.servlet.http.HttpServletResponse
import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import team.aikero.blade.core.constant.UrlVersionConstant.WEB
import team.aikero.blade.core.protocol.DataResponse
import team.aikero.blade.core.protocol.PageVo
import team.aikero.blade.core.protocol.ok
import tech.tiangong.pop.req.product.BarcodePageQueryReq
import tech.tiangong.pop.req.product.GenerateBarCodeReq
import tech.tiangong.pop.resp.product.ProductBarcodePageResp
import tech.tiangong.pop.service.product.BarCodeService
import tech.tiangong.sdp.material.constants.UrlConstant.VERSION_V1


/**
 * 商品管理
 */
@RestController
@RequestMapping("$WEB$VERSION_V1/barcode")
class BarcodeController(
    private val barCodeService: BarCodeService
) {

    /**
     * 商品条码列表
     *
     * @param req 分页对象
     */
    @PostMapping("/page")
    fun page(@Validated @RequestBody req: BarcodePageQueryReq): DataResponse<PageVo<ProductBarcodePageResp>> {
        return ok(barCodeService.page(req))
    }

    /**
     * 批量生成条码
     *
     * @param req
     */
    @PostMapping("/batch-generate")
    fun batchGenerateBarCode(@Validated @RequestBody req: GenerateBarCodeReq): DataResponse<Unit> {
        barCodeService.batchGenerate(req)
        return ok()
    }

    /**
     * 导出条码
     *
     * @param req 入参
     */
    @PostMapping("/export")
    fun export(@Validated @RequestBody req: BarcodePageQueryReq, response: HttpServletResponse) {
        barCodeService.export(req, response)
    }

    /**
     * 提交导出条码下载任务
     *
     * @param req 入参
     */
    @PostMapping("/submit-barcode-export-task")
    fun submitBarcodeExportTask(@Validated @RequestBody req: BarcodePageQueryReq): DataResponse<Unit> {
        barCodeService.submitBarcodeExportTask(req)
        return ok()
    }
}
