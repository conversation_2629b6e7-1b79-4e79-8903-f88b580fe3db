package tech.tiangong.pop.controller.pagecolumnconfig

import jakarta.validation.Valid
import org.springframework.web.bind.annotation.*
import team.aikero.blade.core.protocol.DataResponse
import team.aikero.blade.core.protocol.ok
import tech.tiangong.pop.req.product.SaveShowPageColumnReq
import tech.tiangong.pop.resp.ShowPageColumnResp
import tech.tiangong.pop.service.UserPageColumnConfigService

/**
 * 用户列表页面展示列配置
 */
@RestController
@RequestMapping("/web/v1/user-page-column-config")
class UserPageColumnConfigController(
    private val userPageColumnConfigService: UserPageColumnConfigService,
) {

    /**
     * 根据页面编码获取当前用户的列展示配置
     */
    @GetMapping("/get-current-user-page-column-by-page-code")
    fun getLogisticsRuleByPlatformId(@RequestParam("pageCode") pageCode:String): DataResponse<ShowPageColumnResp?> {
        return ok(userPageColumnConfigService.getCurrentUserPageColumnByPageCode(pageCode))
    }

    /**
     * 保存列表展示列配置(当前用户)
     */
    @PostMapping("/save-show-page-column-config")
    fun saveShowPageColumnConfig(@Valid @RequestBody req: SaveShowPageColumnReq): DataResponse<Void> {
        userPageColumnConfigService.saveShowPageColumnConfig(req)
        return ok()
    }
}