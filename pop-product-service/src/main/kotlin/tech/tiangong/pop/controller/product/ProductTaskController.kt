package tech.tiangong.pop.controller.product

import org.springframework.web.bind.annotation.*
import team.aikero.blade.core.protocol.DataResponse
import team.aikero.blade.core.protocol.ok
import tech.tiangong.pop.req.product.ProductCreateBatchRetryTaskReq
import tech.tiangong.pop.service.product.ProductCreateTaskService

/**
 * 商品创建任务
 */
@RestController
@RequestMapping("/api/v1/product-tasks")
class ProductTaskController(
    private val productCreateTaskService: ProductCreateTaskService
) {
    /**
     * 批量重试任务
     */
    @PostMapping("/create/batch-retry")
    fun batchRetryTask(@RequestBody req: ProductCreateBatchRetryTaskReq): DataResponse<Void> {
        productCreateTaskService.batchRetryTaskByCondition(req)
        return ok()
    }

    /**
     * 重新执行指定任务
     *
     * @param taskId 任务ID
     * @param forceExecute 是否强制执行
     * @return 操作结果
     */
    @PostMapping("/create/execute/{taskId}")
    fun executeTask(
        @PathVariable taskId: Long,
        @RequestParam(required = false, defaultValue = "false") forceExecute: Boolean
    ): DataResponse<Void> {
        productCreateTaskService.submitAndCallback(taskId, forceExecute)
        return ok()
    }

    /**
     * 根据SPU编码回调通知
     */
    @PostMapping("/create/batch-callback")
    fun batchSendCallback(@RequestBody req: ProductCreateBatchRetryTaskReq): DataResponse<Void> {
        productCreateTaskService.batchSendCallbackByCondition(req)
        return ok()
    }

}