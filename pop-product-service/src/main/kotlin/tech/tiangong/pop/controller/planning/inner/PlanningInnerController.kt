package tech.tiangong.pop.controller.planning.inner

import team.aikero.blade.core.protocol.DataResponse
import team.aikero.blade.core.protocol.ok
import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.*
import tech.tiangong.pop.common.req.PlanningSupplyQuantityReq
import tech.tiangong.pop.common.resp.PlanningSupplyQuantityResp
import tech.tiangong.pop.service.PlanningService

/**
 * 企划管理(inner内部接口)
 * <AUTHOR>
 * @date 2024/11/13 14:16
 */
@RestController
@RequestMapping("/inner/v1/planning")
class PlanningInnerController(private val planningService: PlanningService) {
    /**
     * 查询-企划品类供给数统计
     * @param req 请求对象
     * @return
     */
    @PostMapping("/supply/quantity")
    fun supplyQuantityCategory(@Validated @RequestBody req: PlanningSupplyQuantityReq): DataResponse<PlanningSupplyQuantityResp> {
        return ok(planningService.supplyQuantityCategory(req))
    }

}