package tech.tiangong.pop.controller.approval

import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import team.aikero.blade.auth.annotation.PreCheckIgnore
import team.aikero.blade.core.constant.UrlVersionConstant.WEB
import team.aikero.blade.core.protocol.DataResponse
import team.aikero.blade.core.protocol.ok
import tech.tiangong.pop.req.approval.ApprovalDingTalkCallbackReq
import tech.tiangong.pop.service.dingtalk.DingTalkApprovalService

/**
 * 审批流
 */
@RestController
@RequestMapping("$WEB/approval")
class ApprovalController(
    private val dingTalkApprovalService: DingTalkApprovalService,
) {

    /**
     * 分组列表
     *
     * @param req 分页对象
     * @return 属性分组列表
     */
    @PreCheckIgnore
    @PostMapping("/dingtalk/callback")
    fun dingTalkCallBack(@RequestBody req: ApprovalDingTalkCallbackReq): DataResponse<Unit> {
        return ok(dingTalkApprovalService.callbackApprovalResult(req))
    }

}