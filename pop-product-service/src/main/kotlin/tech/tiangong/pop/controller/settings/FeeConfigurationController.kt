package tech.tiangong.pop.controller.settings

import jakarta.validation.Valid
import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.*
import team.aikero.blade.core.protocol.DataResponse
import team.aikero.blade.core.protocol.ok
import tech.tiangong.pop.enums.settings.FeeTypeEnum
import tech.tiangong.pop.req.settings.FeeConfigQueryReq
import tech.tiangong.pop.req.settings.FeeConfigSaveReq
import tech.tiangong.pop.resp.settings.FeeConfigResp
import tech.tiangong.pop.service.settings.FeeConfigurationService

/**
 * 商品销售定价-基础配置-费用
 */
@Validated
@RestController
@RequestMapping("/web/v1/fee-configs")
class FeeConfigurationController(
    private val feeConfigurationService: FeeConfigurationService
) {
    /**
     * 查询费用配置列表
     */
    @GetMapping("/list")
    fun queryFeeConfigs(req: FeeConfigQueryReq): DataResponse<List<FeeConfigResp>> {
        return ok(feeConfigurationService.queryFeeConfigs(req))
    }

    /**
     * 查询物流费用配置列表
     */
    @GetMapping("/logistics")
    fun queryLogisticsFees(): DataResponse<List<FeeConfigResp>> {
        return ok(
            feeConfigurationService.queryFeeConfigs(
                FeeConfigQueryReq(feeType = FeeTypeEnum.LOGISTICS.code)
            )
        )
    }

    /**
     * 保存或更新费用配置
     */
    @PostMapping("/save-or-update")
    fun saveOrUpdate(@RequestBody @Valid req: FeeConfigSaveReq): DataResponse<Boolean> {
        return ok(feeConfigurationService.saveOrUpdate(req))
    }

    /**
     * 批量保存或更新费用配置
     */
    @PostMapping("/batch-save-or-update")
    fun batchSaveOrUpdate(@RequestBody @Valid reqList: List<FeeConfigSaveReq>): DataResponse<Boolean> {
        return ok(feeConfigurationService.batchSaveOrUpdate(reqList))
    }
}
