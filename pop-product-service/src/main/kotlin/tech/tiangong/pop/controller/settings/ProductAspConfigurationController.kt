package tech.tiangong.pop.controller.settings

import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import team.aikero.blade.core.protocol.DataResponse
import team.aikero.blade.core.protocol.ok
import tech.tiangong.pop.req.settings.ProductAspConfigQueryReq
import tech.tiangong.pop.req.settings.ProductAspConfigSaveReq
import tech.tiangong.pop.resp.settings.ProductAspConfigResp
import tech.tiangong.pop.service.settings.ProductAspConfigurationService
import javax.validation.Valid

/**
 * 基础配置-ASP配置
 */
@RestController
@RequestMapping("/api/v1/product/asp-config")
class ProductAspConfigurationController(
    private val productAspConfigurationService: ProductAspConfigurationService
) {

    /**
     * 查询商品ASP配置列表
     */
    @PostMapping("/query")
    fun queryAspConfigs(@RequestBody req: ProductAspConfigQueryReq): DataResponse<List<ProductAspConfigResp>> {
        val result = productAspConfigurationService.queryAspConfigs(req)
        return ok(result)
    }

    /**
     * 保存或更新商品ASP配置
     */
    @PostMapping("/save")
    fun saveOrUpdate(@RequestBody @Valid req: ProductAspConfigSaveReq): DataResponse<Boolean> {
        val result = productAspConfigurationService.saveOrUpdate(req)
        return ok(result)
    }

    /**
     * 批量保存或更新商品ASP配置
     */
    @PostMapping("/batch-save")
    fun batchSaveOrUpdate(@RequestBody @Valid reqList: List<ProductAspConfigSaveReq>): DataResponse<Boolean> {
        val result = productAspConfigurationService.batchSaveOrUpdate(reqList)
        return ok(result)
    }

    /**
     * 刷新商品ASP配置缓存
     */
    @PostMapping("/refresh-cache")
    fun refreshCache(): DataResponse<Boolean> {
        val result = productAspConfigurationService.refreshCache()
        return ok(result)
    }
}