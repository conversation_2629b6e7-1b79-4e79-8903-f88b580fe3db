package tech.tiangong.pop.controller.product.inner

import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import team.aikero.blade.core.annotation.InnerApi
import team.aikero.blade.core.protocol.DataResponse
import team.aikero.blade.core.protocol.ok
import tech.tiangong.pop.common.req.ProductTagAddRegClearReq
import tech.tiangong.pop.service.product.ProductTagService

/**
 * 商品标签
 * <AUTHOR>
 * @date 2025-2-24 17:31:28
 */
@InnerApi
@RestController
@RequestMapping("/inner/v1/product/tag")
class ProductTagInnerController(
    private val productTagService: ProductTagService,
) {

    /**
     * 增加清仓标签
     * @param req
     * @return
     */
    @PostMapping("/add/reg/clear")
    fun addRegClear(@Validated @RequestBody req: ProductTagAddRegClearReq): DataResponse<Unit> {
        productTagService.addTag(req.spuCodes)
        return ok()
    }
}