package tech.tiangong.pop.controller.planning

import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.*
import team.aikero.blade.core.protocol.DataResponse
import team.aikero.blade.core.protocol.PageVo
import team.aikero.blade.core.protocol.ok
import tech.tiangong.pop.req.planning.PlanningLogReq
import tech.tiangong.pop.req.planning.PlanningLogUpdatePageReq
import tech.tiangong.pop.resp.planning.PlanningLogUpdatePageResp
import tech.tiangong.pop.service.PlanningLogService

/**
 * 企划管理日志
 * <AUTHOR>
 * @date 2024/11/13 14:16
 */
@RestController
@RequestMapping("/web/v1/planning/log")
class PlanningLogController(private val planningLogService: PlanningLogService) {

    /**
     * 新增日志
     * @param req
     * @return
     */
    @PostMapping("/update/add")
    fun detail(@Validated @RequestBody req: PlanningLogReq): DataResponse<Unit> {
        planningLogService.addLog(req)
        return ok()
    }

    /**
     * 更新记录(分页)
     * @param req
     * @return
     */
    @PostMapping("/update/page")
    fun page(@Validated @RequestBody req: PlanningLogUpdatePageReq): DataResponse<PageVo<PlanningLogUpdatePageResp>> {
        return ok(planningLogService.page(req))
    }
}