package tech.tiangong.pop.controller.category

import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.*
import team.aikero.blade.core.constant.UrlVersionConstant.WEB
import team.aikero.blade.core.protocol.DataResponse
import team.aikero.blade.core.protocol.ok
import tech.tiangong.pop.req.category.PublishPlatformQueryReq
import tech.tiangong.pop.req.category.SavePublishPlatformReq
import tech.tiangong.pop.resp.category.PublishPlatformVo
import tech.tiangong.pop.service.category.PublishPlatformService

/**
 * 发布平台
 */
@RestController
@RequestMapping("$WEB/v1/publish-platform")
class PublishPlatformController(
    private val publishPlatformService: PublishPlatformService
) {

    /**
     * 发布平台列表
     */
    @PostMapping("/list")
    fun list(@RequestBody @Validated req: PublishPlatformQueryReq): DataResponse<List<PublishPlatformVo>> {
        return ok(publishPlatformService.list(req))
    }

    /**
     * 新增发布平台
     */
    @PostMapping("/add")
    fun add(@RequestBody @Validated req: SavePublishPlatformReq): DataResponse<Boolean> {
        return ok(publishPlatformService.add(req))
    }

    /**
     * 更新发布平台
     */
    @PostMapping("/update")
    fun update(@RequestBody @Validated req: SavePublishPlatformReq): DataResponse<Boolean> {
        return ok(publishPlatformService.update(req))
    }

    /**
     * 逻辑删除发布平台
     */
    @PostMapping("/delete")
    fun delete(@RequestParam id: Long): DataResponse<Boolean> {
        return ok(publishPlatformService.delete(id))
    }
}