package tech.tiangong.pop.controller.regionpricerule

import jakarta.validation.Valid
import org.springframework.web.bind.annotation.*
import org.springframework.web.multipart.MultipartFile
import team.aikero.blade.core.exception.BusinessException
import team.aikero.blade.core.protocol.DataResponse
import team.aikero.blade.core.protocol.ok
import tech.tiangong.pop.common.enums.PlatformEnum
import tech.tiangong.pop.req.regionpricerule.*
import tech.tiangong.pop.resp.product.manage.CalAeRetailPriceForPageResp
import tech.tiangong.pop.resp.regionpricerule.*
import tech.tiangong.pop.service.regionpricerule.RegionPriceRuleManageService
import tech.tiangong.pop.service.regionpricerule.RegionPriceRuleService
import tech.tiangong.pop.service.v2.pricing.PricingVariableService
import java.io.IOException

/**
 * 区域定价基础配置
 */
@RestController
@RequestMapping("/web/v1/region-price-rule")
class RegionPriceRuleController(
    private val regionPriceRuleManageService: RegionPriceRuleManageService,
    private val pricingVariableService: PricingVariableService,
    private val regionPriceRuleService: RegionPriceRuleService,
) {
    /**
     * 保存区域价格-物流费用配置
     */
    @PostMapping("/save-logistics-rule")
    fun saveLogisticsRule(@Valid @RequestBody req: SaveRegionPriceRuleLogisticsReq): DataResponse<Void> {
        regionPriceRuleManageService.saveLogisticsRule(req)
        return ok()
    }

    /**
     * 保存区域价格-物流费用配置
     */
    @GetMapping("/get-logistics-rule-by-platform")
    fun getLogisticsRuleByPlatformId(@RequestParam("platform") platform:String): DataResponse<RegionPriceRuleLogisticsResp?> {
        val platformEnum = PlatformEnum.getByName(platform)?:throw BusinessException("平台编码无效${platform}")
        return ok(regionPriceRuleManageService.getLogisticsRuleByPlatform(platformEnum))
    }

    /**
     * 导入AE保存区域价格-物流费用配置
     */
    @PostMapping("/import-ae-logistics-rule")
    fun importAELogisticsRule(@RequestParam("excelFile") excelFile: MultipartFile): DataResponse<Void> {
        regionPriceRuleManageService.importAELogisticsRule(excelFile)
        return ok()
    }

    /**
     * 保存区域价格-仓储成本配置
     */
    @PostMapping("/save-storage-rule")
    fun saveStorageRule(@Valid @RequestBody req: SaveRegionPriceRuleStorageReq): DataResponse<Void> {
        regionPriceRuleManageService.saveStorageRule(req)
        return ok()
    }

    /**
     * 查询区域价格-仓储成本配置
     */
    @GetMapping("/query-storage-rule-by-platform")
    fun queryStorageRuleByPlatformId(@RequestParam("platform") platform:String): DataResponse<RegionPriceRuleStorageResp?> {
        val platformEnum = PlatformEnum.getByName(platform)?:throw BusinessException("平台编码无效${platform}")
        return ok(regionPriceRuleManageService.queryStorageRuleByPlatform(platformEnum))
    }

    /**
     * 保存区域价格-退货率配置
     */
    @PostMapping("/save-reject-rate-rule")
    fun saveRejectRateRule(@Valid @RequestBody req: List<SaveRegionPriceRuleRejectRateReq>): DataResponse<Void> {
        regionPriceRuleManageService.saveRejectRateRule(req)
        return ok()
    }

    /**
     * 查询区域价格-退货率配置
     */
    @GetMapping("/query-reject-rate-rule")
    fun queryRejectRateRule(): DataResponse<List<RegionPriceRuleRejectRateResp>?> {
        return ok(regionPriceRuleManageService.queryRejectRateRule())
    }

    /**
     * 保存区域价格-广告成本配置
     */
    @PostMapping("/save-ad-rule")
    fun saveAdRule(@Valid @RequestBody req: List<SaveRegionPriceRuleAdReq>): DataResponse<Void> {
        regionPriceRuleManageService.saveAdRule(req)
        return ok()
    }

    /**
     * 查询区域价格-广告成本配置
     */
    @GetMapping("/query-ad-rule")
    fun queryAdRule(): DataResponse<List<RegionPriceRuleAdResp>?> {
        return ok(regionPriceRuleManageService.queryAdRule())
    }

    /**
     * 保存区域价格-佣金配置
     */
    @PostMapping("/save-commission-rule")
    fun saveCommissionRule(@Valid @RequestBody req: List<SaveRegionPriceRuleCommissionReq>): DataResponse<Void> {
        regionPriceRuleManageService.saveCommissionRule(req)
        return ok()
    }

    /**
     * 查询区域价格-佣金配置
     */
    @GetMapping("/query-commission-rule")
    fun queryCommissionRule(): DataResponse<List<RegionPriceRuleCommissionResp>?> {
        return ok(regionPriceRuleManageService.queryCommissionRule())
    }

    /**
     * 保存区域价格-提现手续费配置
     */
    @PostMapping("/save-withdraw-rule")
    fun saveWithdrawRule(@Valid @RequestBody req: List<SaveRegionPriceRuleWithdrawReq>): DataResponse<Void> {
        regionPriceRuleManageService.saveWithdrawRule(req)
        return ok()
    }

    /**
     * 查询区域价格-提现手续费配置
     */
    @GetMapping("/query-withdraw-rule")
    fun queryWithdrawRule(): DataResponse<List<RegionPriceRuleWithdrawResp>?> {
        return ok(regionPriceRuleManageService.queryWithdrawRule())
    }

    /**
     * 保存区域价格-目标毛利率配置
     */
    @PostMapping("/save-gross-margin-rule")
    fun saveGrossMarginRule(@Valid @RequestBody req: List<SaveRegionPriceRuleGrossMarginReq>): DataResponse<Void> {
        regionPriceRuleManageService.saveGrossMarginRule(req)
        return ok()
    }

    /**
     * 查询区域价格-目标毛利率配置
     */
    @GetMapping("/query-gross-margin-rule")
    fun queryGrossMarginRule(): DataResponse<List<RegionPriceRuleGrossMarginResp>?> {
        return ok(regionPriceRuleManageService.queryGrossMarginRule())
    }

    /**
     * 保存区域价格-营销费用配置
     */
    @PostMapping("/save-marketing-rule")
    fun saveMarketingRule(@Valid @RequestBody req: List<SaveRegionPriceRuleMarketingReq>): DataResponse<Void> {
        regionPriceRuleManageService.saveMarketingRule(req)
        return ok()
    }

    /**
     * 查询区域价格-营销费用配置
     */
    @GetMapping("/query-marketing-rule")
    fun queryMarketingRule(): DataResponse<List<RegionPriceRuleMarketingResp>?> {
        return ok(regionPriceRuleManageService.queryMarketingRule())
    }

    /**
     * 保存区域价格-物流支出配置
     */
    @PostMapping("/save-freight-rate-rule")
    fun saveFreightRateRule(@Valid @RequestBody req: SaveRegionPriceRuleFreightRateReq): DataResponse<Void> {
        regionPriceRuleManageService.saveFreightRateRule(req)
        return ok()
    }

    /**
     * 导入AE区域价格-物流支出配置
     */
    @PostMapping("/import-ae-freight-rate-rule")
    @Throws(IOException::class)
    fun importAEFreightRateRule(@RequestParam("excelFile") excelFile: MultipartFile): DataResponse<Void> {
        regionPriceRuleManageService.importAEFreightRateRule(excelFile)
        return ok()
    }

    /**
     * 查询区域价格-物流支出配置
     */
    @GetMapping("/get-freight-rate-rule-by-platform")
    fun getFreightRateRuleByPlatformId(@RequestParam("platform") platform:String): DataResponse<RegionPriceRuleFreightRateResp?> {
        val platformEnum = PlatformEnum.getByName(platform)?:throw BusinessException("平台编码无效${platform}")
        return ok(regionPriceRuleManageService.getFreightRateRuleByPlatform(platformEnum))
    }

    /**
     * 保存区域价格-折扣率配置
     */
    @PostMapping("/save-discount-rate-rule")
    fun saveDiscountRateRule(@Valid @RequestBody req: List<SaveRegionPriceRuleDiscountRateReq>): DataResponse<Void> {
        regionPriceRuleManageService.saveDiscountRateRule(req)
        return ok()
    }

    /**
     * 查询区域价格-折扣率配置
     */
    @GetMapping("/query-discount-rate")
    fun queryDiscountRateRule(): DataResponse<List<RegionPriceRuleDiscountRateResp>?> {
        return ok(regionPriceRuleManageService.queryDiscountRateRule())
    }

    /**
     * 保存区域价格-综合税率配置
     */
    @PostMapping("/save-tax-rate-rule")
    fun saveTaxRateRule(@Valid @RequestBody req: SaveRegionPriceRuleTaxRateReq): DataResponse<Void> {
        regionPriceRuleManageService.saveTaxRateRule(req)
        return ok()
    }

    /**
     * 查询区域价格-综合税率配置
     */
    @GetMapping("/query-tax-rate")
    fun queryTaxRateRule(): DataResponse<RegionPriceRuleTaxRateResp?> {
        return ok(regionPriceRuleManageService.queryTaxRateRule())
    }
    
    /**
     * 清除定价变量缓存
     * 参考ProductPriceCalcHelperV2#invalidateAllCache实现
     */
    @PostMapping("/clear-pricing-variable-cache")
    fun clearPricingVariableCache(): DataResponse<Void> {
        pricingVariableService.invalidateAllCache()
        regionPriceRuleService.invalidateAllCache()
        return ok()
    }

    /**
     * 计算待上架列表中的商品的售价和划线价
     */
    @PostMapping("/cal-ae-sale-retail-price-for-page")
    fun calAeSaleAndRetailPriceForPage(@RequestBody productIds: List<Long>): DataResponse<List<CalAeRetailPriceForPageResp>> {
        return ok(regionPriceRuleService.calAeSaleAndRetailPriceForPage(productIds))
    }
}
