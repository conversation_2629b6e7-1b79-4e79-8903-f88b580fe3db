package tech.tiangong.pop.controller.settings

import jakarta.validation.Valid
import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.*
import org.springframework.web.multipart.MultipartFile
import team.aikero.blade.core.protocol.DataResponse
import team.aikero.blade.core.protocol.PageVo
import team.aikero.blade.core.protocol.ok
import tech.tiangong.pop.req.settings.*
import tech.tiangong.pop.resp.ImportResultResp
import tech.tiangong.pop.resp.settings.*
import tech.tiangong.pop.service.settings.*
import java.io.IOException

/**
 * 基础设置-标题规则
 */
@Validated
@RestController
@RequestMapping("/web/v1/product-title")
class ProductTitleRuleController(
    private val productTitleRuleService: ProductTitleRuleService,
    private val productTitleHotWordService: ProductTitleHotWordService,
    private val productTitleDictionaryFieldService: ProductTitleDictionaryFieldService,
    private val productTitleConditionWordService: ProductTitleConditionWordService,
    private val productTitleConfigService: ProductTitleConfigService,
) {

    /**
     * 保存或更新商品标题规则
     */
    @PostMapping("/rule/save-or-update")
    fun saveOrUpdateRule(@RequestBody @Valid req: ProductTitleRuleSaveReq): DataResponse<Boolean> {
        return ok(productTitleRuleService.saveOrUpdate(req))
    }

    /**
     * 停用商品标题规则
     */
    @PostMapping("/rule/disable")
    fun disableRule(@RequestBody @Valid req: ProductTitleRuleDisableReq): DataResponse<Boolean> {
        return ok(productTitleRuleService.disable(req))
    }

    /**
     * 启用商品标题规则
     */
    @PostMapping("/rule/enable")
    fun enableRule(@RequestBody @Valid req: ProductTitleRuleEnableReq): DataResponse<Boolean> {
        return ok(productTitleRuleService.enable(req))
    }

    /**
     * 查询商品标题规则列表
     */
    @PostMapping("/rule/page")
    fun pageRule(@RequestBody req: ProductTitleRulePageQueryReq): DataResponse<PageVo<ProductTitleRuleResp>> {
        return ok(productTitleRuleService.page(req))
    }

    /**
     * 查询商品标题规则详情
     */
    @GetMapping("/rule/{ruleId}")
    fun queryRuleDetail(@PathVariable("ruleId") ruleId: Long): DataResponse<ProductTitleRuleResp> {
        return ok(productTitleRuleService.queryRuleDetail(ruleId))
    }

    /**
     * 分页查询商品标题热词导入记录
     */
    @PostMapping("/hot-word/import/page")
    fun pageImportHotWord(@RequestBody req: ProductTitleHotWordImportPageQueryReq): DataResponse<PageVo<ProductTitleHotWordImportPageResp>> {
        return ok(productTitleHotWordService.pageImport(req))
    }

    /**
     * 导入商品标题热词
     */
    @PostMapping("/hot-word/import")
    @Throws(IOException::class)
    fun importHotWord(
        @RequestParam("excelFile") excelFile: MultipartFile,
        @RequestParam("fileName") fileName: String
    ): DataResponse<ImportResultResp> {
        val req = ProductTitleHotWordImportReq(fileName = fileName)
        return ok(productTitleHotWordService.importHotWord(excelFile, req))
    }

    /**
     * 分页查询商品标题热词
     */
    @PostMapping("/hot-word/page")
    fun pageHotWord(@RequestBody req: ProductTitleHotWordPageQueryReq): DataResponse<PageVo<ProductTitleHotWordPageResp>> {
        return ok(productTitleHotWordService.pageHotWord(req))
    }

    /**
     * 分页查询商品标题词库字段
     */
    @PostMapping("/dictionary-field/page")
    fun pageDictionaryField(@RequestBody req: ProductTitleDictionaryFieldPageQueryReq): DataResponse<PageVo<ProductTitleDictionaryFieldPageResp>> {
        return ok(productTitleDictionaryFieldService.page(req))
    }

    /**
     * 查询商品标题词库字段详情
     */
    @GetMapping("/dictionary-field/{fieldId}")
    fun getDictionaryFieldDetail(@PathVariable("fieldId") fieldId: Long): DataResponse<ProductTitleDictionaryFieldResp> {
        return ok(productTitleDictionaryFieldService.getFieldDetail(fieldId))
    }

    /**
     * 保存或更新商品标题词库字段
     */
    @PostMapping("/dictionary-field/save-or-update")
    fun saveOrUpdateDictionaryField(@RequestBody @Valid req: ProductTitleDictionaryFieldSaveReq): DataResponse<Boolean> {
        return ok(productTitleDictionaryFieldService.saveOrUpdate(req))
    }

    /**
     * 删除商品标题词库字段
     */
    @PostMapping("/dictionary-field/delete")
    fun deleteDictionaryField(@RequestBody @Valid req: ProductTitleDictionaryFieldDeleteReq): DataResponse<Boolean> {
        return ok(productTitleDictionaryFieldService.delete(req))
    }

    /**
     * 批量导入商品标题词库翻译
     */
    @PostMapping("/dictionary-field/import-excel")
    @Throws(IOException::class)
    fun importExcelDictionaryField(@RequestParam("excelFile") excelFile: MultipartFile): DataResponse<ImportResultResp> {
        return ok(productTitleDictionaryFieldService.importExcel(excelFile))
    }

    /**
     * 分页查询商品标题条件词
     */
    @PostMapping("/condition-word/page")
    fun pageConditionWord(@RequestBody req: ProductTitleConditionWordPageQueryReq): DataResponse<PageVo<ProductTitleConditionWordPageResp>> {
        return ok(productTitleConditionWordService.page(req))
    }

    /**
     * 查询商品标题条件词详情
     */
    @GetMapping("/condition-word/{conditionWordId}")
    fun getConditionWordDetail(@PathVariable("conditionWordId") productTitleConditionWordId: Long): DataResponse<ProductTitleConditionWordResp> {
        return ok(productTitleConditionWordService.getDetail(productTitleConditionWordId))
    }

    /**
     * 保存或更新商品标题条件词
     */
    @PostMapping("/condition-word/save-or-update")
    fun saveOrUpdateConditionWord(@RequestBody @Valid req: ProductTitleConditionWordSaveReq): DataResponse<Boolean> {
        return ok(productTitleConditionWordService.saveOrUpdate(req))
    }

    /**
     * 删除商品标题条件词
     */
    @PostMapping("/condition-word/delete")
    fun deleteConditionWord(@RequestBody @Valid req: ProductTitleConditionWordDeleteReq): DataResponse<Boolean> {
        return ok(productTitleConditionWordService.delete(req))
    }

    /**
     * 清除所有缓存
     */
    @PostMapping("/invalidate-all-cache")
    fun invalidateAllCache(): DataResponse<Boolean> {
        productTitleRuleService.invalidateAllCache()
        productTitleHotWordService.invalidateAllCache()
        productTitleDictionaryFieldService.invalidateAllCache()
        productTitleConditionWordService.invalidateAllCache()
        return ok(true)
    }

    /**
     * 重置热词取值索引
     */
    @PostMapping("/hot-word/reset-index")
    fun resetIndexHotWord(): DataResponse<Boolean> {
        productTitleHotWordService.resetIndex()
        return ok(true)
    }

    /**
     * 删除热词
     */
    @PostMapping("/hot-word/delete")
    fun deleteHotWord(@RequestBody @Valid req: ProductTitleHotWordDeleteReq): DataResponse<Boolean> {
        return ok(productTitleHotWordService.delete(req))
    }

    /**
     * 保存或更新热词
     */
    @PostMapping("/hot-word/save-or-update")
    fun saveOrUpdateHotWord(@RequestBody @Valid req: ProductTitleHotWordSaveReq): DataResponse<Boolean> {
        return ok(productTitleHotWordService.saveOrUpdate(req))
    }

    /**
     * 分页查询标题配置
     */
    @PostMapping("/config/page")
    fun pageConfig(@RequestBody req: ProductTitleConfigPageQueryReq): DataResponse<PageVo<ProductTitleConfigResp>> {
        return ok(productTitleConfigService.pageConfig(req))
    }

    /**
     * 新建标题配置
     */
    @PostMapping("/config/create")
    fun createConfig(@RequestBody @Valid req: ProductTitleConfigCreateReq): DataResponse<Boolean> {
        return ok(productTitleConfigService.createConfig(req) > 0)
    }

    /**
     * 修改标题数量
     */
    @PostMapping("/config/update-count")
    fun updateConfigCount(@RequestBody @Valid req: ProductTitleConfigUpdateCountReq): DataResponse<Boolean> {
        return ok(productTitleConfigService.updateTitleCount(req))
    }

    /**
     * 标题规则应用保存/更新
     */
    @PostMapping("/config/apply-rule")
    fun applyShopConfig(@RequestBody @Valid req: ProductTitleConfigApplyRuleReq): DataResponse<Boolean> {
        return ok(productTitleConfigService.applyRule(req))
    }

    /**
     * 店铺应用保存/更新
     */
    @PostMapping("/config/apply-shop")
    fun applyShopConfig(@RequestBody @Valid req: ProductTitleConfigApplyShopReq): DataResponse<Boolean> {
        return ok(productTitleConfigService.applyShop(req))
    }

    /**
     * 根据ID查询标题配置详情
     */
    @GetMapping("/config/{configId}")
    fun getDetail(@PathVariable("configId") configId: Long): DataResponse<ProductTitleConfigResp> {
        return ok(productTitleConfigService.getDetail(configId))
    }
}