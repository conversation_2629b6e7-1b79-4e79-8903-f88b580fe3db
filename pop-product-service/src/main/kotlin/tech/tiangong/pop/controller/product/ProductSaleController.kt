package tech.tiangong.pop.controller.product

import jakarta.servlet.http.HttpServletResponse
import jakarta.validation.Valid
import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import team.aikero.blade.core.protocol.DataResponse
import team.aikero.blade.core.protocol.ok
import tech.tiangong.pop.common.enums.PlatformEnum
import tech.tiangong.pop.req.product.ae.*
import tech.tiangong.pop.resp.product.CountSystemUpdateFailResp
import tech.tiangong.pop.resp.product.ae.AeConfirmUpdateSupplyPriceQueryResp
import tech.tiangong.pop.service.product.ProductAeConfirmUpdateService
import tech.tiangong.pop.service.product.ProductLazadaConfirmUpdateService

/**
 * 商品管理-已上架-共用接口
 * <AUTHOR>
 * @date 2025-2-12 10:31:11
 */
@Validated
@RestController
@RequestMapping("/web/v1/product/sale")
class ProductSaleController(
    private val productAeConfirmUpdateService: ProductAeConfirmUpdateService,
    private val productLazadaConfirmUpdateService: ProductLazadaConfirmUpdateService,
) {

    /**
     * 查看确认更新-供货价更新-弹窗
     */
    @PostMapping("/confirm-update/supply-price/query")
    fun confirmUpdateSupplyPriceQuery(@Valid @RequestBody req: AeConfirmUpdateSupplyPriceQueryReq): DataResponse<List<AeConfirmUpdateSupplyPriceQueryResp>> {
        return when (req.platform) {
            PlatformEnum.AE -> {
                ok(productAeConfirmUpdateService.confirmUpdateSupplyPriceQuery(req))
            }

            PlatformEnum.LAZADA -> {
                ok(productLazadaConfirmUpdateService.confirmUpdateSupplyPriceQuery(req))
            }

            else -> {
                throw Exception("平台不支持")
            }
        }
    }

    /**
     * 查看确认更新-供货价更新-确认更新(改价)
     */
    @PostMapping("/confirm-update/supply-price/update")
    fun confirmUpdateSupplyPriceUpdate(@Valid @RequestBody req: AeConfirmUpdateSupplyPriceUpdateReq): DataResponse<Unit> {
        return when (req.platform) {
            PlatformEnum.AE -> {
                ok(productAeConfirmUpdateService.confirmUpdateSupplyPriceUpdate(req))
            }

            PlatformEnum.LAZADA -> {
                ok(productLazadaConfirmUpdateService.confirmUpdateSupplyPriceUpdate(req))
            }

            else -> {
                throw Exception("平台不支持")
            }
        }
    }

    /**
     * 超价-价格判断-清仓
     */
    @PostMapping("/price/overrange/clearance")
    fun priceOverrangeClearance(@Valid @RequestBody req: PriceOverrangeClearanceReq): DataResponse<Unit> {
        return when (req.platform) {
            PlatformEnum.AE -> {
                ok(productAeConfirmUpdateService.priceOverrangeClearance(req))
            }

            PlatformEnum.LAZADA -> {
                ok(productLazadaConfirmUpdateService.priceOverrangeClearance(req))
            }

            else -> {
                throw Exception("平台不支持")
            }
        }
    }

    /**
     * 超价-价格判断-重上
     */
    @PostMapping("/price/overrange/re-create")
    fun priceOverrangeReCreate(@Valid @RequestBody req: PriceOverrangeReCreateReq): DataResponse<Unit> {
        return when (req.platform) {
            PlatformEnum.AE -> {
                ok(productAeConfirmUpdateService.priceOverrangeReCreate(req))
            }

            PlatformEnum.LAZADA -> {
                ok(productLazadaConfirmUpdateService.priceOverrangeReCreate(req))
            }

            else -> {
                throw Exception("平台不支持")
            }
        }
    }

    /**
     * 超价-统计系统更新失败数量
     */
    @PostMapping("/price/overrange/system-update-fail/count")
    fun countSystemUpdateFail(@Valid @RequestBody req: CountSystemUpdateFailReq): DataResponse<CountSystemUpdateFailResp> {
        return when (req.platform) {
            PlatformEnum.AE -> {
                ok(productAeConfirmUpdateService.countSystemUpdateFail(req))
            }

            PlatformEnum.LAZADA -> {
                ok(productLazadaConfirmUpdateService.countSystemUpdateFail(req))
            }

            else -> {
                throw Exception("平台不支持")
            }
        }
    }

    /**
     * 系统更新失败-导出结果
     * @param req
     * @param response
     * @return
     */
    @PostMapping("/update/fail/export")
    fun exportByUpdateFail(@Validated @RequestBody req: ExportByUpdateReq, response: HttpServletResponse): DataResponse<Unit> {
        when (req.platform) {
            PlatformEnum.AE -> {
                productAeConfirmUpdateService.exportByUpdate(req, response)
            }

            PlatformEnum.LAZADA -> {
                //lazada只导出更新失败的
                req.updateState = 3
                productLazadaConfirmUpdateService.exportByUpdate(req, response)
            }

            else -> {
                throw Exception("平台不支持")
            }
        }
        return ok()
    }
}
