package tech.tiangong.pop.controller.product.pending

import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.*
import org.springframework.web.multipart.MultipartFile
import team.aikero.blade.core.protocol.DataResponse
import team.aikero.blade.core.protocol.PageVo
import team.aikero.blade.core.protocol.failed
import team.aikero.blade.core.protocol.ok
import tech.tiangong.pop.component.export.TemuProductExportComponent
import tech.tiangong.pop.component.export.pending.error.publish.TemuPublishErrorLogExportComponent
import tech.tiangong.pop.component.export.pending.structure.TemuPlatformStructureExportComponent
import tech.tiangong.pop.component.temu.CallTemuComponent
import tech.tiangong.pop.req.product.ProductPendingPublishLogPageReq
import tech.tiangong.pop.req.product.ae.ProductPendingAePageReq
import tech.tiangong.pop.req.product.temu.*
import tech.tiangong.pop.resp.product.ProductPendingPublishLogPageResp
import tech.tiangong.pop.resp.product.ProductPendingPublishLogStatisticsResp
import tech.tiangong.pop.resp.product.ae.ProductPendingAeStatisticsResp
import tech.tiangong.pop.resp.product.temu.ProductPendingTemuDetailResp
import tech.tiangong.pop.resp.product.temu.ProductPendingTemuPageResp
import tech.tiangong.pop.service.product.pending.ProductPendingTemuService
import java.io.IOException

/**
 * 待上架商品-Temu
 */
@RestController
@RequestMapping("/web/v1/product/pending/temu")
class ProductPendingTemuController(
    private val productPendingTemuService: ProductPendingTemuService,
    private val callTemuComponent: CallTemuComponent,
    private val temuPublishErrorLogExportComponent: TemuPublishErrorLogExportComponent,
    private val temuProductExportComponent: TemuProductExportComponent,
    private val temuPlatformStructureExportComponent: TemuPlatformStructureExportComponent,
) {

    /**
     * 商品列表-分页
     * @tags v0904
     */
    @PostMapping("/page")
    fun page(@Validated @RequestBody req: ProductPendingTemuPageReq): DataResponse<PageVo<ProductPendingTemuPageResp>> {
        return ok(productPendingTemuService.page(req))
    }

    /**
     * 商品列表-统计
     * @tags v0904
     */
    @PostMapping("/statistics")
    fun statistics(@Validated @RequestBody req: ProductPendingAePageReq): DataResponse<ProductPendingAeStatisticsResp> {
        return ok(productPendingTemuService.statistics(req))
    }

    /**
     * 商品详情
     * @tags v0904
     */
    @PostMapping("/detail")
    fun detail(@Validated @RequestBody req: ProductPendingTemuDetailReq): DataResponse<ProductPendingTemuDetailResp> {
        return ok(productPendingTemuService.detail(req))
    }

    /**
     * 商品更新
     * @tags v0904
     */
    @PostMapping("/update")
    fun update(@Validated @RequestBody req: ProductPendingUpdateTemuReq): DataResponse<Unit> {
        productPendingTemuService.update(req)
        return ok()
    }

    /**
     * 任务操作-开始任务
     * @tags v0904
     */
    @PostMapping("/task/start")
    fun start(@Validated @RequestBody temuSpuIds: List<Long>): DataResponse<Unit> {
        if (temuSpuIds.isEmpty()) {
            return failed("请选择商品")
        }
        productPendingTemuService.start(temuSpuIds)
        return ok()
    }

    /**
     * 任务操作-更换人员
     * @tags v0904
     */
    @PostMapping("/task/change")
    fun change(@Validated @RequestBody req: ProductPendingTemuTaskChangeReq): DataResponse<Unit> {
        if (req.temuSpuIds.isEmpty()) {
            return failed("请选择商品")
        }
        if (req.users.isEmpty()) {
            return failed("请选择人员")
        }
        productPendingTemuService.change(req)
        return ok()
    }

    /**
     * 任务操作-取消上架
     * @tags v0904
     */
    @PostMapping("/task/cancel")
    fun cancel(@Validated @RequestBody req: ProductPendingTemuTaskCancelReq): DataResponse<Unit> {
        productPendingTemuService.cancel(req)
        return ok()
    }

    /**
     * 上架Temu平台
     * @tags v0904
     *
     * @param req 入参
     */
    @PostMapping("/platform/update")
    fun platformUpdate(@Validated @RequestBody req: List<ProductPendingTemuPlatformUpdateReq>): DataResponse<Unit> {
        if (req.isEmpty()) {
            return failed("请选择商品")
        }
        req.forEach {
            callTemuComponent.publishProduct(it)
        }
        return ok()
    }

    /**
     * 导出-待上架-上架失败
     * @tags v0904
     */
    @PostMapping("/export/publish-error-log")
    fun exportPublishErrorLog(@Validated @RequestBody req: ProductPendingTemuPageReq): DataResponse<Unit> {
        temuPublishErrorLogExportComponent.createExportTask(req)
        return ok()
    }

    /**
     * 上架记录-统计
     * @tags v0904
     */
    @PostMapping("/publish/log/statistics")
    fun publishLogStatistics(): DataResponse<ProductPendingPublishLogStatisticsResp> {
        return ok(productPendingTemuService.publishLogStatistics())
    }

    /**
     * 上架记录-分页列表
     * @tags v0904
     */
    @PostMapping("/publish/log/page")
    fun publishLogPage(@RequestBody req: ProductPendingPublishLogPageReq): DataResponse<PageVo<ProductPendingPublishLogPageResp>> {
        return ok(productPendingTemuService.publishLogPage(req))
    }

    /**
     * 导出-待上架-商品数据
     * @tags v0904
     */
    @PostMapping("/export-product")
    fun exportProductPublishDataAsync(@Validated @RequestBody req: ProductPendingTemuPageReq): DataResponse<Unit> {
        temuProductExportComponent.createExportTask(req)
        return ok()
    }

    /**
     * 导入-待上架-商品数据
     * @tags v0904
     */
    @PostMapping("/import-product")
    @Throws(IOException::class)
    fun importExcel(@RequestParam("excelFile") excelFile: MultipartFile): DataResponse<Unit> {
        productPendingTemuService.importExcel(excelFile)
        return ok()
    }

    /**
     * 导出商品数据-平台结构
     * @tags v0904
     */
    @PostMapping("/export/platform/submit-export-task")
    fun exportPlatformSubmitExportTask(@Validated @RequestBody req: ProductPendingTemuPageReq): DataResponse<Unit> {
        temuPlatformStructureExportComponent.createExportTask(req)
        return ok()
    }
}