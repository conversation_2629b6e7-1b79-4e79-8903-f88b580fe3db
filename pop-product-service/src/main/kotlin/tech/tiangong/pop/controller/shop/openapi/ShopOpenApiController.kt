package tech.tiangong.pop.controller.shop.openapi

import com.lazada.lazop.util.ApiException
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import team.aikero.blade.core.protocol.DataResponse
import team.aikero.blade.core.protocol.ok
import tech.tiangong.pop.resp.sdk.aliexpress.AliexpressAuthTokenResponse
import tech.tiangong.pop.resp.sdk.lazada.GetTokenResponse
import tech.tiangong.pop.service.product.ShopService

@RestController
@RequestMapping("/openapi/v1" + "/shop")
class ShopOpenApiController(
    private val shopService: ShopService
) {

    /**
     * 授权回调
     *
     * @param code code
     * @return GetTokenResponse
     */
    @GetMapping("/callback/code")
    @Throws(ApiException::class)
    fun code(@RequestParam("code") code: String): DataResponse<GetTokenResponse> {
        return ok(shopService.getToken(code))
    }

    /**
     * AE 授权回调
     *
     * @param code code
     * @return AuthTokenCreateResponse
     */
    @GetMapping("/callback/ae/code")
    @Throws(ApiException::class)
    fun getAEToken(@RequestParam("code") code: String): DataResponse<AliexpressAuthTokenResponse> {
        return ok(shopService.getAEToken(code))
    }
}
