package tech.tiangong.pop.controller.job

import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import team.aikero.blade.auth.annotation.PreCheckIgnore
import team.aikero.blade.auth.withSystemUser
import team.aikero.blade.core.protocol.DataResponse
import team.aikero.blade.core.protocol.ok
import tech.tiangong.pop.req.tryon.GetAiTryOnDetailReq
import tech.tiangong.pop.service.tryon.TryOnTaskService

/**
 * TryOn定时任务
 * */
@PreCheckIgnore
@RestController
@RequestMapping("/job/v1/try-on")
class JobTryOnController(
    private val tryOnTaskService: TryOnTaskService,
) {

    /**
     * 同步AI的tryOn任务结果
     */
    @PostMapping("/sync/ai/try-on/detail")
    fun syncAiTryOnDetail(@RequestBody(required = false) req: GetAiTryOnDetailReq?): DataResponse<Unit> {
        withSystemUser {
            tryOnTaskService.syncAiTryOnDetail(req ?: GetAiTryOnDetailReq())
        }
        return ok()
    }
}
