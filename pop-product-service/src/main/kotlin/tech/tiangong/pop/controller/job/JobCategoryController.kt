package tech.tiangong.pop.controller.job

import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import team.aikero.blade.auth.annotation.PreCheckIgnore
import team.aikero.blade.auth.withSystemUser
import team.aikero.blade.core.protocol.DataResponse
import team.aikero.blade.core.protocol.ok
import tech.tiangong.pop.service.DictToCategoryService

/**
 * 定时任务-品类
 * <AUTHOR>
 * @date 2024/12/9 19:32
 */
@RestController
@RequestMapping("/job/v1/category")
@PreCheckIgnore
class JobCategoryController(
    private val dictToCategoryService: DictToCategoryService,
) {

    /**
     * 同步字典品类到pop品类
     * {"method":"POST","url":"/pop-product-service/inner/v1/job/category/sync/dict","system":"ola", "env":"dev"}
     * @param req
     */
    @PostMapping("/sync/dict")
    fun syncDict(): DataResponse<Unit> {
        withSystemUser {
            dictToCategoryService.dictToCategory()
        }
        return ok()
    }

}