package tech.tiangong.pop.controller.category
import cn.yibuyun.framework.web.base.UrlVersionConstant.WEB
import jakarta.servlet.http.HttpServletRequest
import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.*
import org.springframework.web.multipart.MultipartFile
import team.aikero.blade.core.protocol.DataResponse
import team.aikero.blade.core.protocol.PageVo
import team.aikero.blade.core.protocol.ok
import tech.tiangong.pop.req.category.*
import tech.tiangong.pop.req.product.temu.TemuBaseReq
import tech.tiangong.pop.resp.category.CategoryPackageConfigResp
import tech.tiangong.pop.resp.category.PublishCategoryMappingDetailVo
import tech.tiangong.pop.resp.category.PublishCategoryMappingVo
import tech.tiangong.pop.resp.category.PublishPlatformCategoryVo
import tech.tiangong.pop.service.CategoryMappingImportService
import tech.tiangong.pop.service.ImportResult
import tech.tiangong.pop.service.category.PublishCategoryMappingService
import tech.tiangong.pop.service.category.PublishCategoryPackageConfigService

/**
 * 品类包装配置
 *
 */
@RestController
@RequestMapping("$WEB/v1/publish-category-package-config")
class PublishCategoryPackageConfigController(
    private val publishCategoryPackageConfigService: PublishCategoryPackageConfigService
) {

    /**
     * 保存品类包装配置
     *
     * @param req 入参
     */
    @PostMapping("/save")
    fun save(@RequestBody @Validated req: SavePublishCategoryPackageConfigReq): DataResponse<Unit> {
        publishCategoryPackageConfigService.save(req)
        return ok()
    }

    /**
     * 根据品类ID查询包装配置
     *
     * @param req 入参
     */
    @GetMapping("/get-by-category-id")
    fun getByCategoryId(@RequestParam("categoryId") categoryId:Long): DataResponse<CategoryPackageConfigResp?> {
        return ok(publishCategoryPackageConfigService.getByCategoryId(categoryId))
    }

    /**
     * 复制包装配置
     */
    @PostMapping("/copy")
    fun copy(@RequestBody @Validated req: CopyPublishCategoryPackageConfigReq): DataResponse<Unit> {
        publishCategoryPackageConfigService.copy(req)
        return ok()
    }
}