package tech.tiangong.pop.controller.category

import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.*
import org.springframework.web.multipart.MultipartFile
import team.aikero.blade.core.constant.UrlVersionConstant.WEB
import team.aikero.blade.core.protocol.DataResponse
import team.aikero.blade.core.protocol.ok
import tech.tiangong.pop.req.category.PublishCategoryTreeQueryReq
import tech.tiangong.pop.req.category.SavePublishCategoryReq
import tech.tiangong.pop.resp.category.PublishCategoryMappingTreeNodeVo
import tech.tiangong.pop.resp.category.PublishCategoryTreeNodeVo
import tech.tiangong.pop.service.category.PublishCategoryService

/**
 * 上架品类管理
 */
@RestController
@RequestMapping("$WEB/v1/publish-category")
class PublishCategoryController(
    private val publishCategoryService: PublishCategoryService
) {

    /**
     * 树形列表
     *
     * @param req 分页对象
     * @return 品类树形列表
     */
    @PostMapping("/tree")
    fun tree(@RequestBody req: PublishCategoryTreeQueryReq): DataResponse<List<PublishCategoryTreeNodeVo>> {
        return ok(publishCategoryService.tree(req))
    }

    /**
     * 根据已知品类映射ID查询已映射到同平台同站点同第三方品类的内部品类
     *
     * @param req 分页对象
     * @return 品类树形列表
     */
    @PostMapping("/get-mapping-category-tree")
    fun getMappingCategoryTree(@RequestParam("categoryMappingId") categoryMappingId: Long): DataResponse<List<PublishCategoryMappingTreeNodeVo>> {
        return ok(publishCategoryService.getMappingCategoryTree(categoryMappingId))
    }
    /**
     * 保存品类
     *
     * @param savePublishCategoryReq 品类保存请求
     * @return 操作结果
     */
    @PostMapping("/save")
    fun save(@RequestBody @Validated savePublishCategoryReq: SavePublishCategoryReq): DataResponse<Void> {
        publishCategoryService.save(savePublishCategoryReq)
        return ok()
    }

    /**
     * 删除品类
     *
     * @param publishCategoryIds 入参
     * @return 操作结果
     * @date 2024/8/28
     */
    @PostMapping("/delete")
    fun delete(@RequestBody publishCategoryIds: List<Long>): DataResponse<Void> {
        publishCategoryService.deleteCategory(publishCategoryIds)
        return ok()
    }

    /**
     * 导入
     *
     * @param file 导入文件
     * @return 导入结果信息
     */
    @PostMapping("/import")
    fun importExcel(@RequestParam("file") file: MultipartFile): DataResponse<List<String>> {
        publishCategoryService.importCategory(file)
        return ok()
    }
}