package tech.tiangong.pop.controller.settings

import jakarta.validation.Valid
import org.springframework.web.bind.annotation.*
import team.aikero.blade.core.protocol.DataResponse
import team.aikero.blade.core.protocol.PageVo
import team.aikero.blade.core.protocol.ok
import tech.tiangong.pop.enums.settings.ProductBasicPriceRuleTypeEnum
import tech.tiangong.pop.req.settings.BatchProductBasicPriceRuleSaveReq
import tech.tiangong.pop.req.settings.ProductBasicPriceRulePageReq
import tech.tiangong.pop.resp.settings.ProductBasicPriceRulePageResp
import tech.tiangong.pop.service.settings.ProductBasicPriceRuleService

/**
 * 基础配置-商品销售定价基础价格配置
 */
@RestController
@RequestMapping("/web/v1/product-basic-price-rule")
class ProductBasicPriceRuleController(
    private val productBasicPriceRuleService: ProductBasicPriceRuleService
) {
    /**
     * 分页查询基础价格规则
     */
    @PostMapping("/page")
    fun page(@Valid @RequestBody req: ProductBasicPriceRulePageReq): DataResponse<PageVo<ProductBasicPriceRulePageResp>> {
        return ok(productBasicPriceRuleService.page(req))
    }

    /**
     * 查询ISP倍率规则列表
     */
    @GetMapping("/isp-rate/list")
    fun listIspRate(): DataResponse<List<ProductBasicPriceRulePageResp>> {
        return ok(productBasicPriceRuleService.listByRuleType(ProductBasicPriceRuleTypeEnum.ISP_RATE))
    }

    /**
     * 查询SP倍率规则列表
     */
    @GetMapping("/sp-rate/list")
    fun listSpRate(): DataResponse<List<ProductBasicPriceRulePageResp>> {
        return ok(productBasicPriceRuleService.listByRuleType(ProductBasicPriceRuleTypeEnum.SP_RATE))
    }

    /**
     * 批量保存或更新基础价格规则
     */
    @PostMapping("/batch-save-or-update")
    fun batchSaveOrUpdate(@Valid @RequestBody req: BatchProductBasicPriceRuleSaveReq): DataResponse<Boolean> {
        return ok(productBasicPriceRuleService.batchSaveOrUpdate(req))
    }

    /**
     * 删除价格规则
     */
    @PostMapping("/delete/{ruleId}")
    fun delete(@PathVariable ruleId: Long): DataResponse<Boolean> {
        return ok(productBasicPriceRuleService.delete(ruleId))
    }

    /**
     * 提供一个手动刷新缓存的REST接口
     */
    @GetMapping("/refresh-cache")
    fun refreshCache(): DataResponse<Unit> {
        productBasicPriceRuleService.invalidateAllCache()
        return ok()
    }
}
