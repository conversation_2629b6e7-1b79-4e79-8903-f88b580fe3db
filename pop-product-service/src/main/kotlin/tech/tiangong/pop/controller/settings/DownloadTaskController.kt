package tech.tiangong.pop.controller.settings

import org.springframework.web.bind.annotation.*
import team.aikero.blade.core.protocol.DataResponse
import team.aikero.blade.core.protocol.PageVo
import team.aikero.blade.core.protocol.ok
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import tech.tiangong.pop.req.settings.DownloadTaskPageQueryReq
import tech.tiangong.pop.resp.settings.DownloadTaskPageVo
import tech.tiangong.pop.service.settings.DownloadTaskHandleService
import tech.tiangong.pop.service.settings.DownloadTaskService

/**
 * 基础配置-下载任务管理
 */
@Slf4j
@RestController
@RequestMapping("web/v1/download-task")
class DownloadTaskController(
    private val downloadTaskService: DownloadTaskService,
    private val downloadTaskHandleService: DownloadTaskHandleService
) {

    /**
     * 分页列表
     *
     * @param req 查询参数
     * @return 下载任务分页列表
     */
    @PostMapping("/page")
    fun page(@RequestBody req: DownloadTaskPageQueryReq): DataResponse<PageVo<DownloadTaskPageVo>> {
        log.info { "查询下载任务分页列表，参数: $req" }
        return ok(downloadTaskService.page(req))
    }

    /**
     * 取消下载
     *
     * @param taskId 任务主键
     * @return 操作结果
     */
    @PostMapping("/cancel/{taskId}")
    fun cancelTask(@PathVariable taskId: Long): DataResponse<Unit> {
        log.info { "取消下载任务，任务ID: $taskId" }
        downloadTaskService.cancelTask(taskId)
        return ok()
    }

    /**
     * 重试下载
     *
     * @param taskId 任务主键
     * @return 操作结果
     */
    @PostMapping("/retry/{taskId}")
    fun retryTask(@PathVariable taskId: Long): DataResponse<Unit> {
        log.info { "重试下载任务，任务ID: $taskId" }
        downloadTaskService.retryTask(taskId)
        return ok()
    }

    /**
     * 处理单个任务
     *
     * @param taskId 任务主键
     * @return 操作结果
     */
    @PostMapping("/process/{taskId}")
    fun processTask(@PathVariable taskId: Long): DataResponse<Unit> {
        log.info { "处理单个任务，任务ID: $taskId" }
        downloadTaskHandleService.processTask(taskId)
        return ok()
    }

    /**
     * 处理单个任务-忽略其状态是否已完成等
     *
     * @param taskId 任务主键
     * @return 操作结果
     */
    @PostMapping("/force-process/{taskId}")
    fun forceProcessTask(@PathVariable taskId: Long): DataResponse<Unit> {
        log.info { "强制处理单个任务，任务ID: $taskId" }
        downloadTaskHandleService.forceProcessTask(taskId)
        return ok()
    }
}