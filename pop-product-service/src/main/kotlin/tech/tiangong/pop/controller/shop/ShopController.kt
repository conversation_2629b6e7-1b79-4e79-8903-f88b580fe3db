package tech.tiangong.pop.controller.shop

import com.lazada.lazop.util.ApiException
import org.springframework.beans.BeanUtils
import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.*
import team.aikero.blade.core.constant.UrlVersionConstant.WEB
import team.aikero.blade.core.protocol.DataResponse
import team.aikero.blade.core.protocol.PageVo
import team.aikero.blade.core.protocol.ok
import tech.tiangong.pop.common.req.ShopBrandReq
import tech.tiangong.pop.common.req.ShopListReq
import tech.tiangong.pop.common.req.ShopPageReq
import tech.tiangong.pop.common.req.ShopReq
import tech.tiangong.pop.common.resp.ShopBrandResp
import tech.tiangong.pop.common.resp.ShopResp
import tech.tiangong.pop.req.shop.ShopEdit
import tech.tiangong.pop.service.product.ShopService

/**
 * 店铺管理
 */
@RestController
@RequestMapping("$WEB/v1/shop")
class ShopController(
    private val shopService: ShopService,
) {

    /**
     * 获取店铺分页列表
     *
     * @param req 查询条件
     * @return 店铺列表
     */
    @PostMapping("/page")
    fun page(@RequestBody @Validated req: ShopPageReq): DataResponse<PageVo<ShopResp>> {
        return ok(shopService.page(req))
    }

    /**
     * 获取店铺列表
     *
     * @param req 查询条件
     * @return 店铺列表
     */
    @PostMapping("/list")
    fun list(@RequestBody @Validated req: ShopListReq?): DataResponse<List<ShopResp>> {
        val effectiveReq = req ?: ShopListReq()
        val shopReq = ShopReq()
        BeanUtils.copyProperties(effectiveReq, shopReq)
        if (effectiveReq.platform != null) {
            shopReq.platformId = effectiveReq.platform!!.platformId
        }
        return ok(shopService.getShopList(shopReq))
    }

    /**
     * 新增店铺
     *
     * @param req req
     * @return Void
     */
    @PostMapping("/save")
    fun saveShop(@RequestBody @Validated req: ShopEdit): DataResponse<Void> {
        shopService.saveShop(req)
        return ok()
    }

    /**
     * 编辑店铺
     *
     * @param req req
     * @return Void
     */
    @PostMapping("/edit")
    fun editShop(@RequestBody @Validated req: ShopEdit): DataResponse<Void> {
        shopService.editShop(req)
        return ok()
    }

    /**
     * 获取品牌列表
     *
     * @param req 查询条件
     * @return 品牌列表
     */
    @PostMapping("/brands")
    fun getBrandList(@RequestBody @Validated req: ShopBrandReq): DataResponse<List<ShopBrandResp>> {
        return ok(shopService.getBrandList(req))
    }

    /**
     * 解绑授权
     *
     * @param shopId
     * @return
     */
    @GetMapping("/cancel/{shopId}")
    fun cancelAuth(@PathVariable(name = "shopId") @Validated shopId: Long): DataResponse<Void> {
        shopService.cancelAuth(shopId)
        return ok()
    }

    /**
     * 初始化品牌
     *
     * @param countryCode 站点
     */
    @PostMapping("/init-brand")
    @Throws(ApiException::class, InterruptedException::class)
    fun initBrand(@RequestParam("countryCode") countryCode: String): DataResponse<Void> {
        shopService.initBrand(countryCode)
        return ok()
    }
}
