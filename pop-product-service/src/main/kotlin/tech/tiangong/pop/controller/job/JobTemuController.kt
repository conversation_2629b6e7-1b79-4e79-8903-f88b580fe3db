package tech.tiangong.pop.controller.job

import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import team.aikero.blade.auth.annotation.PreCheckIgnore
import team.aikero.blade.auth.withSystemUser
import team.aikero.blade.core.protocol.DataResponse
import team.aikero.blade.core.protocol.ok
import tech.tiangong.pop.req.product.temu.GetSelectStatusReq
import tech.tiangong.pop.service.product.ProductTemuPullService

/**
 * Temu定时任务
 * */
@PreCheckIgnore
@RestController
@RequestMapping("/job/v1/temu")
class JobTemuController(
    private val productTemuPullService: ProductTemuPullService,
) {

    /**
     * 拉取商品的最新生命周期
     */
    @PostMapping("/get/select/status")
    fun getSelectStatus(@RequestBody(required = false) req: GetSelectStatusReq?): DataResponse<Unit> {
        withSystemUser {
            productTemuPullService.getSelectStatus(req ?: GetSelectStatusReq())
        }
        return ok()
    }



    /**
     * 拉取商品的最新库存  skc维度
     */
    @PostMapping("/get/select/quantity")
    fun getSelectQuantity(@RequestBody(required = false) req: GetSelectStatusReq?): DataResponse<Unit> {
        withSystemUser {
            productTemuPullService.getSelectQuantity(req ?: GetSelectStatusReq())
        }
        return ok()
    }

}
