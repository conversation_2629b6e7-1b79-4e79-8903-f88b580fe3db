package tech.tiangong.pop.controller.category.inner
import jakarta.annotation.Resource
import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.*
import team.aikero.blade.auth.annotation.PreCheckIgnore
import team.aikero.blade.auth.withSystemUser
import team.aikero.blade.core.constant.UrlVersionConstant.INNER
import team.aikero.blade.core.protocol.DataResponse
import team.aikero.blade.core.protocol.ok
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.util.async.runAsync
import tech.tiangong.pop.common.enums.PlatformEnum
import tech.tiangong.pop.common.req.PlatformCategoryQueryReq
import tech.tiangong.pop.common.resp.AlibabaCategoryResp
import tech.tiangong.pop.common.resp.BasePlatformCategoryResp
import tech.tiangong.pop.common.resp.CategoryAttributeResp
import tech.tiangong.pop.service.category.PublishCategoryAttributeService
import tech.tiangong.pop.service.category.PublishCategoryMappingService
import java.util.concurrent.ExecutorService
import java.util.function.Consumer

/**
 * 关联平台品类
 *
 * @date 2024/8/28
 */
@RestController
@RequestMapping("$INNER/v1/publish-category-mapping")
class PublishCategoryMappingInnerController(
    private val publishCategoryMappingService: PublishCategoryMappingService,
    private val publishCategoryAttributeService: PublishCategoryAttributeService,
    @Resource(name = "asyncExecutor")
    private val asyncExecutor: ExecutorService,
) {
    /**
     * 根据1688类目ID查询已同步到商品平台的1688品类
     *
     * @param req
     */
    @PostMapping("/query-alibaba-category-by-platform-category-id")
    @PreCheckIgnore
    fun queryAlibabaCategoryByPlatformCategoryId(@RequestBody @Validated req: PlatformCategoryQueryReq): DataResponse<List<AlibabaCategoryResp>>{
        req.platformId = PlatformEnum.YI_LIU_BA_BA.platformId
        val result:MutableList<AlibabaCategoryResp> = mutableListOf()
        val list = publishCategoryMappingService.queryPlatformCategoryByPlatformCategoryId(req)
        list.forEach(Consumer { v: BasePlatformCategoryResp? ->
            if (v is AlibabaCategoryResp) {
                result.add(v)
            }
        })
        return ok(result)
    }

    /**
     * 根据品类名称（多级-分割）查询品类属性
     */
    @PostMapping("/list-attribute-by-category-name")
    @PreCheckIgnore
    fun listAttributeByCategoryName(@RequestParam("categoryName") categoryName:String):DataResponse<List<CategoryAttributeResp>>{
        return ok(publishCategoryMappingService.listEnableAttributesByCategoryId(categoryName))
    }

    /**
     * 更新已关联的第三方平台属性必填字段
     */
    @PostMapping("/update/platform-attr-request-flag")
    fun updatePlatformAttributeRequestFlag(@RequestBody categoryMappingIds:List<Long>):DataResponse<Unit>{
        categoryMappingIds.forEach {
            runAsync(asyncExecutor) {
                log.info { "======categoryMappingId===${it}" }
                withSystemUser {
                    publishCategoryAttributeService.updatePlatformAttributeRequestFlag(it)
                }
            }
        }
        return ok()
    }
}