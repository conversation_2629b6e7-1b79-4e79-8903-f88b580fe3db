package tech.tiangong.pop.controller.settings

import jakarta.validation.Valid
import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.*
import team.aikero.blade.core.protocol.DataResponse
import team.aikero.blade.core.protocol.PageVo
import team.aikero.blade.core.protocol.ok
import tech.tiangong.pop.req.settings.BatchPriceAlertConfigurationSaveReq
import tech.tiangong.pop.req.settings.PriceAlertConfigurationQueryReq
import tech.tiangong.pop.req.settings.PriceAlertConfigurationSaveReq
import tech.tiangong.pop.resp.settings.PriceAlertConfigurationResp
import tech.tiangong.pop.service.settings.PriceAlertConfigurationService

/**
 * 基础配置-价格预警配置
 */
@Validated
@RestController
@RequestMapping("/web/v1/price-alert-configuration")
class PriceAlertConfigurationController(
    private val priceAlertConfigurationService: PriceAlertConfigurationService
) {
    /**
     * 分页查询价格预警配置
     */
    @PostMapping("/page")
    fun page(@RequestBody @Valid req: PriceAlertConfigurationQueryReq): DataResponse<PageVo<PriceAlertConfigurationResp>> {
        return ok(priceAlertConfigurationService.page(req))
    }

    /**
     * 查询价格预警配置详情
     */
    @GetMapping("/{id}")
    fun getById(@PathVariable("id") id: Long): DataResponse<PriceAlertConfigurationResp?> {
        return ok(priceAlertConfigurationService.getById(id))
    }

    /**
     * 保存或更新价格预警配置
     */
    @PostMapping("/save-or-update")
    fun saveOrUpdate(@RequestBody @Valid req: PriceAlertConfigurationSaveReq): DataResponse<Boolean> {
        return ok(priceAlertConfigurationService.saveOrUpdate(req))
    }

    /**
     * 删除价格预警配置
     */
    @DeleteMapping("/{id}")
    fun delete(@PathVariable("id") id: Long): DataResponse<Boolean> {
        return ok(priceAlertConfigurationService.delete(id))
    }

    /**
     * 批量保存或更新价格预警配置
     */
    @PostMapping("/batch-save-or-update")
    fun batchSaveOrUpdate(@RequestBody @Valid batchReq: BatchPriceAlertConfigurationSaveReq): DataResponse<Boolean> {
        return ok(priceAlertConfigurationService.batchSaveOrUpdate(batchReq))
    }

    /**
     * 清除价格预警配置缓存
     */
    @PostMapping("/invalidate-cache")
    fun invalidateCache(): DataResponse<Boolean> {
        priceAlertConfigurationService.invalidateCache()
        return ok(true)
    }
}