package tech.tiangong.pop.controller.product

import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import team.aikero.blade.core.constant.UrlVersionConstant.WEB
import team.aikero.blade.core.protocol.DataResponse
import team.aikero.blade.core.protocol.ok
import tech.tiangong.pop.req.product.*
import tech.tiangong.pop.resp.product.RepairAeProductDetailResp
import tech.tiangong.pop.resp.product.RepairLazadaProductDetailResp
import tech.tiangong.pop.service.product.ProductLazadaService
import tech.tiangong.pop.service.product.RepairProductService

/**
 * 商品管理-异常数据修复
 *
 * <AUTHOR>
 * @date 2025/1/17
 */
@RestController
@RequestMapping("$WEB/v1/product-data-repair")
class ProductDataRepairController(
    private val productLazadaService: ProductLazadaService,
    private val repairProductService: RepairProductService,
) {

    /**
     * 同步lazada商品详情
     *
     * @param req
     * @return
     * @deprecated 已废弃
     */
    @PostMapping("/fetch-lzdproduct")
    fun fetchBySellerSku(@Validated @RequestBody req: FetchProductReq): DataResponse<Unit> {
        productLazadaService.fetchBySellerSku(req)
        return ok()
    }

    /**
     * 商品详情-Lazada
     *
     * @param req
     * @return ProductDetailResp
     */
    @PostMapping("/lazada/detail")
    fun lazadaDetail(@Validated @RequestBody req: RepairLazadaProductDetailReq): DataResponse<List<RepairLazadaProductDetailResp>> {
        return ok(repairProductService.lazadaDetail(req))
    }

    /**
     * 商品详情-AE
     *
     * @param req
     * @return ProductDetailResp
     */
    @PostMapping("/ae/detail")
    fun aeDetail(@Validated @RequestBody req: RepairAeProductDetailReq): DataResponse<RepairAeProductDetailResp> {
        return ok(repairProductService.aeDetail(req))
    }

    /**
     * 更新商品数据-Lazada
     *
     */
    @PostMapping("/lazada/update")
    fun lazadaUpdate(@Validated @RequestBody req: List<RepairLazadaProductUpdateReq>): DataResponse<Unit> {
        repairProductService.lazadaUpdate(req)
        return ok()
    }

    /**
     * 更新商品数据-AE
     *
     */
    @PostMapping("/ae/update")
    fun aeUpdate(@Validated @RequestBody req: RepairAeProductUpdateReq): DataResponse<Unit> {
        repairProductService.aeUpdate(req)
        return ok()
    }
}
