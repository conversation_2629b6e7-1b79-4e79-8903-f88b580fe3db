package tech.tiangong.pop.controller.job

import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import team.aikero.blade.auth.annotation.PreCheckIgnore
import team.aikero.blade.auth.withSystemUser
import team.aikero.blade.core.protocol.DataResponse
import team.aikero.blade.core.protocol.ok
import team.aikero.blade.util.async.runAsync
import tech.tiangong.pop.component.ae.AeProductScheduledSyncComponent
import tech.tiangong.pop.component.lazada.LazadaProductScheduledSyncComponent
import tech.tiangong.pop.req.product.ProductPartSyncReq
import tech.tiangong.pop.service.product.ProductAeService
import tech.tiangong.pop.service.product.ProductImportService
import java.util.concurrent.ExecutorService

/**
 * 操作数据的任务
 */
@PreCheckIgnore
@RestController
@RequestMapping("/job/v1/product")
class JobProductController(
    private val productImportService: ProductImportService,
    private val productAeService: ProductAeService,
    private val aeProductScheduledSyncComponent: AeProductScheduledSyncComponent,
    private val lazadaProductScheduledSyncComponent: LazadaProductScheduledSyncComponent,
    @Qualifier("xxlJobExecutor")
    private val xxlJobExecutor: ExecutorService,
) {

    /**
     * 定期清理同步任务数据
     * {"method":"POST","url":"/pop-product-service/job/v1/product/sync-await-product","system":"ola", "env":"dev"}
     */
    @PostMapping("/sync-await-product")
    fun cleanSyncTask(
        @RequestBody(required = false) taskIds: List<Long>?,
    ): DataResponse<Unit> {
        productImportService.processQueuingData(taskIds ?: emptyList())
        return ok()
    }

    /**
     * 定时刷新AE商品状态(审核中)
     * {"method":"POST","url":"/pop-product-service/job/v1/product/ae/refresh/status","system":"ola", "env":"dev"}
     */
    @PostMapping("/ae/refresh/status")
    fun refreshAeProductStatus(): DataResponse<Unit> {
        productAeService.scheduleRefreshProductStatus()
        return ok()
    }

    /**
     * 同步所有AE商品 部分信息
     * 给xxl-job调用的接口
     */
    @PostMapping("/ae/part/sync/all")
    fun syncAllAeProductPart(
        @RequestBody(required = false) req: ProductPartSyncReq?,
    ): DataResponse<Unit> {
        withSystemUser {
            runAsync(xxlJobExecutor) {
                aeProductScheduledSyncComponent.syncAllProductPart(req ?: ProductPartSyncReq())
            }
        }
        return ok()
    }

    /**
     * 同步所有lazada商品 部分信息
     * 给xxl-job调用的接口
     */
    @PostMapping("/lazada/part/sync/all")
    fun syncAllLazadaProductPart(
        @RequestBody(required = false) req: ProductPartSyncReq?,
    ): DataResponse<Unit> {
        withSystemUser {
            runAsync(xxlJobExecutor) {
                lazadaProductScheduledSyncComponent.syncAllProductPart(req ?: ProductPartSyncReq())
            }
        }
        return ok()
    }

}