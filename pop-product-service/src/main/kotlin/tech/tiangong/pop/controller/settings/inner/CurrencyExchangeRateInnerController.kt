package tech.tiangong.pop.controller.settings.inner

import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import team.aikero.blade.core.annotation.InnerApi
import team.aikero.blade.core.annotation.feign.InnerFeign
import team.aikero.blade.core.constant.UrlVersionConstant.INNER
import team.aikero.blade.core.protocol.DataResponse
import team.aikero.blade.core.protocol.ok
import tech.tiangong.pop.common.req.ExchangeRateQueryReq
import tech.tiangong.pop.common.resp.ExchangeRateResp
import tech.tiangong.pop.service.settings.CurrencyExchangeRateService

/**
 * 配置汇率
 */
@InnerApi
@InnerFeign
@Validated
@RestController
@RequestMapping("$INNER/v1/exchange-rates")
class CurrencyExchangeRateInnerController(
    private val currencyExchangeRateService: CurrencyExchangeRateService,
) {
    /**
     * 查询汇率列表
     */
    @PostMapping("/query")
    fun queryExchangeRates(@Validated @RequestBody req: ExchangeRateQueryReq): DataResponse<List<ExchangeRateResp>> {
        return ok(currencyExchangeRateService.queryExchangeRatesForInner(req))
    }
}