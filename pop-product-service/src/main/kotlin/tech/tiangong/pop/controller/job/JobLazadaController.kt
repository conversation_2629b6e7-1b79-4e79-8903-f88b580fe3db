package tech.tiangong.pop.controller.job

import com.lazada.lazop.util.ApiException
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import team.aikero.blade.auth.annotation.PreCheckIgnore
import team.aikero.blade.auth.withSystemUser
import team.aikero.blade.core.protocol.DataResponse
import team.aikero.blade.core.protocol.ok
import tech.tiangong.pop.service.product.ShopService

/**
 * lazada定时任务
 * */
@PreCheckIgnore
@RestController
@RequestMapping("/job/v1/lazada")
class JobLazadaController(
    private val shopService: ShopService
) {

    /**
     * lazada店铺token刷新
     */
    @PostMapping("/refreshShopToken")
    @Throws(ApiException::class, InterruptedException::class)
    fun refreshShopToken(): DataResponse<Unit> {
        withSystemUser {
            shopService.refreshShopToken(null)
        }
        return ok()
    }

    /**
     * 保存所有站点下品牌
     */
    @PostMapping("/init-all-countries-brands")
    fun initAllCountriesBrands(): DataResponse<Unit> {
        withSystemUser {
            shopService.initAllCountriesBrands()
        }
        return ok()
    }
}
