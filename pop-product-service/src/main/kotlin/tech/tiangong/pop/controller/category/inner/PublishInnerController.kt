package tech.tiangong.pop.controller.category.inner

import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import team.aikero.blade.auth.annotation.PreCheckIgnore
import team.aikero.blade.core.constant.UrlVersionConstant.INNER
import team.aikero.blade.core.protocol.DataResponse
import team.aikero.blade.core.protocol.ok
import tech.tiangong.pop.common.req.PublishChannelReq
import tech.tiangong.pop.common.req.PublishPlatformReq
import tech.tiangong.pop.common.resp.PublishChannelResp
import tech.tiangong.pop.common.resp.PublishPlatformResp
import tech.tiangong.pop.service.category.PublishChannelService
import tech.tiangong.pop.service.category.PublishPlatformService

@PreCheckIgnore
@RestController
@RequestMapping("$INNER/v1/publish")
class PublishInnerController(
    private val publishChannelService: PublishChannelService,
    private val publishPlatformService: PublishPlatformService
) {

    /**
     * 获取渠道下拉列表
     *
     * @param req 查询条件
     * @return 渠道列表
     */
    @PostMapping("/channel/list")
    fun getChannelList(@RequestBody @Validated req: PublishChannelReq): DataResponse<List<PublishChannelResp>> {
        return ok(publishChannelService.getChannelList(req))
    }

    /**
     * 获取发布平台信息列表
     *
     * @param req 请求参数对象，包含平台名和关联渠道ID等信息
     * @return 包含发布平台信息列表的响应实体
     */
    @PostMapping("/platform/list")
    fun getPlatformList(@RequestBody req: PublishPlatformReq): DataResponse<List<PublishPlatformResp>> {
        val platforms = publishPlatformService.getPlatformList(req)
        return ok(platforms)
    }
}