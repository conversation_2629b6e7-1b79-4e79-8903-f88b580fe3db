package tech.tiangong.pop.controller.category

import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.*
import org.springframework.web.multipart.MultipartFile
import team.aikero.blade.core.constant.UrlVersionConstant.WEB
import team.aikero.blade.core.protocol.DataResponse
import team.aikero.blade.core.protocol.PageVo
import team.aikero.blade.core.protocol.ok
import tech.tiangong.pop.req.category.PublishCategoryAttrPageQueryReq
import tech.tiangong.pop.req.category.PublishCategoryAttrQueryReq
import tech.tiangong.pop.req.category.RelatePublishCategoryAttrReq
import tech.tiangong.pop.req.category.SetPublishCategoryRequestAttrReq
import tech.tiangong.pop.resp.category.PublishCategoryAttrVo
import tech.tiangong.pop.resp.category.PublishGroupWithAttributeVo
import tech.tiangong.pop.service.category.PublishCategoryAttrService

/**
 * 本地品类属性管理
 */
@RestController
@RequestMapping("$WEB/v1/publish-category-attr")
class PublishCategoryAttrController(
    private val publishCategoryAttrService: PublishCategoryAttrService
) {

    /**
     * 根据品类ID查询已关联属性列表
     *
     * @param categoryId 品类ID
     * @return 关联属性列表
     */
    @GetMapping("/list-by-category")
    fun listByCategory(@RequestParam("categoryId") categoryId: Long): DataResponse<List<PublishCategoryAttrVo>> {
        val req = PublishCategoryAttrQueryReq().apply {
            this.categoryId = categoryId
        }
        return ok(publishCategoryAttrService.queryPublishCategoryAttr(req))
    }

    /**
     * 根据品类ID分页查询已关联属性列表
     *
     * @param req 分页查询请求
     * @return 分页关联属性列表
     */
    @PostMapping("/page-category-attr")
    fun queryCategoryAttrByPage(@RequestBody @Validated req: PublishCategoryAttrPageQueryReq): DataResponse<PageVo<PublishCategoryAttrVo>> {
        return ok(publishCategoryAttrService.queryCategoryAttrByPage(req))
    }

    /**
     * 根据品类ID列出待关联的分组属性列表
     *
     * @param categoryId 品类ID
     * @return 待关联的分组及属性列表
     */
    @GetMapping("/list-wait-relate-attribute")
    fun listWaitRelateGroupWithAttribute(@RequestParam("categoryId") categoryId: Long): DataResponse<List<PublishGroupWithAttributeVo>> {
        return ok(publishCategoryAttrService.listWaitRelateGroupWithAttribute(categoryId))
    }

    /**
     * 关联品类和属性
     *
     * @param req 关联请求
     * @return 操作结果
     */
    @PostMapping("/relate-category-attr")
    fun relateCategoryAttr(@RequestBody @Validated req: RelatePublishCategoryAttrReq): DataResponse<Unit> {
        publishCategoryAttrService.relateCategoryAttr(req)
        return ok()
    }

    /**
     * 删除品类下的属性
     *
     * @param categoryAttrId 品类属性关联ID
     * @return 操作结果
     */
    @PostMapping("/remove-category-attr")
    fun removeCategoryAttrs(@RequestParam("categoryAttrId") categoryAttrId: Long): DataResponse<Unit> {
        publishCategoryAttrService.removeCategoryAttrs(listOf(categoryAttrId))
        return ok()
    }

    /**
     * 导入
     *
     * @param file 导入文件
     * @return 导入结果信息
     */
    @PostMapping("/import")
    fun importExcel(@RequestParam("file") file: MultipartFile): DataResponse<List<String>> {
        publishCategoryAttrService.importCategoryAttr(file)
        return ok()
    }

    /**
     * 设置品类必填属性
     *
     * @param req 关联请求
     * @return 操作结果
     */
    @PostMapping("/set-category-request-attr")
    fun setCategoryRequestAttr(@RequestBody @Validated req: SetPublishCategoryRequestAttrReq): DataResponse<Unit> {
        publishCategoryAttrService.setCategoryRequestAttr(req)
        return ok()
    }
}