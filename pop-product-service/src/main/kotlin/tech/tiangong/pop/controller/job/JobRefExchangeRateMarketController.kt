package tech.tiangong.pop.controller.job

import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import team.aikero.blade.auth.annotation.PreCheckIgnore
import team.aikero.blade.auth.withSystemUser
import team.aikero.blade.core.protocol.DataResponse
import team.aikero.blade.core.protocol.ok
import tech.tiangong.pop.service.settings.RefExchangeRateMarketService

/**
 * 参考汇率
 *
 */
@PreCheckIgnore
@RestController
@RequestMapping("/job/v1/market-rates")
class JobRefExchangeRateMarketController(
    private val refExchangeRateMarketService: RefExchangeRateMarketService
) {
    /**
     * 手动触发同步配置中的所有币种
     */
    @PostMapping("/sync/all")
    fun syncAllRates(): DataResponse<Unit> {
        withSystemUser {
            refExchangeRateMarketService.syncAllRates()
        }
        return ok()
    }
}
