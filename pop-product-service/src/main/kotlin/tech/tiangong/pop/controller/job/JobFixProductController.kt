package tech.tiangong.pop.controller.job

import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import team.aikero.blade.auth.annotation.PreCheckIgnore
import team.aikero.blade.auth.withSystemUser
import team.aikero.blade.core.protocol.DataResponse
import team.aikero.blade.core.protocol.ok
import tech.tiangong.pop.common.dto.BarcodeInvChangeDto
import tech.tiangong.pop.req.fix.FixProductReq
import tech.tiangong.pop.req.fix.MarkErrorProductReq
import tech.tiangong.pop.req.fix.SyncLazadaReq
import tech.tiangong.pop.service.TaskService
import tech.tiangong.pop.service.product.BarcodeInvChangeService
import tech.tiangong.pop.service.product.ErrorProductService
import tech.tiangong.pop.service.product.FixProductService

/**
 * 定时任务-修复商品数据
 * <AUTHOR>
 * @date 2024/12/9 19:32
 */
@RestController
@RequestMapping("/job/v1/fix/product")
@PreCheckIgnore
class JobFixProductController(
    private val fixProductService: FixProductService,
    private val errorProductService: ErrorProductService,
    private val barcodeInvChangeService: BarcodeInvChangeService,
    private val taskService: TaskService,
) {

    /**
     * 修复商品数据
     * {"method":"POST","url":"/pop-product-service/job/v1/fix/product/data","system":"ola", "env":"dev"}
     * @param req
     */
    @PostMapping("/data")
    fun data(@RequestBody req: FixProductReq): DataResponse<Unit> {
        withSystemUser {
            fixProductService.fixProduct(req)
        }
        return ok()
    }

    /**
     * 修复商品属性数据
     * {"method":"POST","url":"/pop-product-service/job/v1/fix/product/attribute","system":"ola", "env":"dev"}
     * @param req
     */
    @PostMapping("/attribute")
    fun attribute(@RequestBody req: FixProductReq): DataResponse<Unit> {
        withSystemUser {
            fixProductService.fixProductAttribute(req)
        }
        return ok()
    }

    /**
     * 修复商品图片数据
     * {"method":"POST","url":"/pop-product-service/job/v1/fix/product/image-repository","system":"ola", "env":"dev"}
     * @param req
     */
    @PostMapping("/image-repository")
    fun fixProductImageRepository(@RequestBody req: FixProductReq): DataResponse<Unit> {
        withSystemUser {
            fixProductService.fixProductImageRepository(req)
        }
        return ok()
    }

    /**
     * 修复商品skc
     * {"method":"POST","url":"/pop-product-service/job/v1/fix/product/skc","system":"ola", "env":"dev"}
     * @param req
     */
    @PostMapping("/skc")
    fun fixSkc(@RequestBody req: FixProductReq): DataResponse<Unit> {
        withSystemUser {
            fixProductService.fixSkc(req)
        }
        return ok()
    }

    /**
     * 标记商品异常
     * {"method":"POST","url":"/pop-product-service/job/v1/fix/product/mark/error","system":"ola", "env":"dev"}
     * @param req
     */
    @PostMapping("/mark/error")
    fun markErrorProduct(@RequestBody req: MarkErrorProductReq?): DataResponse<Unit> {
        withSystemUser {
            var r: MarkErrorProductReq? = req
            if (r == null) {
                r = MarkErrorProductReq()
            }
            errorProductService.markError(r)
        }
        return ok()
    }

    /**
     * 同步Lazada商品数据
     * {"method":"POST","url":"/pop-product-service/job/v1/fix/product/sync/task","system":"ola", "env":"dev"}
     * @param taskIds
     */
    @PostMapping("/sync/task")
    fun markErrorProduct(@RequestBody req: SyncLazadaReq?): DataResponse<Unit> {
        withSystemUser {
            taskService.productTaskHandlerJob(req?.taskIds ?: setOf())
        }
        return ok()
    }

    /**
     * 商品库存变动同步
     * {"method":"POST","url":"/pop-product-service/job/v1/fix/product/inv/change","system":"ola", "env":"dev"}
     * @param taskIds
     */
    @PostMapping("/inv/change")
    fun invChange(@RequestBody req: BarcodeInvChangeDto?): DataResponse<Unit> {
        withSystemUser {
            barcodeInvChangeService.barcodeInvChange(req!!)
        }
        return ok()
    }

}
