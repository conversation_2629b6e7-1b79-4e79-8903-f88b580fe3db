package tech.tiangong.pop.controller.tryon

import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.*
import team.aikero.blade.core.protocol.DataResponse
import team.aikero.blade.core.protocol.ok
import tech.tiangong.pop.req.tryon.BatchSubmitTryOnQcTaskReq
import tech.tiangong.pop.req.tryon.SubmitTryOnQcTaskReq
import tech.tiangong.pop.resp.tryon.TryOnTaskQcDetailResp
import tech.tiangong.pop.service.tryon.TryOnTaskQcService

/**
 * tryOn质检任务管理
 * @date 2025-7-15 10:31:11
 */
@Validated
@RestController
@RequestMapping("/web/v1/try-on-task-qc")
class TryOnTaskQcController(
    private val tryOnTaskQcService: TryOnTaskQcService,
) {

    /**
     * 通过质检任务ID获取质检任务明细
     * @param qcId
     * @return TryOnTaskQcDetailResp
     */
    @GetMapping("/get-qc-detail/{qcId}")
    fun getQcDetailById(@PathVariable("qcId") qcId: Long): DataResponse<TryOnTaskQcDetailResp> {
        return ok(tryOnTaskQcService.getQcDetailById(qcId))
    }

    /**
     * 提交质检结果
     *
     * @param req
     */
    @PostMapping("/submit-qc")
    fun submitQc(@Validated @RequestBody req: SubmitTryOnQcTaskReq): DataResponse<Void> {
        tryOnTaskQcService.submitQc(req)
        return ok()
    }

    /**
     * 批量提交质检结果
     */
    @PostMapping("/batch-submit-qc")
    fun batchSubmitQc(@Validated @RequestBody req: BatchSubmitTryOnQcTaskReq): DataResponse<Void> {
        tryOnTaskQcService.batchSubmitQc(req)
        return ok()
    }

}
