package tech.tiangong.pop.controller.settings

import jakarta.validation.Valid
import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.*
import team.aikero.blade.core.protocol.DataResponse
import team.aikero.blade.core.protocol.ok
import tech.tiangong.pop.req.settings.CategoryPriceRangeQueryReq
import tech.tiangong.pop.req.settings.CategoryPriceRangeSaveReq
import tech.tiangong.pop.resp.CategoryPriceRangeResp
import tech.tiangong.pop.service.settings.CategoryPriceRangeService

/**
 * 基础配置-商品销售定价-品类价格区间
 */
@Validated
@RestController
@RequestMapping("/web/v1/category-price-range")
class CategoryPriceRangeController(
    private val categoryPriceRangeService: CategoryPriceRangeService
) {
    /**
     * 查询品类价格区间列表
     */
    @GetMapping("/list")
    fun queryCategoryPriceRanges(req: CategoryPriceRangeQueryReq): DataResponse<List<CategoryPriceRangeResp>> {
        return ok(categoryPriceRangeService.queryCategoryPriceRanges(req))
    }

    /**
     * 保存或更新品类价格区间配置
     */
    @PostMapping("/save-or-update")
    fun saveOrUpdate(@RequestBody @Valid req: CategoryPriceRangeSaveReq): DataResponse<Boolean> {
        return ok(categoryPriceRangeService.saveOrUpdate(req))
    }

    /**
     * 批量保存或更新品类价格区间配置
     */
    @PostMapping("/batch-save-or-update")
    fun batchSaveOrUpdate(@RequestBody @Valid reqList: List<CategoryPriceRangeSaveReq>): DataResponse<Boolean> {
        return ok(categoryPriceRangeService.batchSaveOrUpdate(reqList))
    }
}
