package tech.tiangong.pop.controller.product

import jakarta.servlet.http.HttpServletResponse
import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.*
import org.springframework.web.multipart.MultipartFile
import team.aikero.blade.core.protocol.DataResponse
import team.aikero.blade.core.protocol.PageVo
import team.aikero.blade.core.protocol.ok
import tech.tiangong.ofp.open.common.resp.StockSpuQuantityQueryResp.StockSpuQuantityInfo
import tech.tiangong.pop.bo.ProductCostPricingRequestBO
import tech.tiangong.pop.bo.ProductCostPricingResultBO
import tech.tiangong.pop.common.enums.PlatformEnum
import tech.tiangong.pop.dto.product.ProductOfflineMqV2Dto
import tech.tiangong.pop.req.product.*
import tech.tiangong.pop.resp.ImportResultResp
import tech.tiangong.pop.resp.product.*
import tech.tiangong.pop.service.product.*
import tech.tiangong.pop.service.settings.ProductCostPricingService


/**
 * 商品管理
 */
@RestController
@RequestMapping("/web/v1/product")
class ProductController(
    private val productV2Service: ProductV2Service,
    private val productManageService: ProductManageService,
    private val productLazadaOfflineService: ProductLazadaOfflineService,
    private val productPublishTaskExportService: ProductPublishTaskExportService,
    private val productPublishFailedTaskExportService: ProductPublishFailedTaskExportService,
    private val productImportService: ProductImportService,
    private val productPublishFailedTaskExportByAwaitService: ProductPublishFailedTaskExportByAwaitService,
    private val productBatchUpdateService: ProductBatchUpdateService,
    private val productCostPricingService: ProductCostPricingService,
    private val productPriceManagementService: ProductPriceManagementService,
    private val productOfflineFailedTaskExportService: ProductOfflineFailedTaskExportService,
) {

    /**
     * 待上架列表-导入进度
     *
     * @param req 分页对象
     * @return PageVo<ImportProductRecordResp>
    </ImportProductRecordResp> */
    @PostMapping("/import-product/page")
    fun processImportPage(@Validated @RequestBody req: ImportProductRecordPageQueryReq?): DataResponse<PageVo<ImportProductRecordResp>> {
        return ok(productImportService.page(req))
    }

    /**
     * 待上架列表-发布失败商品
     *
     * @param req 入参
     * @date 2024/9/2
     */
    @PostMapping("/submit/await-publishfailed-export-task")
    fun submitAwaitPublishProductExportTask(@Validated @RequestBody req: ExportPublishFailedReq?): DataResponse<Unit> {
        productPublishFailedTaskExportByAwaitService.submitPublishFailedExportTask(req)
        return ok()
    }

    /**
     * 实物库存
     *
     * @param productId 商品ID
     * @return StockSpuQuantityQueryResp.StockSpuQuantityInfo
     */
    @GetMapping("/sotck/quantity/{productId}")
    fun actlStockQuantityDetail(@PathVariable(name = "productId") productId: Long): DataResponse<List<StockSpuQuantityInfo>> {
        return ok(productV2Service.stockQuantityDetail(productId))
    }

    /**
     * 统计数量
     *
     * @return ProductCountResp
     */
    @PostMapping("/product/items/count")
    fun getProductCounts(@Validated @RequestBody req: ProductCountReq): DataResponse<ProductCountResp> {
        return ok(productV2Service.getProductCounts(req))
    }

    /**
     * 翻译
     *
     * @param req
     * @return Unit
     */
    @PostMapping("/translate")
    @Throws(Exception::class)
    fun translate(@Validated @RequestBody req: TranslateTitleReq): DataResponse<List<TranslateTitleResp>> {
        return ok(productV2Service.translate(req))
    }

    /**
     * 操作日记
     *
     * @param productId
     * @return Unit
     */
    @GetMapping("/operate-log/{productId}")
    fun operateLogList(@PathVariable(name = "productId") productId: Long): DataResponse<List<ProductOperateLogResp>> {
        return ok(productV2Service.operateLogList(productId))
    }

    /**
     * 更新记录
     *
     * @param productId
     * @return Unit
     */
    @GetMapping("/update-log/{productId}")
    fun updateLogLost(@PathVariable(name = "productId") productId: Long): DataResponse<List<ProductUpdateLogResp>> {
        return ok(productV2Service.updateLogLost(productId))
    }

    /**
     * 发布错误日志
     *
     * @param req
     * @return Unit
     */
    @PostMapping("/publish-errorlog")
    fun publishErrorLog(@Validated @RequestBody req: ErrorLogQueryReq): DataResponse<PageVo<PublishErrorLogResp>> {
        return ok(productV2Service.publishErrorLog(req))
    }

    /**
     * 批量修改价格
     *
     * @param req 入参
     * @date 2024/9/2
     */
    @PostMapping("/batch/update-price")
    fun batchUpdatePrice(@Validated @RequestBody req: BatchUpdatePriceReq): DataResponse<Unit> {
        productPriceManagementService.batchUpdatePrice(req)
        return ok()
    }

    /**
     * 批量修改库存
     *
     * @param req 入参
     * @date 2024/9/2
     */
    @PostMapping("/batch/update-quantity")
    fun batchStockQuantity(@Validated @RequestBody req: BatchUpdateQuantityReq): DataResponse<Unit> {
        productV2Service.batchStockQuantity(req)
        return ok()
    }

    /**
     * 批量同步其他店铺
     *
     * @param req 入参
     * @date 2024/9/2
     */
    @PostMapping("/batch/sync-shop")
    fun syncOtherShop(@Validated @RequestBody req: CloneProductToOtherShopReq): DataResponse<Unit> {
        productV2Service.syncOtherShop(req)
        return ok()
    }

    /**
     * 商品SKU列表查询-v2
     *
     * @param req 商品SKU查询请求
     * @return SKU分页数据
     * @date 2025/01/08
     */
    @PostMapping("/sku/online/list/v2")
    fun pageOnlineSkuList(@Validated @RequestBody req: OnlineSkuListReq): DataResponse<PageVo<OnlineSkuListResp>> {
        return ok(productLazadaOfflineService.pageOnlineSkuList(req))
    }

    /**
     * 商品SKU列表查询-统计数量
     *
     * @param req 商品SKU查询请求
     * @date 2025/01/14
     */
    @PostMapping("/sku/online/count")
    fun countOnlineSku(@Validated @RequestBody req: OnlineSkuListReq): DataResponse<OnlineSkuCountResp> {
        return ok(productLazadaOfflineService.countOnlineSku(req))
    }

    /**
     * 批量下架-v2
     *
     * @param req 批量下架请求参数
     * @date 2025/01/08
     */
    @PostMapping("/batch/offline/v2")
    fun batchSendOfflineMessageV2(@Validated @RequestBody req: OfflineSubmitReq): DataResponse<Unit> {
        productLazadaOfflineService.batchSendOfflineMessageV2(req)
        return ok()
    }

    /**
     * 消费下架消息-V2
     *
     * @param req 批量下架请求参数
     * @date 2025/01/10
     */
    @PostMapping("/offline/consume/v2")
    fun offlineV2(@Validated @RequestBody req: ProductOfflineMqV2Dto): DataResponse<Unit> {
        productLazadaOfflineService.consumeOfflineMessageV2(req)
        return ok()
    }

    /**
     * 异步导出上架数据
     *
     * @param req 入参
     * @date 2024/9/2
     */
    @PostMapping("/submit-export-task")
    fun submitExportTask(@Validated @RequestBody req: ExportPublishProductReq): DataResponse<Unit> {
        productPublishTaskExportService.submitExportTask(req)
        return ok()
    }

    /**
     * 异步导出上架失败数据
     *
     * @param req 入参
     * @date 2024/9/2
     */
    @PostMapping("/submit/publishfailed-export-task")
    fun submitExportTask(@Validated @RequestBody req: ExportPublishFailedReq): DataResponse<Unit> {
        productPublishFailedTaskExportService.submitPublishFailedExportTask(req)
        return ok()
    }

    /**
     * 异步导出下架失败数据(SPU)
     *
     * @param req 入参
     * @date 2024/9/2
     */
    @PostMapping("/submit/offlinefailed-export-task")
    fun submitExportTaskOffline(@Validated @RequestBody req: FailedOfflineExportTaskReq): DataResponse<Unit> {
        productOfflineFailedTaskExportService.submitOfflineFailedExportTask(req)
        return ok()
    }

    /**
     * 异步导出下架失败数据
     *
     * @param req 入参
     * @date 2025/01/08
     */
    @PostMapping("/submit-failed-offline-export-task")
    fun submitFailedOfflineExportTask(@Validated @RequestBody req: FailedOfflineExportTaskReq): DataResponse<Unit> {
        productLazadaOfflineService.submitFailedOfflineExportTask(req)
        return ok()
    }

    /**
     * 批量更新-导出数据
     *
     * @param req
     * @param response
     * @return
     */
    @PostMapping("/batch/update/export")
    fun exportByBatchUpdate(@Validated @RequestBody req: ExportByBatchUpdateReq, response: HttpServletResponse) {
        productBatchUpdateService.exportByBatchUpdate(response, req)
    }

    /**
     * 批量更新-导入更新
     *
     * @param taskType
     * @param platform
     * @param file
     * @return
     */
    @PostMapping("/batch/update/import")
    fun exportByBatchUpdate(
        @RequestParam("taskType") taskType: Int,
        @RequestParam("platform") platform: PlatformEnum,
        @RequestParam("excelFile") file: MultipartFile,
    ): DataResponse<ImportResultResp> {
        return ok(productBatchUpdateService.importByBatchUpdate(taskType, platform, file))
    }

    /**
     * 批量价格兜底 - 仅校验Excel中的价格数据，不进行导入
     */
    @PostMapping("/batch/price-inv/check")
    fun checkPriceInv(
        @RequestParam("platform") platform: PlatformEnum,
        @RequestParam("excelFile") file: MultipartFile
    ): DataResponse<List<ProductPriceAlertSimpleCheckResp>> {
        return ok(productBatchUpdateService.checkPriceInv(platform, file))
    }

    /**
     * 批量更新-更新图片
     *
     * @param req 入参
     * @date 2025/03/11
     */
    @PostMapping("/batch/update/image")
    fun submitBatchUpdateImageTask(@Validated @RequestBody req: ProductBatchUpdateImageReq): DataResponse<Unit> {
        productBatchUpdateService.submitBatchUpdateImageTask(req)
        return ok()
    }

    /**
     * 计算商品SKC的定价成本
     *
     * @param costPricingRequest 商品定价成本计算请求
     * @return List<ProductCostPricingResultBO>
    </ProductCostPricingResultBO> */
    @PostMapping("/cost-pricing/calculate")
    fun calculateCostPriceBatch(@RequestBody costPricingRequest: ProductCostPricingRequestBO): List<ProductCostPricingResultBO> {
        return productCostPricingService.calculateCostPrice(costPricingRequest)
    }

    /**
     * 批量更新-导出数据TEMU
     *
     * @param req
     * @param response
     * @return
     */
    @PostMapping("/batch/temu-update/export")
    fun exportByTemuBatchUpdate(@Validated @RequestBody req: ExportByBatchUpdateReq, response: HttpServletResponse) {
        productBatchUpdateService.exportByBatchUpdate(response, req)
    }

    /**
     * 初始化风格历史数据
     */
    @PostMapping("/initHisDataV2")
    fun initHisDataV2(@RequestParam("file") file: MultipartFile): DataResponse<Void> {
        productManageService.initHisDataV2(file)
        return ok()
    }
}
