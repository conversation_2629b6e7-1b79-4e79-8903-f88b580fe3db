package tech.tiangong.pop.controller.exception

import org.springframework.core.Ordered
import org.springframework.core.annotation.Order
import org.springframework.web.bind.MethodArgumentNotValidException
import org.springframework.web.bind.annotation.ExceptionHandler
import org.springframework.web.bind.annotation.RestControllerAdvice
import team.aikero.blade.core.enums.NetworkCode
import team.aikero.blade.core.protocol.DataResponse
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import jakarta.validation.ConstraintViolationException

@RestControllerAdvice
@Order(Ordered.HIGHEST_PRECEDENCE)
class ValidationExceptionHandler {

    @ExceptionHandler(MethodArgumentNotValidException::class)
    fun handleValidationException(ex: MethodArgumentNotValidException): DataResponse<Unit> {
        // 记录详细日志
        log.warn {
            val detailedErrors = ex.bindingResult.fieldErrors.joinToString("\n") {
                "字段：${it.field}，拒绝的值：${it.rejectedValue ?: "空值"}，错误信息：${it.defaultMessage}"
            }
            "参数校验失败：\n$detailedErrors"
        }

        // 返回有意义的错误信息
        val errorMessage = ex.bindingResult.fieldErrors.joinToString("；") {
            "${it.field}：${it.defaultMessage ?: "参数无效"}"
        }

        return createErrorResponse(message = errorMessage)
    }

    @ExceptionHandler(ConstraintViolationException::class)
    fun handleConstraintViolationException(ex: ConstraintViolationException): DataResponse<Unit> {
        // 记录详细日志
        log.warn {
            val detailedErrors = ex.constraintViolations.joinToString("\n") {
                "属性：${it.propertyPath}，无效值：${it.invalidValue ?: "空值"}，错误信息：${it.message}"
            }
            "约束校验失败：\n$detailedErrors"
        }

        // 返回有意义的错误信息
        val errorMessage = ex.constraintViolations.joinToString("；") {
            "${it.propertyPath}：${it.message ?: "验证失败"}"
        }

        return createErrorResponse(message = errorMessage)
    }

    /**
     * 创建统一的错误响应
     */
    private fun createErrorResponse(message: String): DataResponse<Unit> {
        return DataResponse(
            successful = false,
            code = NetworkCode.BAD_REQUEST.code,
            message = message
        )
    }
}
