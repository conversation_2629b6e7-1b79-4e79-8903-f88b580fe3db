package tech.tiangong.pop.controller.order

import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import team.aikero.blade.core.protocol.DataResponse
import team.aikero.blade.core.protocol.PageVo
import team.aikero.blade.core.protocol.ok
import tech.tiangong.pop.req.order.ErrorOrderBatchProcessReq
import tech.tiangong.pop.req.order.ErrorOrderPageReq
import tech.tiangong.pop.resp.order.ErrorOrderPageResp
import tech.tiangong.pop.service.ErrorOrderService
import jakarta.servlet.http.HttpServletResponse

/**
 * 异常订单
 * <AUTHOR>
 * @date 2024/11/14 10:17
 */
@RestController
@RequestMapping("/web/v1/error/order")
class ErrorOrderController(private val errorOrderService: ErrorOrderService) {

    /**
     * 列表分页
     * @param req 请求对象
     * @return
     */
    @PostMapping("/page")
    fun page(@Validated @RequestBody req: ErrorOrderPageReq): DataResponse<PageVo<ErrorOrderPageResp>> {
        return ok(errorOrderService.page(req))
    }

    /**
     * 批量处理
     * @param req 请求对象
     * @return
     */
    @PostMapping("/batch/process")
    fun batchProcess(@Validated @RequestBody req: ErrorOrderBatchProcessReq): DataResponse<Unit> {
        return ok(errorOrderService.batchProcess(req))
    }

    /**
     * 导出
     * @param req 请求对象
     * @return
     */
    @PostMapping("/export")
    fun export(response: HttpServletResponse, @Validated @RequestBody req: ErrorOrderPageReq) {
        errorOrderService.export(response, req)
    }
}
