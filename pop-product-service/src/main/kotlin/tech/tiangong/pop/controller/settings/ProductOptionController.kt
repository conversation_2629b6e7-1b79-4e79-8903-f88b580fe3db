package tech.tiangong.pop.controller.settings

import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import team.aikero.blade.core.protocol.DataResponse
import team.aikero.blade.core.protocol.ok
import tech.tiangong.pop.resp.settings.ProductOptionResp
import tech.tiangong.pop.service.settings.ProductOptionService


/**
 * 基础配置-商品下拉框
 */
@RestController
@RequestMapping("/web/v1/v1" + "/product-options")
class ProductOptionController(
    private val productOptionService: ProductOptionService
) {

    /**
     * 获取计算运算符选项列表
     * @return 计算运算符的响应列表
     */
    @GetMapping("/price-calculate-operators")
    fun getPriceCalculateOperators(): DataResponse<List<ProductOptionResp>> =
        ok(productOptionService.getPriceCalculateOperators())

    /**
     * 获取条件运算符选项列表
     * @return 条件运算符的响应列表
     */
    @GetMapping("/price-condition-operators")
    fun getPriceConditionOperators(): DataResponse<List<ProductOptionResp>> =
        ok(productOptionService.getPriceConditionOperators())

    /**
     * 获取条件变量选项列表
     * @return 条件变量的响应列表
     */
    @GetMapping("/price-condition-variables")
    fun getPriceConditionVariables(): DataResponse<List<ProductOptionResp>> =
        ok(productOptionService.getPriceConditionVariables())

    /**
     * 获取公式变量选项列表
     * @return 公式变量的响应列表
     */
    @GetMapping("/price-formula-variables")
    fun getPriceFormulaVariables(): DataResponse<List<ProductOptionResp>> =
        ok(productOptionService.getPriceFormulaVariables())

    /**
     * 获取公式类型选项列表
     * @return 公式变量的响应列表
     */
    @GetMapping("/price-formula-types")
    fun getPriceFormulaTypes(): DataResponse<List<ProductOptionResp>> =
        ok(productOptionService.getPriceFormulaTypes())
    /**
     * 获取ISP字段选项列表
     * @return ISP字段的响应列表
     */
    @GetMapping("/price-basic-isp-fields")
    fun getPriceBasicIspFields(): DataResponse<List<ProductOptionResp>> =
        ok(productOptionService.getPriceBasicIspFields())

    /**
     * 获取SP字段选项列表
     * @return SP字段的响应列表
     */
    @GetMapping("/price-basic-sp-fields")
    fun getPriceBasicSpFields(): DataResponse<List<ProductOptionResp>> =
        ok(productOptionService.getPriceBasicSpFields())

    /**
     * 获取比较运算符选项列表
     * @return 比较运算符的响应列表
     */
    @GetMapping("/price-operators")
    fun getPriceOperators(): DataResponse<List<ProductOptionResp>> =
        ok(productOptionService.getPriceOperators())

    /**
     * 获取 SKU 动态字段选项列表
     * @return SKU 动态字段的响应列表
     */
    @GetMapping("/sku-code-dynamic-fields")
    fun getSkuCodeDynamicFields(): DataResponse<List<ProductOptionResp>> =
        ok(productOptionService.getSkuCodeDynamicFields())

    /**
     * 获取 SKU 规则类型选项列表
     * @return SKU 规则类型的响应列表
     */
    @GetMapping("/sku-code-rule-types")
    fun getSkuCodeRuleTypes(): DataResponse<List<ProductOptionResp>> =
        ok(productOptionService.getSkuCodeRuleTypes())

    /**
     * 获取所有Lazada店铺类型选项
     *
     * @return List<ProductOptionResp> 店铺类型选项列表
     */
    @GetMapping("/lazada-shop-types")
    fun getLazadaShopTypeOptions(): DataResponse<List<ProductOptionResp>> =
        ok(productOptionService.getLazadaShopTypeOptions())
}
