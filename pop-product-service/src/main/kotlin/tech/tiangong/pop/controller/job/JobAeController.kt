package tech.tiangong.pop.controller.job

import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import team.aikero.blade.auth.annotation.PreCheckIgnore
import team.aikero.blade.auth.withSystemUser
import team.aikero.blade.core.protocol.DataResponse
import team.aikero.blade.core.protocol.ok
import tech.tiangong.pop.req.selected.AePullSelectedSingleReq
import tech.tiangong.pop.service.product.ShopService
import tech.tiangong.pop.service.selected.AePullProductDataService

/**
 * AE定时任务
 * */
@PreCheckIgnore
@RestController
@RequestMapping("/job/v1/ae")
class JobAeController(
    private val shopService: ShopService,
    private val aePullProductDataService: AePullProductDataService,
) {

    /**
     * 定时-拉取AE商品数据(精品)
     */
    @PostMapping("/pull/selected")
    fun pullSelected(): DataResponse<Unit> {
        withSystemUser {
            aePullProductDataService.pullSelected()
        }
        return ok()
    }

    /**
     * 定时-拉取数据-单个商品(精品)
     */
    @PostMapping("/pull/selected/single")
    fun pullSelectedSingle(@RequestBody req: AePullSelectedSingleReq): DataResponse<Unit> {
        withSystemUser {
            aePullProductDataService.pullSelectedSingle(req.aeProductIds, req.shopId)
        }
        return ok()
    }

    /**
     * 定时-补充AE商品未映射的品类(若有则补充, 无则忽略)
     */
    @PostMapping("/fix/category")
    fun fixCategory(): DataResponse<Unit> {
        withSystemUser {
            aePullProductDataService.fixCategory()
        }
        return ok()
    }

    /**
     * 定时-更新AE商品任务(精品)
     */
    @PostMapping("/update/platform")
    fun updatePlatform(): DataResponse<Unit> {
        withSystemUser {
            aePullProductDataService.updatePlatform()
        }
        return ok()
    }

    /**
     * 定时-拉取识别选中结果(精品)
     */
    @PostMapping("/pull/recognition/selected")
    fun pullRecognitionSelected(): DataResponse<Unit> {
        withSystemUser {
            aePullProductDataService.pullRecognitionSelected()
        }
        return ok()
    }

    /**
     * 定时-拉取识别风格结果(精品)
     */
    @PostMapping("/pull/recognition/style")
    fun pullRecognitionStyle(): DataResponse<Unit> {
        withSystemUser {
            aePullProductDataService.pullRecognitionStyle()
        }
        return ok()
    }
}
