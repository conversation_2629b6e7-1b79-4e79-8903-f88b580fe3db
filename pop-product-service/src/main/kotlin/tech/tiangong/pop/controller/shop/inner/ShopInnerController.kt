package tech.tiangong.pop.controller.shop.inner

import com.lazada.lazop.util.ApiException
import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.*
import team.aikero.blade.auth.*
import team.aikero.blade.auth.annotation.PreCheckIgnore
import team.aikero.blade.core.annotation.feign.InnerFeign
import team.aikero.blade.core.constant.UrlVersionConstant.INNER
import team.aikero.blade.core.protocol.DataResponse
import team.aikero.blade.core.protocol.ok
import tech.tiangong.pop.common.req.ShopBrandReq
import tech.tiangong.pop.common.req.ShopReq
import tech.tiangong.pop.common.resp.ShopBrandResp
import tech.tiangong.pop.common.resp.ShopResp
import tech.tiangong.pop.resp.sdk.lazada.GetTokenResponse
import tech.tiangong.pop.service.product.ShopService


@RestController
@RequestMapping("$INNER/v1/shop")
@InnerFeign
class ShopInnerController(
    private val shopService: ShopService
) {

    /**
     * 获取店铺列表
     *
     * @param req 查询条件
     * @return 店铺列表
     */
    @PostMapping("/list")
    @PreCheckIgnore
    fun getShopList(@RequestBody @Validated req: ShopReq): DataResponse<List<ShopResp>> {
        return ok(shopService.getShopList(req))
    }

    /**
     * 刷新lazada品类树
     *
     * @return Void
     */
    @PostMapping("/refresh/category/tree")
    @Throws(ApiException::class)
    fun refreshCategoryTree(@RequestParam(value = "country") country: String): DataResponse<Void> {
        withSystemUser {
            try {
                shopService.refreshCategoryTree(country)
            } catch (e: Exception) {
                throw RuntimeException(e)
            }
        }
        return ok()
    }

    /**
     * 获取品牌列表
     *
     * @param req 查询条件
     * @return 品牌列表
     */
    @PostMapping("/brands")
    @PreCheckIgnore
    fun getBrandList(@RequestBody @Validated req: ShopBrandReq): DataResponse<List<ShopBrandResp>> {
        return ok(shopService.getBrandList(req))
    }

    /**
     * 授权回调
     *
     * @param code code
     * @return GetTokenResponse
     */
    @GetMapping("/callback/code")
    @PreCheckIgnore
    @Throws(ApiException::class)
    fun code(@RequestParam("code") code: String): DataResponse<GetTokenResponse> {
        return ok(shopService.getToken(code))
    }

    /**
     * token刷新
     *
     * @param shopId
     * @return Void
     */
    @GetMapping("/refreshShopToken")
    @Throws(ApiException::class, InterruptedException::class)
    fun refreshShopToken(@RequestParam(name = "shopId", required = false) shopId: Long?): DataResponse<Void> {
        shopService.refreshShopToken(shopId)
        return ok()
    }
}
