package tech.tiangong.pop.controller.tag

import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import team.aikero.blade.core.protocol.DataResponse
import team.aikero.blade.core.protocol.ok
import tech.tiangong.pop.enums.ProductTagEnum
import tech.tiangong.pop.resp.tag.GetAllConfigResp
import tech.tiangong.pop.resp.tag.GetAllConfigValueResp

/**
 * 标签
 * <AUTHOR>
 * @date 2025-5-20 19:42:07
 */
@RestController
@RequestMapping("/web/tag")
class TagController {

    /**
     * 获取标签配置
     * @return
     */
    @PostMapping("/config/get/all")
    fun getAllConfig(): DataResponse<List<GetAllConfigResp>> {
        return ok(ProductTagEnum.getAllTags().map {
            GetAllConfigResp(it.tagKey, it.tagKeyDesc, it.tagValue.map { value ->
                GetAllConfigValueResp(value.tagValue, value.tagValueDesc)
            })
        })
    }
}