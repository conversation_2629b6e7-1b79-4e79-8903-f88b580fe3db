package tech.tiangong.pop.controller.product.inner

import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import team.aikero.blade.core.protocol.DataResponse
import team.aikero.blade.core.protocol.ok
import tech.tiangong.pop.common.dto.CreateProductDto
import tech.tiangong.pop.service.product.ProductCreateTaskService

/**
 * 创建商品-内部接口
 */
@RestController
@RequestMapping("/inner/v1/product")
class ProductCreateInnerController(
    private val productCreateTaskService: ProductCreateTaskService,
) {

    /**
     * 创建商品
     *
     * @param req
     * @return Void
     */
    @PostMapping("/create-product")
    fun createProduct(@Validated @RequestBody req: CreateProductDto): DataResponse<Unit> {
        productCreateTaskService.createProductTask(req)
        return ok()
    }

    /**
     * 批量创建商品
     *
     * @param req
     * @return Void
     */
    @PostMapping("/batch-create-product")
    fun batchCreateProduct(@Validated @RequestBody req: List<CreateProductDto>): DataResponse<Unit> {
        productCreateTaskService.batchCreateProductTask(req)
        return ok()
    }
}
