package tech.tiangong.pop.controller.planning

import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.*
import org.springframework.web.multipart.MultipartFile
import team.aikero.blade.core.protocol.DataResponse
import team.aikero.blade.core.protocol.PageVo
import team.aikero.blade.core.protocol.ok
import tech.tiangong.pop.req.planning.*
import tech.tiangong.pop.resp.ImportResultResp
import tech.tiangong.pop.resp.planning.DictPlatformCategoryResp
import tech.tiangong.pop.resp.planning.PlanningDetailResp
import tech.tiangong.pop.resp.planning.PlanningPageResp
import tech.tiangong.pop.resp.planning.PlanningPresetCategoryResp
import tech.tiangong.pop.service.PlanningService

/**
 * 企划管理
 * <AUTHOR>
 * @date 2024/11/13 14:16
 */
@RestController
@RequestMapping("/web/v1/planning")
class PlanningController(private val planningService: PlanningService) {
    /**
     * 校验企划 渠道-平台-月份 是否存在
     * @param req 请求对象
     * @return
     */
    @PostMapping("/check/exist")
    fun checkPlanningExist(@Validated @RequestBody req: PlanningCheckExistReq): DataResponse<Boolean> {
        return ok(planningService.checkPlanningExist(req))
    }

    /**
     * 创建/编辑-企划汇总
     * @param req 请求对象
     * @return
     */
    @PostMapping("/summary/create/update")
    fun summaryCreate(@Validated @RequestBody req: PlanningSummaryCreateReq): DataResponse<Long> {
        return ok(planningService.summaryCreate(req))
    }

    /**
     * 创建/编辑-开发节奏
     * @param req 请求对象
     * @return
     */
    @PostMapping("/develop/rhythm/create/update")
    fun developRhythmCreate(@Validated @RequestBody req: PlanningDevelopRhythmCreateReq): DataResponse<Long> {
        return ok(planningService.developRhythmCreate(req))
    }

    /**
     * 创建/编辑-上架节奏
     * @param req 请求对象
     * @return
     */
    @PostMapping("/publish/rhythm/create/update")
    fun publishRhythmCreate(@Validated @RequestBody req: PlanningPublishRhythmCreateReq): DataResponse<Long> {
        return ok(planningService.publishRhythmCreate(req))
    }

    /**
     * 创建/编辑-品类价格
     * @param req 请求对象
     * @return
     */
    @PostMapping("/category/price/create/update")
    fun categoryPriceCreate(@Validated @RequestBody req: PlanningCategoryPriceCreateReq): DataResponse<Long> {
        return ok(planningService.categoryPriceCreate(req))
    }

    /**
     * 获取企划详情(包括企划汇总,开发节奏,上架节奏,品类价格)
     * @param planningId 企划id
     * @return 企划详情
     */
    @PostMapping("/detail/{planningId}")
    fun detail(@PathVariable planningId: Long): DataResponse<PlanningDetailResp> {
        return ok(planningService.detail(planningId))
    }

    /**
     * 获取企划汇总列表
     * @param req 企划id
     * @return
     */
    @PostMapping("/page")
    fun page(@Validated @RequestBody req: PlanningPageReq): DataResponse<PageVo<PlanningPageResp>> {
        return ok(planningService.page(req))
    }

    /**
     * 复制企划
     * @param req 企划id
     * @return 新企划id
     */
    @PostMapping("/copy")
    fun copy(@Validated @RequestBody req: PlanningCopyReq): DataResponse<Long> {
        return ok(planningService.copy(req))
    }

    /**
     * 删除企划
     * @param req 企划id
     * @return
     */
    @PostMapping("/delete")
    fun delete(@Validated @RequestBody req: PlanningDeleteReq): DataResponse<Unit> {
        return ok(planningService.delete(req))
    }

    /**
     * 企划推送灵感
     * @param req 企划id
     * @return
     */
    @PostMapping("/push/inspiration")
    fun pushInspiration(@Validated @RequestBody req: PlanningPushInspirationReq): DataResponse<Unit> {
        return ok(planningService.pushInspiration(req))
    }

    /**
     * 字典内部品类code获取平台品类
     * @param dictCode
     * @return
     */
    @PostMapping("/get/platform/category")
    fun getPlatformCategoryByDictCode(@Validated @RequestBody req: DictPlatformCategoryReq): DataResponse<DictPlatformCategoryResp> {
        return ok(planningService.getPlatformCategoryByDictCode(req))
    }

    /**
     * 企划预设品类
     * @return
     */
    @PostMapping("/preset/category")
    fun getPresetCategory(@Validated @RequestBody req: PlanningPresetCategoryReq): DataResponse<List<PlanningPresetCategoryResp>> {
        return ok(planningService.getPresetCategory(req))
    }

    /**
     * 导入预设品类
     *
     * @param file
     */
    @PostMapping("/preset/category/import")
    fun importPresetCategory(@RequestParam("file") file: MultipartFile): DataResponse<ImportResultResp> {
        return ok(planningService.importPresetCategory(file))
    }
}