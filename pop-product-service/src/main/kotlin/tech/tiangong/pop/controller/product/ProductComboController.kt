package tech.tiangong.pop.controller.product

import jakarta.servlet.http.HttpServletResponse
import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import team.aikero.blade.core.constant.UrlVersionConstant.WEB
import team.aikero.blade.core.protocol.DataResponse
import team.aikero.blade.core.protocol.PageVo
import team.aikero.blade.core.protocol.ok
import tech.tiangong.pop.req.product.combo.ComboBarcodePageReq
import tech.tiangong.pop.req.product.combo.ComboBarcodeUpdateRecordReq
import tech.tiangong.pop.req.product.combo.ComboSellerSkuDetailReq
import tech.tiangong.pop.req.product.combo.ComboSkuConfigReq
import tech.tiangong.pop.resp.product.combo.ComboBarcodePageResp
import tech.tiangong.pop.resp.product.combo.ComboBarcodeUpdateRecordResp
import tech.tiangong.pop.resp.product.combo.ComboSellerSkuDetailResp
import tech.tiangong.pop.resp.product.combo.ComboSkuConfigResp
import tech.tiangong.pop.service.product.ProductComboService

/**
 * 组合商品相关接口
 */
@RestController
@RequestMapping("$WEB/product/combo")
class ProductComboController(
    private val productComboService: ProductComboService,
) {

    /**
     * 获取组合商品sku配置-弹窗
     */
    @PostMapping("/sku/config")
    fun getComboSkuConfig(@Validated @RequestBody req: ComboSkuConfigReq): DataResponse<ComboSkuConfigResp> {
        return ok(productComboService.getComboSkuConfig(req))
    }

    /**
     * 条码-组合商品-分页列表
     */
    @PostMapping("/barcode/page")
    fun barcodePage(@Validated @RequestBody req: ComboBarcodePageReq): DataResponse<PageVo<ComboBarcodePageResp>> {
        return ok(productComboService.barcodePage(req))
    }

    /**
     * 条码-组合商品-详情
     */
    @PostMapping("/seller/sku/detail")
    fun sellerSkuDetail(@Validated @RequestBody req: ComboSellerSkuDetailReq): DataResponse<List<ComboSellerSkuDetailResp>> {
        return ok(productComboService.sellerSkuDetail(req))
    }

    /**
     * 条码-组合商品-导出
     */
    @PostMapping("/export")
    fun export(response: HttpServletResponse, @Validated @RequestBody req: ComboBarcodePageReq): DataResponse<Unit> {
        productComboService.export(response, req)
        return ok()
    }

    /**
     * 条码-组合商品-更新记录
     */
    @PostMapping("/update/record")
    fun updateRecord(@Validated @RequestBody req: ComboBarcodeUpdateRecordReq): DataResponse<ComboBarcodeUpdateRecordResp> {
        return ok(productComboService.updateRecord(req))
    }
}