package tech.tiangong.pop.controller.product

import org.jetbrains.annotations.NotNull
import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.*
import team.aikero.blade.core.protocol.DataResponse
import team.aikero.blade.core.protocol.PageVo
import team.aikero.blade.core.protocol.ok
import tech.tiangong.eis.temu.enums.TemuCountryEnum
import tech.tiangong.eis.temu.enums.TemuLanguageRelationEnum
import tech.tiangong.eis.temu.enums.TemuPlaceOfOrigionEnum
import tech.tiangong.eis.temu.enums.TemuSiteEnum
import tech.tiangong.pop.req.product.lazada.QuerySkuByUpdateStockPriceReq
import tech.tiangong.pop.req.product.temu.*
import tech.tiangong.pop.resp.product.temu.*
import tech.tiangong.pop.service.product.ProductTemuService


/**
 * 商品管理-Temu
 * <AUTHOR>
 * @date 2025-06-05 10:31:11
 */
@Validated
@RestController
@RequestMapping("/web/v1/product/temu")
class ProductTemuController(
    private val productTemuService: ProductTemuService,
) {

    /**
     * Temu-产地配置
     */
    @PostMapping("/config/origin")
    fun originConfig(): DataResponse<List<TemuOriginConfigResp>> {
        return ok(
            TemuCountryEnum.entries
                .map {
                    TemuOriginConfigResp(
                        it.code,
                        it.countryName,
                        it.shortName,
                        it.chineseName
                    )
                })
    }


    /**
     * Temu-二级产地配置
     */
    @PostMapping("/config/place-of-origion")
    fun placeOfOrigionConfig(): DataResponse<List<TemuPlaceOfOrigionResp>> {
        return ok(
            TemuPlaceOfOrigionEnum.entries
                .map {
                    TemuPlaceOfOrigionResp(
                        it.region2Id,
                        it.province
                    )
                })
    }


    /**
     * Temu-站点配置
     */
    @PostMapping("/config/site")
    fun siteConfig(): DataResponse<List<TemuSiteConfigResp>> {
        return ok(
            TemuSiteEnum.entries
                .filter { it.isOpen }
                .map {
                    TemuSiteConfigResp(
                        it.siteId,
                        it.siteName
                    )
                })
    }

    /**
     * Temu-语言配置
     */
    @PostMapping("/config/language")
    fun languageConfig(): DataResponse<List<TemuLanguageConfigResp>> {
        return ok(
            TemuLanguageRelationEnum.entries
                .map {
                    TemuLanguageConfigResp(
                        it.languageCode,
                        it.languageName,
                        it.siteIdList,
                    )
                })
    }

    /**
     * Temu-商品分页
     *
     * @param req
     * @return ProductTemuPageResp
     */
    @PostMapping("/page")
    fun page(@Validated @RequestBody req: ProductTemuPageQueryReq): DataResponse<PageVo<ProductTemuPageResp>> {
        return ok(productTemuService.page(req))
    }


    /**
     * TEMU-商品详情
     *
     * @param req
     * @return ProductDetailResp
     */
    @PostMapping("/detail")
    fun detail(@Validated @RequestBody req: ProductTemuDetailReq): DataResponse<ProductTemuDetailResp> {
        return ok(productTemuService.detail(req))
    }


    /**
     * TEMU-统计数量
     */
    @PostMapping("/count")
    fun getProductCounts(@Validated @RequestBody req: ProductTemuPageQueryReq): DataResponse<ProductTemuCountResp> {
        return ok(productTemuService.getProductCounts(req))
    }


    /**
     * TEMU-商品基础维度统计（SPU/SKC数量）
     */
    @PostMapping("/dimension-counts")
    fun countProductDimensions(
        @Validated @RequestBody req: ProductTemuPageQueryReq,
    ): DataResponse<ProductStatsVO> {
        return ok(productTemuService.getCountSpuAndSkc(req))
    }


    /**
     * TEMU-商品更新
     *
     * @param req
     * @return
     */
    @PostMapping("/update")
    fun update(@Validated @RequestBody req: ProductTemuPublishedUpdateReq): DataResponse<Unit> {
        productTemuService.update(req)
        return ok()
    }


    /**
     * TEMU-单个/批量上架
     *
     * @param req
     * @return
     */
    @PostMapping("/publish")
    fun updateExistingPublish(@Validated @RequestBody req: ProductTemuExistingPublishReq): DataResponse<Unit> {
//        callAeComponent.publishExistingOrAddCountry(req)
        return ok()
    }


    /**
     * TEMU-修改库存
     *
     * @param req
     * @return
     */
    @PostMapping("/inventory/update")
    fun updateInventory(@Validated @RequestBody req: ProductTemuInventoryUpdateReq): DataResponse<Unit> {
        productTemuService.inventoryUpdate(req)
        return ok()
    }


    /**
     * TEMU-批量修改库存
     *
     * @param req
     * @return
     */
    @PostMapping("/inventory/batch-update")
    fun updateInventory(@Validated @RequestBody req: ProductBatchInventoryUpdateReq): DataResponse<Unit> {
        productTemuService.inventoryBatchUpdate(req)
        return ok()
    }


    /**
     * TEMU-批量修改申报价格
     *
     * @param req
     * @return
     */
    @PostMapping("/declaredPrice/batch-update")
    fun updateDeclaredPrice(@Validated @RequestBody req: ProductBatchDeclaredPriceUpdateReq): DataResponse<Unit> {
//        callAeComponent.publishExistingOrAddCountry(req)
        return ok()
    }


    /**
     * TEMU-批量修改发货仓、运费模版
     *
     * @param req
     * @return
     */
    @PostMapping("/deliver/batch-update")
    fun updateDeliver(@Validated @RequestBody req: ProductBatchDeliverUpdateReq): DataResponse<Unit> {
//        callAeComponent.publishExistingOrAddCountry(req)
        return ok()
    }


    /**
     * TEMU-批量修改体积与重量、SKU分类
     *
     * @param req
     * @return
     */
    @PostMapping("/weight/batch-update")
    fun updateWeight(@Validated @RequestBody req: ProductBatchWeightUpdateReq): DataResponse<Unit> {
//        callAeComponent.publishExistingOrAddCountry(req)
        return ok()
    }


    /**
     * 单个修改库存查询
     */
    @GetMapping("/inventory/update/{saleGoodsId}")
    fun inventoryUpdateQuery(@PathVariable @NotNull("销售商品ID不能为空") saleGoodsId: Long): DataResponse<List<InventoryUpdateResp>> {
        return ok(productTemuService.inventoryUpdateQuery(saleGoodsId))
    }


    /**
     * 批量修改库存查询
     */
    @PostMapping("/inventory/batch-update-query")
    fun inventoryBatchUpdateQuery(@Validated @RequestBody req: ProductBatchUpdateQuery): DataResponse<InventoryBatchUpdateResp> {
        return ok(productTemuService.inventoryUpdateBatchQuery(req))
    }


    /**
     * 批量修改价格查询
     */
    @PostMapping("/declared-price/batch-update-query")
    fun declaredPriceBatchUpdateQuery(@Validated @RequestBody req: ProductBatchUpdateQuery): DataResponse<PriceBatchUpdateResp> {
//        callAeComponent.publishExistingOrAddCountry(req)
        return ok()
    }


    /**
     * 弹窗查询-编辑库存/价格
     *
     * @param req
     */
    @PostMapping("/update/sku/stock/price/query")
    fun querySkuByUpdateStockPrice(@Validated @RequestBody req: QuerySkuByUpdateStockPriceReq): DataResponse<List<TemuQuerySkuByUpdateStockPriceResp>> {
        return ok(productTemuService.querySkuByUpdateStockPrice(req))
    }


    /**
     * 异步导出上架数据
     *
     * @param req 入参
     * @date 2024/9/2
     */
    @PostMapping("/submit-export-task")
    fun submitExportTask(@Validated @RequestBody req: ProductTemuPageQueryReq): DataResponse<Unit> {
        productTemuService.submitExportTask(req)
        return ok()
    }


    /**
     * 查询库存
     */
    @GetMapping("/query/inventory/{saleGoodsId}")
    fun queryInventory(@PathVariable @NotNull("销售商品ID不能为空") saleGoodsId: Long): DataResponse<InventoryQueryResp> {
        return ok()
    }


}