package tech.tiangong.pop.controller.tryon

import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import team.aikero.blade.core.protocol.DataResponse
import team.aikero.blade.core.protocol.ok
import tech.tiangong.pop.req.tryon.BatchConfigTryOnTaskReq
import tech.tiangong.pop.req.tryon.SaveTryOnTaskByRuleReq
import tech.tiangong.pop.resp.tryon.PrepareTryOnTaskResp
import tech.tiangong.pop.service.tryon.TryOnTaskService

/**
 * tryOn任务管理
 * @date 2025-7-15 10:31:11
 */
@Validated
@RestController
@RequestMapping("/web/v1/try-on-task")
class TryOnTaskController(
    private val tryOnTaskService: TryOnTaskService,
) {

    /**
     * 准备配置TryOn任务
     * @param req
     * @return List<PrepareTryOnTaskResp>
     */
    @PostMapping("/prepare-try-on-task")
    fun prepareTryOnTaskByProductId(@RequestBody productIds: List<Long>): DataResponse<List<PrepareTryOnTaskResp>> {
        return ok(tryOnTaskService.prepareTryOnTaskByProductId(productIds))
    }

    /**
     * 通过规则创建tryOn任务
     *
     * @param req
     */
    @PostMapping("/save-try-on-task-by-rule")
    fun saveTryOnTaskByRule(@Validated @RequestBody req: List<SaveTryOnTaskByRuleReq>): DataResponse<Void> {
        tryOnTaskService.saveTryOnTaskByRule(req)
        return ok()
    }

    /**
     * 通过配置创建tryOn任务
     */
    @PostMapping("/save-try-on-task-by-config")
    fun saveTryOnTaskByConfig(@Validated @RequestBody req: BatchConfigTryOnTaskReq): DataResponse<Void> {
        tryOnTaskService.saveTryOnTaskByConfig(req)
        return ok()
    }

}
