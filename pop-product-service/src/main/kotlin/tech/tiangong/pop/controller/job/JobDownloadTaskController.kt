package tech.tiangong.pop.controller.job

import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import team.aikero.blade.auth.annotation.PreCheckIgnore
import team.aikero.blade.auth.withSystemUser
import team.aikero.blade.core.protocol.DataResponse
import team.aikero.blade.core.protocol.ok
import tech.tiangong.pop.service.settings.DownloadTaskHandleService

/**
 *
 *下载任务
 */
@PreCheckIgnore
@RestController
@RequestMapping("/job/v1/download")
class JobDownloadTaskController(
    private val downloadTaskHandleService: DownloadTaskHandleService
) {

    /**
     * 处理下载任务
     * @return DataResponse<Unit>
     */
    @PostMapping("/process")
    fun processTasks(): DataResponse<Unit> {
        withSystemUser {
            downloadTaskHandleService.processTasks()
        }
        return ok()
    }
}
