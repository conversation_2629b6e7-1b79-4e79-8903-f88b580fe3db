package tech.tiangong.pop.controller.settings

import org.springframework.web.bind.annotation.*
import team.aikero.blade.core.protocol.DataResponse
import team.aikero.blade.core.protocol.ok
import tech.tiangong.pop.common.enums.CurrencyEnum
import tech.tiangong.pop.resp.settings.ExchangeRateMarketResp
import tech.tiangong.pop.service.settings.RefExchangeRateMarketService

/**
 * 基础设置-参考汇率
 */
@RestController
@RequestMapping("/web/v1/market-rates")
class RefExchangeRateMarketController(
    private val refExchangeRateMarketService: RefExchangeRateMarketService
) {
    /**
     * 查询USD市场汇率
     */
    @GetMapping("/usd")
    fun queryUsdMarketRates(): DataResponse<List<ExchangeRateMarketResp>> {
        return ok(refExchangeRateMarketService.queryMarketRates(CurrencyEnum.USD.code))
    }

    /**
     * 查询CNY市场汇率
     */
    @GetMapping("/cny")
    fun queryCnyMarketRates(): DataResponse<List<ExchangeRateMarketResp>> {
        return ok(refExchangeRateMarketService.queryMarketRates(CurrencyEnum.CNY.code))
    }

    /**
     * 手动触发同步单个币种
     */
    @PostMapping("/sync/{currency}")
    fun syncRates(@PathVariable currency: String) : DataResponse<Unit>{
        refExchangeRateMarketService.syncExternalRates(currency.uppercase())
        return ok()
    }

    /**
     * 手动触发同步配置中的所有币种
     */
    @PostMapping("/sync/all")
    fun syncAllRates() : DataResponse<Unit>{
        refExchangeRateMarketService.syncAllRates()
        return ok()
    }
}
