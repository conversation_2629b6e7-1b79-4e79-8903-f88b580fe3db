package tech.tiangong.pop.controller.image

import jakarta.servlet.http.HttpServletResponse
import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.*
import team.aikero.blade.core.constant.UrlVersionConstant.WEB
import team.aikero.blade.core.protocol.DataResponse
import team.aikero.blade.core.protocol.PageVo
import team.aikero.blade.core.protocol.ok
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import tech.tiangong.pop.req.image.*
import tech.tiangong.pop.resp.image.BatchReplaceImagesResp
import tech.tiangong.pop.resp.image.ImageExistingSpuInfoResp
import tech.tiangong.pop.resp.image.ImageRepDetailVO
import tech.tiangong.pop.resp.image.ImageRepositoryPageVO
import tech.tiangong.pop.service.image.ImageRepositoryService
import tech.tiangong.pop.service.image.VisualImageSyncService
import tech.tiangong.sdp.design.vo.dto.VisualImageMqDto

/**
 * 图片管理
 */
@Slf4j
@RestController
@RequestMapping("$WEB/v1/image-manager")
class ImageManagerController(
    private val imageRepositoryService: ImageRepositoryService,
    private val visualImageSyncService: VisualImageSyncService,
) {

    /**
     * 列表
     *
     * @param req 分页对象
     * @return 图片列表分页数据
     */
    @PostMapping("/page")
    fun page(@Validated @RequestBody req: LazadaImageQueryReq): DataResponse<PageVo<ImageRepositoryPageVO>> {
        log.info { "查询图片列表，请求参数: $req" }
        return ok(imageRepositoryService.page(req))
    }

    /**
     * 批量删除
     *
     * @param req 删除请求
     * @return 操作结果
     */
    @Deprecated(message = "视觉中心需求功能迭代移除，后续下线")
    // @PostMapping("/delete")
    fun bathDelete(@Validated @RequestBody req: LazadaImageDeletedReq): DataResponse<Unit> {
        log.info { "批量删除图片，请求参数: $req" }
        imageRepositoryService.delete(req)
        return ok()
    }

    /**
     * 图片详情
     *
     * @param imageRepositoryId 图片主键ID
     * @return 图片详情数据
     */
    @PostMapping("/detail/{imageRepositoryId}")
    fun detail(@PathVariable("imageRepositoryId") imageRepositoryId: Long): DataResponse<ImageRepDetailVO> {
        log.info { "查询图片详情，图片ID: $imageRepositoryId" }
        return ok(imageRepositoryService.detail(imageRepositoryId))
    }

    /**
     * 保存
     *
     * @param req 保存请求
     * @return 操作结果
     */
    @PostMapping("/save")
    fun save(@Validated @RequestBody req: ImageSaveReq): DataResponse<Void> {
        log.info { "保存图片信息，请求参数: $req" }
        imageRepositoryService.save(req)
        return ok()
    }

    /**
     * 编辑
     *
     * @param req 编辑请求
     * @return 操作结果
     */
    @PostMapping("/edit")
    fun edit(@Validated @RequestBody req: ImageEditReq): DataResponse<Void> {
        log.info { "编辑图片信息，请求参数: $req" }
        imageRepositoryService.edit(req)
        return ok()
    }

    /**
     * 批量保存或更新图片信息
     *
     * @param req 图片保存请求
     * @return 操作结果
     */
    @PostMapping("/save-or-update")
    fun saveOrUpdate(@Validated @RequestBody req: ImageSaveReq): DataResponse<Void> {
        log.info { "批量保存或更新图片信息，请求参数: $req" }
        imageRepositoryService.saveOrUpdate(req)
        return ok()
    }

    /**
     * 获取已存在的SPU信息
     *
     * @param req 已存在SPU查询请求
     * @return 已存在的SPU信息列表
     */
    @PostMapping("/existing-spu")
    fun getExistingSpu(@Validated @RequestBody req: ImageExistingSpuReq): DataResponse<List<ImageExistingSpuInfoResp>> {
        log.info { "查询已存在的SPU信息，请求参数: $req" }
        val result = imageRepositoryService.getExistingSpu(req)
        return ok(result)
    }

    /**
     * 提交图片下载任务
     *
     * @param req 入参
     * @return 操作结果
     * @date 2024/12/3
     */
    @PostMapping("/submit-image-download-task")
    fun submitImageDownloadTask(@Validated @RequestBody req: ImageDownloadReq): DataResponse<Void> {
        log.info { "提交图片下载任务，请求参数: $req" }
        imageRepositoryService.submitImageDownloadTask(req)
        return ok()
    }

    /**
     * 批量替换图片
     *
     * @param req 入参
     * @return 批量替换结果
     */
    @Deprecated(message = "视觉中心需求功能迭代移除，后续下线")
    // @PostMapping("/batch-replace-images")
    fun batchReplaceImages(@Validated @RequestBody req: BatchReplaceImagesReq): DataResponse<BatchReplaceImagesResp> {
        log.info { "批量替换图片，请求参数: $req" }
        return ok(imageRepositoryService.batchReplaceImages(req))
    }

    /**
     * 导出图片
     *
     * @param req 入参
     */
    @PostMapping("/export")
    fun export(@Validated @RequestBody req: LazadaImageQueryReq, response: HttpServletResponse) {
        imageRepositoryService.export(req, response)
    }

    /**
     * 批量同步视觉中心图片
     *
     * @param req 入参
     */
    @PostMapping("/sync-visual-image")
    fun syncVisualImage(@RequestBody req: List<VisualImageMqDto>): DataResponse<Void> {
        visualImageSyncService.batchSyncVisualImage(req)
        return ok()
    }

}