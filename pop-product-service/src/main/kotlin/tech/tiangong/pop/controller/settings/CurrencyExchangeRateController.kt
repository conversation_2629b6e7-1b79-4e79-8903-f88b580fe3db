package tech.tiangong.pop.controller.settings

import jakarta.validation.Valid
import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.*
import team.aikero.blade.core.protocol.DataResponse
import team.aikero.blade.core.protocol.ok
import tech.tiangong.pop.common.enums.CurrencyEnum
import tech.tiangong.pop.req.settings.ExchangeRateQueryReq
import tech.tiangong.pop.req.settings.ExchangeRateSaveReq
import tech.tiangong.pop.resp.settings.ExchangeRateResp
import tech.tiangong.pop.service.settings.CurrencyExchangeRateService

/**
 * 基础配置-配置汇率
 */
@Validated
@RestController
@RequestMapping("/web/v1/exchange-rates")
class CurrencyExchangeRateController(
    private val currencyExchangeRateService: CurrencyExchangeRateService
) {
    /**
     * 查询人民币汇率列表
     */
    @GetMapping("/cny")
    fun queryCnyRates(): DataResponse<List<ExchangeRateResp>> {
        return ok(
            currencyExchangeRateService.queryExchangeRates(
                ExchangeRateQueryReq(currencyType = CurrencyEnum.CNY.code)
            )
        )
    }

    /**
     * 查询美元汇率列表
     */
    @GetMapping("/usd")
    fun queryUsdRates(): DataResponse<List<ExchangeRateResp>> {
        return ok(
            currencyExchangeRateService.queryExchangeRates(
                ExchangeRateQueryReq(currencyType = CurrencyEnum.USD.code)
            )
        )
    }

    /**
     * 保存或更新汇率配置
     *
     * @param req 保存请求对象
     * @return 操作成功与否，成功返回true，失败返回false
     */
    @PostMapping("/save-or-update")
    fun saveOrUpdate(@RequestBody @Valid req: ExchangeRateSaveReq): DataResponse<Boolean> {
        return ok(currencyExchangeRateService.saveOrUpdate(req))
    }

    /**
     * 批量保存或更新汇率配置
     *
     * @param reqList 批量保存请求对象列表
     * @return 操作成功与否，成功返回true，失败返回false
     */
    @PostMapping("/batch-save-or-update")
    fun batchSaveOrUpdate(@RequestBody @Valid reqList: List<ExchangeRateSaveReq>): DataResponse<Boolean> {
        return ok(currencyExchangeRateService.batchSaveOrUpdate(reqList))
    }

    /**
     * 失效全部缓存
     */
    @PostMapping("/invalidate-all-cache")
    fun invalidateAllCache(): DataResponse<Unit> {
        currencyExchangeRateService.invalidateAllCache()
        return ok()
    }
}
