package tech.tiangong.pop.controller.product

import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import team.aikero.blade.core.protocol.DataResponse
import team.aikero.blade.core.protocol.PageVo
import team.aikero.blade.core.protocol.ok
import tech.tiangong.pop.req.product.task.TaskExportByBatchUpdateReq
import tech.tiangong.pop.req.product.task.TaskUpdateProgressPageReq
import tech.tiangong.pop.resp.product.task.TaskUpdateProgressPageVo
import tech.tiangong.pop.service.TaskService
import jakarta.servlet.http.HttpServletResponse
import jakarta.validation.Valid
import tech.tiangong.pop.enums.PlatformProductPullTaskStatusEnum

/**
 * 任务调度
 */
@Validated
@RestController
@RequestMapping("/web/v1/task")
class TaskController(
    private val taskService: TaskService,
) {

    /**
     * 更新进度-分页
     *
     * @param req
     * @return
     */
    @PostMapping("/update/progress/page")
    fun updateProgressPage(@Valid @RequestBody req: TaskUpdateProgressPageReq): DataResponse<PageVo<TaskUpdateProgressPageVo>> {
        return ok(taskService.updateProgressPage(req))
    }

    /**
     * 更新失败-导出结果
     * @param req
     * @param response
     * @return
     */
    @Deprecated(message = "用exportByBatchUpdateTaskType方法代替")
    @PostMapping("/update/fail/export")
    fun exportByUpdateFail(@Validated @RequestBody req: TaskExportByBatchUpdateReq, response: HttpServletResponse): DataResponse<Void> {
        req.taskStatus = PlatformProductPullTaskStatusEnum.FAILED.code
        taskService.exportByBatchUpdateTaskType(response, req)
        return ok()
    }

    /**
     * 批量更新任务-导出结果
     * @param req
     * @param response
     * @return
     */
    @PostMapping("/batch-update/export")
    fun exportByBatchUpdateTaskType(@Validated @RequestBody req: TaskExportByBatchUpdateReq, response: HttpServletResponse): DataResponse<Void> {
        taskService.exportByBatchUpdateTaskType(response, req)
        return ok()
    }
}
