package tech.tiangong.pop.controller.settings

import org.springframework.web.bind.annotation.*
import team.aikero.blade.core.protocol.DataResponse
import team.aikero.blade.core.protocol.PageVo
import team.aikero.blade.core.protocol.ok
import tech.tiangong.pop.req.settings.SkuCodeGenerateReq
import tech.tiangong.pop.req.settings.SkuCodeRulePageReq
import tech.tiangong.pop.req.settings.SkuCodeRuleSaveReq
import tech.tiangong.pop.resp.settings.SkuCodeRulePageResp
import tech.tiangong.pop.resp.settings.SkuRuleElementResp
import tech.tiangong.pop.service.SkuCodeGenerateService
import tech.tiangong.pop.service.SkuCodeRuleService
import jakarta.validation.Valid

/**
 * 基础设置-商品上架编码规则
 */
@RestController
@RequestMapping("/web/v1/sku-code-rule")
class SkuCodeRuleController(
    private val skuCodeRuleService: SkuCodeRuleService,
    private val skuCodeGenerateService: SkuCodeGenerateService

) {
    /**
     * SKU编码规则分页查询接口
     *
     * @param req 包含分页和查询条件的请求对象
     * @return 返回包含分页结果的响应对象，内含SKU编码规则信息列表
     */
    @PostMapping("/page")
    fun page(@Valid @RequestBody req: SkuCodeRulePageReq): DataResponse<PageVo<SkuCodeRulePageResp>> {
        return ok(skuCodeRuleService.page(req))
    }

    /**
     * 获取指定SKU编码规则的元素列表接口
     *
     * @param skuCodeRuleId SKU编码规则的主键ID
     * @return 返回包含SKU规则元素信息的响应对象列表
     */
    @GetMapping("/elements/{skuCodeRuleId}")
    fun getElements(@PathVariable skuCodeRuleId: Long): DataResponse<List<SkuRuleElementResp>> {
        return ok(skuCodeRuleService.getElements(skuCodeRuleId))
    }

    /**
     * 保存或更新SKU编码规则接口
     *
     * @param req 包含需要保存或更新的SKU编码规则数据的请求对象
     * @return 返回操作成功与否的响应对象，成功返回true，失败返回false
     */
    @PostMapping("/save-or-update")
    fun saveOrUpdate(@Valid @RequestBody req: SkuCodeRuleSaveReq): DataResponse<Boolean> {
        return ok(skuCodeRuleService.saveOrUpdate(req))
    }

    /**
     * 删除SKU编码规则接口
     *
     * @param skuCodeRuleId SKU编码规则的主键ID
     * @return 返回操作成功与否，成功返回true，失败返回false
     */
    @PostMapping("/delete/{skuCodeRuleId}")
    fun delete(@PathVariable skuCodeRuleId: Long): DataResponse<Boolean> {
        return ok(skuCodeRuleService.delete(skuCodeRuleId))
    }

    /**
     * 生成SKU编码
     */
    @PostMapping("/generate")
    fun generateSkuCode(@RequestBody @Valid req: SkuCodeGenerateReq): DataResponse<String> {
        val skuCode = skuCodeGenerateService.generateSkuCode(req)
        return ok(skuCode)
    }
}
