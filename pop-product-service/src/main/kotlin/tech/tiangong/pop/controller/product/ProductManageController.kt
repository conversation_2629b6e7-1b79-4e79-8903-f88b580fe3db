package tech.tiangong.pop.controller.product

import jakarta.validation.Valid
import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import team.aikero.blade.core.protocol.DataResponse
import team.aikero.blade.core.protocol.PageVo
import team.aikero.blade.core.protocol.ok
import tech.tiangong.pop.component.ProductCommonComponent
import tech.tiangong.pop.component.export.ImportFailProductExportComponent
import tech.tiangong.pop.enums.ImportSourceEnum
import tech.tiangong.pop.req.product.*
import tech.tiangong.pop.req.product.manage.ProductManagePageReq
import tech.tiangong.pop.req.product.v2.AePricingCalculateReq
import tech.tiangong.pop.resp.product.*
import tech.tiangong.pop.resp.product.manage.ProductManagePageResp
import tech.tiangong.pop.resp.product.v2.AePricingCalResp
import tech.tiangong.pop.resp.product.v2.AeUnifiedPricingResp
import tech.tiangong.pop.service.product.ProductManageService
import tech.tiangong.pop.service.product.ProductPriceManagementService
import tech.tiangong.pop.service.product.ProductTitleGenerateService
import tech.tiangong.pop.service.product.price.sale.AeSalePricingService
import tech.tiangong.pop.service.v2.pricing.AeTemplatePricingService
import java.io.IOException

/**
 * 商品管理v2
 *
 * <AUTHOR>
 * @date 2025-3-29 20:13:34
 */
@RestController
@RequestMapping("/web/product/manage")
class ProductManageController(
    private val productManageService: ProductManageService,
    private val productPriceManagementService: ProductPriceManagementService,
    private val productTitleGenerateService: ProductTitleGenerateService,
    private val aeTemplatePricingService: AeTemplatePricingService,
    private val aeSalePricingService: AeSalePricingService,
    private val productCommonComponent: ProductCommonComponent,
    private val importFailProductExportComponent: ImportFailProductExportComponent,
) {

    /**
     * 企划审核-分页
     * @tags v0904
     */
    @PostMapping("/plan-audit/page")
    fun page(@Validated @RequestBody req: ProductManagePageReq): DataResponse<PageVo<ProductManagePageResp>> {
        return ok(productManageService.page(req))
    }

    /**
     * 企划审核
     */
    @PostMapping("/plan-audit")
    fun planAudit(@Validated @RequestBody req: ProductPlanAuditReq): DataResponse<Unit> {
        productManageService.planAudit(req)
        return ok()
    }

    /**
     * 修改商品的毛利率配置
     */
    @PostMapping("/update-gross-margin")
    fun updateProductGrossMargin(@Validated @RequestBody req: UpdateProductGrossMarginReq): DataResponse<Unit> {
        productManageService.updateProductGrossMargin(req)
        return ok()
    }

    /**
     * 修改商品指定店铺的毛利率配置
     */
    @PostMapping("/update-gross-margin-by-shop")
    fun updateProductGrossMarginByShop(@Validated @RequestBody req: UpdateProductGrossMarginByShopReq): DataResponse<Unit> {
        productManageService.updateProductGrossMarginByShop(req)
        return ok()
    }

    /**
     * 计算skc价格
     *
     * @param req
     * @return
     */
    @PostMapping("/skc/cul/price")
    @Throws(IOException::class)
    fun skcCulPrice(@RequestBody req: AutoCalPriceReq): DataResponse<List<AutoCalPriceResp>> {
        return ok(productPriceManagementService.autoCalPrice(req))
    }

    /**
     * 预览生成单个商品标题
     */
    @PostMapping("/title-generate/preview")
    fun previewTitleGenerate(@RequestBody @Valid req: ProductTitleGenerateReq): DataResponse<ProductTitleGenerateResp> {
        return ok(productTitleGenerateService.previewTitle(req))
    }

    /**
     * 批量预览生成商品标题
     */
    @PostMapping("/title-generate/batch-preview")
    fun batchPreviewTitleGenerate(@RequestBody @Valid req: BatchProductTitleGenerateReq): DataResponse<BatchProductTitleGenerateResp> {
        return ok(productTitleGenerateService.batchPreviewTitle(req))
    }

    /**
     * 根据标题配置生成全部标题
     */
    @PostMapping("/title-generate/rule-config/multi")
    fun generateMultiTitles(@RequestBody @Valid req: ProductTitleConfigMultiGenerateReq): DataResponse<ProductTitleConfigMultiGenerateResp> {
        return ok(productTitleGenerateService.generateMultiTitlesByConfig(req))
    }

    /**
     * 批量根据标题配置生成全部标题
     */
    @PostMapping("/title-generate/rule-config/batch-multi")
    fun batchGenerateMultiTitles(@RequestBody @Valid req: BatchProductTitleConfigMultiGenerateReq): DataResponse<BatchProductTitleConfigMultiGenerateResp> {
        return ok(productTitleGenerateService.batchGenerateMultiTitlesByConfig(req))
    }

    /**
     * 根据标题配置生成指定下标标题
     */
    @PostMapping("/title-generate/rule-config/specific")
    fun generateSpecificTitle(@RequestBody @Valid req: ProductTitleConfigSpecificGenerateReq): DataResponse<ProductTitleConfigSpecificGenerateResp> {
        return ok(productTitleGenerateService.generateSpecificTitleByConfig(req))
    }

    /**
     * AE V2 价格计算（仅计算）
     */
    @PostMapping("/v2/ae/pricing/calculate")
    fun aeV2PricingCalculate(@Valid @RequestBody req: AePricingCalculateReq): DataResponse<AePricingCalResp> {
        return ok(aeTemplatePricingService.calculate(req))
    }

    /**
     * AE V2 价格计算并保存
     */
    @PostMapping("/v2/ae/pricing/calculate-and-save")
    fun aeV2PricingCalculateAndSave(@Valid @RequestBody req: AePricingCalculateReq): DataResponse<AePricingCalResp> {
        return ok(aeTemplatePricingService.calculateAndSave(req))
    }

    /**
     * AE 按模板批量计算售价（支持多店铺）【兼容V1、V2】
     */
    @PostMapping("/ae/pricing/template")
    fun autoCalPriceByTemplate(
        @RequestBody request: TemplatePricingReq,
    ): AeUnifiedPricingResp {
        return aeSalePricingService.autoCalPriceByTemplate(
            request.templateSpuId,
            request.shopIds
        )
    }

    /**
     * AE 按销售Id+单店铺计算售价【兼容V1、V2】
     */
    @PostMapping("/ae/pricing/sale")
    fun autoCalPriceBySale(
        @RequestBody request: SalePricingReq,
    ): AeUnifiedPricingResp {
        return aeSalePricingService.autoCalPriceBySale(
            request.saleGoodsId,
            request.shopId
        )
    }

    /**
     * 查看上架失败原因(三方平台共用)
     * @tags v0904
     */
    @PostMapping("/pending/publish-errorlog")
    fun publishErrorLog(@RequestBody req: PendingErrorLogQueryReq): DataResponse<PageVo<PublishErrorLogResp>> {
        return ok(productManageService.publishErrorLog(req))
    }

    /**
     * 待上架-导入进度(三方平台共用)
     * @tags v0904
     */
    @PostMapping("/pending/import-product-log")
    fun importProductLog(@RequestBody req: ImportProductPageReq): DataResponse<PageVo<ImportProductPageResp>> {
        return ok(productCommonComponent.importProductPage(req, req.platformId, ImportSourceEnum.PENDING_UPLOAD.code))
    }

    /**
     * 模板-导出"导入失败"
     * @tags v0904
     */
    @PostMapping("/submit/importfail-product-export-task")
    fun submitImportProductFailExportTask(@Validated @RequestBody req: ExportAwaitProductReq): DataResponse<Unit> {
        importFailProductExportComponent.createExportTask(req)
        return ok()
    }
}
