package tech.tiangong.pop.controller.product.pending

import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.*
import org.springframework.web.multipart.MultipartFile
import team.aikero.blade.core.protocol.DataResponse
import team.aikero.blade.core.protocol.PageVo
import team.aikero.blade.core.protocol.failed
import team.aikero.blade.core.protocol.ok
import tech.tiangong.pop.component.export.LazadaProductExportComponent
import tech.tiangong.pop.component.export.pending.error.publish.LazadaPublishErrorLogExportComponent
import tech.tiangong.pop.component.lazada.CallLazadaComponent
import tech.tiangong.pop.req.product.ProductPendingPublishLogPageReq
import tech.tiangong.pop.req.product.ae.ProductPendingAePageReq
import tech.tiangong.pop.req.product.lazada.*
import tech.tiangong.pop.resp.product.ProductPendingPublishLogPageResp
import tech.tiangong.pop.resp.product.ProductPendingPublishLogStatisticsResp
import tech.tiangong.pop.resp.product.ae.ProductPendingAeStatisticsResp
import tech.tiangong.pop.resp.product.lazada.ProductPendingLazadaDetailResp
import tech.tiangong.pop.resp.product.lazada.ProductPendingLazadaPageResp
import tech.tiangong.pop.service.product.pending.ProductPendingLazadaService
import java.io.IOException

/**
 * 待上架商品-Lazada
 */
@RestController
@RequestMapping("/web/v1/product/pending/lazada")
class ProductPendingLazadaController(
    private val productPendingLazadaService: ProductPendingLazadaService,
    private val callLazadaComponent: CallLazadaComponent,
    private val lazadaPublishErrorLogExportComponent: LazadaPublishErrorLogExportComponent,
    private val lazadaProductExportComponent: LazadaProductExportComponent,
) {

    /**
     * 商品列表-分页
     * @tags v0904
     */
    @PostMapping("/page")
    fun page(@Validated @RequestBody req: ProductPendingLazadaPageReq): DataResponse<PageVo<ProductPendingLazadaPageResp>> {
        return ok(productPendingLazadaService.page(req))
    }

    /**
     * 商品列表-统计
     * @tags v0904
     */
    @PostMapping("/statistics")
    fun statistics(@Validated @RequestBody req: ProductPendingAePageReq): DataResponse<ProductPendingAeStatisticsResp> {
        return ok(productPendingLazadaService.statistics(req))
    }

    /**
     * 商品详情
     * @tags v0904
     */
    @PostMapping("/detail")
    fun detail(@Validated @RequestBody req: ProductPendingLazadaDetailReq): DataResponse<ProductPendingLazadaDetailResp> {
        return ok(productPendingLazadaService.detail(req))
    }

    /**
     * 商品更新
     * @tags v0904
     */
    @PostMapping("/update")
    fun update(@Validated @RequestBody req: ProductPendingUpdateLazadaReq): DataResponse<Unit> {
        productPendingLazadaService.update(req)
        return ok()
    }

    /**
     * 任务操作-开始任务
     * @tags v0904
     */
    @PostMapping("/task/start")
    fun start(@Validated @RequestBody lazadaSpuIds: List<Long>): DataResponse<Unit> {
        if (lazadaSpuIds.isEmpty()) {
            return failed("请选择商品")
        }
        productPendingLazadaService.start(lazadaSpuIds)
        return ok()
    }

    /**
     * 任务操作-更换人员
     * @tags v0904
     */
    @PostMapping("/task/change")
    fun change(@Validated @RequestBody req: ProductPendingLazadaTaskChangeReq): DataResponse<Unit> {
        if (req.lazadaSpuIds.isEmpty()) {
            return failed("请选择商品")
        }
        if (req.users.isEmpty()) {
            return failed("请选择人员")
        }
        productPendingLazadaService.change(req)
        return ok()
    }

    /**
     * 任务操作-取消上架
     * @tags v0904
     */
    @PostMapping("/task/cancel")
    fun cancel(@Validated @RequestBody req: ProductPendingLazadaTaskCancelReq): DataResponse<Unit> {
        productPendingLazadaService.cancel(req)
        return ok()
    }

    /**
     * 上架Lazada平台
     * @tags v0904
     *
     * @param req 入参
     */
    @PostMapping("/platform/update")
    fun platformUpdate(@Validated @RequestBody req: List<ProductPendingLazadaPlatformUpdateReq>): DataResponse<Unit> {
        if (req.isEmpty()) {
            return failed("请选择商品")
        }
        req.forEach {
            callLazadaComponent.publishProduct(it)
        }
        return ok()
    }

    /**
     * 导出-待上架-上架失败
     * @tags v0904
     */
    @PostMapping("/export/publish-error-log")
    fun exportPublishErrorLog(@Validated @RequestBody req: ProductPendingLazadaPageReq): DataResponse<Unit> {
        lazadaPublishErrorLogExportComponent.createExportTask(req)
        return ok()
    }

    /**
     * 上架记录-统计
     * @tags v0904
     */
    @PostMapping("/publish/log/statistics")
    fun publishLogStatistics(): DataResponse<ProductPendingPublishLogStatisticsResp> {
        return ok(productPendingLazadaService.publishLogStatistics())
    }

    /**
     * 上架记录-分页列表
     * @tags v0904
     */
    @PostMapping("/publish/log/page")
    fun publishLogPage(@RequestBody req: ProductPendingPublishLogPageReq): DataResponse<PageVo<ProductPendingPublishLogPageResp>> {
        return ok(productPendingLazadaService.publishLogPage(req))
    }

    /**
     * 导出-待上架-商品数据
     * @tags v0904
     */
    @PostMapping("/export-product")
    fun exportProductPublishDataAsync(@Validated @RequestBody req: ProductPendingLazadaPageReq): DataResponse<Unit> {
        lazadaProductExportComponent.createExportTask(req)
        return ok()
    }

    /**
     * 导入-待上架-商品数据
     * @tags v0904
     */
    @PostMapping("/import-product")
    @Throws(IOException::class)
    fun importExcel(@RequestParam("excelFile") excelFile: MultipartFile): DataResponse<Unit> {
        productPendingLazadaService.importExcel(excelFile)
        return ok()
    }
}