package tech.tiangong.pop.enums

/**
 * 字典枚举-对应字典服务列表
 * <AUTHOR>
 * @date 2024-12-9 23:50:59
 */
enum class DictEnum(val dictCode: String, val desc: String) {
    PLM_CLOTHING_BAND("plm_clothing_band", "款式波段"),
    FD_PRINTING("fd-printing", "建议印花"),
    SKC_CANCEL_REASON("skc_cancel_reason", "SKC取消原因"),
    TRAY_TYPE("tray_type", "货盘类型"),
    PRICE_BAND("price_band", "价格带"),
    RUNNING_DIAGRAM_PROBLEM("running_diagram_problem", "跑图问题反馈"),
    INSPIRATION_CANCEL_REASON("inspiration_cancel_reason", "灵感淘汰原因"),
    PLANNING_SOURCE("planning_source", "企划来源"),
    SUPPLY_MODE("supply_mode", "供给方式（商品类型）"),
    NATIONAL("national", "国家站点"),
    JV_STYLE("jv-style", "风格"),
    CLOTHING_CATEGORY("clothing_category", "内部品类"),
    PLM_STANDARD_SIZE("plm_standard_size", "尺码组"),
    CLOTHING_COLOR("clothing_color", "颜色库"),
    MARKET_STYLE("Market_Style", "市场风格库"),
    NATIONAL_MARKET("National_Market", "市场国家库"),
    AE_SHIPPING_FROM("AE_Shipping_From", "AE发货地区域"),
    AE_SHIPPING_TO("AE_Shipping_To", "AE目的地区域"),
    TRYON_RULE("tryon_rule", "Tryon规则"),
    AE_LIST_REG("AE_LIST_REG", "AE上架区域"),
    APS_CATEGORY_TYPE("aps_category_type", "织造方式"),
    JV_SCENE("JV_scene", "场景"),
}