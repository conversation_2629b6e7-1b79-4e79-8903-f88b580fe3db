package tech.tiangong.pop.enums

/**
 * 待上架列表字段
 */
enum class PageColumnEnum(
    /**
     * 字段编码
     */
    val columnCode: String,
    /**
     * 描述
     */
    val desc: String,
    /**
     * 是否默认显示，1是0否
     */
    val defaultShow: Int = 1,
    /**
     * 页面编码
     */
    val page:PageCodeEnum,
) {
    //待上架列表字段
    mainImgUrl("mainImgUrl", "主图",1,PageCodeEnum.productPage),
    supplyMode("supplyMode", "供给方式",1,PageCodeEnum.productPage),
    waves("waves", "波次",1,PageCodeEnum.productPage),
    spuCode("spuCode", "SPU",1,PageCodeEnum.productPage),
    tagCodes("tagCodes", "标签",1,PageCodeEnum.productPage),
    imagePackageState("imagePackageState", "图包状态",1,PageCodeEnum.productPage),
    shopList("shopList","选款店铺",1,PageCodeEnum.productPage),
    salePrice("salePrice","售价",0,PageCodeEnum.productPage),
    retailPrice("retailPrice","划线价",0,PageCodeEnum.productPage),
    productCost("productCost","商品成本",0,PageCodeEnum.productPage),
    logisticsCost("logisticsCost","物流成本",0,PageCodeEnum.productPage),
    storageCost("storageCost","仓储成本",0,PageCodeEnum.productPage),
    taxRate("taxRate","综合税率",0,PageCodeEnum.productPage),
    rejectRate("rejectRate","退货率",0,PageCodeEnum.productPage),
    adCost("adCost","广告成本",0,PageCodeEnum.productPage),
    commission("commission","交易佣金",0,PageCodeEnum.productPage),
    withdraw("withdraw","提现手续费",0,PageCodeEnum.productPage),
    grossMargin("grossMargin","毛利率",0,PageCodeEnum.productPage),
    marketing("marketing","营销费用",0,PageCodeEnum.productPage),
    freightRate("freightRate","物流支出",0,PageCodeEnum.productPage),
    discountRate("discountRate","折扣率",0,PageCodeEnum.productPage),
    country("country","选款国家站点",1,PageCodeEnum.productPage),
    categoryName("categoryName","品类",1,PageCodeEnum.productPage),
    stockQuantity("stockQuantity","实物可用库存",1,PageCodeEnum.productPage),
    creatorName("creatorName","创建人/时间",1,PageCodeEnum.productPage),
    submitPlatformNameList("submitPlatformNameList","已提交平台",1,PageCodeEnum.productPage),
    reviserName("reviserName","更新人",1,PageCodeEnum.productPage),
    revisedTime("revisedTime","更新时间",1,PageCodeEnum.productPage),
    planAuditState("planAuditState","企划审核",0,PageCodeEnum.productPage),
    planAuditorName("planAuditorName","审核人/时间",0,PageCodeEnum.productPage),

    //商品企划审核列表
    mainImgUrl_productPlanAuditPage("mainImgUrl", "主图",1,PageCodeEnum.productPlanAuditPage),
    productType_productPlanAuditPage("productType","商品分类",1,PageCodeEnum.productPlanAuditPage),
    supplyMode_productPlanAuditPage("supplyMode", "供给方式",1,PageCodeEnum.productPlanAuditPage),
    goodsType_productPlanAuditPage("goodsType","商品类型",1,PageCodeEnum.productPlanAuditPage),
    waves_productPlanAuditPage("waves", "波次",1,PageCodeEnum.productPlanAuditPage),
    spuCode_productPlanAuditPage("spuCode", "SPU",1,PageCodeEnum.productPlanAuditPage),
    tagCodes_productPlanAuditPage("tagCodes", "标签",1,PageCodeEnum.productPlanAuditPage),
    imagePackageState_productPlanAuditPage("imagePackageState", "图包状态",1,PageCodeEnum.productPlanAuditPage),
    marketName_productPlanAuditPage("marketName","市场",1,PageCodeEnum.productPlanAuditPage),
    shopList_productPlanAuditPage("shopList","选款店铺",1,PageCodeEnum.productPlanAuditPage),
    salePrice_productPlanAuditPage("salePrice","售价",0,PageCodeEnum.productPlanAuditPage),
    retailPrice_productPlanAuditPage("retailPrice","划线价",0,PageCodeEnum.productPlanAuditPage),
    productCost_productPlanAuditPage("productCost","商品成本",0,PageCodeEnum.productPlanAuditPage),
    logisticsCost_productPlanAuditPage("logisticsCost","物流成本",0,PageCodeEnum.productPlanAuditPage),
    storageCost_productPlanAuditPage("storageCost","仓储成本",0,PageCodeEnum.productPlanAuditPage),
    taxRate_productPlanAuditPage("taxRate","综合税率",0,PageCodeEnum.productPlanAuditPage),
    rejectRate_productPlanAuditPage("rejectRate","退货率",0,PageCodeEnum.productPlanAuditPage),
    adCost_productPlanAuditPage("adCost","广告成本",0,PageCodeEnum.productPlanAuditPage),
    commission_productPlanAuditPage("commission","交易佣金",0,PageCodeEnum.productPlanAuditPage),
    withdraw_productPlanAuditPage("withdraw","提现手续费",0,PageCodeEnum.productPlanAuditPage),
    grossMargin_productPlanAuditPage("grossMargin","毛利率",0,PageCodeEnum.productPlanAuditPage),
    marketing_productPlanAuditPage("marketing","营销费用",0,PageCodeEnum.productPlanAuditPage),
    freightRate_productPlanAuditPage("freightRate","物流支出",0,PageCodeEnum.productPlanAuditPage),
    discountRate_productPlanAuditPage("discountRate","折扣率",0,PageCodeEnum.productPlanAuditPage),
    shopGrossMargin("shopGrossMargin","毛利率记录",0,PageCodeEnum.productPlanAuditPage),
    country_productPlanAuditPage("country","选款国家站点",1,PageCodeEnum.productPlanAuditPage),
    categoryName_productPlanAuditPage("categoryName","品类",1,PageCodeEnum.productPlanAuditPage),
    stockQuantity_productPlanAuditPage("stockQuantity","实物可用库存",1,PageCodeEnum.productPlanAuditPage),
    creatorName_productPlanAuditPage("creatorName","创建人/时间",1,PageCodeEnum.productPlanAuditPage),
    submitPlatformNameList_productPlanAuditPage("submitPlatformNameList","已上架平台",1,PageCodeEnum.productPlanAuditPage),
    submitShopList_productPlanAuditPage("submitShopList","已上架店铺",1,PageCodeEnum.productPlanAuditPage),
    reviserName_productPlanAuditPage("reviserName","更新人",1,PageCodeEnum.productPlanAuditPage),
    revisedTime_productPlanAuditPage("revisedTime","更新时间",1,PageCodeEnum.productPlanAuditPage),
    planAuditState_productPlanAuditPage("planAuditState","企划审核",0,PageCodeEnum.productPlanAuditPage),
    planAuditorName_productPlanAuditPage("planAuditorName","审核人/时间",0,PageCodeEnum.productPlanAuditPage),
    sourceType_productPlanAuditPage("sourceType","来源",1,PageCodeEnum.productPlanAuditPage),
    ;

    companion object {

        /**
         * 匹配code
         */
        fun getByCode(code: String?): PageColumnEnum? {
            if (code == null) {
                return null
            }
            return PageColumnEnum.entries.find { it.columnCode == code }
        }
        /**
         * 匹配code
         */
        fun getByPackageCodeAndColumnCode(pageCode:String?,code: String?): PageColumnEnum? {
            if (code == null || pageCode==null) {
                return null
            }
            return PageColumnEnum.entries.firstOrNull() { it.columnCode == code && it.page.pageCode == pageCode }
        }
        /**
         * 匹配code
         */
        fun listByPageCode(pageCode: String?): List<PageColumnEnum>? {
            if (pageCode == null) {
                return null
            }
            return PageColumnEnum.entries.filter { it.page.pageCode == pageCode }.toList()
        }
    }
}