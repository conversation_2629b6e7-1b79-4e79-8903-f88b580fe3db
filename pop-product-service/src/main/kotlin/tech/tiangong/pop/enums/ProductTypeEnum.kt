package tech.tiangong.pop.enums

/**
 * 商品分类1AI-时装2AI-现货3AI-POD
 */
enum class ProductTypeEnum(
    /**
     * code
     */
    val code: Int,
    /**
     * desc
     */
    val desc: String
) {
    /**
     * AI-时装
     */
    AI_STYLE(1, "AI-时装"),
    /**
     * AI-现货
     */
    AI_TRY_ON(2, "AI-现货"),
    /**
     * AI-POD
     */
    AI_POD(3, "AI-POD"),
;

    companion object {
        fun getByCode(code: Int?): ProductTypeEnum? {
            if (code == null) {
                return null
            }
            return entries.find { it.code == code }
        }
        fun getByDesc(desc: String?): ProductTypeEnum? {
            if (desc == null) {
                return null
            }
            return entries.find { it.desc == desc }
        }
    }
}
