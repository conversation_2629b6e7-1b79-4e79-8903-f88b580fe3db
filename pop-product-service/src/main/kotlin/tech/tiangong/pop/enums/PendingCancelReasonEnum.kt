package tech.tiangong.pop.enums

/**
 * 待上架取消原因枚举
 */
enum class PendingCancelReasonEnum(
    val code: Int,
    val desc: String,
) {

    STYLE_PROBLEM(1, "款式问题"),
    STYLE_ERROR(2, "款式错误"),
    PRICE_TOO_HIGH(3, "价格过高"),
    PRICE_TOO_LOW(4, "价格过低"),
    OTHER(5, "其他"),
    ;

    companion object {
        fun getByCode(code: Int?): PendingCancelReasonEnum? {
            return PendingCancelReasonEnum.entries.firstOrNull { it.code == code }
        }
    }

}