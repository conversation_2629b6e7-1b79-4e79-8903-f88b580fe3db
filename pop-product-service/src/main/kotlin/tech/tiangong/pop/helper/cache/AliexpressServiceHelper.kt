package tech.tiangong.pop.helper.cache

import org.springframework.data.redis.core.RedisTemplate
import org.springframework.stereotype.Component
import team.aikero.blade.core.exception.BusinessException
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.util.json.parseJsonList
import team.aikero.blade.util.json.toJson
import tech.tiangong.pop.config.AliexpressProperties
import tech.tiangong.pop.constant.RedisCacheConstants.AE_CATEGORY_ATTRIBUTES_CACHE_KEY_PREFIX
import tech.tiangong.pop.constant.RedisCacheConstants.AE_FREIGHT_TEMPLATE_CACHE_KEY_PREFIX
import tech.tiangong.pop.constant.RedisCacheConstants.AE_MANUFACTURE_LIST_CACHE_KEY_PREFIX
import tech.tiangong.pop.constant.RedisCacheConstants.AE_MSR_LIST_CACHE_KEY_PREFIX
import tech.tiangong.pop.constant.RedisCacheConstants.AE_PRODUCT_GROUPS_CACHE_KEY_PREFIX
import tech.tiangong.pop.constant.RedisCacheConstants.AE_PROMISE_TEMPLATE_CACHE_KEY_PREFIX
import tech.tiangong.pop.constant.RedisCacheConstants.AE_SELLER_RELATION_CACHE_KEY_PREFIX
import tech.tiangong.pop.dao.repository.ShopRepository
import tech.tiangong.pop.resp.product.ae.*
import tech.tiangong.pop.resp.sdk.aliexpress.AliexpressCategoryAttributeResponse
import tech.tiangong.pop.resp.sdk.aliexpress.AliexpressSellerRelationResponse
import tech.tiangong.pop.service.AliexpressService
import java.util.concurrent.TimeUnit

@Slf4j
@Component
class AliexpressServiceHelper(
    private val aliexpressService: AliexpressService,
    private val redisTemplate: RedisTemplate<String, Any>,
    private val shopRepository: ShopRepository,
    private val aliexpressProperties: AliexpressProperties,
    ) {
    companion object {
        const val CACHE_EXPIRATION_MINUTES = 5L
        const val ATTRIBUTE_CACHE_EXPIRATION_MINUTES = 30L
    }

    /**
     * 查询类目属性【有缓存】
     *
     * @param shopId 店铺 ID （可选，如果为 null 则使用配置中的 tokenShopId）
     * @param categoryId 类目ID（必需）
     * @param param2 可选参数
     * @return AliexpressCategoryAttributeResponse 类目属性响应
     */
    fun getCachedCategoryAttributes(
        shopId: Long?,
        categoryId: Long,
        param2: String? = null,
    ): AliexpressCategoryAttributeResponse {
        val tokenShopId = shopId ?: aliexpressProperties.aePlatform.tokenShopId
        // 构建缓存键，将param2也加入缓存键中
        val cacheKey = buildAttributesCacheKey(tokenShopId, categoryId, param2)

        val cachedResult = redisTemplate.opsForValue().get(cacheKey) as? AliexpressCategoryAttributeResponse

        if (cachedResult != null) {
            log.info { "从缓存中获取类目属性，类目ID: $categoryId, param2: ${param2 ?: "无"}" }
            return cachedResult
        }

        val shop = shopRepository.getById(tokenShopId) ?: throw IllegalArgumentException("店铺不存在")
        val accessToken = shop.token ?: throw IllegalArgumentException("店铺token不存在")

        log.info { "缓存中不存在类目属性，开始查询类目属性，类目ID: $categoryId, param2: ${param2 ?: "无"}" }
        val result = aliexpressService.queryCategoryAttributes(accessToken, categoryId, param2)

        if (result.isSuccess()) {
            redisTemplate.opsForValue().set(cacheKey, result, ATTRIBUTE_CACHE_EXPIRATION_MINUTES, TimeUnit.MINUTES)
            log.info { "将查询结果缓存到Redis，缓存键: $cacheKey, timeout: $ATTRIBUTE_CACHE_EXPIRATION_MINUTES MINUTES" }
        }

        return result
    }

    /**
     * 查询运费模板列表【有缓存】
     *
     * @param shopId 店铺ID
     * @param channelSellerId 渠道卖家ID（可选）
     * @return List<ProductAeFreightTemplateResp> 运费模板列表
     */
    fun getCachedFreightTemplateList(
        shopId: Long,
        channelSellerId: String?
    ): List<ProductAeFreightTemplateResp> {
        val cacheKey = AE_FREIGHT_TEMPLATE_CACHE_KEY_PREFIX + shopId

        // 使用字符串存储，避免类型转换问题
        val cachedJson = redisTemplate.opsForValue().get(cacheKey) as? String

        // 如果缓存存在，将JSON反序列化为对象列表
        if (!cachedJson.isNullOrBlank()) {
            log.info { "从缓存中获取运费模板列表，店铺ID: $shopId" }
            return cachedJson.parseJsonList(ProductAeFreightTemplateResp::class.java)
        }

        log.info { "缓存中不存在运费模板列表，开始查询，店铺ID: $shopId" }
        val shop = shopRepository.getById(shopId) ?: throw IllegalArgumentException("店铺不存在")
        val accessToken = shop.token ?: throw IllegalArgumentException("店铺token不存在")

        val response = aliexpressService.queryFreightTemplateList(accessToken)
        if (!response.isSuccess()) {
            throw BusinessException("查询运费模板失败: ${response.getJoinErrorMsg()}")
        }

        // 使用Kotlin的映射函数和简洁语法
        val result = response.freightTemplateList?.map { template ->
            ProductAeFreightTemplateResp(
                templateId = template.templateId,
                templateName = template.templateName,
                isDefault = template.isDefault
            )
        } ?: emptyList()

        // 将对象列表序列化为JSON字符串存入Redis
        redisTemplate.opsForValue().set(cacheKey, result.toJson(), CACHE_EXPIRATION_MINUTES, TimeUnit.MINUTES)
        log.info { "将运费模板列表缓存到Redis，缓存键: $cacheKey, timeout: $CACHE_EXPIRATION_MINUTES 分钟" }

        return result
    }

    /**
     * 查询承诺模板列表【有缓存】
     *
     * @param shopId 店铺ID
     * @param templateId 模板ID
     * @return List<ProductAePromiseTemplateResp> 承诺模板列表
     */
    fun getCachedPromiseTemplates(
        shopId: Long,
        templateId: Long
    ): List<ProductAePromiseTemplateResp> {
        val cacheKey = "${AE_PROMISE_TEMPLATE_CACHE_KEY_PREFIX}${shopId}:${templateId}"

        // 使用字符串存储，避免类型转换问题
        val cachedJson = redisTemplate.opsForValue().get(cacheKey) as? String

        // 如果缓存存在，将JSON反序列化为对象列表
        if (!cachedJson.isNullOrBlank()) {
            log.info { "从缓存中获取承诺模板列表，店铺ID: $shopId, 模板ID: $templateId" }
            return cachedJson.parseJsonList(ProductAePromiseTemplateResp::class.java)
        }

        log.info { "缓存中不存在承诺模板列表，开始查询，店铺ID: $shopId, 模板ID: $templateId" }
        val shop = shopRepository.getById(shopId) ?: throw IllegalArgumentException("店铺不存在")
        val accessToken = shop.token ?: throw IllegalArgumentException("店铺token不存在")

        val response = aliexpressService.queryPromiseTemplates(accessToken, templateId)
        if (!response.isSuccess()) {
            throw BusinessException("查询承诺模板失败: ${response.getJoinErrorMsg()}")
        }

        // 使用Kotlin的映射函数和简洁语法
        val result = response.result?.templateList?.map { template ->
            ProductAePromiseTemplateResp(
                id = template.id,
                name = template.name
            )
        } ?: emptyList()

        // 将对象列表序列化为JSON字符串存入Redis
        redisTemplate.opsForValue().set(cacheKey, result.toJson(), CACHE_EXPIRATION_MINUTES, TimeUnit.MINUTES)
        log.info { "将承诺模板列表缓存到Redis，缓存键: $cacheKey, timeout: $CACHE_EXPIRATION_MINUTES 分钟" }

        return result
    }

    /**
     * 查询MSR列表【有缓存】
     *
     * @param shopId 店铺ID
     * @param channelSellerId 渠道卖家ID（可选）
     * @param channel 渠道（可选）
     * @return List<ProductAeMsrItemResp> MSR列表
     */
    fun getCachedMsrList(
        shopId: Long,
        channelSellerId: Long?,
        channel: String?
    ): List<ProductAeMsrItemResp> {
        val cacheKey = "${AE_MSR_LIST_CACHE_KEY_PREFIX}${shopId}:${channelSellerId ?: "none"}:${channel ?: "none"}"

        // 使用字符串存储，避免类型转换问题
        val cachedJson = redisTemplate.opsForValue().get(cacheKey) as? String

        // 如果缓存存在，将JSON反序列化为对象列表
        if (!cachedJson.isNullOrBlank()) {
            log.info { "从缓存中获取MSR列表，店铺ID: $shopId" }
            return cachedJson.parseJsonList(ProductAeMsrItemResp::class.java)
        }

        log.info { "缓存中不存在MSR列表，开始查询，店铺ID: $shopId" }
        val shop = shopRepository.getById(shopId) ?: throw IllegalArgumentException("店铺不存在")
        val accessToken = shop.token ?: throw IllegalArgumentException("店铺token不存在")

        val response = aliexpressService.queryMsrList(accessToken, channelSellerId, channel)
        if (!response.isSuccess()) {
            throw BusinessException("查询MSR列表失败: ${response.getJoinErrorMsg()}")
        }

        // 使用Kotlin的映射函数和简洁语法
        val result = response.result?.data?.map { msr ->
            ProductAeMsrItemResp(
                id = msr.id,
                name = msr.name
            )
        } ?: emptyList()

        // 将对象列表序列化为JSON字符串存入Redis
        redisTemplate.opsForValue().set(cacheKey, result.toJson(), CACHE_EXPIRATION_MINUTES, TimeUnit.MINUTES)
        log.info { "将MSR列表缓存到Redis，缓存键: $cacheKey, timeout: $CACHE_EXPIRATION_MINUTES 分钟" }

        return result
    }

    /**
     * 查询制造商列表【有缓存】
     *
     * @param shopId 店铺ID
     * @param channelSellerId 渠道卖家ID（可选）
     * @param channel 渠道（可选）
     * @return List<ProductAeManufactureItemResp> 制造商列表
     */
    fun getCachedManufactureList(
        shopId: Long,
        channelSellerId: Long?,
        channel: String?
    ): List<ProductAeManufactureItemResp> {
        val cacheKey = "${AE_MANUFACTURE_LIST_CACHE_KEY_PREFIX}${shopId}:${channelSellerId ?: "none"}:${channel ?: "none"}"

        // 使用字符串存储，避免类型转换问题
        val cachedJson = redisTemplate.opsForValue().get(cacheKey) as? String

        // 如果缓存存在，将JSON反序列化为对象列表
        if (!cachedJson.isNullOrBlank()) {
            log.info { "从缓存中获取制造商列表，店铺ID: $shopId" }
            return cachedJson.parseJsonList(ProductAeManufactureItemResp::class.java)
        }

        log.info { "缓存中不存在制造商列表，开始查询，店铺ID: $shopId" }
        val shop = shopRepository.getById(shopId) ?: throw IllegalArgumentException("店铺不存在")
        val accessToken = shop.token ?: throw IllegalArgumentException("店铺token不存在")

        val response = aliexpressService.queryManufactureList(accessToken, channelSellerId, channel)
        if (!response.isSuccess()) {
            throw BusinessException("查询制造商列表失败: ${response.getJoinErrorMsg()}")
        }

        // 使用Kotlin的映射函数和简洁语法
        val result = response.result?.data?.map { manufacture ->
            ProductAeManufactureItemResp(
                id = manufacture.id,
                name = manufacture.name
            )
        } ?: emptyList()

        // 将对象列表序列化为JSON字符串存入Redis
        redisTemplate.opsForValue().set(cacheKey, result.toJson(), CACHE_EXPIRATION_MINUTES, TimeUnit.MINUTES)
        log.info { "将制造商列表缓存到Redis，缓存键: $cacheKey, timeout: $CACHE_EXPIRATION_MINUTES 分钟" }

        return result
    }

    /**
     * 查询产品组列表【有缓存】
     *
     * @param shopId 店铺ID
     * @return List<ProductAeProductGroupResp> 产品组列表
     */
    fun getCachedProductGroups(shopId: Long): List<ProductAeProductGroupResp> {
        val cacheKey = "${AE_PRODUCT_GROUPS_CACHE_KEY_PREFIX}${shopId}"

        // 使用字符串存储，避免类型转换问题
        val cachedJson = redisTemplate.opsForValue().get(cacheKey) as? String

        // 如果缓存存在，将JSON反序列化为对象列表
        if (!cachedJson.isNullOrBlank()) {
            log.info { "从缓存中获取产品组列表，店铺ID: $shopId" }
            return cachedJson.parseJsonList(ProductAeProductGroupResp::class.java)
        }

        log.info { "缓存中不存在产品组列表，开始查询，店铺ID: $shopId" }
        val shop = shopRepository.getById(shopId) ?: throw IllegalArgumentException("店铺不存在")
        val accessToken = shop.token ?: throw IllegalArgumentException("店铺token不存在")

        val response = aliexpressService.queryProductGroups(accessToken)
        if (!response.isSuccess()) {
            throw BusinessException("查询产品组列表失败: ${response.getJoinErrorMsg()}")
        }

        // 使用Kotlin的映射函数和简洁语法
        val result = response.result?.targetList?.map { group ->
            ProductAeProductGroupResp(
                groupId = group.groupId,
                groupName = group.groupName,
                childGroupList = group.childGroupList?.map { childGroup ->
                    ProductAeProductChildGroupResp(
                        groupId = childGroup.groupId,
                        groupName = childGroup.groupName
                    )
                }
            )
        } ?: emptyList()

        // 将对象列表序列化为JSON字符串存入Redis
        redisTemplate.opsForValue().set(cacheKey, result.toJson(), CACHE_EXPIRATION_MINUTES, TimeUnit.MINUTES)
        log.info { "将产品组列表缓存到Redis，缓存键: $cacheKey, timeout: $CACHE_EXPIRATION_MINUTES 分钟" }

        return result
    }

    /**
     * 失效所有类目属性缓存
     */
    fun invalidateAllCachedCategoryAttributes() {
        val pattern = "$AE_CATEGORY_ATTRIBUTES_CACHE_KEY_PREFIX*"
        val keys = redisTemplate.keys(pattern)
        if (!keys.isNullOrEmpty()) {
            redisTemplate.delete(keys)
            log.info { "已清除所有类目属性缓存，数量: ${keys.size}" }
        }
    }

    /**
     * 失效所有类目属性缓存
     */
    fun invalidateAllCachedCategoryAttributes(shopId: Long) {
        val pattern = "${AE_CATEGORY_ATTRIBUTES_CACHE_KEY_PREFIX}${shopId}*"
        val keys = redisTemplate.keys(pattern)
        if (!keys.isNullOrEmpty()) {
            redisTemplate.delete(keys)
            log.info { "已清除所有类目属性缓存，数量: ${keys.size}" }
        }
    }

    /**
     * 失效指定店铺的所有运费模板缓存
     */
    fun invalidateFreightTemplateCache(shopId: Long) {
        val cacheKey = "${AE_FREIGHT_TEMPLATE_CACHE_KEY_PREFIX}${shopId}"
        redisTemplate.delete(cacheKey)
        log.info { "已清除店铺[$shopId]的运费模板缓存" }
    }

    /**
     * 失效指定店铺的所有承诺模板缓存
     */
    fun invalidatePromiseTemplateCache(shopId: Long) {
        val pattern = "${AE_PROMISE_TEMPLATE_CACHE_KEY_PREFIX}${shopId}:*"
        val keys = redisTemplate.keys(pattern)
        if (!keys.isNullOrEmpty()) {
            redisTemplate.delete(keys)
            log.info { "已清除店铺[$shopId]的承诺模板缓存，数量: ${keys.size}" }
        }
    }

    /**
     * 失效指定店铺的所有MSR列表缓存
     */
    fun invalidateMsrListCache(shopId: Long) {
        val pattern = "${AE_MSR_LIST_CACHE_KEY_PREFIX}${shopId}:*"
        val keys = redisTemplate.keys(pattern)
        if (!keys.isNullOrEmpty()) {
            redisTemplate.delete(keys)
            log.info { "已清除店铺[$shopId]的MSR列表缓存，数量: ${keys.size}" }
        }
    }

    /**
     * 失效指定店铺的所有制造商列表缓存
     */
    fun invalidateManufactureListCache(shopId: Long) {
        val pattern = "${AE_MANUFACTURE_LIST_CACHE_KEY_PREFIX}${shopId}:*"
        val keys = redisTemplate.keys(pattern)
        if (!keys.isNullOrEmpty()) {
            redisTemplate.delete(keys)
            log.info { "已清除店铺[$shopId]的制造商列表缓存，数量: ${keys.size}" }
        }
    }

    /**
     * 失效指定店铺的产品组列表缓存
     */
    fun invalidateProductGroupsCache(shopId: Long) {
        val cacheKey = "${AE_PRODUCT_GROUPS_CACHE_KEY_PREFIX}${shopId}"
        redisTemplate.delete(cacheKey)
        log.info { "已清除店铺[$shopId]的产品组列表缓存" }
    }

    /**
     * 失效指定店铺的速卖通卖家关系缓存
     */
    fun invalidateSellerRelationCache(shopId: Long) {
        val cacheKey = "${AE_SELLER_RELATION_CACHE_KEY_PREFIX}${shopId}"
        redisTemplate.delete(cacheKey)
        log.info { "已清除速卖通卖家关系缓存, 店铺ID: $shopId" }
    }

    /**
     * 失效指定店铺的所有缓存
     */
    fun invalidateAllShopCache(shopId: Long) {
        invalidateFreightTemplateCache(shopId)
        invalidatePromiseTemplateCache(shopId)
        invalidateMsrListCache(shopId)
        invalidateManufactureListCache(shopId)
        invalidateProductGroupsCache(shopId)
        invalidateSellerRelationCache(shopId)
        invalidateAllCachedCategoryAttributes(shopId)
        log.info { "已清除店铺[$shopId]的所有缓存" }
    }

    /**
     * 失效所有缓存
     */
    fun invalidateAllCache() {
        val patterns = listOf(
            "$AE_CATEGORY_ATTRIBUTES_CACHE_KEY_PREFIX*",
            "$AE_FREIGHT_TEMPLATE_CACHE_KEY_PREFIX*",
            "$AE_PROMISE_TEMPLATE_CACHE_KEY_PREFIX*",
            "$AE_MSR_LIST_CACHE_KEY_PREFIX*",
            "$AE_MANUFACTURE_LIST_CACHE_KEY_PREFIX*",
            "$AE_PRODUCT_GROUPS_CACHE_KEY_PREFIX*",
            "$AE_SELLER_RELATION_CACHE_KEY_PREFIX*",
        )

        var totalDeleted = 0
        patterns.forEach { pattern ->
            val keys = redisTemplate.keys(pattern)
            if (!keys.isNullOrEmpty()) {
                redisTemplate.delete(keys)
                totalDeleted += keys.size
            }
        }

        log.info { "已清除所有AliExpress相关缓存，总数: $totalDeleted" }
    }

    /**
     * 查询卖家关系【有缓存】
     *
     * @return AliexpressSellerRelationResponse
     */
    fun getCachedSellerRelations(shopId: Long): AliexpressSellerRelationResponse {
        val cacheKey = "${AE_SELLER_RELATION_CACHE_KEY_PREFIX}${shopId}"

        val cached = redisTemplate.opsForValue().get(cacheKey) as? AliexpressSellerRelationResponse
        if (cached != null) {
            log.info { "从缓存中获取卖家关系，店铺ID: $shopId" }
            return cached
        }

        log.info { "缓存中无卖家关系，开始远程查询，店铺ID: $shopId" }
        val shop = shopRepository.getById(shopId) ?: throw IllegalArgumentException("店铺不存在")
        val accessToken = shop.token ?: throw IllegalArgumentException("店铺token不存在")
        val response = aliexpressService.querySellerRelations(accessToken)
        if (!response.isSuccess()) {
            throw BusinessException("查询速卖通卖家关系失败: ${response.getJoinErrorMsg()}")
        }
        redisTemplate.opsForValue().set(cacheKey, response, CACHE_EXPIRATION_MINUTES, TimeUnit.MINUTES)
        log.info { "将卖家关系缓存到Redis，key: $cacheKey, timeout: $CACHE_EXPIRATION_MINUTES 分钟" }
        return response
    }

    /**
     * 构建缓存键
     *
     * @param shopId 店铺ID
     * @param categoryId 类目ID
     * @param param2 可选参数
     * @return 包含所有非空参数的缓存键
     */
    private fun buildAttributesCacheKey(shopId: Long, categoryId: Long, param2: String?): String {
        return if (param2.isNullOrBlank()) {
            // 如果param2为空，只使用categoryId
            "$AE_CATEGORY_ATTRIBUTES_CACHE_KEY_PREFIX${shopId}:${categoryId}"
        } else {
            // 如果param2不为空，添加到缓存键中
            "$AE_CATEGORY_ATTRIBUTES_CACHE_KEY_PREFIX${shopId}:${categoryId}:${param2}"
        }
    }
}