package tech.tiangong.pop.resp.product.center

import tech.tiangong.pop.common.resp.CategoryAttributeResp
import tech.tiangong.pop.dao.entity.dto.ProductPackageInfoDto
import tech.tiangong.pop.dto.FileUploadDTO
import java.math.BigDecimal
import java.time.LocalDateTime

data class ProductCenterDetailResp(
    /**
     * 商品id
     */
    var productId: Long? = null,
    /**
     * 主图url链接
     */
    var mainImgUrl: String? = null,
    /**
     * spu编码
     */
    var spuCode: String? = null,
    /**
     * 供给方式
     */
    var supplyMode: String? = null,
    /**
     * 商品类型
     */
    var goodsType: String? = null,
    /**
     * 品类ID
     */
    var categoryId: Long? = null,
    /**
     * 品类编码（多级-分割）
     */
    var categoryCode: String? = null,
    /**
     * 品类名称（多级-分割）
     */
    var categoryName: String? = null,
    /**
     * 波次
     */
    var waves: String? = null,
    /**
     * 货盘类型
     */
    var goodsRepType: String? = null,
    /**
     * 款式风格名称
     */
    var clothingStyleName: String? = null,
    /**
     * 织造方式code
     */
    var weaveModeCode: String? = null,
    /**
     * 织造方式
     */
    var weaveMode: String? = null,
    /**
     * 市场编码
     */
    var marketCode: String? = null,
    /**
     * 市场名称
     */
    var marketName: String? = null,

    /**
     * 市场系列编码
     */
    var marketSeriesCode: String? = null,
    /**
     * 市场系列名称
     */
    var marketSeriesName: String? = null,

    /**
     * 站点(建议)
     */
    var countryList: List<String>? = listOf(),
    /**
     * 选款店铺(建议)
     */
    var shopList: List<ProductCenterDetailShopResp>? = listOf(),
    /**
     * 选款人姓名
     */
    var selectStyleName: String? = null,
    /**
     * 选款时间
     */
    var selectStyleTime: LocalDateTime? = null,
    /**
     * 企划来源
     */
    var planSourceName: String? = null,
    /**
     * 企划类型(1:企划内/2:企划外)
     */
    var planningType: Int? = null,
    /**
     * 品质等级
     */
    var qualityLevel: String? = null,

    /**
     * 品质等级编号
     */
    var qualityLevelCode: String? = null,
    /**
     * 商品主题编码
     */
    var productThemeCode: String? = null,

    /**
     * 商品主题名称
     */
    var productThemeName: String? = null,

    /**
     * 灵感图URL
     */
    var inspiraImgUrl: String? = null,

    /**
     * 灵感来源
     */
    var inspiraSource: String? = null,

    /**
     * 灵感图来源站点
     */
    var inspiraCountry: String? = null,
    /**
     * 版型号
     */
    var prototypeNum: String? = null,
    /**
     * 印花类型
     * @see tech.tiangong.pop.common.enums.PrintTypeEnum
     */
    var printType: String? = null,

    /**
     * 现货类型【1现货try on ；2ifashion】
     */
    var spotType: Int? = null,
    /**
     * 现货类型OPS编码
     */
    var spotTypeCode: String? = null,
    /**
     * 定价类型【1按返单规则；2不返单规则 】
     */
    var pricingType: Int? = null,
    /**
     * 创建人ID
     */
    var creatorId: Long? = null,
    /**
     * 创建人名称
     */
    var creatorName: String? = null,
    /**
     * 创建时间
     */
    var createdTime: LocalDateTime? = null,

    /**
     * 更新人ID
     */
    var reviserId: Long? = null,
    /**
     * 修改人名称
     */
    var reviserName: String? = null,
    /**
     * 更新时间
     */
    var revisedTime: LocalDateTime? = null,
    /**
     * 首次上架时间
     */
    var publishTime: LocalDateTime? = null,
    /**
     * 首次上架人ID
     */
    var publishUserId: Long? = null,
    /**
     * 首次上架人
     */
    var publishUserName: String? = null,

    /**
     * 最新上架时间
     */
    var latestPublishTime: LocalDateTime? = null,
    /**
     * 最新上架人ID
     */
    var latestPublishUserId: Long? = null,
    /**
     * 最新上架人
     */
    var latestPublishUserName: String? = null,

    /**
     * 商品上架状态【0待上架;1上架；2下架】
     */
    var publishState: Int? = null,

    /**
     * 上游款式是否已取消1是0否
     */
    var spuCanceled: Int? = null,

    /**
     * 商品链接
     */
    var productLink: String? = null,
    /**
     * 买手id
     */
    var buyerId: Long? = null,

    /**
     * 买手名称
     */
    var buyerName: String? = null,

    /**
     * 商品名（中文）
     */
    var spuName: String? = null,

    /**
     * 商品名翻译
     */
    var spuNameTrans: String? = null,

    /** 图包状态1未完成2已完成3更新中4已更新 */
    var imagePackageState: Int? = null,
    /**
     * 视频状态1未完成2已完成3更新中4已更新
     * 参考 ProductVideoStateEnum
     */
    var videoState: Int? = null,
    /**
     * 商品资料完善状态0待补全1已完善
     */
    var resourceState: Int? = null,



    /** 款式类型1设计款2现货款3数据印花款 */
    var styleType: Int? = null,
    /**
     * 企划审核状态0待审核1通过2不通过
     * 参考 ProductPlanAuditStateEnum
     */
    var planAuditState: Int? = null,

    /**
     * 商品来源1款式平台2手动导入
     * 参考 ProductSourceTypeEnum
     */
    var sourceType:Int? = null,

    /**
     * 商品类型1AI时装2AI现货3AI POD
     * 参考 ProductTypeEnum
     */
    var productType:Int? = null,
    /**
     * 物流包装信息
     * 参考 ProductPackageInfoDto
     */
    var packageInfo: ProductPackageInfoDto? = null,

    /**
     * 电子说明书 上传文件JSON数组
     * 参考 FileUploadDTO
     */
    var instructions: List<FileUploadDTO>? = null,

    /**
     * 场景名称(ops: JV_scene)
     */
    var sceneName: String? = null,

    /**
     * 场景编码
     */
    var sceneCode: String? = null,

    /**
     * 灵感图来源编码
     */
    var inspirationImageSourceCode: String? = null,

    /**
     * 灵感图来源
     */
    var inspirationImageSource: String? = null,

    /**
     * 灵感源品牌编码
     */
    var inspirationBrandCode: String? = null,

    /**
     * 灵感源品牌
     */
    var inspirationBrand: String? = null,
    /**
     * 企划来源name
     */
    var planningSourceName: String? = null,

    /**
     * 企划来源code
     */
    var planningSourceCode: String? = null,

    /**
     * 商品skc
     */
    var skcList: List<ProductCenterDetailSkcResp>? = listOf(),
    /**
     * 尺寸表
     */
    var sizeList: List<ProductSizeDetailResp>? = listOf(),
    /**
     * 商品属性值
     */
    var productAttributeValueList: List<ProductAttributesV2Vo>? = listOf(),
    /**
     * 商品图片及视频
     */
    var productImagesAndVideo: ProductImagePackageAndVideoResp? = null,
    /**
     * 品类属性选项(包含商品属性和平台属性)
     */
    var categoryAttributeList: List<CategoryAttributeResp>? = listOf(),
){}


data class ProductCenterDetailShopResp(
    /**
     * 选款店铺ID(建议)
     */
    var shopId: Long? = null,
    /**
     * 选款店铺名称(建议)
     */
    var shopName: String? = null,
)

data class ProductCenterDetailSkcResp(
    /**
     * 主键ID
     */
    var productSkcId: Long? = null,

    /**
     * 商品id
     */
    var productId: Long? = null,

    /**
     * SKC
     */
    var skc: String? = null,

    /**
     * 内部颜色
     */
    var color: String? = null,

    /**
     * 颜色编码（如White）
     */
    var colorCode: String? = null,

    /**
     * 颜色缩写编码（如 "WH" ）
     */
    var colorAbbrCode: String? = null,

    /**
     * 平台颜色
     */
    var platformColor: String? = null,

    /**
     * SKC状态【1可用；0取消】
     */
    var state: Int? = null,

    /**
     *  导入标识【1为导入】
     */
    var importFlag: Int? = null,

    /**
     * skc图片（多个用英文逗号分割）
     */
    var pictures: String? = null,

    /**
     * 跨境价
     */
    var cbPrice: BigDecimal? = null,

    /**
     * 本土价（供货价）
     */
    var localPrice: BigDecimal? = null,

    /**
     * 采购价
     */
    var purchasePrice: BigDecimal? = null,

    /**
     * 定价成本
     */
    var costPrice: BigDecimal? = null,

    /**
     * 核价类型：1.精准核价 2.预估核价
     */
    var checkPriceType: Int? = null,

    /**
     * 核价师
     */
    var pricerId: Long? = null,

    /**
     * 核价师
     */
    var pricerName: String? = null,
)