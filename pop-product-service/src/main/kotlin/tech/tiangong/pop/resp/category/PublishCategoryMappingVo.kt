package tech.tiangong.pop.resp.category

import java.io.Serializable
import java.time.LocalDateTime

/**
 * 关联平台品类
 *
 * <AUTHOR>
 * @date 2024/8/28
 */
class PublishCategoryMappingVo : Serializable {
    /**
     * 平台品类映射ID
     */
    var categoryMappingId: Long? = null

    /**
     * 品类id
     */
    var publishCategoryId: Long? = null

    /**
     * 关联平台ID
     */
    var platformId: Long? = null

    /**
     * 关联平台品类ID
     */
    var platformCategoryId: String? = null

    /**
     * 关联平台品类名称
     */
    var platformCategoryName: String? = null

    /**
     * 国家
     */
    var country: String? = null

    /**
     * 渠道名称
     */
    var channelName: String? = null

    /**
     * 平台名称
     */
    var platformName: String? = null

    /**
     * 渠道ID
     */
    var channelId: Long? = null

    /**
     * 备注
     */
    var remark: String? = null

    /**
     * 是否有映射第三方属性1是0否
     */
    var hasAttributeMapping: Int? = null
    /**
     * 修改人名称
     */
    var reviserName: String? = null

    /**
     * 更新时间
     */
    var revisedTime: LocalDateTime? = null
}
