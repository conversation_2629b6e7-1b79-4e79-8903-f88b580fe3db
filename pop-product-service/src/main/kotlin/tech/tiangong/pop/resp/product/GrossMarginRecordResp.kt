package tech.tiangong.pop.resp.product

import java.math.BigDecimal
import java.time.LocalDateTime

class GrossMarginRecordResp(
    /**
     * 主键
     */
    var recordId: Long? = null,

    /**
     * 商品ID
     */
    var productId: Long? = null,
    /**
     * 店铺ID
     */
    var shopId: Long? = null,
    /**
     * 毛利率
     */
    var grossMargin: BigDecimal? = null,
    /**
     * 调整人ID
     */
    var creatorId: Long? = null,
    /**
     * 创建人名称
     */
    var creatorName: String? = null,
    /**
     * 调整时间
     */
    var createdTime: LocalDateTime? = null,
) {
}