package tech.tiangong.pop.resp.product.manage

import tech.tiangong.pop.resp.product.center.ProductCenterPageShopResp
import java.math.BigDecimal
import java.time.LocalDateTime

data class ProductManagePageResp(
    /**
     * 商品id
     */
    var productId: Long? = null,
    /**
     * 主图url链接
     */
    var mainImgUrl: String? = null,
    /**
     * 商品分类 1AI-时装 2AI-现货 3AI-POD
     */
    var productType: Int? = null,
    /**
     * 供给方式
     */
    var supplyMode: String? = null,
    /**
     * 商品类型
     */
    var goodsType: String? = null,
    /**
     * 波次
     */
    var waves: String? = null,
    /**
     * spu编码
     */
    var spuCode: String? = null,
    /**
     * 标签 desc中文
     * @see tech.tiangong.pop.enums.ProductTagEnum
     */
    var tagCodes: List<String>? = listOf(),

    /** 图包状态1未完成2已完成3更新中4已更新 */
    var imagePackageState: Int? = null,
    /**
     * 市场编码
     */
    var marketCode: String? = null,
    /**
     * 市场名称
     */
    var marketName: String? = null,
    /**
     * 选款店铺(建议)
     */
    var shopList: List<ProductManagePageShopResp>? = listOf(),
    /**
     * 站点(建议)
     */
    var countryList: List<String>? = listOf(),
    /**
     * 品类ID
     */
    var categoryId: Long? = null,
    /**
     * 品类编码（多级-分割）
     */
    var categoryCode: String? = null,
    /**
     * 品类名称（多级-分割）
     */
    var categoryName: String? = null,
    /**
     * 已上架平台
     */
    var submitPlatformNameList: List<String>? = listOf(),
    /**
     * 已上架店铺
     */
    var submitShopList: List<ProductCenterPageShopResp>? = listOf(),
    /**
     * 创建人id
     */
    var creatorId: Long? = null,
    /**
     * 创建人
     */
    var creatorName: String? = null,
    /**
     * 创建时间
     */
    var createdTime: LocalDateTime? = null,
    /**
     * 最近修改人id;
     */
    var reviserId: Long? = null,
    /**
     * 最近修改人名称;
     */
    var reviserName: String? = null,
    /**
     * 更新时间
     */
    var revisedTime: LocalDateTime? = null,
    /**
     * 是否更新(1是, 0否)
     */
    var update: Int? = 0,
    /**
     * 价格是否异常(是1, 否0)
     */
    var priceException: Int? = 0,
    /**
     * 平台同步状态【0待同步；1已同步；2同步中;-1同步失败】
     */
    var platformUpdateState: Int? = null,
    /**
     * 提交状态: 待提交:0, 已提交:1
     */
    var submitStatus: Int? = null,

    /** 款式类型1设计款2现货款3数据印花款 */
    var styleType: Int? = null,


    /**
     * 企划审核状态0待审核1通过2不通过
     * 参考 ProductPlanAuditStateEnum
     */
    var planAuditState: Int? = null,
    /**
     * 企划审核人ID
     */
    var planAuditorId: Long? = null,
    /**
     * 企划审核人
     */
    var planAuditorName: String? = null,
    /**
     * 企划审核时间
     */
    var planAuditTime: LocalDateTime? = null,
    /**
     * 售价
     */
    var salePrice: SalePriceResp? = null,
    /**
     * 划线价
     */
    var retailPrice: RetailPriceResp? = null,
    /**
     * 商品成本
     */
    var productCost: BigDecimal? = null,
    /**
     * 物流成本（美元）
     */
    var logisticsCost: LogisticsCostResp? = null,
    /**
     * 仓储成本（美元）
     */
    var storageCost: StorageCostResp? = null,
    /**
     * 综合税率
     */
    var taxRate: BigDecimal? = null,
    /**
     * 退货率
     */
    var rejectRate: BigDecimal? = null,
    /**
     * 广告成本
     */
    var adCost: BigDecimal? = null,
    /**
     * 交易佣金
     */
    var commission:BigDecimal? = null,
    /**
     * 提现手续费
     */
    var withdraw:BigDecimal? = null,
    /**
     * 毛利率
     */
    var grossMargin:BigDecimal? = null,
    /**
     * 营销费用
     */
    var marketing:BigDecimal? = null,
    /**
     * 物流支出
     */
    var freightRate:FreightRateResp? = null,
    /**
     * 折扣率
     */
    var discountRate:BigDecimal? = null,
    /**
     * 毛利率记录
     */
    var shopGrossMargin:List<ShopGrossMarginConfigResp>? = null,
    /**
     * 来源1款式平台2手动导入
     */
    var sourceType: Int? = null,
){}

class ShopGrossMarginConfigResp(
    /**
     * 选款店铺毛利率
     */
    var grossMargin: BigDecimal?=null,
    /**
     * 店铺ID
     */
    var shopId:Long?=null,
    /**
     * 店铺名称
     */
    var shopName:String?=null,
) {

}

data class StorageCostResp(
    /**
     * 仓储成本最大值（美元）
     */
    var storageCostMax: BigDecimal? = null,
    /**
     * 仓储成本最小值（美元）
     */
    var storageCostMin: BigDecimal? = null,
){

}
data class LogisticsCostResp(

    /**
     * 物流成本最大值（美元）
     */
    var logisticsCostMax: BigDecimal? = null,
    /**
     * 物流成本最小值（美元）
     */
    var logisticsCostMin: BigDecimal? = null,
){

}
data class SalePriceResp(
    /**
     * 售价最大值
     */
    var salePriceMax: BigDecimal? = null,
    /**
     * 售价最小值
     */
    var salePriceMin: BigDecimal? = null,
    /**
     * 售价最大值（美元）
     */
    var salePriceUSDMax: BigDecimal? = null,
    /**
     * 售价最小值（美元）
     */
    var salePriceUSDMin: BigDecimal? = null,
){}
data class RetailPriceResp(
    /**
     * 划线价最大值
     */
    var retailPriceMax: BigDecimal? = null,
    /**
     * 划线价最小值
     */
    var retailPriceMin: BigDecimal? = null,
    /**
     * 划线价最大值（美元）
     */
    var retailPriceUSDMax: BigDecimal? = null,
    /**
     * 划线价最小值（美元）
     */
    var retailPriceUSDMin: BigDecimal? = null,
){}

data class FreightRateResp(
    /**
     * 物流支出最大值
     */
    var freightRateMax:BigDecimal? = null,
    /**
     * 物流支出最小值
     */
    var freightRateMin:BigDecimal? = null,
){

}
data class ProductManagePageShopResp(
    /**
     * 选款店铺ID(建议)
     */
    var shopId: Long? = null,
    /**
     * 选款店铺名称(建议)
     */
    var shopName: String? = null,
){}