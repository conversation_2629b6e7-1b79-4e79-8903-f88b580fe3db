package tech.tiangong.pop.component

import com.baomidou.mybatisplus.extension.kotlin.KtQueryWrapper
import org.springframework.stereotype.Component
import team.aikero.blade.core.enums.Bool
import team.aikero.blade.core.exception.BusinessException
import team.aikero.blade.core.toolkit.isNotBlank
import team.aikero.blade.core.toolkit.isNull
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.util.json.toJson
import tech.tiangong.pop.common.enums.PlatformEnum
import tech.tiangong.pop.component.ae.AeUpdateProductComponent
import tech.tiangong.pop.component.lazada.LazadaUpdateProductComponent
import tech.tiangong.pop.component.lazada.PublishToLazadaComponent
import tech.tiangong.pop.component.temu.TemuUpdateProductComponent
import tech.tiangong.pop.dao.entity.*
import tech.tiangong.pop.dao.repository.*
import tech.tiangong.pop.dto.mq.LazadaPriceMqDto
import tech.tiangong.pop.dto.product.AeProductUpdateOption
import tech.tiangong.pop.enums.PlatformProductPullTaskStatusEnum
import java.util.*

/**
 * 商品更新逻辑
 * <AUTHOR>
 * @date 2025-2-26 19:05:28
 */
@Slf4j
@Component
class ProductUpdateComponent(
    private val taskInfoRepository: TaskInfoRepository,
    private val productRepository: ProductRepository,
    private val shopRepository: ShopRepository,
    private val saleGoodsRepository: SaleGoodsRepository,
    private val saleSkuRepository: SaleSkuRepository,
    private val saleSkcRepository: SaleSkcRepository,
    private val aeSaleGoodsRepository: AeSaleGoodsRepository,
    private val aeSaleSkuRepository: AeSaleSkuRepository,
    private val aeSaleSkcRepository: AeSaleSkcRepository,
    private val productSkuSyncComponent: ProductSkuSyncComponent,
    private val publishToLazadaComponent: PublishToLazadaComponent,
    private val lazadaUpdateProductComponent: LazadaUpdateProductComponent,
    private val aeUpdateProductComponent: AeUpdateProductComponent,
    private val temuSaleGoodsRepository: TemuSaleGoodsRepository,
    private val temuUpdateProductComponent: TemuUpdateProductComponent,
) {
    fun updateTitle(task: ProductBatchUpdateTitleTask?) {
        if (task == null) {
            throw BusinessException("更新商品标题，参数为空")
        }
        if (task.spuCode.isNullOrBlank()
            || task.shopName.isNullOrBlank()
            || task.title.isNullOrBlank()
        ) {
            throw BusinessException("更新商品标题，参数为空 task=${task.toJson()}")
        }

        // 任务进行中
        taskInfoRepository.updateStatus(task.taskId!!, PlatformProductPullTaskStatusEnum.PROCESSING, null)

        val productList = productRepository.getListBySpuCode(task.spuCode!!)

        val platform = PlatformEnum.getByPlatformId(task.platformId!!) ?: throw BusinessException("更新商品标题，平台不存在 platformId=${task.platformId}")
        val shop = shopRepository.getByShopName(task.shopName!!, platform) ?: throw BusinessException("更新商品标题，店铺不存在 task=${task.toJson()}")
        when (platform) {
            PlatformEnum.LAZADA -> {
                productList.forEach { product ->
                    val saleGoodsList = saleGoodsRepository.list(
                        KtQueryWrapper(SaleGoods::class.java)
                            .eq(SaleGoods::productId, product.productId)
                            .eq(SaleGoods::country, task.country)
                            .eq(SaleGoods::shopId, shop.shopId)
                    )
                    if (saleGoodsList.isEmpty()) {
                        // 如果saleGoods不存在, 则只更改product
                        productSkuSyncComponent.setProductTitle(product, task.country!!, task.title!!)
                    } else {
                        saleGoodsList.forEach { saleGoods ->
                            // 比较本地数据是否一致, 不一致则需修改
                            var update = false
                            if (!Objects.equals(saleGoods.productTitle, task.title)) {
                                saleGoods.productTitle = task.title
                                update = true
                            }
                            if (!update) {
                                // 任务完成
                                log.info { "更新商品标题, 无需更新数据 task=${task.toJson()}" }
                                return@forEach
                            }

                            // 更新本地数据
                            saleGoodsRepository.updateById(saleGoods)
                            productSkuSyncComponent.setProductTitle(product, task.country!!, task.title!!)

                            // 发布标题更新到LZD
                            try {
                                if (saleGoods.platformProductId == null) {
                                    log.info { "商品未发布, 不需要调用lzd api task=${task.toJson()}" }
                                    return@forEach
                                }
                                // 休眠2秒再发布
                                Thread.sleep(2000)
                                lazadaUpdateProductComponent.updateProduct(shop.shopId!!, saleGoods)
                            } catch (e: Exception) {
                                log.error(e) { "更新商品标题，发布更新到LZD 失败 task=${task.toJson()} e=${e.message}" }
                                e.printStackTrace()
                                // 任务失败
                                taskInfoRepository.updateStatus(task.taskId!!, PlatformProductPullTaskStatusEnum.FAILED, e.message)
                            }
                        }
                    }
                }
            }

            PlatformEnum.AE -> {
                productList.forEach { product ->
                    val saleGoodsList = aeSaleGoodsRepository.list(
                        KtQueryWrapper(AeSaleGoods::class.java)
                            .eq(AeSaleGoods::productId, product.productId)
                            .eq(AeSaleGoods::shopId, shop.shopId)
                    )
                    if (saleGoodsList.isNotEmpty()) {
                        saleGoodsList.forEach { saleGoods ->
                            // 比较本地数据是否一致, 不一致则需修改
                            var update = false
                            if (!Objects.equals(saleGoods.productTitle, task.title)) {
                                saleGoods.productTitle = task.title
                                update = true
                            }
                            if (!update) {
                                // 任务完成
                                log.info { "更新商品标题, 无需更新数据 task=${task.toJson()}" }
                                return@forEach
                            }

                            // 更新本地数据
                            aeSaleGoodsRepository.updateById(saleGoods)

                            // 发布标题更新到AE
                            try {
                                if (saleGoods.platformProductId == null) {
                                    log.info { "商品未发布, 不需要调用AE api task=${task.toJson()}" }
                                    return@forEach
                                }
                                // 休眠2秒再发布
                                Thread.sleep(2000)
                                aeUpdateProductComponent.createOrUpdateProduct(
                                    shopId = saleGoods.shopId!!,
                                    saleGoods = saleGoods,
                                    option = AeProductUpdateOption(callSetGroupApi = false)
                                )
                            } catch (e: Exception) {
                                log.error(e) { "更新商品标题，发布更新到AE 失败 task=${task.toJson()} e=${e.message}" }
                                e.printStackTrace()
                                // 任务失败
                                taskInfoRepository.updateStatus(task.taskId!!, PlatformProductPullTaskStatusEnum.FAILED, e.message)
                            }
                        }
                    }
                }
            }

            else -> {

            }
        }


        // 任务完成
        taskInfoRepository.updateStatus(task.taskId!!, PlatformProductPullTaskStatusEnum.COMPLETED, null)
    }

    fun updatePriceInv(task: ProductBatchUpdatePriceInventoryTask?) {
        if (task == null) {
            log.error { "更新商品价格库存，参数为空" }
            throw BusinessException("更新商品价格库存，参数为空")
        }

        if (task.spuCode.isNullOrBlank()
            || task.shopName.isNullOrBlank()
            || task.skcCode.isNullOrBlank()
            || task.color.isNullOrBlank()
            || task.size.isNullOrBlank()
            || task.retailPrice.isNull()
            || task.salePrice.isNull()
            || task.stock.isNull()
        ) {
            throw BusinessException("更新商品价格库存，参数为空 task=${task.toJson()}")
        }
        // 任务进行中
        taskInfoRepository.updateStatus(task.taskId!!, PlatformProductPullTaskStatusEnum.PROCESSING, null)

        // 更新本地数据 只更新 划线价、销售价、库存
        val productList = productRepository.getListBySpuCode(task.spuCode!!)

        val platform = PlatformEnum.getByPlatformId(task.platformId!!) ?: throw BusinessException("更新商品价格库存，平台不存在 platformId=${task.platformId}")
        val shop = shopRepository.getByShopName(task.shopName!!, platform) ?: throw BusinessException("更新商品价格库存，店铺不存在 task=${task.toJson()}")
        when (platform) {
            PlatformEnum.LAZADA -> {
                productList.forEach { product ->
                    val saleGoodsList = saleGoodsRepository.list(
                        KtQueryWrapper(SaleGoods::class.java)
                            .eq(SaleGoods::productId, product.productId)
                            .eq(SaleGoods::country, task.country)
                            .eq(SaleGoods::shopId, shop.shopId)
                    )
                    if (saleGoodsList.isEmpty()) {
                        log.error { "更新商品价格库存，saleGoods不存在 task=${task.toJson()}" }
                        throw BusinessException("更新商品价格库存，saleGoods不存在")
                    }

                    saleGoodsList.forEach { saleGoods ->

                        val skc = saleSkcRepository.findBySaleGoodsIdAndSkc(saleGoods.saleGoodsId!!, task.skcCode!!)
                        if (skc == null) {
                            log.error { "更新商品价格库存，skc不存在 task=${task.toJson()}, productId=${product.productId}, skcCode=${task.skcCode}" }
                            throw BusinessException("更新商品价格库存，skc不存在 productId:${product.productId!!}, skc:${task.skcCode!!}")
                        }

                        val saleSkuList = saleSkuRepository.list(
                            KtQueryWrapper(SaleSku::class.java)
                                .eq(SaleSku::saleGoodsId, saleGoods.saleGoodsId)
                                .eq(SaleSku::sizeName, task.size)
                                .eq(SaleSku::saleSkcId, skc.saleSkcId)
                        )
                        if (saleSkuList.isEmpty()) {
                            log.info { "更新商品价格库存，saleSku不存在(可能未发布) task=${task.toJson()}" }
                            return@forEach
                        }

                        // 比较本地数据是否一致, 不一致则需修改
                        val updateSaleSkuList = mutableListOf<SaleSku>()
                        saleSkuList.forEach {
                            var update = false
                            if (!Objects.equals(it.retailPrice, task.retailPrice)) {
                                it.retailPrice = task.retailPrice
                                update = true
                            }
                            if (!Objects.equals(it.salePrice, task.salePrice)) {
                                it.salePrice = task.salePrice
                                update = true
                            }
                            if (!Objects.equals(it.stockQuantity, task.stock)) {
                                it.stockQuantity = task.stock!!.toLong()
                                update = true
                            }
                            if (update) {
                                updateSaleSkuList.add(it)
                            }
                        }
                        if (updateSaleSkuList.isEmpty()) {
                            // 任务完成
                            log.info { "更新商品价格库存, 无需更新数据 task=${task.toJson()}" }
                            return@forEach
                        }

                        // 更新本地数据
                        saleSkuRepository.updateBatchById(updateSaleSkuList)

                        // 发布价格库存更新到LZD
                        try {
                            if (saleGoods.platformProductId == null) {
                                log.info { "商品未发布, 不需要调用lzd api task=${task.toJson()}" }
                                return@forEach
                            }
                            // 休眠2秒再发布
                            Thread.sleep(2000)
                            val lazadaPriceMqDtoList = mutableListOf<LazadaPriceMqDto>()
                            updateSaleSkuList.forEach {
                                val dto = LazadaPriceMqDto().apply {
                                    this.platformId = saleGoods?.platformId
                                    this.shopId = shop.shopId
                                    this.shopName = it.shopName
                                    this.productId = it.productId
                                    this.saleProductId = it.saleGoodsId
                                    this.saleSkuId = it.saleSkuId
                                    this.lazadaCountry = it.country
                                    this.lazadaShopToken = shop.token
                                    this.lazadaItemId = it.platformProductId.toString()
                                    this.lazadaSkuId = it.platformSkuId
                                    this.lazadaSellerSku = it.sellerSku
                                    this.lazadaPrice = it.retailPrice.toString()
                                    this.lazadaSalePrice = it.salePrice.toString()
                                    this.lazadaStockQuantity = it.stockQuantity.toString()
                                }
                                lazadaPriceMqDtoList.add(dto)
                            }
                            publishToLazadaComponent.processLazadaPriceUpdate(lazadaPriceMqDtoList)
                        } catch (e: Exception) {
                            log.error(e) { "更新商品价格库存，发布更新到LZD 失败 task=${task.toJson()} e=${e.message}" }
                            throw e
                        }
                    }
                }
            }

            PlatformEnum.AE -> {
                productList.forEach { product ->
                    val saleGoodsList = aeSaleGoodsRepository.list(
                        KtQueryWrapper(AeSaleGoods::class.java)
                            .eq(AeSaleGoods::productId, product.productId)
                            .eq(AeSaleGoods::shopId, shop.shopId)
                    )
                    if (saleGoodsList.isEmpty()) {
                        log.error { "更新商品价格库存，saleGoods不存在 task=${task.toJson()}" }
                        throw BusinessException("更新商品价格库存，saleGoods不存在")
                    }

                    saleGoodsList.forEach { saleGoods ->

                        val skc = aeSaleSkcRepository.findBySaleGoodsIdAndSkc(saleGoods.saleGoodsId!!, task.skcCode!!)
                        if (skc == null) {
                            log.error { "更新商品价格库存，skc不存在 task=${task.toJson()}, productId=${product.productId}, skcCode=${task.skcCode}" }
                            throw BusinessException("更新商品价格库存，skc不存在 productId:${product.productId!!}, skc:${task.skcCode!!}")
                        }

                        val ktQueryWrapper = KtQueryWrapper(AeSaleSku::class.java)
                            .eq(AeSaleSku::saleGoodsId, saleGoods.saleGoodsId)
                            .eq(AeSaleSku::sizeName, task.size)
                            .eq(AeSaleSku::saleSkcId, skc.saleSkcId)
                            .eq(AeSaleSku::enableState, Bool.YES.code)
                        if (task.shipsFrom.isNotBlank()) {
                            // 有指定的发货地
                            ktQueryWrapper.eq(AeSaleSku::shipsFromAttributeValueName, task.shipsFrom)
                        } else {
                            // 空的发货地
                            ktQueryWrapper.isNull(AeSaleSku::shipsFromAttributeValueName)
                        }

                        val saleSkuList = aeSaleSkuRepository.list(ktQueryWrapper)
                        if (saleSkuList.isEmpty()) {
                            log.info { "更新商品价格库存，saleSku不存在(可能未发布) task=${task.toJson()}" }
                            return@forEach
                        }

                        // 比较本地数据是否一致, 不一致则需修改
                        val updateSaleSkuList = mutableListOf<AeSaleSku>()
                        saleSkuList.forEach {
                            var update = false
                            if (!Objects.equals(it.retailPrice, task.retailPrice)) {
                                it.retailPrice = task.retailPrice
                                update = true
                            }
                            if (!Objects.equals(it.salePrice, task.salePrice)) {
                                it.salePrice = task.salePrice
                                update = true
                            }
                            if (!Objects.equals(it.stockQuantity, task.stock)) {
                                it.stockQuantity = task.stock!!.toLong()
                                update = true
                            }
                            if (update) {
                                updateSaleSkuList.add(it)
                            }
                        }
                        if (updateSaleSkuList.isEmpty()) {
                            // 任务完成
                            log.info { "更新商品价格库存, 无需更新数据 task=${task.toJson()}" }
                            return@forEach
                        }

                        // 更新本地数据
                        aeSaleSkuRepository.updateBatchById(updateSaleSkuList)

                        // 发布价格库存更新到AE
                        try {
                            if (saleGoods.platformProductId == null) {
                                log.info { "商品未发布, 不需要调用AE api task=${task.toJson()}" }
                                return@forEach
                            }
                            // 休眠2秒再发布
                            Thread.sleep(2000)

                            // 批量更新价格
                            aeUpdateProductComponent.updatePrice(saleGoods, shop)
                            // 批量更新库存
                            aeUpdateProductComponent.updateStock(saleGoods, shop)

                        } catch (e: Exception) {
                            log.error(e) { "更新商品价格库存，发布更新到AE 失败 task=${task.toJson()} e=${e.message}" }
                            throw e
                        }
                    }
                }
            }

            else -> {

            }
        }


        // 任务完成
        taskInfoRepository.updateStatus(task.taskId!!, PlatformProductPullTaskStatusEnum.COMPLETED, null)
    }

    fun updateImage(task: ProductBatchUpdateImageTask?) {
        if (task == null) {
            throw BusinessException("更新商品图片，参数为空")
        }
        if (task.spuCode.isNullOrBlank()
            || task.shopId == null
            || task.productId == null
        ) {
            throw BusinessException("更新商品图片，参数不全 task=${task.toJson()}")
        }

        // 更新任务状态为处理中
        taskInfoRepository.updateStatus(task.taskId!!, PlatformProductPullTaskStatusEnum.PROCESSING, null)

        try {
            val shop = shopRepository.getById(task.shopId!!) ?: throw BusinessException("更新商品图片，店铺不存在 task=${task.toJson()}")
            val product = productRepository.getById(task.productId!!) ?: throw BusinessException("更新商品图片，商品不存在 task=${task.toJson()}")

            val platform = PlatformEnum.getByPlatformId(task.platformId!!)
            when (platform) {
                PlatformEnum.LAZADA -> {
                    // 调用图片发布逻辑（图片已在publishToLazada内部处理）
                    val saleGoodsList = saleGoodsRepository.list(
                        KtQueryWrapper(SaleGoods::class.java)
                            .eq(SaleGoods::productId, product.productId)
                            .eq(SaleGoods::shopId, shop.shopId)
                            .isNotNull(SaleGoods::platformProductId)
                    )
                    saleGoodsList.forEach { saleGoods ->
                        lazadaUpdateProductComponent.updateProduct(saleGoods.shopId!!, saleGoods)
                    }
                }

                PlatformEnum.AE -> {
                    // 调用图片发布逻辑（图片已在publishToLazada内部处理）
                    val saleGoodsList = aeSaleGoodsRepository.list(
                        KtQueryWrapper(AeSaleGoods::class.java)
                            .eq(AeSaleGoods::productId, product.productId)
                            .eq(AeSaleGoods::shopId, shop.shopId)
                            .isNotNull(AeSaleGoods::platformProductId)
                    )
                    saleGoodsList.forEach { saleGoods ->
                        aeUpdateProductComponent.createOrUpdateProduct(
                            shopId = saleGoods.shopId!!,
                            saleGoods = saleGoods,
                            option = AeProductUpdateOption(callSetGroupApi = false)
                        )
                    }
                }

                PlatformEnum.TEMU -> {
                    // 调用图片发布逻辑（图片已在publishToLazada内部处理）
                    val saleGoodsList = temuSaleGoodsRepository.list(
                        KtQueryWrapper(TemuSaleGoods::class.java)
                            .eq(TemuSaleGoods::productId, product.productId)
                            .eq(TemuSaleGoods::shopId, shop.shopId)
                            .isNotNull(TemuSaleGoods::platformProductId)
                    )
                    saleGoodsList.forEach { saleGoods ->
                        temuUpdateProductComponent.updateProductPicture(saleGoods.shopId!!, saleGoods)
                    }
                }


                else -> {

                }
            }


            // 任务完成
            taskInfoRepository.updateStatus(task.taskId!!, PlatformProductPullTaskStatusEnum.COMPLETED, null)

        } catch (e: Exception) {
            log.error(e) { "更新商品图片失败 task=${task.toJson()} e=${e.message}" }
            taskInfoRepository.updateStatus(task.taskId!!, PlatformProductPullTaskStatusEnum.FAILED, e.message)
            throw e
        }
    }
}
