package tech.tiangong.pop.component.importfile.pending

import org.apache.commons.collections4.CollectionUtils
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional
import team.aikero.blade.core.enums.Bool
import team.aikero.blade.core.exception.BusinessException
import team.aikero.blade.core.toolkit.isNotBlank
import team.aikero.blade.core.toolkit.isNotEmpty
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.sequence.id.IdHelper
import team.aikero.blade.util.json.toJson
import tech.tiangong.eis.temu.enums.TemuCountryEnum
import tech.tiangong.eis.temu.enums.TemuLanguageRelationEnum
import tech.tiangong.eis.temu.enums.TemuPlaceOfOrigionEnum
import tech.tiangong.eis.temu.enums.TemuSiteEnum
import tech.tiangong.pop.common.constant.ProductConstant
import tech.tiangong.pop.common.enums.CurrencyEnum
import tech.tiangong.pop.common.enums.PlatformEnum.TEMU
import tech.tiangong.pop.common.enums.ProductShopBusinessType
import tech.tiangong.pop.common.enums.YesOrNoEnum
import tech.tiangong.pop.common.req.BatchCreateBarCodeReq
import tech.tiangong.pop.component.GenerateSellerSkuComponent
import tech.tiangong.pop.component.ProductCommonComponent
import tech.tiangong.pop.config.LazadaDefaultProperties
import tech.tiangong.pop.constant.ProductInnerConstant
import tech.tiangong.pop.dao.entity.*
import tech.tiangong.pop.dao.repository.*
import tech.tiangong.pop.dto.product.ImportTemuProductDTO
import tech.tiangong.pop.dto.product.ProductInitDto.ProductInitSkcDto
import tech.tiangong.pop.enums.ProductPendingTaskStatusEnum
import tech.tiangong.pop.req.product.ProductTitleConfigMultiGenerateReq
import tech.tiangong.pop.req.product.temu.*
import tech.tiangong.pop.resp.product.ColorMapResp
import tech.tiangong.pop.service.product.BarCodeService
import tech.tiangong.pop.service.product.ProductTitleGenerateService
import kotlin.collections.get

/**
 * Temu待上架-导入商品数据
 */
@Component
@Slf4j
class TemuPendingProductImportComponent(
    private val productRepository: ProductRepository,
    private val productSkcRepository: ProductSkcRepository,
    private val productBarCodeRepository: ProductBarCodeRepository,
    private val productTemplateTemuSkcRepository: ProductTemplateTemuSkcRepository,
    private val productTemplateTemuSkuRepository: ProductTemplateTemuSkuRepository,
    private val productTemplateTemuSpuRepository: ProductTemplateTemuSpuRepository,
    private val barCodeService: BarCodeService,
    private val lazadaDefaultProperties: LazadaDefaultProperties,
    private val productTitleGenerateService: ProductTitleGenerateService,
    private val productCommonComponent: ProductCommonComponent,
    private val generateSellerSkuComponent: GenerateSellerSkuComponent,
    private val shopRepository: ShopRepository,
) {

    private val platform = TEMU

    /**
     * 商品导入-执行逻辑-TEMU
     * @param importDto
     * @param colorMap
     * @param imageMap
     * @param publishCategories
     */
    @Transactional(rollbackFor = [Exception::class])
    fun doImportTemuProduct(
        importDto: ImportTemuProductDTO,
        colorMap: List<ColorMapResp>,
        imageMap: Map<String, ImageRepository>,
        publishCategories: List<PublishCategory>,
    ) {

        val shop = shopRepository.ktQuery()
            .eq(Shop::shopName, importDto.listingShopName)
            .eq(Shop::platformId, platform.platformId)
            .list()
            .firstOrNull()
        if (shop == null) {
            throw BusinessException("Temu店铺:${importDto.listingShopName}不存在")
        }
        // 查询待上架商品SPU: SPU+店铺, 且任务状态 != 已完成
        val templateSpuList = productTemplateTemuSpuRepository.ktQuery()
            .eq(ProductTemplateTemuSpu::shopId, shop.shopId)
            .eq(ProductTemplateTemuSpu::spuCode, importDto.spuCode)
            .ne(ProductTemplateTemuSpu::taskStatus, ProductPendingTaskStatusEnum.COMPLETED.code)
            .list()
        if (templateSpuList.isEmpty()) {
            throw BusinessException("Temu店铺:${importDto.listingShopName} SPU:${importDto.spuCode} 商品不存在或已完成")
        }

        templateSpuList.forEach { templateSpu ->
            // 更新product
            upsertProductByImportTemu(templateSpu, importDto, imageMap)

            val product = productRepository.getById(templateSpu.productId!!)

            // 设置商品属性
            productCommonComponent.setAttributesByImport(importDto, product, platform.platformId)

            // 更新模板SPU, SKC, SKU
            upsertTemuTemplateSpuSkcSku(importDto, product, templateSpu)
        }
    }

    /**
     * 商品导入-新增更新商品-TEMU
     * @param templateSpu
     * @param importDto
     * @param imageMap
     * @return
     */
    private fun upsertProductByImportTemu(
        templateSpu: ProductTemplateTemuSpu,
        importDto: ImportTemuProductDTO,
        imageMap: Map<String, ImageRepository>,
    ) {
        val upsertProduct = productRepository.getById(templateSpu.productId!!)

        // 更新值
        val imageRepository = imageMap[upsertProduct.spuCode]
        if (imageRepository != null) {
            upsertProduct.mainImgUrl = imageRepository.mainUrl
        }
        upsertProduct.productTitle = importDto.productName
        upsertProduct.sizeGroupName = "字母码"
        upsertProduct.sizeGroupCode = ProductConstant.SOURCE_GROUP_CODE

        // 入库
        productRepository.updateById(upsertProduct)

        //写库后再同步款式类型和图包状态
        productCommonComponent.initStyleTypeAndImagePackageState(upsertProduct)
    }

    /**
     * 更新模板SPU, SKC, SKU
     * @param product
     */
    private fun upsertTemuTemplateSpuSkcSku(importDto: ImportTemuProductDTO, product: Product, templateSpu: ProductTemplateTemuSpu) {

        val pair = upsertTemuTemplateSpuSkc(product, templateSpu)
        val spuTemus = pair.first

        spuTemus
            ?.filter { it.spuCode == importDto.spuCode }
            ?.forEach { spuTemu ->

                val newOriginPlaceReq = ProductTemuCountryOriginPlaceReq()

                // 更新SPU产地
                // 根据逗号拆分
                val originPlaceNameList = importDto.originPlaceName?.split(",")
                if (originPlaceNameList.isNotEmpty()) {
                    // 提取一级产地
                    val firstOriginPlaceName = originPlaceNameList?.firstOrNull()
                    if (firstOriginPlaceName.isNotBlank()) {
                        val temuCountryEnum = TemuCountryEnum.entries.find { tce -> tce.chineseName == firstOriginPlaceName }
                        if (temuCountryEnum != null) {
                            newOriginPlaceReq.originCode = temuCountryEnum.shortName
                            newOriginPlaceReq.originName = temuCountryEnum.chineseName
                        }
                    }

                    if (originPlaceNameList!!.size > 1) {
                        // 提取二级产地
                        val secondOriginPlaceName = originPlaceNameList[1]
                        if (secondOriginPlaceName.isNotBlank()) {
                            val temuPlaceOfOrigionEnum = TemuPlaceOfOrigionEnum.entries.find { tce -> tce.province == secondOriginPlaceName }
                            if (temuPlaceOfOrigionEnum != null) {
                                newOriginPlaceReq.secondaryOriginCode = temuPlaceOfOrigionEnum.region2Id
                                newOriginPlaceReq.secondaryOriginName = temuPlaceOfOrigionEnum.province
                            }
                        }
                    }
                }
                if (newOriginPlaceReq.originCode.isNotBlank()) {
                    // 更新SPU的产地
                    spuTemu.countryOriginPlace = newOriginPlaceReq.toJson()
                    productTemplateTemuSpuRepository.updateById(spuTemu)
                }

                // 更新素材语言
                val materialLanguagesJson = importDto.materialLanguages?.split(",")
                if (materialLanguagesJson.isNotEmpty()) {
                    val mlList: List<MaterialLanguage>? = materialLanguagesJson?.mapNotNull { idm ->
                        val tlrEnum = TemuLanguageRelationEnum.entries.find { it.languageName == idm }
                        if (tlrEnum != null) {
                            MaterialLanguage().apply {
                                this.languageCode = tlrEnum.languageCode
                                this.languageName = tlrEnum.languageName
                            }
                        } else {
                            null
                        }
                    }
                    if (mlList.isNotEmpty()) {
                        // 更新素材语言
                        spuTemu.materialLanguages = mlList!!.toJson()
                        productTemplateTemuSpuRepository.updateById(spuTemu)
                    }
                }

                if (importDto.productName.isNotBlank() || importDto.productNameEn.isNotBlank()) {
                    spuTemu.productNameEn = importDto.productNameEn
                    spuTemu.productName = importDto.productName
                    productTemplateTemuSpuRepository.updateById(spuTemu)
                }

                var classifiedAttr: ClassifiedAttr? = null
                if (importDto.skuClassification.isNotBlank()
                    && importDto.quantity != null
                    && importDto.quantity!! > 0
                    && importDto.unit.isNotBlank()
                ) {
                    val skuClassificationEnum = ClassifiedAttrTypeCodeEnum.getByDesc(importDto.skuClassification)
                    val quantity = importDto.quantity
                    val unitEnum = ClassifiedAttrUnitEnum.getByDesc(importDto.unit)

                    classifiedAttr = ClassifiedAttr().apply {
                        this.typeCode = skuClassificationEnum!!.code
                        this.typeName = skuClassificationEnum.desc
                        this.quantity = quantity
                        this.unitCode = unitEnum!!.code
                        this.unit = unitEnum.desc
                        this.isPacking = null
                    }
                }

                val lastSkcTemuList = pair.second?.get(spuTemu.temuSpuId)
                // 按照import的尺码先upsert sku
                lastSkcTemuList
                    ?.filter { it.skc == importDto.skcCode }
                    ?.forEach { skc ->

                        // 判断sku是否存在
                        importDto.size!!.split(",").forEach { size ->
                            // 判断尺码是否有条码
                            val barCode =
                                productBarCodeRepository.getBySpuCodeAndSkcAndSize(spuTemu.spuCode, skc.skc, size)
                            var newBarcode: String? = null
                            if (barCode == null) {
                                // 添加条码
                                val barcodeReq = BatchCreateBarCodeReq().apply {
                                    this.categoryCode = product.categoryCode
                                    this.categoryName = product.categoryName
                                    this.skcCode = skc.skc
                                    this.color = skc.color
                                    this.spuCode = product.spuCode
                                    this.groupName = product.sizeGroupName
                                    this.sourceGroupCode = product.sizeGroupCode
                                    this.sizeValues = listOf(size)
                                    this.inspiraImgUrl = product.inspiraImgUrl
                                    this.supplyMode = product.supplyMode
                                    this.designImgUrl = skc.pictures
                                    this.mainImgUrl = skc.pictures
                                }
                                val barcodeResp = barCodeService.createBarcodeByForce(listOf(barcodeReq))
                                if (CollectionUtils.isNotEmpty(barcodeResp)) {
                                    newBarcode = barcodeResp[0].barcode
                                }
                            } else {
                                newBarcode = barCode.barcode
                            }


                            val skuTemus = productTemplateTemuSkuRepository.getByTemuSkcIdAndSizeNameList(
                                skc.temuSkcId!!,
                                size
                            ).filter { it.sizeName == size }
                            if (skuTemus.isNotEmpty()) {
                                skuTemus.forEach {
                                    // 更新
                                    if (newBarcode != null) {
                                        it.barcode = newBarcode
                                    }
                                    it.enableState = Bool.YES.code

                                    it.declaredPrice = listOf(
                                        FullyManagedPrice(CurrencyEnum.CNY.code, CurrencyEnum.CNY.currencyName, CurrencyEnum.CNY.symbol, importDto.declaredPriceCny),
                                        FullyManagedPrice(CurrencyEnum.USD.code, CurrencyEnum.USD.currencyName, CurrencyEnum.USD.symbol, importDto.declaredPriceUsd)
                                    ).toJson()

                                    it.recommendedPrice = listOf(
                                        FullyManagedPrice(CurrencyEnum.CNY.code, CurrencyEnum.CNY.currencyName, CurrencyEnum.CNY.symbol, importDto.retailPriceCny),
                                        FullyManagedPrice(CurrencyEnum.USD.code, CurrencyEnum.USD.currencyName, CurrencyEnum.USD.symbol, importDto.retailPriceUsd)
                                    ).toJson()

                                    it.classifiedAttrs = classifiedAttr?.toJson()
                                    it.longestEdge = importDto.maxLength
                                    it.secondEdge = importDto.middleLength
                                    it.shortestEdge = importDto.minLength
                                    it.weight = importDto.weight

                                    if (importDto.stockQuantity != null) {
                                        it.stockQuantity = importDto.stockQuantity
                                    }

                                    if (it.stockQuantity == null) {
                                        it.stockQuantity = lazadaDefaultProperties.defaultStock.toLong()
                                    }
                                }
                                productTemplateTemuSkuRepository.updateBatchById(skuTemus)
                            } else {
                                // 新增
                                val skuTemu = ProductTemplateTemuSku().apply {
                                    this.temuSkuId = IdHelper.getId()
                                    this.temuSkcId = skc.temuSkcId
                                    this.temuSpuId = spuTemu.temuSpuId
                                    if (newBarcode != null) {
                                        this.barcode = newBarcode
                                    }
                                    this.sizeName = size
                                    //半托给个默认站点
                                    if (spuTemu.businessType == ProductShopBusinessType.SEMI_MANAGED.value) {
                                        this.countryName = TemuSiteEnum.US_STATION.siteName
                                        this.country = TemuSiteEnum.US_STATION.siteId.toString()
                                    }

                                    this.declaredPrice = listOf(
                                        FullyManagedPrice(CurrencyEnum.CNY.code, CurrencyEnum.CNY.currencyName, CurrencyEnum.CNY.symbol, importDto.declaredPriceCny),
                                        FullyManagedPrice(CurrencyEnum.USD.code, CurrencyEnum.USD.currencyName, CurrencyEnum.USD.symbol, importDto.declaredPriceUsd)
                                    ).toJson()

                                    this.recommendedPrice = listOf(
                                        FullyManagedPrice(CurrencyEnum.CNY.code, CurrencyEnum.CNY.currencyName, CurrencyEnum.CNY.symbol, importDto.retailPriceCny),
                                        FullyManagedPrice(CurrencyEnum.USD.code, CurrencyEnum.USD.currencyName, CurrencyEnum.USD.symbol, importDto.retailPriceUsd)
                                    ).toJson()
                                    this.enableState = Bool.YES.code

                                    this.classifiedAttrs = classifiedAttr?.toJson()
                                    this.longestEdge = importDto.maxLength
                                    this.secondEdge = importDto.middleLength
                                    this.shortestEdge = importDto.minLength
                                    this.weight = importDto.weight
                                    this.stockQuantity = importDto.stockQuantity
                                }
                                if (skuTemu.stockQuantity == null) {
                                    skuTemu.stockQuantity = lazadaDefaultProperties.defaultStock.toLong()
                                }
                                productTemplateTemuSkuRepository.save(skuTemu)
                            }
                        }

                    }
            }
    }

    /**
     * 更新Temu模板SKC和SKU
     */
    private fun upsertTemuTemplateSpuSkc(product: Product, templateSpu: ProductTemplateTemuSpu, skcList: List<ProductInitSkcDto>? = null): Pair<List<ProductTemplateTemuSpu>?, Map<Long?, List<ProductTemplateTemuSkc>>?> {
        val multiTitleResp = try {
            productTitleGenerateService.generateMultiTitlesByConfig(ProductTitleConfigMultiGenerateReq().apply {
                this.productId = product.productId
                this.product = product
                this.shopId = product.shopId
                this.platform = TEMU
            })
        } catch (e: Exception) {
            log.warn(e) { "生成多标题失败，回退到单标题生成: productId=${product.productId}" }
            null
        }

        // 内联 title 赋值逻辑
        fun setGeneratedTitle(spu: ProductTemplateTemuSpu) {
            if (multiTitleResp?.titleData != null) {
                // 使用多标题数据
                spu.setParsedGeneratedTitles(multiTitleResp.titleData)

                // 使用第一个标题作为主标题
                val firstTitle = multiTitleResp.titleData.titles.firstOrNull()?.title
                if (!firstTitle.isNullOrBlank() && firstTitle.length < ProductInnerConstant.getTitleMaxLength(TEMU)) {
                    spu.productNameEn = firstTitle
                    spu.productName = firstTitle
                }

                // 设置多标题相关标记
                val firstTitleData = multiTitleResp.titleData.titles.firstOrNull()
                spu.generatedTitleOverLengthFlag = if (firstTitleData?.isOverLength == true) YesOrNoEnum.YES.code else YesOrNoEnum.NO.code
                spu.generatedTitleMissingFieldsJson = firstTitleData?.missingFields?.toJson()
            }
        }
        // 处理SPU
        val updateSpu = templateSpu.apply {
            productId = product.productId
            spuCode = product.spuCode
            productName = product.productTitle
            productNameEn = product.productTitle
            sizeGroupName = product.sizeGroupName
            sizeGroupCode = product.sizeGroupCode
        }
        setGeneratedTitle(templateSpu)
        productTemplateTemuSpuRepository.updateById(updateSpu)

        val spuTemus = listOf(templateSpu)
        val temuSpuIds = spuTemus.map { it.temuSpuId!! }
        val typeSpu = spuTemus.associateBy { it.temuSpuId!! }

        // 处理SKC
        // 取出模板skc
        val skcTemuList = productTemplateTemuSkcRepository.listByTemuInSpuId(temuSpuIds)

        //SPU下的skc
        val skcMapBySpuId: Map<Long?, List<ProductTemplateTemuSkc>> = skcTemuList.groupBy { it.temuSpuId }
        // 取出商品skc
        val productSkcList = productSkcRepository.getByProductId(product.productId!!)


        typeSpu.forEach { (t, u) ->
            val skcs = skcMapBySpuId[u.temuSpuId] ?: emptyList()
            val skcMap = skcs.associateBy { it.skc }
            // 以模板skc为基准, aeSpuId+color为key, 遍历productSkcList, 存在则更新, 不存在则新增
            productSkcList.forEach {
                val skc = skcMap[it.skc]
                if (skc != null) {
                    // 更新
                    skc.productSkcId = it.productSkcId
                    skc.skc = it.skc
                    skc.color = it.color
                    skc.platformColor = it.color
                    skc.colorCode = it.colorCode
                    skc.colorAbbrCode = it.colorAbbrCode
                    skc.pictures = it.pictures
                    skc.state = it.state
                    skc.cbPrice = it.cbPrice
                    skc.localPrice = it.localPrice
                    skc.purchasePrice = it.purchasePrice
                    skc.costPrice = it.costPrice
                    productTemplateTemuSkcRepository.updateById(skc)
                } else {
                    // 新增
                    val skc = ProductTemplateTemuSkc().apply {
                        this.temuSkcId = IdHelper.getId()
                        this.temuSpuId = t
                        this.productSkcId = it.productSkcId
                        this.skc = it.skc
                        this.color = it.color
                        this.platformColor = it.color
                        this.colorCode = it.colorCode
                        this.colorAbbrCode = it.colorAbbrCode
                        this.pictures = it.pictures
                        this.state = it.state
                        this.cbPrice = it.cbPrice
                        this.localPrice = it.localPrice
                        this.purchasePrice = it.purchasePrice
                        this.costPrice = it.costPrice
                    }
                    productTemplateTemuSkcRepository.save(skc)
                }
            }
        }

        // 再获取一次SKC, 上面有新增和更新
        val lastSkcTemuList = productTemplateTemuSkcRepository.listByTemuInSpuId(temuSpuIds)
        //用来返回 没有其他的含义
        val lastSkcTemuMap: Map<Long?, List<ProductTemplateTemuSkc>> = lastSkcTemuList.groupBy { it.temuSpuId }

        // 初始化尺码
        // skcList map key=skcCode
        val skcMapByCode: Map<String?, List<ProductInitSkcDto>>? = skcList?.groupBy { it.skcCode }
        lastSkcTemuList.forEach { skc ->
            val productTemplateTemuSpu = typeSpu[skc.temuSpuId!!]
            skcMapByCode?.get(skc.skc)?.forEach { skcDto ->
                skcDto.sizeList?.forEach { size ->
                    // 获取条码
                    val barCode = productBarCodeRepository.getBySpuCodeAndSkcAndSize(spuTemus.firstOrNull()?.spuCode, skc.skc, size)
                    // 判断sku是否存在
                    val skuTemus = productTemplateTemuSkuRepository.getByTemuSkcIdAndSizeNameList(
                        skc.temuSkcId!!,
                        size
                    )
                    if (skuTemus.isEmpty()) {
                        // 新增
                        val skuTemu = ProductTemplateTemuSku().apply {
                            this.temuSkuId = IdHelper.getId()
                            this.temuSkcId = skc.temuSkcId
                            this.temuSpuId = skc.temuSpuId
                            //                            this.sellerSku = importDto.skuCode
                            barCode?.let { this.barcode = it.barcode }
                            this.sizeName = size
                            this.enableState = Bool.YES.code
                            //                            if (price != null) {
                            //                                this.salePrice = price.salePrice
                            //                                this.retailPrice = price.retailPrice
                            //                            }
                            //半托给个默认站点
                            if (productTemplateTemuSpu?.businessType == ProductShopBusinessType.SEMI_MANAGED.value) {
                                this.countryName = TemuSiteEnum.US_STATION.siteName
                                this.country = TemuSiteEnum.US_STATION.siteId.toString()
                            }
                        }
                        val sellerSku = generateSellerSkuComponent.generateSellerSku(
                            product,
                            productTemplateTemuSpu!!,
                            skc,
                            skuTemu
                        )
                        skuTemu.sellerSku = sellerSku
                        productTemplateTemuSkuRepository.save(skuTemu)
                    } else {
                        //                        if (price != null) {
                        //                            skuAe.salePrice = price.salePrice
                        //                            skuAe.retailPrice = price.retailPrice
                        //                        }
                        productTemplateTemuSkuRepository.updateBatchById(skuTemus)
                    }
                }
            }
        }
        return Pair(spuTemus, lastSkcTemuMap)
    }

}