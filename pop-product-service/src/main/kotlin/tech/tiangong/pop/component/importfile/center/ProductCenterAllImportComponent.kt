package tech.tiangong.pop.component.importfile.center

import com.alibaba.excel.EasyExcel
import org.apache.commons.collections4.CollectionUtils
import org.springframework.stereotype.Component
import org.springframework.web.multipart.MultipartFile
import team.aikero.blade.core.toolkit.isNotBlank
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.util.json.toJson
import tech.tiangong.pop.common.exception.BaseBizException
import tech.tiangong.pop.dao.entity.ImportProductRecord
import tech.tiangong.pop.dao.repository.ImportProductRecordRepository
import tech.tiangong.pop.enums.ImportDataTypeEnum
import tech.tiangong.pop.enums.ImportSourceEnum
import tech.tiangong.pop.listeners.ImportProductExcelListener

/**
 * 商品中心-全部信息导入组件
 * 负责导入商品中心的全部信息数据
 */
@Component
@Slf4j
class ProductCenterAllImportComponent (
    private val importProductRecordRepository: ImportProductRecordRepository,
) {

    /**
     * 导入
     */
    fun importExcel(file: MultipartFile) {
        val listener = ImportProductExcelListener()
        EasyExcel.read(file.inputStream, listener).doReadAll()

        val importList = listener.getProductList()
            .asSequence()
            .filter { it.supplyMode.isNotBlank() }
            .filter { it.productTitle.isNotBlank() }
            .filter { it.spuCode.isNotBlank() }
            .filter { it.categoryName.isNotBlank() }
            .filter { it.skcCode.isNotBlank() }
            .toList()
        if (CollectionUtils.isEmpty(importList)) {
            throw BaseBizException("导入Lazada数据为空")
        }


        /*
        先读取excel
        1. 商品基本信息
        2. 物流包装
        3. 平台属性
        4. 商品属性

        按照不同的sheet insert到不同的importDataType导入任务(必须按照顺序保存, 因为要先执行1生成SPU, 再执行4生成skc)
        通过各自importDataType的导入逻辑各自处理
         */


        val list = importList.map {
            ImportProductRecord().apply {
                this.supplyMode = it.supplyMode
                this.spuCode = it.spuCode
                this.skc = it.skcCode
                this.importDataType = ImportDataTypeEnum.ALL.code
                this.importSource = ImportSourceEnum.PRODUCT_CENTER.code
                this.importData = it.toJson()
            }
        }
        importProductRecordRepository.saveBatch(list, 100)
    }

    /**
     * 执行导入逻辑
     */
    fun doImport(importData: String) {
        TODO("Not yet implemented")
    }
}
