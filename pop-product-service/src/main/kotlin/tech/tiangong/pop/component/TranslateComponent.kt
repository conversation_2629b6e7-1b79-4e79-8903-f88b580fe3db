package tech.tiangong.pop.component

import com.aliyun.alimt20181012.Client
import com.aliyun.alimt20181012.models.TranslateRequest
import com.aliyun.teaopenapi.models.Config
import com.aliyun.teautil.models.RuntimeOptions
import org.springframework.core.env.Environment
import org.springframework.stereotype.Component
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.util.json.toJson
import tech.tiangong.pop.common.exception.PublishAscBizException
import tech.tiangong.pop.utils.getRootMessage

/**
 * 翻译组件
 */
@Component
@Slf4j
class TranslateComponent(
    private val environment: Environment,
) {
    /**
     * 调用阿里云翻译服务，将英文标题翻译为对应国家语言。
     *
     * @param text 原始英文标题
     * @param country 目标国家编码（如 TH/VN/ID）
     * @return 翻译后的标题（若失败抛异常）
     */
    fun translateAndSetTitle(text: String?, country: String): String? {
        // 空文本直接返回空字符串，避免浪费调用资源
        if (text.isNullOrBlank()) {
            log.warn { "translateAndSetTitle: input text is empty, skip translation. country=$country" }
            return ""
        }

        // 目标语言选择逻辑
        val language = when (country.trim().uppercase()) {
            "TH" -> "th"
            "VN" -> "vi"
            "ID" -> "id"
            else -> "en" // 其它国家默认英文
        }

        // 初始化阿里云翻译配置
        val config = Config()
            .setAccessKeyId(environment.getProperty("aliyun.translate.accessKey"))
            .setAccessKeySecret(environment.getProperty("aliyun.translate.accessKeySecret"))
            .setEndpoint(environment.getProperty("aliyun.translate.endpointUrl"))

        val client = Client(config)
        val request = TranslateRequest()
            .setSourceLanguage("en")
            .setScene("title")
            .setTargetLanguage(language)
            .setSourceText(text)
            .setFormatType("text")

        // 设置运行时参数，启用重试
        val runtimeOptions = RuntimeOptions().apply {
            autoretry = true
            maxAttempts = 6
        }

        // 调用翻译接口
        try {
            val response = client.translateWithOptions(request, runtimeOptions)
            val body = response.body
            if (body.data == null || body.code == null || body.code != 200) {
                log.error { "Aliyun translate failed: request=${request.toJson()}, response=${response.toJson()}" }
                throw PublishAscBizException("翻译出错，请重试，响应：${response.toJson()}")
            }
            val translated = body.data.translated
            log.info { "Aliyun translate success: country=$country, targetLang=$language, input=$text, translated=${translated}" }
            return translated
        } catch (ex: Exception) {
            log.error(ex) { "Aliyun translate exception: country=$country, targetLang=$language, input=$text" }
            throw PublishAscBizException("翻译异常: ${ex.getRootMessage()}")
        }
    }

}