package tech.tiangong.pop.component.export.center

import com.alibaba.excel.EasyExcel
import com.baomidou.mybatisplus.extension.plugins.pagination.Page
import com.google.common.net.MediaType
import org.springframework.stereotype.Component
import team.aikero.blade.core.exception.BusinessException
import team.aikero.blade.core.toolkit.isNotBlank
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.util.json.parseJson
import team.aikero.blade.util.json.toJson
import tech.tiangong.pop.common.enums.PrintTypeEnum
import tech.tiangong.pop.component.MarketStyleComponent
import tech.tiangong.pop.component.export.DownloadTaskInterface
import tech.tiangong.pop.dao.entity.DownloadTask
import tech.tiangong.pop.dao.repository.ProductRepository
import tech.tiangong.pop.dto.FileUploadDTO
import tech.tiangong.pop.dto.export.ProductCenterBasicInfoExportDto
import tech.tiangong.pop.enums.DownloadTaskTypeEnum
import tech.tiangong.pop.enums.ProductTypeEnum
import tech.tiangong.pop.enums.SupplyModeEnum
import tech.tiangong.pop.helper.UploaderOssHelper
import tech.tiangong.pop.req.product.center.ProductCenterPageReq
import tech.tiangong.pop.service.settings.DownloadTaskService
import tech.tiangong.pop.utils.getRootMessage
import java.io.File
import java.io.IOException
import java.time.LocalDateTime
import java.time.ZoneId
import java.time.format.DateTimeFormatter
import java.util.*

/**
 * 商品中心-商品基本信息导出组件
 * 负责导出商品中心的商品基本信息数据
 */
@Component
@Slf4j
class ProductCenterBasicInfoExportComponent(
    private val productRepository: ProductRepository,
    private val downloadTaskService: DownloadTaskService,
    private val uploaderOssHelper: UploaderOssHelper,
    private val marketStyleComponent: MarketStyleComponent,
) : DownloadTaskInterface() {

    /**
     * 创建导出任务
     */
    fun createExportTask(req: ProductCenterPageReq) {
        try {
            val pageNum: Long = 1
            val pageSize: Long = 1000
            val page = productRepository.getCenterPage(Page(pageNum, pageSize), req)

            if (page.records.isEmpty()) {
                throw BusinessException("暂无数据")
            }
            // 创建下载任务
            val dateStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss", Locale.getDefault()).withZone(ZoneId.systemDefault()))
            val taskName = "导出商品中心_商品基本信息_$dateStr"
            downloadTaskService.createTask(
                taskName,
                DownloadTaskTypeEnum.PRODUCT_CENTER_BASIC_INFO_TASK,
                req.toJson()
            );
            log.info { "导出商品中心_商品基本信息 创建下载任务，任务名称：${taskName}" }
        } catch (e: Exception) {
            log.error(e) { "导出商品中心_商品基本信息-创建下载任务, 异常，请求参数：${req.toJson()}" }
            throw BusinessException("导出商品中心_商品基本信息_任务异常：" + e.getRootMessage());
        }
    }

    /**
     * 生成Excel文件
     */
    override fun export(reqStr: String?, tempDir: File, task: DownloadTask): FileUploadDTO {
        val req = reqStr?.parseJson<ProductCenterPageReq>() ?: throw BusinessException("导出任务逻辑-参数为空 task=${task.toJson()}")
        var pageNum: Long = 1
        val pageSize: Long = 1000

        val marketStyleMap = marketStyleComponent.getMarketNodeMap(1)

        val dataList = mutableListOf<ProductCenterBasicInfoExportDto>()

        while (true) {
            val page = productRepository.getCenterPage(Page(pageNum, pageSize), req)
            if (page.records.isEmpty()) {
                break
            }
            pageNum++

            page.records.forEach { product ->
                dataList.add(ProductCenterBasicInfoExportDto().apply {
                    this.spuCode = product.spuCode
                    this.productType = product.productType?.let { ProductTypeEnum.getByCode(it) }?.desc
                    this.supplyMode = product.supplyMode?.let { SupplyModeEnum.getByCode(it) }?.desc
                    this.goodsType = product.goodsType
                    this.categoryName = product.categoryName
                    this.brandName = product.brandName
                    this.waves = product.waves
                    this.goodsRepType = product.goodsRepType
                    this.clothingStyleName = product.clothingStyleName
                    this.weaveMode = product.weaveMode
                    this.market = marketStyleMap?.get(product.marketCode)
//                    this.seasonName = product.seasonName          // TODO 新字段
//                    this.targetAudience = product.targetAudience          // TODO 新字段
                    this.sceneName = product.sceneName
                    this.countryName = product.countrys
                    this.shopName = product.shopName
                    this.prototypeNum = product.prototypeNum
                    this.printType = product.printType?.let { PrintTypeEnum.fromCode(it) }?.desc
                    this.creatorName = product.creatorName
                    this.createdTime = product.createdTime
                    this.reviserName = product.reviserName
                    this.revisedTime = product.revisedTime
                    this.publishUserName = product.publishUserName
                    this.publishTime = product.publishTime
                    this.productTitle = product.spuName
                    this.productTitleEn = product.spuNameTrans
                    this.buyerName = product.buyerName
                    this.productLink = product.productLink


                    // 最后检查, 如果空则赋值"-"
                    this.productType = this.productType.takeIf { it.isNotBlank() } ?: "-"
                    this.supplyMode = this.supplyMode.takeIf { it.isNotBlank() } ?: "-"
                    this.goodsType = this.goodsType.takeIf { it.isNotBlank() } ?: "-"
                    this.categoryName = this.categoryName.takeIf { it.isNotBlank() } ?: "-"
                    this.brandName = this.brandName.takeIf { it.isNotBlank() } ?: "-"
                    this.waves = this.waves.takeIf { it.isNotBlank() } ?: "-"
                    this.goodsRepType = this.goodsRepType.takeIf { it.isNotBlank() } ?: "-"
                    this.clothingStyleName = this.clothingStyleName.takeIf { it.isNotBlank() } ?: "-"
                    this.weaveMode = this.weaveMode.takeIf { it.isNotBlank() } ?: "-"
                    this.market = this.market.takeIf { it.isNotBlank() } ?: "-"
                    this.seasonName = this.seasonName.takeIf { it.isNotBlank() } ?: "-"
                    this.targetAudience = this.targetAudience.takeIf { it.isNotBlank() } ?: "-"
                    this.sceneName = this.sceneName.takeIf { it.isNotBlank() } ?: "-"
                    this.countryName = this.countryName.takeIf { it.isNotBlank() } ?: "-"
                    this.shopName = this.shopName.takeIf { it.isNotBlank() } ?: "-"
                    this.prototypeNum = this.prototypeNum.takeIf { it.isNotBlank() } ?: "-"
                    this.printType = this.printType.takeIf { it.isNotBlank() } ?: "-"
                    this.creatorName = this.creatorName.takeIf { it.isNotBlank() } ?: "-"
                    this.reviserName = this.reviserName.takeIf { it.isNotBlank() } ?: "-"
                    this.publishUserName = this.publishUserName.takeIf { it.isNotBlank() } ?: "-"
                    this.productTitle = this.productTitle.takeIf { it.isNotBlank() } ?: "-"
                    this.productTitleEn = this.productTitleEn.takeIf { it.isNotBlank() } ?: "-"
                    this.buyerName = this.buyerName.takeIf { it.isNotBlank() } ?: "-"
                    this.productLink = this.productLink.takeIf { it.isNotBlank() } ?: "-"
                })
            }
        }

        try {
            // 创建Excel文件
            val excelFile = File(tempDir, task.taskName + ".xlsx")

            // 写入文件
            EasyExcel.write(excelFile)
                .head(ProductCenterBasicInfoExportDto::class.java)
                .sheet("Sheet1")
                .doWrite(dataList);

            return uploaderOssHelper.createFileUploadDTO(excelFile, MediaType.MICROSOFT_EXCEL.type())
        } catch (e: IOException) {
            log.error(e) { "导出失败:" }
            throw BusinessException("导出失败")
        }
    }
}
