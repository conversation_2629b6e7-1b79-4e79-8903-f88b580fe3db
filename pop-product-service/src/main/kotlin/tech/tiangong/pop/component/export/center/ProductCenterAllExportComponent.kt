package tech.tiangong.pop.component.export.center

import com.alibaba.excel.EasyExcel
import com.alibaba.excel.ExcelWriter
import com.alibaba.excel.write.metadata.WriteSheet
import com.baomidou.mybatisplus.extension.plugins.pagination.Page
import com.google.common.net.MediaType
import org.springframework.stereotype.Component
import team.aikero.blade.core.exception.BusinessException
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.util.json.parseJson
import team.aikero.blade.util.json.toJson
import tech.tiangong.pop.component.export.DownloadTaskInterface
import tech.tiangong.pop.dao.entity.DownloadTask
import tech.tiangong.pop.dao.entity.Product
import tech.tiangong.pop.dao.repository.ProductRepository
import tech.tiangong.pop.dto.FileUploadDTO
import tech.tiangong.pop.dto.export.ProductCenterBasicInfoExportDto
import tech.tiangong.pop.dto.export.ProductCenterLogisticsPackageExportDto
import tech.tiangong.pop.enums.DownloadTaskTypeEnum
import tech.tiangong.pop.helper.UploaderOssHelper
import tech.tiangong.pop.req.product.center.ProductCenterPageReq
import tech.tiangong.pop.service.settings.DownloadTaskService
import tech.tiangong.pop.utils.getRootMessage
import java.io.File
import java.io.FileOutputStream
import java.io.IOException
import java.time.LocalDateTime
import java.time.ZoneId
import java.time.format.DateTimeFormatter
import java.util.*

/**
 * 商品中心-全部信息导出组件
 * 负责导出商品中心的全部信息数据
 */
@Component
@Slf4j
class ProductCenterAllExportComponent(
    private val productRepository: ProductRepository,
    private val downloadTaskService: DownloadTaskService,
    private val uploaderOssHelper: UploaderOssHelper,

    private val productCenterAttrExportComponent: ProductCenterAttrExportComponent,
    private val productCenterBasicInfoExportComponent: ProductCenterBasicInfoExportComponent,
    private val productCenterLogisticsPackageExportComponent: ProductCenterLogisticsPackageExportComponent,
    private val productCenterPlatformAttrExportComponent: ProductCenterPlatformAttrExportComponent,
) : DownloadTaskInterface() {

    /**
     * 创建导出任务
     */
    fun createExportTask(req: ProductCenterPageReq) {
        try {
            val pageNum: Long = 1
            val pageSize: Long = 1000
            val page = productRepository.getCenterPage(Page(pageNum, pageSize), req)

            if (page.records.isEmpty()) {
                throw BusinessException("暂无数据")
            }
            // 创建下载任务
            val dateStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss", Locale.getDefault()).withZone(ZoneId.systemDefault()))
            val taskName = "导出商品中心_全部信息_$dateStr"
            downloadTaskService.createTask(
                taskName,
                DownloadTaskTypeEnum.PRODUCT_CENTER_ALL_TASK,
                req.toJson()
            );
            log.info { "导出商品中心_全部信息 创建下载任务，任务名称：${taskName}" }
        } catch (e: Exception) {
            log.error(e) { "导出商品中心_全部信息-创建下载任务, 异常，请求参数：${req.toJson()}" }
            throw BusinessException("导出商品中心_全部信息_任务异常：" + e.getRootMessage());
        }
    }

    /**
     * 生成Excel文件
     */
    override fun export(reqStr: String?, tempDir: File, task: DownloadTask): FileUploadDTO {
        val req = reqStr?.parseJson<ProductCenterPageReq>() ?: throw BusinessException("导出任务逻辑-参数为空 task=${task.toJson()}")
        var pageNum: Long = 1
        val pageSize: Long = 1000

        val productList = mutableListOf<Product>()

        while (true) {
            val page = productRepository.getCenterPage(Page(pageNum, pageSize), req)
            if (page.records.isEmpty()) {
                break
            }
            pageNum++

            page.records.forEach {
                productList.add(it)
            }
        }

        try {
            // 创建Excel文件
            val excelFile = File(tempDir, task.taskName + ".xlsx")

            // 1. 创建ExcelWriter对象（整个导出过程复用此对象）
            val excelWriter: ExcelWriter = EasyExcel.write(FileOutputStream(excelFile)).build()

            try {
                // 商品基本信息
                excelWriter.write(
                    productCenterBasicInfoExportComponent.buildExcelData(productList),
                    EasyExcel.writerSheet("商品基本信息")
                        .head(ProductCenterBasicInfoExportDto::class.java)
                        .build()
                )

                // 物流包装
                excelWriter.write(
                    productCenterLogisticsPackageExportComponent.buildExcelData(productList),
                    EasyExcel.writerSheet("物流包装")
                        .head(ProductCenterLogisticsPackageExportDto::class.java)
                        .build()
                )

                // 平台属性
                val platformAttrDataList = productCenterPlatformAttrExportComponent.buildExcelData(productList)
                val head = productCenterPlatformAttrExportComponent.buildHead(platformAttrDataList)
                val exportData = productCenterPlatformAttrExportComponent.convertData(platformAttrDataList, head)
                val dynamicSheet: WriteSheet = EasyExcel.writerSheet("平台属性")
                    .head(head)
                    .build()
                excelWriter.write(exportData, dynamicSheet)

                // 商品属性
                productCenterAttrExportComponent.buildExcelData(productList)
                    .groupBy { it.category }
                    .forEach { (category, list) ->
                        if (category != null) {
                            val head = productCenterAttrExportComponent.buildHead(list)
                            val exportData = productCenterAttrExportComponent.convertData(list, head)
                            val dynamicSheet: WriteSheet = EasyExcel.writerSheet(category)
                                .head(head)
                                .build()
                            excelWriter.write(exportData, dynamicSheet)
                        }
                    }
            } catch (e: Exception) {
                log.error(e) { "导出失败:" }
                throw BusinessException("导出失败")
            } finally {
                // 5. 关闭ExcelWriter，释放资源
                excelWriter.finish()
            }

            return uploaderOssHelper.createFileUploadDTO(excelFile, MediaType.MICROSOFT_EXCEL.type())
        } catch (e: IOException) {
            log.error(e) { "导出失败:" }
            throw BusinessException("导出失败")
        }
    }
}
