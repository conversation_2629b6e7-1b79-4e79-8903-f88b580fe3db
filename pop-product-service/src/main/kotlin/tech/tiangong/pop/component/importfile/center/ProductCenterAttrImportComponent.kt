package tech.tiangong.pop.component.importfile.center

import com.alibaba.excel.EasyExcel
import com.alibaba.excel.read.listener.PageReadListener
import org.apache.commons.collections4.CollectionUtils
import org.springframework.stereotype.Component
import org.springframework.web.multipart.MultipartFile
import team.aikero.blade.core.enums.Bool
import team.aikero.blade.core.toolkit.isBlank
import team.aikero.blade.core.toolkit.isNotBlank
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.sequence.id.IdHelper
import team.aikero.blade.util.json.parseJson
import team.aikero.blade.util.json.toJson
import tech.tiangong.pop.common.enums.SpotTypeOpsEnum
import tech.tiangong.pop.common.exception.BaseBizException
import tech.tiangong.pop.component.ColorComponent
import tech.tiangong.pop.dao.entity.*
import tech.tiangong.pop.dao.repository.*
import tech.tiangong.pop.dto.export.ProductCenterAttrExportDto
import tech.tiangong.pop.enums.ImportDataTypeEnum
import tech.tiangong.pop.enums.ImportSourceEnum
import tech.tiangong.pop.enums.ProductPricingTypeEnum

/**
 * 商品中心-商品属性导入组件
 * 负责导入商品中心的商品属性数据
 */
@Component
@Slf4j
class ProductCenterAttrImportComponent(
    private val importProductRecordRepository: ImportProductRecordRepository,
    private val productRepository: ProductRepository,
    private val productSkcRepository: ProductSkcRepository,
    private val colorComponent: ColorComponent,
    private val publishAttributeRepository: PublishAttributeRepository,
    private val publishAttributeValueRepository: PublishAttributeValueRepository,
    private val productAttributesV2Repository: ProductAttributesV2Repository,
) {

    /**
     * 导入
     */
    fun importExcel(file: MultipartFile) {
        val importDataList = mutableListOf<ProductCenterAttrExportDto>()

        // 读取Excel并映射到UserData实体类
        EasyExcel.read(
            file.inputStream,
            ProductCenterAttrExportDto::class.java,
            PageReadListener { dataList ->
                log.info { "读取到${dataList.size}条数据" }
                dataList.forEach { user ->
                    importDataList.add(user)
                }
            }
        ).doReadAll()   // 读取所有sheet

        val importList = importDataList
            .asSequence()
            .filter { it.spuCode.isNotBlank() }
            .filter { it.skc.isNotBlank() }
            .toList()
        if (CollectionUtils.isEmpty(importList)) {
            throw BaseBizException("导入数据为空")
        }

        // 创建导入任务
        val list = importList.map {
            ImportProductRecord().apply {
                this.spuCode = it.spuCode
                this.skc = it.skc
                this.importDataType = ImportDataTypeEnum.PRODUCT_ATTR.code
                this.importSource = ImportSourceEnum.PRODUCT_CENTER.code
                this.importData = it.toJson()
            }
        }
        importProductRecordRepository.saveBatch(list, 100)
    }

    /**
     * 执行导入逻辑
     */
    fun doImport(importData: String) {
        if (importData.isBlank()) {
            throw BaseBizException("解析json失败: $importData")
        }
        // 获取商品
        val data = importData.parseJson<ProductCenterAttrExportDto>()
        val productList = productRepository.listBySpuCodes(listOf(data.spuCode!!))
        if (productList.isEmpty()) {
            return
        }

        // 颜色字典
        val colorList = colorComponent.getColorMap()
        val dictColor = colorList.firstOrNull { it.colorCode == data.color }

        productList.forEach { product ->
            // 获取SKC
            val productSkc = productSkcRepository.getByProductIdAndSkc(product.productId!!, data.skc!!)
            if (productSkc == null) {
                // 创建SKC
                productSkcRepository.save(
                    ProductSkc().apply {
                        this.productSkcId = IdHelper.getId()
                        this.productId = product.productId
                        this.skc = data.skc
                        this.color = data.color
                        this.colorCode = dictColor?.colorCode
                        this.colorAbbrCode = dictColor?.colorAabbr
                        this.platformColor = data.color
                        this.sizeNames = data.size?.split(",")?.joinToString(",") { it.trim() }
                        this.localPrice = data.supplyPrice
                        this.purchasePrice = data.purchasePrice
                        this.importFlag = Bool.YES.code
                    }
                )
            } else {
                // 更新SKC
                productSkcRepository.updateById(
                    productSkc.apply {
                        this.productSkcId = productSkc.productSkcId
                        this.color = data.color
                        this.colorCode = dictColor?.colorCode
                        this.colorAbbrCode = dictColor?.colorAabbr
                        this.platformColor = data.color
                        this.sizeNames = data.size?.split(",")?.joinToString(",") { it.trim() }
                        this.localPrice = data.supplyPrice
                        this.purchasePrice = data.purchasePrice
                    }
                )
            }

            // 更新product相关信息
            val updateProduct = product.apply {
                this.productId = product.productId
                this.spotTypeCode = SpotTypeOpsEnum.getByDesc(data.spotType)?.dictCode
                this.pricingType = ProductPricingTypeEnum.getByDesc(data.pricingType)?.code
            }
            productRepository.updateById(updateProduct)

            // TODO UPSERT商品属性
            if (data.dynamicProperties.isNotEmpty()) {
                data.dynamicProperties.forEach { (attrKey, attrValue) ->
                    // attrKey移除前缀"属性-"
                    val newAttrKey = attrKey.removePrefix("属性-")
                    if (newAttrKey.isBlank()) {
                        return@forEach
                    }
                    // attrValue分离最后一个空格(单位)
                    val split = attrValue.split(" ")
                    val newAttrValue = attrValue.firstOrNull()
                    if (newAttrValue == null) {
                        return@forEach
                    }
                    val unit = split.lastOrNull()
                    val attrGroup = publishAttributeRepository.ktQuery()
                        .eq(PublishAttribute::attributeName, newAttrKey)
                        .eq(PublishAttribute::state, Bool.YES.code)
                        .list()
                        .firstOrNull()
                    if (attrGroup == null) {
                        return@forEach
                    }
                    val attrValue = publishAttributeValueRepository.ktQuery()
                        .eq(PublishAttributeValue::attributeValue, newAttrValue)
                        .eq(PublishAttributeValue::state, Bool.YES.code)
                        .list()
                        .firstOrNull()
                    if (attrValue == null) {
                        return@forEach
                    }

                    val attrInfo = productAttributesV2Repository.listByProductIdAndAttrId(product.productId!!, attrGroup.attributeId!!)
                    if (attrInfo == null) {
                        // 创建
                        productAttributesV2Repository.save(
                            ProductAttributesV2().apply {
                                this.productAttributesId = IdHelper.getId()
                                this.productId = product.productId
                                this.categoryId = product.categoryId
                                this.attributeId = attrGroup.attributeId
                                this.attributeValueId = attrValue.attributeValueId
                                // this.attributeValue = newAttrValue
                                this.unit = unit
                            }
                        )
                    } else {
                        // 更新
                        productAttributesV2Repository.updateById(
                            attrInfo.apply {
                                this.productAttributesId = attrInfo.productAttributesId
                                this.attributeValueId = attrValue.attributeValueId
                                // this.attributeValue = newAttrValue
                                this.unit = unit
                            }
                        )
                    }
                }
            }
        }
    }
}