package tech.tiangong.pop.component

import com.baomidou.mybatisplus.extension.kotlin.KtQueryWrapper
import org.apache.commons.collections4.CollectionUtils
import org.apache.commons.lang3.StringUtils
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import team.aikero.blade.core.exception.BusinessException
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.util.json.toJson
import tech.tiangong.pop.common.constant.LazadaConstants
import tech.tiangong.pop.common.enums.YesOrNoEnum
import tech.tiangong.pop.dao.entity.LazadaOriginalProductSku
import tech.tiangong.pop.dao.entity.LazadaOriginalProductSpu
import tech.tiangong.pop.dao.repository.LazadaOriginalProductSkuRepository
import tech.tiangong.pop.dao.repository.LazadaOriginalProductSpuRepository
import tech.tiangong.pop.dao.repository.SaleGoodsRepository
import tech.tiangong.pop.resp.sdk.lazada.LazadaProductItemDetailResp
import tech.tiangong.pop.resp.sdk.lazada.LzdProductDetail
import tech.tiangong.pop.service.lazada.LazadaApiService
import tech.tiangong.pop.utils.RetryTemplateUtils
import java.time.Instant
import java.time.LocalDateTime
import java.time.ZoneId
import java.util.*

/**
 * 同步Lazada商品
 * <AUTHOR>
 * @date 2025-2-14 15:51:33
 */
@Slf4j
@Service
class LazadaProductSyncComponent(
    private var lazadaApiService: LazadaApiService,
    private var lazadaOriginalProductSpuRepository: LazadaOriginalProductSpuRepository,
    private var lazadaOriginalProductSkuRepository: LazadaOriginalProductSkuRepository,
    private var saleGoodsRepository: SaleGoodsRepository,
) {

    /**
     * 获取Lazada商品详情
     *
     * @param itemId    saleGoods.getPlatformProductId()
     * @param country   saleGoods.getCountry()
     * @param token     shop.getToken()
     * @param shopShortCode     shop.shopShortCode()
     * @return
     */
    @Transactional(rollbackFor = [Exception::class])
    fun retryGetLzdProductDetail(
        itemId: String,
        country: String,
        token: String,
        shopShortCode: String,
    ): Pair<LazadaProductItemDetailResp, Pair<LazadaOriginalProductSpu?, List<LazadaOriginalProductSku>?>?> {
        val map = mutableMapOf<String, Double>()
        map[RetryTemplateUtils.MAX_INTERVAL] = 600.0
        map[RetryTemplateUtils.INITIAL_INTERVAL] = 100.0
        map[RetryTemplateUtils.MAX_ATTEMPTS] = 3.0
        map[RetryTemplateUtils.MULTIPLIER] = 1.1
        val result = lazadaApiService.getProductDetailByItemId(country, itemId, token)
        if (!Objects.equals(LazadaConstants.LAZADA_API_SUCCESS_CODE, result.code)) {
            if (Objects.equals("207", result.code)) {
                log.warn { "获取Lazada商品详情, SKU不存在 itemId: $itemId, country: $country, message: ${result.message}" }
                // 逻辑删除spu
                lazadaOriginalProductSpuRepository.logicDeleteByIdAndCountry(itemId.toLong(), country)
                return Pair(result, null)
            }
            log.error { "获取Lazada商品详情失败, itemId: $itemId, country: $country, message: ${result.message}" }
            return Pair(result, null)
        }

        var resultSpu: LazadaOriginalProductSpu? = null
        var resultSkuList = mutableListOf<LazadaOriginalProductSku>()
        val spu = result?.data
        if (spu == null) {
            return Pair(result, Pair(resultSpu, resultSkuList))
        }

        // 记录源数据
        // SPU
        val lazadaOriginalProductSpu = LazadaOriginalProductSpu().apply {
            this.itemId = spu.itemId
            this.title = spu.attributes?.name
            this.country = country
            this.shopShortCode = shopShortCode
            this.lzdStatus = spu.status
            this.lazadaCreatedTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(spu.createdTime!!.toLong()), ZoneId.systemDefault())
            this.lazadaUpdatedTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(spu.updatedTime!!.toLong()), ZoneId.systemDefault())
            this.primaryCategory = spu.primaryCategory?.toLong()
            this.images = spu.images?.toJson()
            this.marketImages = spu.marketImages?.toJson()
            this.attributes = spu.attributes?.toJson()
            this.deleted = YesOrNoEnum.NO.code
        }

        // 先查本地有没有pid记录, 有则直接用SPU_CODE
        val saleGoods = saleGoodsRepository.getByPlatformProductId(lazadaOriginalProductSpu.itemId!!, country)

        resultSpu = lazadaOriginalProductSpu
        lazadaOriginalProductSpuRepository.upsert(lazadaOriginalProductSpu)
        // SKU
        val skus = spu.skus
        if (CollectionUtils.isNotEmpty(skus)) {
            val skuList = mutableListOf<LazadaOriginalProductSku>()
            skus?.forEach {
                val sku = LazadaOriginalProductSku().apply {
                    this.skuId = it.skuId
                    this.lzdStatus = it.status
                    this.itemId = lazadaOriginalProductSpu.itemId
                    this.country = lazadaOriginalProductSpu.country
                    this.sellerSku = it.sellerSku
                    this.extractSpuCode = if (saleGoods != null) saleGoods.spuCode else getSpuCode(it.sellerSku)
                    this.shopSku = it.shopSku
                    this.colorFamily = if (StringUtils.isNotEmpty(it.colorFamily)) {
                        it.colorFamily
                    } else {
                        it.color
                    }
//                        this.sizeGroup = it.sizeUp
                    this.size = if (StringUtils.isNotEmpty(it.size)) {
                        it.size
                    } else {
                        it.sizeUp
                    }
                    this.url = it.url
                    this.images = it.images?.toJson()
                    this.specialPrice = it.specialPrice?.toBigDecimal()
                    this.price = it.price?.toBigDecimal()
                    this.quantity = it.quantity?.toInt()
                    this.available = it.available
                    this.packageWidth = it.packageWidth?.toBigDecimal()
                    this.packageHeight = it.packageHeight?.toBigDecimal()
                    this.packageLength = it.packageLength?.toBigDecimal()
                    this.packageWeight = it.packageWeight?.toBigDecimal()
                    this.deleted = YesOrNoEnum.NO.code
                }
                skuList.add(sku)
            }
            if (CollectionUtils.isNotEmpty(skuList)) {
                resultSkuList = skuList
                // 根据itemId拿到SKU源表内的所有数据, 已表内数据为基准, 比较lzd最新数据, lzd已删除的, 库内要标记为逻辑删除
                val skuListInDb = lazadaOriginalProductSkuRepository.list(
                    KtQueryWrapper(LazadaOriginalProductSku::class.java)
                        .eq(LazadaOriginalProductSku::itemId, lazadaOriginalProductSpu.itemId)
                        .eq(LazadaOriginalProductSku::country, lazadaOriginalProductSpu.country)
                )
                if (CollectionUtils.isNotEmpty(skuListInDb)) {
                    val oldSkuIds = skuListInDb.map { it.skuId }.distinct()
                    val newSkuIds = skuList.map { it.skuId }.distinct()
                    oldSkuIds.forEach { skuId ->
                        if (!newSkuIds.contains(skuId)) {
                            lazadaOriginalProductSkuRepository.logicDelete(skuId!!)
                        }
                    }
                }
                lazadaOriginalProductSkuRepository.upsertBatch(skuList)
            }
        }
        return Pair(result, Pair(resultSpu, resultSkuList))
    }

    /**
     * 提取SPU编码
     *
     * @param sellerSku
     * @return
     */
    private fun getSpuCode(sellerSku: String?): String? {
        var tmp: String? = null
        if (StringUtils.isNotEmpty(sellerSku)) {
            // 使用-解析, 获取第一个元素
            tmp = sellerSku?.split("-")?.first()
            if (Objects.equals(sellerSku, tmp)) {
                // 如果和原来的值相同, 则-处理不了, 使用_
                tmp = sellerSku?.split("_")?.first()
            }
        }
        return tmp?.trim()
    }

    /**
     * 根据sellerSku获取Lazada商品详情
     *
     * @param sellerSku
     * @param country
     * @param token
     * @param shopShortCode
     * @return
     */
    @Transactional(rollbackFor = [Exception::class])
    fun getBySellerSku(sellerSku: String, country: String, token: String, shopShortCode: String): Pair<LzdProductDetail, List<Pair<LazadaOriginalProductSpu?, List<LazadaOriginalProductSku>?>>> {
        val result: LzdProductDetail? = lazadaApiService.listLzdProducts(country, token, listOf(sellerSku))
        if (result == null) {
            log.error { "获取Lazada商品详情失败, sellerSku: $sellerSku, country: $country, shopShortCode: $shopShortCode" }
            throw BusinessException("获取Lazada商品详情失败")
        }
        if (!Objects.equals(LazadaConstants.LAZADA_API_SUCCESS_CODE, result.code)) {
            if (Objects.equals("207", result.code)) {
                log.warn { "获取Lazada商品详情, SKU不存在 sellerSku: $sellerSku, country: $country, message: ${result.message}" }
                return Pair(result, listOf())
            }
            log.error { "获取Lazada商品详情失败, sellerSku: $sellerSku, country: $country, message: ${result.message}" }
            return Pair(result, listOf())
        }

        val pair = mutableListOf<Pair<LazadaOriginalProductSpu?, List<LazadaOriginalProductSku>?>>()

        result.data?.products?.forEach { spu ->
            // 记录源数据
            val resultSpu: LazadaOriginalProductSpu?
            var resultSkuList = mutableListOf<LazadaOriginalProductSku>()
            // SPU
            val lazadaOriginalProductSpu = LazadaOriginalProductSpu().apply {
                this.itemId = spu.itemId
                this.title = spu.attributes?.name
                this.country = country
                this.shopShortCode = shopShortCode
                this.lzdStatus = spu.status
                this.lazadaCreatedTime = spu.createdTime?.let { LocalDateTime.ofInstant(Instant.ofEpochMilli(it), ZoneId.systemDefault()) }
                this.lazadaUpdatedTime = spu.updatedTime?.let { LocalDateTime.ofInstant(Instant.ofEpochMilli(it), ZoneId.systemDefault()) }
                this.primaryCategory = spu.primaryCategory?.toLong()
                this.images = spu.images?.toJson()
                this.marketImages = spu.marketImages?.toJson()
                this.attributes = spu.attributes?.toJson()
                this.deleted = YesOrNoEnum.NO.code
            }
            resultSpu = lazadaOriginalProductSpu
            lazadaOriginalProductSpuRepository.upsert(lazadaOriginalProductSpu)
            // SKU
            val skus = spu.skus
            if (CollectionUtils.isNotEmpty(skus)) {
                val skuList = mutableListOf<LazadaOriginalProductSku>()
                skus?.forEach {
                    val sku = LazadaOriginalProductSku().apply {
                        this.skuId = it.skuId
                        this.lzdStatus = it.status
                        this.itemId = lazadaOriginalProductSpu.itemId
                        this.country = lazadaOriginalProductSpu.country
                        this.sellerSku = it.sellerSku
                        this.extractSpuCode = it.sellerSku?.split("-")?.first()
                        this.shopSku = it.shopSku
                        this.colorFamily = if (StringUtils.isNotEmpty(it.colorFamily)) {
                            it.colorFamily
                        } else {
                            it.color
                        }
//                        this.sizeGroup = it.sizeUp
                        this.size = if (StringUtils.isNotEmpty(it.sizeUp)) {
                            it.sizeUp
                        } else {
                            it.saleProp?.size ?: it.saleProp?.sizeUp
                        }
                        this.url = it.url
                        this.images = it.images?.toJson()
                        this.specialPrice = it.specialPrice?.toBigDecimal()
                        this.price = it.price?.toBigDecimal()
                        this.quantity = it.quantity
                        this.available = it.available
                        this.packageWidth = it.packageWidth?.toBigDecimal()
                        this.packageHeight = it.packageHeight?.toBigDecimal()
                        this.packageLength = it.packageLength?.toBigDecimal()
                        this.packageWeight = it.packageWeight?.toBigDecimal()
                        this.deleted = YesOrNoEnum.NO.code
                    }
                    skuList.add(sku)
                }
                if (CollectionUtils.isNotEmpty(skuList)) {
                    resultSkuList = skuList
                    // 根据itemId拿到SKU源表内的所有数据, 已表内数据为基准, 比较lzd最新数据, lzd已删除的, 库内要标记为逻辑删除
                    val skuListInDb = lazadaOriginalProductSkuRepository.list(
                        KtQueryWrapper(LazadaOriginalProductSku::class.java)
                            .eq(LazadaOriginalProductSku::itemId, lazadaOriginalProductSpu.itemId)
                            .eq(LazadaOriginalProductSku::country, lazadaOriginalProductSpu.country)
                    )
                    if (CollectionUtils.isNotEmpty(skuListInDb)) {
                        val oldSkuIds = skuListInDb.map { it.skuId }.distinct()
                        val newSkuIds = skuList.map { it.skuId }.distinct()
                        oldSkuIds.forEach { skuId ->
                            if (!newSkuIds.contains(skuId)) {
                                lazadaOriginalProductSkuRepository.logicDelete(skuId!!)
                            }
                        }
                    }
                    lazadaOriginalProductSkuRepository.upsertBatch(skuList)
                }
            }
            pair.add(Pair(resultSpu, resultSkuList))
        }
        return Pair(result, pair)
    }
}
