package tech.tiangong.pop.component

import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional
import team.aikero.blade.core.exception.BusinessException
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.sequence.id.IdHelper
import team.aikero.blade.util.json.toJson
import tech.tiangong.pop.common.enums.PlatformEnum
import tech.tiangong.pop.dao.entity.ProductPublishTask
import tech.tiangong.pop.dao.entity.TaskInfo
import tech.tiangong.pop.dao.repository.ProductPublishTaskRepository
import tech.tiangong.pop.dao.repository.TaskInfoRepository
import tech.tiangong.pop.dto.product.ProductPublishDto
import tech.tiangong.pop.enums.PlatformProductPullTaskStatusEnum
import tech.tiangong.pop.enums.PlatformProductPullTaskTypeEnum

/**
 * 商品发布/更新
 * <AUTHOR>
 * @date 2025-3-20 16:11:30
 */
@Slf4j
@Component
class ProductPublishComponent(
    private val taskInfoRepository: TaskInfoRepository,
    private val productPublishTaskRepository: ProductPublishTaskRepository,
) {

    /**
     * 创建任务
     * @param dto
     */
    @Transactional(rollbackFor = [Exception::class])
    fun createTask(dto: ProductPublishDto) {

        val info = TaskInfo().apply {
            this.taskId = IdHelper.getId()
            this.taskName = "${PlatformProductPullTaskTypeEnum.PUBLISH_PRODUCT.desc}-${dto.shopId}-${dto.isPublish}"
            this.taskType = PlatformProductPullTaskTypeEnum.PUBLISH_PRODUCT.code
            this.platformId = PlatformEnum.LAZADA.platformId
            this.taskStatus = PlatformProductPullTaskStatusEnum.PENDING.code
            this.retryCount = 0
        }
        taskInfoRepository.save(info)

        val task = ProductPublishTask().apply {
            this.taskId = info.taskId
            this.shopId = dto.shopId
            this.productId = dto.productId
            this.channelId = dto.channelId
            this.platformId = dto.platformId
            this.publishOrUpdate = publishToType(dto.isPublish)
            this.countryList = dto.countryList?.toJson()
        }
        productPublishTaskRepository.save(task)
    }

    /**
     * 商品发布/更新
     * @param task
     */
    fun publishLazada(task: ProductPublishTask?) {
        if (task == null) {
            throw BusinessException("商品发布/更新，参数为空")
        }

        // 任务进行中
        taskInfoRepository.updateStatus(task.taskId!!, PlatformProductPullTaskStatusEnum.PROCESSING, null)


        // 发布标题更新到LZD
        try {
            // TODO_新实现，原方法废弃
        } catch (e: Exception) {
            log.error(e) { "商品发布/更新 失败 task=${task.toJson()} e=${e.message}" }
            e.printStackTrace()
            // 任务失败
            taskInfoRepository.updateStatus(task.taskId!!, PlatformProductPullTaskStatusEnum.FAILED, e.message)
        }

        // 任务完成
        taskInfoRepository.updateStatus(task.taskId!!, PlatformProductPullTaskStatusEnum.COMPLETED, null)
    }

    /**
     * 字段转换
     * 是否上架【true上架；false更新】 转为 publish_or_update发布1, 更新0
     */
    fun publishToType(publish: Boolean): Int {
        return if (publish) 1 else 0
    }

    /**
     * 字段转换
     * publish_or_update发布1, 更新0 转为 是否上架【true上架；false更新】
     */
    fun publishToType(publishToType: Int?): Boolean {
        return publishToType != null && publishToType == 1
    }
}