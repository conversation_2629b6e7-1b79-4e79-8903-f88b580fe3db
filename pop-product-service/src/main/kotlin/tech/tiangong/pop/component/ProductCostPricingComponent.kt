package tech.tiangong.pop.component

import com.google.common.cache.CacheBuilder
import com.google.common.cache.CacheLoader
import com.google.common.cache.LoadingCache
import org.springframework.stereotype.Service
import team.aikero.blade.core.exception.BusinessException
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import tech.tiangong.pop.bo.ProductCostPricingRequestV2BO
import tech.tiangong.pop.bo.ProductCostPricingResultV2BO
import tech.tiangong.pop.common.enums.CountryTypeEnum
import tech.tiangong.pop.common.enums.SdpStyleTypeEnum
import tech.tiangong.pop.common.enums.SpotTypeOpsEnum
import tech.tiangong.pop.config.PublishProperties
import tech.tiangong.pop.dao.entity.CategoryPriceRange
import tech.tiangong.pop.dao.entity.Product
import tech.tiangong.pop.dao.repository.CategoryPriceRangeRepository
import tech.tiangong.pop.dto.product.CostPricingSkcDto
import tech.tiangong.pop.enums.CostPriceCalculationTypeEnum
import tech.tiangong.pop.enums.ProductPricingTypeEnum
import tech.tiangong.pop.enums.SupplyModeEnum
import tech.tiangong.pop.enums.settings.ProductPriceOperatorEnum
import tech.tiangong.pop.helper.ProductPublishHelper
import java.math.BigDecimal
import java.util.*
import java.util.concurrent.TimeUnit

/**
 * 计算定价成本
 */
@Slf4j
@Service
class ProductCostPricingComponent(
    private val publishProperties: PublishProperties,
    private val categoryPriceRangeRepository: CategoryPriceRangeRepository,
) {

    companion object {
        private const val CACHE_EXPIRE_MINUTES = 5L
    }

    /**
     * 品类价格区间缓存
     */
    private val categoryPriceRangeCache: LoadingCache<String, Optional<CategoryPriceRange>> = CacheBuilder.newBuilder()
        .expireAfterWrite(CACHE_EXPIRE_MINUTES, TimeUnit.MINUTES)
        .build(CacheLoader.from { categoryCode ->
            Optional.ofNullable(
                categoryPriceRangeRepository.ktQuery()
                    .eq(CategoryPriceRange::categoryCode, categoryCode)
                    .eq(CategoryPriceRange::conditionOperator, ProductPriceOperatorEnum.GT.code)
                    .orderByDesc(CategoryPriceRange::costAmount)
                    .last("LIMIT 1")
                    .one()
            )
        })

    /**
     * 计算定价成本
     */
    fun calculateCostPriceV2(req: ProductCostPricingRequestV2BO): List<ProductCostPricingResultV2BO> {

        // 判断是否为跨境类型
        val isCbType = Objects.equals(req.shop.countryType, CountryTypeEnum.CB.code) && ProductPublishHelper.isLoginNum(req.product)

        val product = req.product
        val skcList = req.skcList
        // 0. 预先获取共用数据，减少重复计算
        // 商品分类条件
        val isTryOnMode = product.supplyMode == SupplyModeEnum.TRY_ON.dictCode
        val styleType = SdpStyleTypeEnum.getByCode(product.styleType)?:SdpStyleTypeEnum.UNKNOWN
        val supplyMode = SupplyModeEnum.getByCode(product.supplyMode)
        // 计算类型
        val calculationType = when (product.pricingType) {
            ProductPricingTypeEnum.RETURN_RULE.code -> CostPriceCalculationTypeEnum.TRY_ON_RETURN
            ProductPricingTypeEnum.NO_RETURN_RULE.code -> CostPriceCalculationTypeEnum.TRY_ON_NO_RETURN
            else -> CostPriceCalculationTypeEnum.TRY_ON_NO_RETURN
        }

        return skcList.map { skc ->
            // 获取单个SKC的供货价
            val supplyPrice = getSkuSupplyPrice(skc, isCbType, styleType,supplyMode)
            when {
                // 非TRY_ON模式，使用最大供货价
                !isTryOnMode -> {
                    createMaxSupplyPriceResult(skc, supplyPrice)
                }

                // I_FASHION类型
                product.spotTypeCode == SpotTypeOpsEnum.A2.dictCode -> {
                    calculateIfashionCost(skc, supplyPrice)
                }

                // TRY_ON类型
                product.spotTypeCode == SpotTypeOpsEnum.A1.dictCode -> {
                    // 处理TRY_ON成本价计算
                    // 预获取品类价格区间(TryOnMode)
                    val categoryMaxPrice = if (isTryOnMode && product.spotTypeCode == SpotTypeOpsEnum.A1.dictCode) {
                        product.categoryCode?.takeUnless { it.isBlank() }?.let {
                            categoryPriceRangeCache.get(it).orElse(null)
                        }
                    } else null
                    // 损益临界值和折扣率（常量值提取避免重复获取）
                    val breakEvenPoint = publishProperties.pricingCostBreakEvenPoint
                    val costDiscount = publishProperties.pricingCostDiscount
                    processTryOnCost(
                        product, skc, supplyPrice, calculationType,
                        categoryMaxPrice, breakEvenPoint, costDiscount
                    )
                }

                else -> {
                    log.error { "未知的现货类型: ${product.spotTypeCode}" }
                    return emptyList()
                }
            }
        }
    }

    /**
     * 获取单个SKC的供货价
     */
    private fun getSkuSupplyPrice(
        skc: CostPricingSkcDto,
        isCbType: Boolean,
        styleType: SdpStyleTypeEnum,
        supplyMode: SupplyModeEnum?
    ): BigDecimal {
        return when {
            supplyMode!=null && supplyMode == SupplyModeEnum.OBM_SPOT_GOODS && skc.purchasePrice != null -> skc.purchasePrice
            isCbType -> skc.cbPrice ?: BigDecimal.ZERO
            //设计款，供给方式-仿款，拿供货价计算
            styleType == SdpStyleTypeEnum.DESIGN && supplyMode!=null && supplyMode == SupplyModeEnum.OBM_REPLICA -> skc.localPrice ?: BigDecimal.ZERO
            //现货款，供给方式-仿款，拿采购价计算
            styleType == SdpStyleTypeEnum.SPOT && supplyMode!=null && supplyMode == SupplyModeEnum.OBM_REPLICA -> skc.purchasePrice ?: BigDecimal.ZERO
            else -> skc.localPrice ?: BigDecimal.ZERO
        }
    }

    /**
     * 创建使用最大供货价的成本结果
     */
    private fun createMaxSupplyPriceResult(
        skc: CostPricingSkcDto,
        maxSupplyPrice: BigDecimal,
    ): ProductCostPricingResultV2BO {
        return ProductCostPricingResultV2BO(
            skc = skc.skc!!,
            costPrice = maxSupplyPrice,
            calculationType = CostPriceCalculationTypeEnum.MAX_SUPPLY_PRICE
        )
    }

    /**
     * 处理TRY_ON成本价计算
     */
    private fun processTryOnCost(
        product: Product,
        skc: CostPricingSkcDto,
        supplyPrice: BigDecimal,
        calculationType: CostPriceCalculationTypeEnum,
        categoryMaxPrice: CategoryPriceRange?,
        breakEvenPoint: BigDecimal,
        costDiscount: BigDecimal,
    ): ProductCostPricingResultV2BO {
        // Step 1: 基础成本计算
        val step1CostPrice = when (product.pricingType) {
            ProductPricingTypeEnum.RETURN_RULE.code -> {
                // 返单规则计算
                if (supplyPrice > breakEvenPoint) {
                    supplyPrice.multiply(costDiscount)
                } else {
                    supplyPrice
                }
            }

            ProductPricingTypeEnum.NO_RETURN_RULE.code -> {
                // 不返单规则计算
                val purchasePrice = skc.purchasePrice ?: BigDecimal.ZERO
                when {
                    supplyPrice < purchasePrice -> supplyPrice
                    supplyPrice > breakEvenPoint -> supplyPrice.multiply(costDiscount)
                    else -> supplyPrice
                }
            }

            else -> throw BusinessException("未知的定价类型: ${product.pricingType}")
        }

        // 根据定价类型决定是否应用品类最大值判断
        val finalCostPrice = if (product.pricingType == ProductPricingTypeEnum.RETURN_RULE.code) {
            // 返单规则计算不需要品类最大值判断
            step1CostPrice
        } else {
            // 其他计算类型需要品类最大值判断
            when {
                categoryMaxPrice != null && step1CostPrice > categoryMaxPrice.costAmount -> {
                    categoryMaxPrice.costAmount ?: step1CostPrice
                }

                else -> step1CostPrice
            }
        }

        return ProductCostPricingResultV2BO(
            skc = skc.skc,
            costPrice = finalCostPrice,
            calculationType = calculationType,
            message = buildCostMessage(supplyPrice, step1CostPrice, finalCostPrice)
        )
    }

    private fun findMaxSupplyPrice(product: Product, skcs: List<CostPricingSkcDto>, isCbType: Boolean): BigDecimal {
        // // 待上架逻辑, 获取max
        return if (SupplyModeEnum.OBM_SPOT_GOODS.dictCode == product.supplyMode) {
            skcs.mapNotNull { it.purchasePrice }.maxOrNull() ?: BigDecimal.ZERO
        } else if (isCbType) {
            skcs.mapNotNull { it.cbPrice }.maxOrNull() ?: BigDecimal.ZERO
        } else {
            skcs.mapNotNull { it.localPrice }.maxOrNull() ?: BigDecimal.ZERO
        }
    }

    private fun calculateIfashionCost(
        skc: CostPricingSkcDto,
        supplyPrice: BigDecimal,
    ): ProductCostPricingResultV2BO {
        return ProductCostPricingResultV2BO(
            skc = skc.skc,
            costPrice = supplyPrice,
            calculationType = CostPriceCalculationTypeEnum.I_FASHION_PURCHASE
        )
    }

    private fun buildCostMessage(
        maxSupplyPrice: BigDecimal,
        step1Cost: BigDecimal,
        finalCost: BigDecimal,
    ): String {
        return """
            计算过程：
            1. 最大供货价（核价）: $maxSupplyPrice
            2. Step1基础成本: $step1Cost
            3. 最终成本价: $finalCost
        """.trimIndent()
    }
}