package tech.tiangong.pop.component

import com.baomidou.mybatisplus.extension.kotlin.KtQueryWrapper
import com.baomidou.mybatisplus.extension.plugins.pagination.Page
import org.apache.commons.collections4.CollectionUtils
import org.springframework.stereotype.Component
import team.aikero.blade.core.enums.Bool
import team.aikero.blade.core.protocol.PageReq
import team.aikero.blade.core.protocol.PageVo
import team.aikero.blade.core.toolkit.isNotBlank
import team.aikero.blade.core.toolkit.isNotEmpty
import team.aikero.blade.core.toolkit.isNotNull
import team.aikero.blade.core.toolkit.isNull
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import tech.tiangong.pop.common.enums.ProductImagePackageStateEnum
import tech.tiangong.pop.common.enums.SdpStyleTypeEnum
import tech.tiangong.pop.dao.entity.ImportProductRecord
import tech.tiangong.pop.dao.entity.Product
import tech.tiangong.pop.dao.entity.ProductAttributes
import tech.tiangong.pop.dao.repository.ImageRepositoryRepository
import tech.tiangong.pop.dao.repository.ImportProductRecordRepository
import tech.tiangong.pop.dao.repository.ProductAttributesRepository
import tech.tiangong.pop.dao.repository.ProductRepository
import tech.tiangong.pop.dto.product.ImportBaseProductDTO
import tech.tiangong.pop.external.DesignClientExternal
import tech.tiangong.pop.helper.PageRespHelper
import tech.tiangong.pop.req.product.FillProductStyleTypeReq
import tech.tiangong.pop.req.product.RefreshProductImagePackageStateReq
import tech.tiangong.pop.resp.product.ImportProductPageResp

@Component
@Slf4j
class ProductCommonComponent(
    private val productAttributesRepository: ProductAttributesRepository,
    private val productRepository: ProductRepository,
    private val designClientExternal: DesignClientExternal,
    private val imageRepositoryRepository: ImageRepositoryRepository,
    private val importProductRecordRepository: ImportProductRecordRepository,
) {

    /**
     * 商品导入记录-分页
     * @param req 分页参数
     * @param platformId 平台id
     * @param importSource 导入来源 ImportSourceEnum
     * @return 分页数据
     */
    fun importProductPage(req: PageReq, platformId: Long, importSource: Int): PageVo<ImportProductPageResp> {
        val page = Page<ImportProductRecord>(req.pageNum.toLong(), req.pageSize.toLong())
        val queryWrapper = KtQueryWrapper(ImportProductRecord::class.java)
            .eq(ImportProductRecord::platformId, platformId)
            .eq(ImportProductRecord::importSource, importSource)
            .orderByDesc(ImportProductRecord::importProductRecordId)
        val pageRecord = importProductRecordRepository.page(page, queryWrapper)
        if (pageRecord.records.isEmpty()) {
            return PageRespHelper.empty()
        }
        val resultList = pageRecord.records.map { record ->
            ImportProductPageResp().apply {
                this.importProductRecordId = record.importProductRecordId
                this.supplyMode = record.supplyMode
                this.spuCode = record.spuCode
                this.skc = record.skc
                this.processState = record.processState
                this.errorMsg = record.errorMsg
                this.createdTime = record.createdTime
                this.creatorName = record.creatorName
            }
        }
        return PageRespHelper.of(page.current.toInt(), page.total, resultList)
    }

    /**
     * 商品导入-设置商品属性
     * @param product
     * @param importDto
     */
    fun setAttributesByImport(
        importDto: ImportBaseProductDTO,
        product: Product,
        platformId: Long,
    ) {
        // 取出属性
        val mergedAttributes = mutableMapOf<String, String?>()
        importDto.dynamicAttributes.forEach { (key, value) ->
            // 过滤空值的属性
            if (value.isNullOrBlank()) {
                return@forEach
            }
            // 如果已存在该属性名，值覆盖原有的值
            mergedAttributes[key] = value
        }
        val valueReqs = ImportBaseProductDTO.convertDynamicAttributesToList(mergedAttributes)
        if (CollectionUtils.isNotEmpty(valueReqs)) {
            //设置商品属性
            //先删除已有属性
            productAttributesRepository.removeByProductIdAndPlatformId(product.productId!!, platformId)
            if (CollectionUtils.isEmpty(valueReqs)) {
                return
            }
            val attributeIdValueResps = productRepository.selectAttributeName(valueReqs)
            if (CollectionUtils.isNotEmpty(attributeIdValueResps)) {
                val attrList = attributeIdValueResps.map { k ->
                    ProductAttributes().apply {
                        this.productId = product.productId
                        this.categoryId = product.categoryId
                        this.attributeId = k.attributeId
                        this.attributeValueId = k.attributeValueId
                        this.platformId = platformId
                    }
                }
                productAttributesRepository.saveBatch(attrList)
            }
        }
    }

    fun initStyleTypeAndImagePackageState(upsertProduct: Product) {
        if (upsertProduct.styleType == null) {
            try {
                val req = FillProductStyleTypeReq()
                req.productIds = listOf(upsertProduct.productId!!)
                fillStyleType(req)
            } catch (e: Exception) {
                log.error(e) { "同步商品款式类型失败 product_id: ${upsertProduct.productId}" }
            }
        }
        if (upsertProduct.imagePackageState == null) {
            try {
                val req = RefreshProductImagePackageStateReq()
                req.productIds = listOf(upsertProduct.productId!!)
                refreshImagePackageState(req)
            } catch (e: Exception) {
                log.error(e) { "同步商品图包状态失败 product_id: ${upsertProduct.productId}" }
            }
        }
    }


    /**
     * 补录商品的款式类型
     */
    fun fillStyleType(req: FillProductStyleTypeReq) {
        if (req.productIds.isNullOrEmpty()) {
            log.warn { "补录商品的款式类型失败，商品ID为空" }
            return
        }
        val updateProducts: MutableList<Product> = mutableListOf()
        val dbProducts: List<Product> = productRepository.listByIds(req.productIds)
        if (dbProducts.isNotEmpty()) {
            val spuCodeToProductMap = dbProducts.filter { it.spuCode.isNotBlank() }.groupBy { it.spuCode!! }
            if (spuCodeToProductMap.isNotEmpty()) {
                val reslut: Map<String, Int> = designClientExternal.getStyleTypeBySpuCodes(spuCodeToProductMap.keys.toList())
                spuCodeToProductMap.forEach { (spuCode, dbProducts) ->
                    val styleType = reslut[spuCode]
                    if (styleType.isNotNull() && SdpStyleTypeEnum.UNKNOWN.code != styleType) {
                        updateProducts.addAll(dbProducts.map { v ->
                            val updateProduct = Product()
                            updateProduct.productId = v.productId
                            updateProduct.styleType = SdpStyleTypeEnum.getByCode(styleType)?.code ?: SdpStyleTypeEnum.UNKNOWN.code
                            updateProduct
                        }.toList())
                    }
                }
            }
        }

        //更新
        if (updateProducts.isNotEmpty()) {
            productRepository.fillProductStyleType(updateProducts)
        }
    }

    /**
     * 刷新商品的图包状态
     */
    fun refreshImagePackageState(req: RefreshProductImagePackageStateReq) {
        if (req.productIds.isNullOrEmpty()) {
            log.warn { "刷新商品的图包状态失败，商品ID为空" }
            return
        }
        val dbProducts: List<Product> = productRepository.listByIds(req.productIds)
        if (dbProducts.isNotEmpty()) {
            val updateProducts: MutableList<Product> = mutableListOf()
            val spuCodeToProductMap = dbProducts.filter { it.spuCode.isNotBlank() }.groupBy { it.spuCode!! }
            if (spuCodeToProductMap.isNotEmpty()) {
                val reslut: Map<String, Int> = designClientExternal.getImagePackageStateBySpuCodes(spuCodeToProductMap.keys.toList())
                /**
                 * -未完成：无图包信息，无图包版本信息
                 *
                 * -更新中：款式管理的视觉任务进度状态="进行中"
                 *
                 * -已更新：有图包版本信息&提交状态=已提交&最新版本未被应用到上架
                 *
                 * -已完成：以下2种情况均为已完成
                 *
                 * -a.有图包版本信息&提交状态=未提交
                 *
                 * -b.有图包版本信息&提交状态=已提交&最新版本已被应用上架
                 */
                spuCodeToProductMap.forEach { (spuCode, dbProducts) ->
                    //POP端没有图包，则未完成
                    val imageRepository = imageRepositoryRepository.getBySpuCode(spuCode)
                    if (imageRepository == null) {
                        updateProducts.addAll(dbProducts.map { v ->
                            val updateProduct = Product()
                            updateProduct.productId = v.productId
                            updateProduct.imagePackageState = ProductImagePackageStateEnum.INCOMPLETE.code
                            updateProduct
                        }.toList())
                    }
                    //视觉那边有图包处理中
                    else if (reslut[spuCode] == ProductImagePackageStateEnum.UPDATING.code) {
                        updateProducts.addAll(dbProducts.map { v ->
                            val updateProduct = Product()
                            updateProduct.productId = v.productId
                            updateProduct.imagePackageState = ProductImagePackageStateEnum.UPDATING.code
                            updateProduct
                        }.toList())
                    } else {
                        updateProducts.addAll(dbProducts.map { v ->
                            //已上架,但上架时图包版本，跟最新的图包版本不一致
                            if (v.isSyncPlatform == Bool.YES.code) {
                                if (v.imageVersionNum.isNull() || imageRepository.versionNum != v.imageVersionNum) {
                                    val updateProduct = Product()
                                    updateProduct.productId = v.productId
                                    updateProduct.imagePackageState = ProductImagePackageStateEnum.UPDATED.code
                                    updateProduct
                                } else {
                                    val updateProduct = Product()
                                    updateProduct.productId = v.productId
                                    updateProduct.imagePackageState = ProductImagePackageStateEnum.COMPLETED.code
                                    updateProduct
                                }
                            }
                            //不是已上架状态，有图包，图包状态为已完成
                            else {
                                val updateProduct = Product()
                                updateProduct.productId = v.productId
                                updateProduct.imagePackageState = ProductImagePackageStateEnum.COMPLETED.code
                                updateProduct
                            }
                        }.toList())
                    }
                }
            }

            //更新
            if (updateProducts.isNotEmpty()) {
                productRepository.refreshImagePackageState(updateProducts)
            }
        }
    }
}