package tech.tiangong.pop.component

import team.aikero.blade.logging.core.annotation.Slf4j
import org.redisson.api.RedissonClient
import org.springframework.stereotype.Component
import java.time.Duration
import java.time.LocalDate
import java.time.ZoneId
import java.util.*
import java.util.function.LongUnaryOperator

/**
 * 生成流水号
 * <AUTHOR>
 * @date 2024/11/15 11:38
 */
@Slf4j
@Component
class SerialNumComponent(val redissonClient: RedissonClient) {
    fun generateTaskCode(keyName: String?, taskCode: String, length: Int): String {
        return generateTaskCode(keyName, taskCode, length, null)
    }

    fun generateTaskCode(keyName: String?, taskCode: String, length: Int, updateFunction: LongUnaryOperator?): String {
        if (Objects.nonNull(updateFunction)) {
            return taskCode + String.format("%0" + length + "d", getSerialNumber(keyName, updateFunction))
        }
        return taskCode + String.format("%0" + length + "d", getSerialNumber(keyName))
    }

    fun getSerialNumber(keyName: String?): Int {
        //改用redissonClient方式
        val rAtomicLong = redissonClient.getAtomicLong(keyName)
        rAtomicLong.expire(Duration.ofSeconds(getEveryDayTime()))
        return rAtomicLong.incrementAndGet().toInt()
    }

    fun getSerialNumber(keyName: String?, updateFunction: LongUnaryOperator?): Int {
        //改用redissonClient方式
        val rAtomicLong = redissonClient.getAtomicLong(keyName)
        rAtomicLong.expire(Duration.ofSeconds(getEveryDayTime()))
        var previousValue: Long
        var newValue: Long
        do {
            previousValue = redissonClient.getAtomicLong(keyName).get()
            newValue = updateFunction!!.applyAsLong(previousValue)
        } while (!rAtomicLong.compareAndSet(previousValue, newValue))

        return newValue.toInt()
    }

    /**
     * 获取每一天的00:00的毫秒数
     *
     * @return
     */
    fun getEveryDayTime(): Long {
        val localDate = LocalDate.now()
        val localDateTime = localDate.atStartOfDay()
        return localDateTime.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli()
    }
}