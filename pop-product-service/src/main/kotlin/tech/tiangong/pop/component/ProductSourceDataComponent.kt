package tech.tiangong.pop.component

import org.springframework.stereotype.Component
import team.aikero.blade.logging.core.annotation.Slf4j
import tech.tiangong.pop.common.enums.PlatformEnum
import tech.tiangong.pop.common.enums.ProductUpdateTypeEnum
import tech.tiangong.pop.service.product.update.ProductUpdateSourceDataServiceSelector

/**
 * 商品来源数据组件
 */
@Slf4j
@Component
class ProductSourceDataComponent(
    private val productUpdateSourceDataServiceSelector: ProductUpdateSourceDataServiceSelector,
) {

    /**
     * 保存商品来源数据
     * @param productId
     */
    fun saveProductSourceData(updateTypeEnum: ProductUpdateTypeEnum, data: String, productId: Long) {
        PlatformEnum.entries.forEach { entry ->
            productUpdateSourceDataServiceSelector.getSourceDataHandler(entry)?.saveData(updateTypeEnum, data, productId)
        }
    }
}