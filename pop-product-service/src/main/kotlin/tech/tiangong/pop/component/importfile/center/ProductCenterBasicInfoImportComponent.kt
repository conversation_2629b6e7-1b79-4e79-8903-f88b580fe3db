package tech.tiangong.pop.component.importfile.center

import com.alibaba.excel.EasyExcel
import com.alibaba.excel.read.listener.PageReadListener
import org.apache.commons.collections4.CollectionUtils
import org.springframework.stereotype.Component
import org.springframework.web.multipart.MultipartFile
import team.aikero.blade.core.toolkit.isBlank
import team.aikero.blade.core.toolkit.isNotBlank
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.sequence.id.IdHelper
import team.aikero.blade.util.json.parseJson
import team.aikero.blade.util.json.toJson
import tech.tiangong.pop.common.enums.PrintTypeEnum
import tech.tiangong.pop.common.exception.BaseBizException
import tech.tiangong.pop.component.MarketStyleComponent
import tech.tiangong.pop.dao.entity.ImportProductRecord
import tech.tiangong.pop.dao.entity.Product
import tech.tiangong.pop.dao.entity.PublishCategory
import tech.tiangong.pop.dao.repository.ImportProductRecordRepository
import tech.tiangong.pop.dao.repository.ProductRepository
import tech.tiangong.pop.dao.repository.PublishCategoryRepository
import tech.tiangong.pop.dao.repository.ShopRepository
import tech.tiangong.pop.dto.export.ProductCenterBasicInfoExportDto
import tech.tiangong.pop.enums.DictEnum
import tech.tiangong.pop.enums.ImportDataTypeEnum
import tech.tiangong.pop.enums.ImportSourceEnum
import tech.tiangong.pop.external.DictClientExternal

/**
 * 商品中心-商品基本信息导入组件
 * 负责导入商品中心的商品基本信息数据
 */
@Component
@Slf4j
class ProductCenterBasicInfoImportComponent(
    private val importProductRecordRepository: ImportProductRecordRepository,
    private val productRepository: ProductRepository,
    private val shopRepository: ShopRepository,
    private val publishCategoryRepository: PublishCategoryRepository,
    private val dictClientExternal: DictClientExternal,
    private val marketStyleComponent: MarketStyleComponent,
) {

    /**
     * 导入
     */
    fun importExcel(file: MultipartFile) {
        val importDataList = mutableListOf<ProductCenterBasicInfoExportDto>()

        // 读取Excel并映射到UserData实体类
        EasyExcel.read(
            file.inputStream,
            ProductCenterBasicInfoExportDto::class.java,
            PageReadListener { dataList ->
                log.info { "读取到${dataList.size}条数据" }
                dataList.forEach { user ->
                    importDataList.add(user)
                }
            }
        ).sheet() // 读取第一个sheet
            .doRead()

        val importList = importDataList
            .asSequence()
            .filter { it.spuCode.isNotBlank() }
            .filter { it.categoryName.isNotBlank() }
            .toList()
        if (CollectionUtils.isEmpty(importList)) {
            throw BaseBizException("导入数据为空")
        }

        // 创建导入任务
        val list = importList.map {
            ImportProductRecord().apply {
                this.spuCode = it.spuCode
                this.importDataType = ImportDataTypeEnum.PRODUCT_BASIC_INFO.code
                this.importSource = ImportSourceEnum.PRODUCT_CENTER.code
                this.importData = it.toJson()
            }
        }
        importProductRecordRepository.saveBatch(list, 100)
    }

    /**
     * 执行导入逻辑
     */
    fun doImport(importData: String) {
        if (importData.isBlank()) {
            throw BaseBizException("解析json失败: $importData")
        }
        val data = importData.parseJson<ProductCenterBasicInfoExportDto>()
        val productList = productRepository.listBySpuCodes(listOf(data.spuCode!!))
        if (productList.isNotEmpty()) {
            // 存在: 更新
            productList.forEach { product ->
                val updateProduct = Product().apply {
                    this.productId = product.productId
                }
                val isUpdate = buildProduct(data, product, updateProduct)
                if (isUpdate) {
                    productRepository.updateById(updateProduct)
                }
            }
        } else {
            // 不存在: 新增
            val saveProduct = Product().apply {
                this.productId = IdHelper.getId()
                this.spuCode = data.spuCode
                this.buyerName = data.buyerName

                // 品类
                if (data.categoryName.isNotBlank()) {
                    val category = getCategory(data.categoryName!!)
                    if (category != null) {
                        this.categoryId = category.publishCategoryId
                        this.categoryCode = category.cateoryCode
                        this.categoryName = data.categoryName
                    }
                }
            }
            buildProduct(data, null, saveProduct)
            productRepository.save(saveProduct)
        }
    }

    /**
     * 根据完成品类名找到对应的品类
     * 如: 女装>上装类>T恤找到对应的T恤品类
     */
    private fun getCategory(categoryName: String): PublishCategory? {
        val categoryNameList = categoryName.split(">")
        // 按顺序查询
        var parentId: Long = 0
        var lastCategory: PublishCategory? = null
        for (name in categoryNameList) {
            val category = publishCategoryRepository.getByParentIdAndCategoryName(parentId, name)
            if (category != null) {
                parentId = category.publishCategoryId!!
                lastCategory = category
            }
        }
        return lastCategory
    }

    /**
     * 构建product
     * @param data 导入数据
     * @param product 已有的product
     * @param updateProduct 更新的product
     * @return 是否更新成功
     */
    private fun buildProduct(data: ProductCenterBasicInfoExportDto, product: Product?, updateProduct: Product): Boolean {
        var isUpdate = false
        if (data.brandName.isNotBlank() && product?.brandName != data.brandName) {
            updateProduct.brandName = data.brandName
            isUpdate = true
        }
        if (data.waves.isNotBlank() && product?.waves != data.waves) {
            updateProduct.waves = data.waves
            isUpdate = true
        }
        if (data.goodsRepType.isNotBlank() && product?.goodsRepType != data.goodsRepType) {
            updateProduct.goodsRepType = data.goodsRepType
            isUpdate = true
        }
        if (data.clothingStyleName.isNotBlank() && product?.clothingStyleName != data.clothingStyleName) {
            val marketStyleMap = marketStyleComponent.getMarketNodeMap(3)
            marketStyleMap?.forEach { (code, name) ->
                if (name == data.clothingStyleName) {
                    updateProduct.clothingStyleCode = code
                    updateProduct.clothingStyleName = data.clothingStyleName
                    isUpdate = true
                }
            }
        }
        if (data.weaveMode.isNotBlank() && product?.weaveMode != data.weaveMode) {
            val dictData = dictClientExternal.getTopByDictCode(DictEnum.APS_CATEGORY_TYPE)
            if (dictData != null) {
                val dict = dictData
                    .children?.find { it.dictName == data.weaveMode }
                if (dict != null) {
                    updateProduct.weaveModeCode = dict.dictCode
                    updateProduct.weaveMode = data.weaveMode
                    isUpdate = true
                }
            }
        }
        if (data.market.isNotBlank()) {
            val marketStyleMap = marketStyleComponent.getMarketNodeMap(1)
            marketStyleMap?.forEach { (code, name) ->
                if (name == data.clothingStyleName) {
                    updateProduct.marketCode = code
                    isUpdate = true
                }
            }
        }

        if (data.seasonName.isNotBlank()) {
//                    updateProduct.seasonName = data.seasonName          // TODO 新字段
            isUpdate = true
        }

        if (data.targetAudience.isNotBlank()) {
//                    updateProduct.targetAudience = data.targetAudience          // TODO 新字段
            isUpdate = true
        }
        if (data.sceneName.isNotBlank()) {
            val dictData = dictClientExternal.getTopByDictCode(DictEnum.APS_CATEGORY_TYPE)
            if (dictData != null) {
                val dict = dictData
                    .children?.find { it.dictName == data.weaveMode }
                if (dict != null) {
                    updateProduct.sceneCode = dict.dictCode
                    updateProduct.sceneName = data.sceneName
                    isUpdate = true
                }
            }
        }
        if (data.countryName.isNotBlank() && product?.countrys != data.countryName) {
            updateProduct.countrys = data.countryName
            isUpdate = true
        }
        if (data.shopName.isNotBlank() && product?.shopName != data.shopName) {
            // 店铺
            if (data.shopName.isNotBlank()) {
                val shop = shopRepository.getByShopName(data.shopName!!)
                if (shop != null) {
                    updateProduct.shopId = shop.shopId
                    updateProduct.shopName = shop.shopName
                }
            }
            isUpdate = true
        }
        if (data.prototypeNum.isNotBlank() && product?.prototypeNum != data.prototypeNum) {
            updateProduct.prototypeNum = data.prototypeNum
            isUpdate = true
        }
        if (data.printType.isNotBlank() && product?.printType != data.printType) {
            updateProduct.printType = data.printType?.let { PrintTypeEnum.fromDesc(it) }?.code
            isUpdate = true
        }
        if (data.productTitle.isNotBlank() && product?.spuName != data.productTitle) {
            updateProduct.spuName = data.productTitle
            isUpdate = true
        }
        if (data.productTitleEn.isNotBlank() && product?.spuNameTrans != data.productTitleEn) {
            updateProduct.spuNameTrans = data.productTitleEn
            isUpdate = true
        }
        if (data.productLink.isNotBlank() && product?.productLink != data.productLink) {
            updateProduct.productLink = data.productLink
            isUpdate = true
        }
        return isUpdate
    }
}
