package tech.tiangong.pop.component

import org.springframework.stereotype.Component
import team.aikero.blade.core.enums.Bool
import team.aikero.blade.logging.core.annotation.Slf4j
import tech.tiangong.pop.enums.DictEnum
import tech.tiangong.pop.external.DictClientExternal

/**
 * @Author: caicijie
 * @description:
 * @Date: 2025/6/25 17:38
 */
@Component
@Slf4j
class MarketStyleComponent(
    private val dictClientExternal: DictClientExternal,
) {
    /**
     * 根据层级获取市场相关字典的键值映射
     * @param level 节点层级：1 表示一级（市场），2 表示二级（系列），3 表示三级（风格）
     * @return Map<dictCode, dictName>
     */
    fun getMarketNodeMap(level: Int): Map<String, String>? {
        val marketDictVo = dictClientExternal.getTopByDictCode(DictEnum.MARKET_STYLE) ?: return null
        val children = marketDictVo.children ?: return null

        return when (level) {
            1 -> {
                // 一级节点：市场
                children.filter { parent -> parent.state == Bool.YES.code }
                    .associateBy({ it.dictCode }, { it.dictName })
            }

            2 -> {
                // 二级节点：市场-系列
                children.filter { parent -> parent.state == Bool.YES.code }.flatMap { parent ->
                    parent.children.orEmpty().filter { child -> child.state == Bool.YES.code }.map { child ->
                        child.dictCode to child.dictName
                    }
                }.toMap()
            }

            3 -> {
                // 三级节点：市场-系列-风格
                children.filter { parent -> parent.state == Bool.YES.code }.flatMap { parent ->
                    parent.children.orEmpty().filter { child -> child.state == Bool.YES.code }.flatMap { child ->
                        child.children.orEmpty().filter { style -> style.state == Bool.YES.code }.map { style ->
                            style.dictCode to style.dictName
                        }
                    }
                }.toMap()
            }

            else -> throw IllegalArgumentException("不支持的层级：$level")
        }
    }

    fun getMarketNameMap(level: Int): Map<String, String> {
        val marketDictVo = dictClientExternal.getTopByDictCode(DictEnum.MARKET_STYLE)
        if (marketDictVo == null || marketDictVo.children == null) {
            return emptyMap()
        }

        val result = mutableMapOf<String, String>()
        val children = marketDictVo.children

        when (level) {
            1 -> {
                children?.forEach { child ->
                    result[child.dictName] = child.dictCode
                }
                return result
            }

            2 -> {
                children?.forEach { parent ->
                    parent.children?.forEach { child ->
                        result["${parent.dictName}-${child.dictName}"] = child.dictCode
                    }
                }
                return result
            }

            3 -> {
                children?.forEach { parent ->
                    parent.children?.forEach { child ->
                        child.children?.forEach { series ->
                            result["${parent.dictName}-${child.dictName}-${series.dictName}"] = series.dictCode
                        }
                    }
                }
                return result
            }

            else -> throw IllegalArgumentException("不支持的层级：$level")
        }

    }
}