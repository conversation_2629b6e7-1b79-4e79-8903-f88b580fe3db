package tech.tiangong.pop.component.export.center

import com.alibaba.excel.EasyExcel
import com.alibaba.excel.annotation.ExcelProperty
import com.baomidou.mybatisplus.extension.plugins.pagination.Page
import com.google.common.net.MediaType
import org.springframework.stereotype.Component
import team.aikero.blade.core.exception.BusinessException
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.util.json.parseJson
import team.aikero.blade.util.json.toJson
import tech.tiangong.pop.component.export.DownloadTaskInterface
import tech.tiangong.pop.dao.entity.DownloadTask
import tech.tiangong.pop.dao.entity.Product
import tech.tiangong.pop.dao.entity.ProductAttributesV2
import tech.tiangong.pop.dao.repository.ProductAttributesV2Repository
import tech.tiangong.pop.dao.repository.ProductRepository
import tech.tiangong.pop.dao.repository.PublishAttributeRepository
import tech.tiangong.pop.dao.repository.PublishAttributeValueRepository
import tech.tiangong.pop.dto.FileUploadDTO
import tech.tiangong.pop.dto.export.ProductCenterPlatformAttrExportDto
import tech.tiangong.pop.enums.DownloadTaskTypeEnum
import tech.tiangong.pop.helper.UploaderOssHelper
import tech.tiangong.pop.req.product.center.ProductCenterPageReq
import tech.tiangong.pop.service.settings.DownloadTaskService
import tech.tiangong.pop.utils.getRootMessage
import java.io.File
import java.io.IOException
import java.time.LocalDateTime
import java.time.ZoneId
import java.time.format.DateTimeFormatter
import java.util.*

/**
 * 商品中心-平台属性导出组件
 * 负责导出商品中心的平台属性数据
 */
@Component
@Slf4j
class ProductCenterPlatformAttrExportComponent(
    private val productRepository: ProductRepository,
    private val downloadTaskService: DownloadTaskService,
    private val uploaderOssHelper: UploaderOssHelper,
    private val productAttributesV2Repository: ProductAttributesV2Repository,
    private val publishAttributeRepository: PublishAttributeRepository,
    private val publishAttributeValueRepository: PublishAttributeValueRepository,
) : DownloadTaskInterface() {

    /**
     * 创建导出任务
     */
    fun createExportTask(req: ProductCenterPageReq) {
        try {
            val pageNum: Long = 1
            val pageSize: Long = 1000
            val page = productRepository.getCenterPage(Page(pageNum, pageSize), req)

            if (page.records.isEmpty()) {
                throw BusinessException("暂无数据")
            }
            // 创建下载任务
            val dateStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss", Locale.getDefault()).withZone(ZoneId.systemDefault()))
            val taskName = "导出商品中心_平台属性_$dateStr"
            downloadTaskService.createTask(
                taskName,
                DownloadTaskTypeEnum.PRODUCT_CENTER_PLATFORM_ATTR_TASK,
                req.toJson()
            );
            log.info { "导出商品中心_平台属性 创建下载任务，任务名称：${taskName}" }
        } catch (e: Exception) {
            log.error(e) { "导出商品中心_平台属性-创建下载任务, 异常，请求参数：${req.toJson()}" }
            throw BusinessException("导出商品中心_平台属性_任务异常：" + e.getRootMessage());
        }
    }

    /**
     * 生成Excel文件
     */
    override fun export(reqStr: String?, tempDir: File, task: DownloadTask): FileUploadDTO {
        val req = reqStr?.parseJson<ProductCenterPageReq>() ?: throw BusinessException("导出任务逻辑-参数为空 task=${task.toJson()}")
        var pageNum: Long = 1
        val pageSize: Long = 1000

        val productList = mutableListOf<Product>()

        while (true) {
            val page = productRepository.getCenterPage(Page(pageNum, pageSize), req)
            if (page.records.isEmpty()) {
                break
            }
            pageNum++

            productList.addAll(page.records)
        }

        try {
            // 创建Excel文件
            val excelFile = File(tempDir, task.taskName + ".xlsx")
            // 构建Excel数据
            val dataList = buildExcelData(productList)
            // 设置Excel数据
            setFileData(dataList, excelFile)

            return uploaderOssHelper.createFileUploadDTO(excelFile, MediaType.MICROSOFT_EXCEL.type())
        } catch (e: IOException) {
            log.error(e) { "导出失败:" }
            throw BusinessException("导出失败")
        }
    }

    /**
     * 构建Excel数据
     */
    fun buildExcelData(productList: List<Product>): List<ProductCenterPlatformAttrExportDto> {

        val dataList = mutableListOf<ProductCenterPlatformAttrExportDto>()
        productList.forEach { product ->
            // 提取属性
            val attrList = productAttributesV2Repository.listByProductId(product.productId!!) ?: emptyList()
            val attrListMap = convertAttrsToMap(attrList)

            dataList.add(ProductCenterPlatformAttrExportDto().apply {
                this.spuCode = product.spuCode
                this.category = product.categoryName
                // 组装动态属性
                this.dynamicProperties = attrListMap
            })
        }
        return dataList
    }

    /**
     * 数据设置到File
     */
    fun setFileData(dataList: List<ProductCenterPlatformAttrExportDto>, excelFile: File) {
        // 1. 构建表头（通过注解获取固定列 + 动态列）
        val head = buildHead(dataList)
        // 2. 转换数据格式
        val exportData = convertData(dataList, head)
        // 3. 导出Excel
        EasyExcel.write(excelFile)
            .head(head)
            .sheet("平台属性")
            .doWrite(exportData)
    }

    /**
     * 构建表头：固定列（通过@ExcelProperty获取） + 动态列（平台属性-前缀）
     */
    fun buildHead(dataList: List<ProductCenterPlatformAttrExportDto>): List<List<String>> {
        val head = mutableListOf<List<String>>()

        // 1. 添加固定列表头（通过反射读取@ExcelProperty注解）
        val fixedFields = ProductCenterPlatformAttrExportDto::class.java.declaredFields
            .filter { it.isAnnotationPresent(ExcelProperty::class.java) }
            .sortedBy { it.getAnnotation(ExcelProperty::class.java).index } // 按index排序

        fixedFields.forEach { field ->
            val excelProperty = field.getAnnotation(ExcelProperty::class.java)
            head.add(excelProperty.value.toList()) // 添加注解中定义的列名
        }

        // 2. 收集所有动态列（去重，带"平台属性-"前缀）
        val dynamicColumns = mutableSetOf<String>()
        dataList.forEach { dto ->
            dto.dynamicProperties.forEach { (key, _) ->
                dynamicColumns.add("平台属性-$key")
            }
        }

        // 3. 添加动态列表头（按自然顺序排序）
        dynamicColumns.sorted().forEach { columnName ->
            head.add(listOf(columnName))
        }

        return head
    }

    /**
     * 转换数据格式：固定字段值 + 动态字段值
     */
    fun convertData(
        dataList: List<ProductCenterPlatformAttrExportDto>,
        head: List<List<String>>,
    ): List<List<Any?>> {
        // 获取固定字段（带@ExcelProperty注解的字段）
        val fixedFields = ProductCenterPlatformAttrExportDto::class.java.declaredFields
            .filter { it.isAnnotationPresent(ExcelProperty::class.java) }
            .sortedBy { it.getAnnotation(ExcelProperty::class.java).index }
            .onEach { it.isAccessible = true } // 允许访问私有字段

        // 提取动态列名（排除固定列）
        val dynamicColumnNames = head.subList(fixedFields.size, head.size).map { it[0] }

        return dataList.map { dto ->
            val rowData = mutableListOf<Any?>()

            // 1. 添加固定字段值（通过反射获取字段值）
            fixedFields.forEach { field ->
                rowData.add(field.get(dto))
            }

            // 2. 添加动态字段值
            dynamicColumnNames.forEach { columnName ->
                val key = columnName.substringAfter("平台属性-")
                rowData.add(dto.dynamicProperties[key] ?: "")
            }

            rowData
        }
    }

    /**
     * 转换属性为map
     */
    private fun convertAttrsToMap(attrs: List<ProductAttributesV2>): Map<String, String> {
        val map = mutableMapOf<String, String>()
        attrs.forEach { attr ->
            val attrGroup = publishAttributeRepository.getById(attr.attributeId)
            val attrValue = publishAttributeValueRepository.getById(attr.attributeValueId)
            val value = "${attr.attributeValue} ${attr.unit}"
            map[attrGroup.attributeName!!] = value.ifBlank { attrValue.attributeValue ?: "" }
        }
        return map
    }
}

