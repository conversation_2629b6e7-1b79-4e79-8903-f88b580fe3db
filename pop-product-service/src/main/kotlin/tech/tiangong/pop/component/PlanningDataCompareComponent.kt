package tech.tiangong.pop.component

import org.apache.commons.collections4.CollectionUtils
import org.springframework.stereotype.Component
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.util.json.toJson
import tech.tiangong.pop.common.enums.YesOrNoEnum
import tech.tiangong.pop.dao.entity.CargoTrayInfo
import tech.tiangong.pop.dao.entity.PlanningInfo
import tech.tiangong.pop.dao.entity.PlanningSummary
import tech.tiangong.pop.dao.repository.PlanningCategoryPriceRepository
import tech.tiangong.pop.dao.repository.PlanningDevelopRhythmRepository
import tech.tiangong.pop.dao.repository.PlanningPublishRhythmRepository
import tech.tiangong.pop.enums.SupplyModeEnum
import tech.tiangong.pop.req.planning.CargoTrayDataItem
import tech.tiangong.pop.req.planning.PlanningSummaryCreateReq
import tech.tiangong.pop.req.planning.PlanningSummaryShopDataItem

/**
 * 企划数据比较
 * <AUTHOR>
 * @date 2024/12/12 15:30
 */
@Component
@Slf4j
class PlanningDataCompareComponent(
    private val planningDevelopRhythmRepository: PlanningDevelopRhythmRepository,
    private val planningPublishRhythmRepository: PlanningPublishRhythmRepository,
    private val planningCategoryPriceRepository: PlanningCategoryPriceRepository,
) {

    /**
     * 比较企划信息
     *
     * @param req
     * @param oldPlanning
     * @return
     */
    fun comparePlanningInfo(req: PlanningSummaryCreateReq, oldPlanning: PlanningInfo): Pair<Boolean, PlanningInfo> {
        var isUpdate = false
        val planningInfoNew = PlanningInfo().apply {
            this.planningId = oldPlanning.planningId
            this.planningName = checkCompare(req.planningName, oldPlanning.planningName) {
                isUpdate = true
            }
            this.channelId = checkCompare(req.channelId, oldPlanning.channelId) { isUpdate = true }
            this.channelName = checkCompare(req.channelName, oldPlanning.channelName) { isUpdate = true }
            this.platformId = checkCompare(req.platformId, oldPlanning.platformId) { isUpdate = true }
            this.platformName = checkCompare(req.platformName, oldPlanning.platformName) { isUpdate = true }
            this.applicableMonth = checkCompare(req.applicableMonth, oldPlanning.applicableMonth) { isUpdate = true }
        }
        if (!isUpdate) {
            log.info { "[comparePlanningInfo]无更新" }
        }
        return Pair(isUpdate, planningInfoNew)
    }

    /**
     * 比较企划汇总-包含删除逻辑
     *
     * @param planningId
     * @param req
     * @param old
     * @return
     */
    fun compareDeletedPlanningSummary(planningId: Long, req: MutableList<PlanningSummaryShopDataItem>?, old: List<PlanningSummary>): Pair<Boolean, MutableList<PlanningSummary>> {
        if (req.isNullOrEmpty()) {
            return Pair(false, mutableListOf())
        }
        val planningSummaryReqList = mutableListOf<PlanningSummary>()
        req.forEach {
            it.supplyModeData?.forEach { item ->
                item.countrySiteData?.forEach { countrySiteItem ->
                    val planningSummary = PlanningSummary()
                    planningSummary.planningId = planningId
                    planningSummary.shopId = it.shopId
                    planningSummary.shopName = it.shopName
                    planningSummary.countrySiteCode = countrySiteItem.countrySiteCode
                    planningSummary.countrySiteName = countrySiteItem.countrySiteName
                    planningSummary.supplyModeCode = item.supplyMode
                    planningSummary.supplyModeName = SupplyModeEnum.getByCode(item.supplyMode)?.desc
                    planningSummary.supplyQuantity = countrySiteItem.supplyQuantity
                    planningSummary.deleted = YesOrNoEnum.NO.code
                    planningSummaryReqList.add(planningSummary)
                }
            }
        }
        val planningSummaryOldList = old.map {
            val planningSummary = PlanningSummary()
            planningSummary.planningId = it.planningId
            planningSummary.shopId = it.shopId
            planningSummary.shopName = it.shopName
            planningSummary.countrySiteCode = it.countrySiteCode
            planningSummary.countrySiteName = it.countrySiteName
            planningSummary.supplyModeCode = it.supplyModeCode
            planningSummary.supplyModeName = it.supplyModeName
            planningSummary.supplyQuantity = it.supplyQuantity
            planningSummary.deleted = it.deleted
            planningSummary
        }
        val isUpdate = planningSummaryReqList.toJson() != planningSummaryOldList.toJson()
        if (!isUpdate) {
            log.info { "[comparePlanningSummary]无更新" }
            return Pair(isUpdate, planningSummaryReqList)
        }

        /*
          注意: 汇总数据更新需要清空所有tab数据
          1、店铺A和B，新增店铺C，不影响
          2、店铺A和B，删除店铺A，对应店铺A的开发节奏、上架节奏、品类数据都清空,店铺B数据不影响
          3、店铺A和B，店铺A(TH和MY)，删除站点MY，只清空店铺A对应站点MY的数据：开发节奏、上架节奏、品类，店铺A的TH站点和店铺B的数据不影响
          4、店铺A和B，店铺A(TH和MY)，修改店铺A站点MY的相关数量，只清空店铺A站点MY的开发节奏数据，上架节奏和品类保持不变，店铺A的TH站点和店铺B的数据不影响
          5、店铺A和B，新增站点，不影响

          删除店铺: 删除店铺对应的所有数据
          删除站点: 清空店铺+站点对应的所有数据
          更新数量: 清空店铺+站点对应的开发节奏数据
           */

        // new转为map key=shop
        val newShopMap = planningSummaryReqList.groupBy { it.shopId }
        // old转为map key=shop
        val oldShopMap = planningSummaryOldList.groupBy { it.shopId }

        // 以old为基准，匹配new: 如果new不存在, 即删除, 需要删除其他TAB的店铺相关数据
        oldShopMap.keys.forEach { shopId ->
            val newList = newShopMap[shopId]
            if (newList.isNullOrEmpty()) {
                // 注意: 汇总数据更新需要清空所有tab数据
                planningDevelopRhythmRepository.deleteByPlanningIdPhysical(planningId, shopId)
                planningPublishRhythmRepository.deleteByPlanningIdPhysical(planningId, shopId)
                planningCategoryPriceRepository.deleteByPlanningIdPhysical(planningId, shopId)
            }
        }
        // 以new为基准，匹配old: 如果old不存在, 即新增, 无需操作; 如果更新则下一步处理
        newShopMap.forEach { (shopId, newList) ->
            val oldList = oldShopMap[shopId]
            if (oldList.isNullOrEmpty()) {
                // 新增, 不处理
                return@forEach
            }
            // 判断站点维度
            // new转为map key=countrySiteCode
            val newCountrySiteCodeMap = newList.groupBy { it.countrySiteCode }
            // old转为map key=countrySiteCode
            val oldCountrySiteCodeMap = oldList.groupBy { it.countrySiteCode }

            // 以old为基准，匹配new: 如果new不存在, 即删除, 需要删除其他TAB的店铺相关数据
            oldCountrySiteCodeMap.keys.forEach { countrySiteCode ->
                val newCountrySiteCodeList = newCountrySiteCodeMap[countrySiteCode]
                if (newCountrySiteCodeList.isNullOrEmpty()) {
                    // 注意: 汇总数据更新需要清空所有tab数据
                    planningDevelopRhythmRepository.deleteByPlanningIdPhysical(planningId, shopId, countrySiteCode)
                    planningPublishRhythmRepository.deleteByPlanningIdPhysical(planningId, shopId, countrySiteCode)
                    planningCategoryPriceRepository.deleteByPlanningIdPhysical(planningId, shopId, countrySiteCode)
                }
            }

            // 以new为基准，匹配old: 如果old不存在, 即新增, 无需操作; 如果更新则下一步处理
            newCountrySiteCodeMap.forEach { (countrySiteCode, newList) ->
                val oldCountrySiteCodeList = oldCountrySiteCodeMap[countrySiteCode]
                if (oldCountrySiteCodeList.isNullOrEmpty()) {
                    // 新增, 不处理
                    return@forEach
                }

                // 比较同店铺同站点的更新, 只是改变数量, 才删除对应的开发节奏数据
                if (CollectionUtils.isNotEmpty(newList) && CollectionUtils.isNotEmpty(oldCountrySiteCodeList)) {
                    val countrySiteCodeUpdate = newList.toJson() != oldCountrySiteCodeList.toJson()
                    if (countrySiteCodeUpdate) {
                        // 店铺+站点维度数据有更新(非新增和删除), 删除对应的开发节奏数据
                        planningDevelopRhythmRepository.deleteByPlanningIdPhysical(planningId, shopId, countrySiteCode)
                    }
                }
            }
        }
        return Pair(isUpdate, planningSummaryReqList)
    }

    /**
     * 比较货盘(字段调整必须2个list赋值一起改)
     *
     * @param planningId
     * @param req
     * @param old
     * @return
     */
    fun comparePlanningCargoTray(planningId: Long, req: MutableList<CargoTrayDataItem>?, old: List<CargoTrayInfo>): Pair<Boolean, MutableList<CargoTrayInfo>> {
        if (req.isNullOrEmpty()) {
            return Pair(false, mutableListOf())
        }
        val cargoTrayDataList = mutableListOf<CargoTrayInfo>()
        req.forEach {
            val cargoTrayInfo = CargoTrayInfo()
            cargoTrayInfo.planningId = planningId
            cargoTrayInfo.cargoTrayCode = it.cargoTrayCode
            cargoTrayInfo.quantity = it.quantity
            cargoTrayDataList.add(cargoTrayInfo)
        }
        val cargoTrayInfoListOld = old.map {
            val cargoTrayInfo = CargoTrayInfo()
            cargoTrayInfo.planningId = it.planningId
            cargoTrayInfo.cargoTrayCode = it.cargoTrayCode
            cargoTrayInfo.quantity = it.quantity
            cargoTrayInfo
        }
        val isUpdate = cargoTrayDataList.toJson() != cargoTrayInfoListOld.toJson()
        if (!isUpdate) {
            log.info { "[comparePlanningCargoTray]无更新" }
        }
        return Pair(isUpdate, cargoTrayDataList)
    }

    /**
     * 比较
     *
     * @param T
     * @param newValue
     * @param oldValue
     * @param runnable
     * @return
     */
    private fun <T> checkCompare(newValue: T?, oldValue: T?, runnable: Runnable): T? {
        var result = oldValue
        if (newValue != oldValue) {
            result = newValue
            runnable.run()
        }
        return result
    }
}