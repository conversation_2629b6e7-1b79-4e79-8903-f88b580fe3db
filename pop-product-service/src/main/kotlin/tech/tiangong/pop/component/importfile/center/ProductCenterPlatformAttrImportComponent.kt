package tech.tiangong.pop.component.importfile.center

import com.alibaba.excel.EasyExcel
import com.alibaba.excel.read.listener.PageReadListener
import org.apache.commons.collections4.CollectionUtils
import org.springframework.stereotype.Component
import org.springframework.web.multipart.MultipartFile
import team.aikero.blade.core.enums.Bool
import team.aikero.blade.core.toolkit.isBlank
import team.aikero.blade.core.toolkit.isNotBlank
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.sequence.id.IdHelper
import team.aikero.blade.util.json.parseJson
import team.aikero.blade.util.json.toJson
import tech.tiangong.pop.common.exception.BaseBizException
import tech.tiangong.pop.dao.entity.ImportProductRecord
import tech.tiangong.pop.dao.entity.ProductAttributesV2
import tech.tiangong.pop.dao.entity.PublishAttribute
import tech.tiangong.pop.dao.entity.PublishAttributeValue
import tech.tiangong.pop.dao.repository.*
import tech.tiangong.pop.dto.export.ProductCenterPlatformAttrExportDto
import tech.tiangong.pop.enums.ImportDataTypeEnum
import tech.tiangong.pop.enums.ImportSourceEnum

/**
 * 商品中心-平台属性导入组件
 * 负责导入商品中心的平台属性数据
 */
@Component
@Slf4j
class ProductCenterPlatformAttrImportComponent(
    private val importProductRecordRepository: ImportProductRecordRepository,
    private val productRepository: ProductRepository,
    private val publishAttributeRepository: PublishAttributeRepository,
    private val publishAttributeValueRepository: PublishAttributeValueRepository,
    private val productAttributesV2Repository: ProductAttributesV2Repository,
) {

    /**
     * 导入
     */
    fun importExcel(file: MultipartFile) {
        val importDataList = mutableListOf<ProductCenterPlatformAttrExportDto>()

        // 读取Excel并映射到UserData实体类
        EasyExcel.read(
            file.inputStream,
            ProductCenterPlatformAttrExportDto::class.java,
            PageReadListener { dataList ->
                log.info { "读取到${dataList.size}条数据" }
                dataList.forEach { user ->
                    importDataList.add(user)
                }
            }
        ).sheet()
            .doRead()

        val importList = importDataList
            .asSequence()
            .filter { it.spuCode.isNotBlank() }
            .toList()
        if (CollectionUtils.isEmpty(importList)) {
            throw BaseBizException("导入数据为空")
        }

        // 创建导入任务
        val list = importList.map {
            ImportProductRecord().apply {
                this.spuCode = it.spuCode
                this.importDataType = ImportDataTypeEnum.PLATFORM_ATTR.code
                this.importSource = ImportSourceEnum.PRODUCT_CENTER.code
                this.importData = it.toJson()
            }
        }
        importProductRecordRepository.saveBatch(list, 100)
    }

    /**
     * 执行导入逻辑
     */
    fun doImport(importData: String) {
        if (importData.isBlank()) {
            throw BaseBizException("解析json失败: $importData")
        }
        // 获取商品
        val data = importData.parseJson<ProductCenterPlatformAttrExportDto>()
        val productList = productRepository.listBySpuCodes(listOf(data.spuCode!!))
        if (productList.isEmpty()) {
            return
        }

        productList.forEach { product ->

            // TODO UPSERT商品属性 (平台属性类型)
            if (data.dynamicProperties.isNotEmpty()) {
                data.dynamicProperties.forEach { (attrKey, attrValue) ->
                    // attrKey移除前缀"属性-"
                    val newAttrKey = attrKey.removePrefix("平台属性-")
                    if (newAttrKey.isBlank()) {
                        return@forEach
                    }
                    // attrValue分离最后一个空格(单位)
                    val split = attrValue.split(" ")
                    val newAttrValue = attrValue.firstOrNull()
                    if (newAttrValue == null) {
                        return@forEach
                    }
                    val unit = split.lastOrNull()
                    val attrGroup = publishAttributeRepository.ktQuery()
                        .eq(PublishAttribute::attributeName, newAttrKey)
                        .eq(PublishAttribute::state, Bool.YES.code)
                        .list()
                        .firstOrNull()
                    if (attrGroup == null) {
                        return@forEach
                    }
                    val attrValue = publishAttributeValueRepository.ktQuery()
                        .eq(PublishAttributeValue::attributeValue, newAttrValue)
                        .eq(PublishAttributeValue::state, Bool.YES.code)
                        .list()
                        .firstOrNull()
                    if (attrValue == null) {
                        return@forEach
                    }

                    val attrInfo = productAttributesV2Repository.listByProductIdAndAttrId(product.productId!!, attrGroup.attributeId!!)
                    if (attrInfo == null) {
                        // 创建
                        productAttributesV2Repository.save(
                            ProductAttributesV2().apply {
                                this.productAttributesId = IdHelper.getId()
                                this.productId = product.productId
                                this.categoryId = product.categoryId
                                this.attributeId = attrGroup.attributeId
                                this.attributeValueId = attrValue.attributeValueId
                                // this.attributeValue = newAttrValue
                                this.unit = unit
                            }
                        )
                    } else {
                        // 更新
                        productAttributesV2Repository.updateById(
                            attrInfo.apply {
                                this.productAttributesId = attrInfo.productAttributesId
                                this.attributeValueId = attrValue.attributeValueId
                                // this.attributeValue = newAttrValue
                                this.unit = unit
                            }
                        )
                    }
                }
            }
        }
    }
}
