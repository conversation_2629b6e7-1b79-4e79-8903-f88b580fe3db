package tech.tiangong.pop.component

import org.apache.commons.collections4.CollectionUtils
import org.springframework.stereotype.Component
import team.aikero.admin.common.vo.AttributeVo
import team.aikero.admin.common.vo.DictVo
import team.aikero.blade.logging.core.annotation.Slf4j
import tech.tiangong.pop.enums.DictEnum
import tech.tiangong.pop.external.DictClientExternal
import tech.tiangong.pop.resp.product.ColorMapResp

/**
 * 颜色组件
 * <AUTHOR>
 * @date 2025-3-7 15:25:35
 */
@Component
@Slf4j
class ColorComponent(
    private val dictClientExternal: DictClientExternal,
) {

    /**
     * 获取颜色映射
     * @return 颜色映射
     */
    fun getColorMap(): List<ColorMapResp> {
        val respList = mutableListOf<ColorMapResp>()
        val colorDictVo: DictVo? = dictClientExternal.getTopByDictCode(DictEnum.CLOTHING_COLOR)
        if (colorDictVo == null || CollectionUtils.isEmpty(colorDictVo.children)) {
            return respList
        }
        for (dictVo in colorDictVo.children!!) {
            if (CollectionUtils.isNotEmpty(dictVo.children)) {
                for (child in dictVo.children!!) {
                    val colorMapResp = ColorMapResp()
                    colorMapResp.color = child.dictName
                    val attributes: List<AttributeVo>? = child.attributes
                    if (CollectionUtils.isNotEmpty(attributes)) {
                        val ywfy = attributes?.firstOrNull { k -> k.code == "YWFY" }
                        val ywsx = attributes?.firstOrNull { k -> k.code == "YWSX" }
                        if (ywfy != null && ywsx != null) {
                            colorMapResp.colorAabbr = ywsx.name
                            colorMapResp.colorCode = ywfy.name
                            respList.add(colorMapResp)
                        }
                    }
                }

            }
        }
        return respList
    }
}