package tech.tiangong.pop.component.export.center

import com.alibaba.excel.EasyExcel
import com.baomidou.mybatisplus.extension.plugins.pagination.Page
import com.google.common.net.MediaType
import org.springframework.stereotype.Component
import team.aikero.blade.core.exception.BusinessException
import team.aikero.blade.core.toolkit.isNotBlank
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.util.json.parseJson
import team.aikero.blade.util.json.toJson
import tech.tiangong.pop.component.export.DownloadTaskInterface
import tech.tiangong.pop.dao.entity.DownloadTask
import tech.tiangong.pop.dao.entity.Product
import tech.tiangong.pop.dao.entity.PublishCategoryPackageConfig
import tech.tiangong.pop.dao.entity.dto.CategoryPackageConfigVolumeDto
import tech.tiangong.pop.dao.entity.dto.ProductPackageInfoDto
import tech.tiangong.pop.dao.repository.ProductRepository
import tech.tiangong.pop.dao.repository.PublishCategoryPackageConfigRepository
import tech.tiangong.pop.dto.FileUploadDTO
import tech.tiangong.pop.dto.export.ProductCenterLogisticsPackageExportDto
import tech.tiangong.pop.enums.DownloadTaskTypeEnum
import tech.tiangong.pop.enums.OuterPackingShapeEnum
import tech.tiangong.pop.enums.OuterPackingTypeEnum
import tech.tiangong.pop.helper.UploaderOssHelper
import tech.tiangong.pop.req.product.center.ProductCenterPageReq
import tech.tiangong.pop.service.settings.DownloadTaskService
import tech.tiangong.pop.utils.getRootMessage
import java.io.File
import java.io.IOException
import java.time.LocalDateTime
import java.time.ZoneId
import java.time.format.DateTimeFormatter
import java.util.*

/**
 * 商品中心-物流包装导出组件
 * 负责导出商品中心的物流包装数据
 */
@Component
@Slf4j
class ProductCenterLogisticsPackageExportComponent(
    private val productRepository: ProductRepository,
    private val publishCategoryPackageConfigRepository: PublishCategoryPackageConfigRepository,
    private val downloadTaskService: DownloadTaskService,
    private val uploaderOssHelper: UploaderOssHelper,
) : DownloadTaskInterface() {

    /**
     * 创建导出任务
     */
    fun createExportTask(req: ProductCenterPageReq) {
        try {
            val pageNum: Long = 1
            val pageSize: Long = 1000
            val page = productRepository.getCenterPage(Page(pageNum, pageSize), req)

            if (page.records.isEmpty()) {
                throw BusinessException("暂无数据")
            }
            // 创建下载任务
            val dateStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss", Locale.getDefault()).withZone(ZoneId.systemDefault()))
            val taskName = "导出商品中心_物流包装_$dateStr"
            downloadTaskService.createTask(
                taskName,
                DownloadTaskTypeEnum.PRODUCT_CENTER_LOGISTICS_PACKAGE_TASK,
                req.toJson()
            );
            log.info { "导出商品中心_物流包装 创建下载任务，任务名称：${taskName}" }
        } catch (e: Exception) {
            log.error(e) { "导出商品中心_物流包装-创建下载任务, 异常，请求参数：${req.toJson()}" }
            throw BusinessException("导出商品中心_物流包装_任务异常：" + e.getRootMessage());
        }
    }

    /**
     * 生成Excel文件
     */
    override fun export(reqStr: String?, tempDir: File, task: DownloadTask): FileUploadDTO {
        val req = reqStr?.parseJson<ProductCenterPageReq>() ?: throw BusinessException("导出任务逻辑-参数为空 task=${task.toJson()}")
        var pageNum: Long = 1
        val pageSize: Long = 1000

        val productList = mutableListOf<Product>()

        while (true) {
            val page = productRepository.getCenterPage(Page(pageNum, pageSize), req)
            if (page.records.isEmpty()) {
                break
            }
            pageNum++

            productList.addAll(page.records)
        }

        try {
            // 创建Excel文件
            val excelFile = File(tempDir, task.taskName + ".xlsx")
            // 构建Excel数据
            val dataList = buildExcelData(productList)
            // 设置Excel数据
            setFileData(dataList, excelFile)
            return uploaderOssHelper.createFileUploadDTO(excelFile, MediaType.MICROSOFT_EXCEL.type())
        } catch (e: IOException) {
            log.error(e) { "导出失败:" }
            throw BusinessException("导出失败")
        }
    }

    /**
     * 构建Excel数据
     */
    fun buildExcelData(productList: List<Product>): List<ProductCenterLogisticsPackageExportDto> {
        val categoryIds = productList.mapNotNull { it.categoryId }
        var categoryIdPackageConfigMap: Map<Long?, PublishCategoryPackageConfig> = emptyMap()
        if (categoryIds.isNotEmpty()) {
            val categoryPackageConfigs = publishCategoryPackageConfigRepository.listByCategoryIds(categoryIds)
            if (categoryPackageConfigs?.isNotEmpty() == true) {
                categoryIdPackageConfigMap = categoryPackageConfigs.associateBy { it.publishCategoryId }
            }
        }

        val dataList = mutableListOf<ProductCenterLogisticsPackageExportDto>()
        productList.forEach { product ->
            dataList.add(ProductCenterLogisticsPackageExportDto().apply {
                this.spuCode = product.spuCode
                this.category = product.categoryName
                val packageInfo = product.packageInfo?.parseJson<ProductPackageInfoDto>()
                val volumeInfo = packageInfo?.packingVolume?.parseJson<CategoryPackageConfigVolumeDto>()
                this.longestEdge = volumeInfo?.maxSideLength?.toString()
                this.secondLongestEdge = volumeInfo?.secondSideLength?.toString()
                this.shortestEdge = volumeInfo?.minSideLength?.toString()
                this.productWeight = packageInfo?.weight?.toString()
                this.wrapperType = packageInfo?.outerPackingShape?.let { OuterPackingShapeEnum.getByCode(it)?.desc }
                this.wrapperShape = packageInfo?.outerPackingType?.let { OuterPackingTypeEnum.getByCode(it)?.desc }

                // 匹配配置, 填充空指
                val packageConfig = categoryIdPackageConfigMap[product.categoryId]
                if (packageConfig != null) {
                    val volumeInfo = packageConfig.packingVolume?.parseJson<CategoryPackageConfigVolumeDto>()
                    this.longestEdge = this.longestEdge.takeIf { it.isNotBlank() } ?: volumeInfo?.maxSideLength?.toString()
                    this.secondLongestEdge = this.secondLongestEdge.takeIf { it.isNotBlank() } ?: volumeInfo?.secondSideLength?.toString()
                    this.shortestEdge = this.shortestEdge.takeIf { it.isNotBlank() } ?: volumeInfo?.minSideLength?.toString()
                    this.productWeight = this.productWeight.takeIf { it.isNotBlank() } ?: packageConfig.weight?.toString()
                    this.wrapperType = this.wrapperType.takeIf { it.isNotBlank() } ?: packageConfig.outerPackingShape?.let { OuterPackingShapeEnum.getByCode(it)?.desc }
                    this.wrapperShape = this.wrapperShape.takeIf { it.isNotBlank() } ?: packageConfig.outerPackingType?.let { OuterPackingTypeEnum.getByCode(it)?.desc }
                }

                // 最后检查, 如果空则赋值"-"
                this.longestEdge = this.longestEdge.takeIf { it.isNotBlank() } ?: "-"
                this.secondLongestEdge = this.secondLongestEdge.takeIf { it.isNotBlank() } ?: "-"
                this.shortestEdge = this.shortestEdge.takeIf { it.isNotBlank() } ?: "-"
                this.productWeight = this.productWeight.takeIf { it.isNotBlank() } ?: "-"
                this.wrapperType = this.wrapperType.takeIf { it.isNotBlank() } ?: "-"
                this.wrapperShape = this.wrapperShape.takeIf { it.isNotBlank() } ?: "-"
            })
        }
        return dataList
    }

    /**
     * 数据设置到File
     */
    fun setFileData(dataList: List<ProductCenterLogisticsPackageExportDto>, excelFile: File) {
        // 写入文件
        EasyExcel.write(excelFile)
            .head(ProductCenterLogisticsPackageExportDto::class.java)
            .sheet("物流包装")
            .doWrite(dataList);
    }
}
