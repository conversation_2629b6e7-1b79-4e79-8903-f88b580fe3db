package tech.tiangong.pop.component.importfile.pending

import org.apache.commons.lang3.StringUtils
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional
import team.aikero.blade.core.enums.Bool
import team.aikero.blade.core.exception.BusinessException
import team.aikero.blade.core.toolkit.isEmpty
import team.aikero.blade.core.toolkit.isNotBlank
import team.aikero.blade.core.toolkit.isNotEmpty
import team.aikero.blade.core.toolkit.isNull
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.sequence.id.IdHelper
import team.aikero.blade.util.json.parseJsonList
import team.aikero.blade.util.json.toJson
import tech.tiangong.pop.common.constant.ProductConstant
import tech.tiangong.pop.common.enums.PlatformEnum.AE
import tech.tiangong.pop.common.enums.ProductAeTaxTypeEnum
import tech.tiangong.pop.common.enums.SpotTypeOpsEnum
import tech.tiangong.pop.common.enums.YesOrNoEnum
import tech.tiangong.pop.common.exception.BaseBizException
import tech.tiangong.pop.common.req.BatchCreateBarCodeReq
import tech.tiangong.pop.component.ProductCommonComponent
import tech.tiangong.pop.config.AliexpressProperties
import tech.tiangong.pop.config.LazadaDefaultProperties
import tech.tiangong.pop.constant.ProductInnerConstant
import tech.tiangong.pop.dao.entity.*
import tech.tiangong.pop.dao.repository.*
import tech.tiangong.pop.dto.AeNationalQuoteConfigDto
import tech.tiangong.pop.dto.product.ImportAeProductDTO
import tech.tiangong.pop.dto.product.ProductAePropertyValueItemDTO
import tech.tiangong.pop.dto.product.ProductInitDto.ProductInitSkcDto
import tech.tiangong.pop.dto.product.ProductTitleConfigGenerationDTO
import tech.tiangong.pop.dto.product.ProductTitleConfigGenerationItemDTO
import tech.tiangong.pop.enums.*
import tech.tiangong.pop.helper.ProductPublishAeHelper
import tech.tiangong.pop.req.product.ProductTitleConfigMultiGenerateReq
import tech.tiangong.pop.req.product.ae.ProductAeAttributeQueryReq
import tech.tiangong.pop.resp.product.ColorMapResp
import tech.tiangong.pop.resp.product.ae.ProductAeAttributeResp
import tech.tiangong.pop.service.product.BarCodeService
import tech.tiangong.pop.service.product.ProductAeService
import tech.tiangong.pop.service.product.ProductTitleGenerateService
import tech.tiangong.pop.service.settings.ProductTitleConfigService
import java.time.LocalDateTime
import kotlin.collections.get

/**
 * AE待上架-导入商品数据
 */
@Component
@Slf4j
class AePendingProductImportComponent(
    private val productRepository: ProductRepository,
    private val productSkcRepository: ProductSkcRepository,
    private val shopRepository: ShopRepository,
    private val productBarCodeRepository: ProductBarCodeRepository,
    private val productTemplateAeSkcRepository: ProductTemplateAeSkcRepository,
    private val productTemplateAeSkuRepository: ProductTemplateAeSkuRepository,
    private val productTemplateAeSpuRepository: ProductTemplateAeSpuRepository,
    private val barCodeService: BarCodeService,
    private val lazadaDefaultProperties: LazadaDefaultProperties,
    private val productAeService: ProductAeService,
    private val aliexpressProperties: AliexpressProperties,
    private val productTitleGenerateService: ProductTitleGenerateService,
    private val productTitleConfigService: ProductTitleConfigService,
    private val productPublishAeHelper: ProductPublishAeHelper,
    private val regionPriceRuleLogisticsRepository: RegionPriceRuleLogisticsRepository,
    private val productCommonComponent: ProductCommonComponent,
) {

    /**
     * 导入-待上架-商品数据-导入执行逻辑
     * @param importDto
     * @param colorMap
     * @param imageMap
     * @param publishCategories
     */
    @Transactional(rollbackFor = [Exception::class])
    fun doImportAeProduct(
        importDto: ImportAeProductDTO,
        colorMap: List<ColorMapResp>,
        imageMap: Map<String, ImageRepository>,
        publishCategories: List<PublishCategory>,
    ) {

        val shop = shopRepository.ktQuery()
            .eq(Shop::shopName, importDto.listingShopName)
            .eq(Shop::platformId, AE.platformId)
            .list()
            .firstOrNull()
        if (shop == null) {
            throw BusinessException("AE店铺:${importDto.listingShopName}不存在")
        }
        // 查询待上架商品SPU: SPU+店铺, 且任务状态 != 已完成
        val templateSpuList = productTemplateAeSpuRepository.ktQuery()
            .eq(ProductTemplateAeSpu::shopId, shop.shopId)
            .eq(ProductTemplateAeSpu::spuCode, importDto.spuCode)
            .ne(ProductTemplateAeSpu::taskStatus, ProductPendingTaskStatusEnum.COMPLETED.code)
            .list()
        if (templateSpuList.isEmpty()) {
            throw BusinessException("AE店铺:${importDto.listingShopName} SPU:${importDto.spuCode} 商品不存在或已完成")
        }

        templateSpuList.forEach { templateSpu ->
            // 更新product
            upsertProductByImportAe(templateSpu, importDto, imageMap)
            // 重新查询一次product
            val product = productRepository.getById(templateSpu.productId!!)

            // 设置商品属性
            productCommonComponent.setAttributesByImport(importDto, product, AE.platformId)

            // 更新模板SPU, SKC, SKU
            upsertAeTemplateSpuSkcSku(importDto, product, templateSpu)

            // 导入: 补充定价区域
            upsertPricingRegion(importDto, templateSpu)
        }
    }


    /**
     * 更新模板SPU, SKC, SKU（优化发货地处理，新增旧SKU置为禁用逻辑）
     */
    private fun upsertAeTemplateSpuSkcSku(importDto: ImportAeProductDTO, product: Product, templateSpu: ProductTemplateAeSpu) {
        val (spuAe, lastSkcAeList) = upsertAeTemplateSpuSkc(product, templateSpu)

        // 更新SPU属性
        val invDeductionEnum = InvDeductionEnum.getByDesc(importDto.invDeductionName)
        spuAe.invDeduction = invDeductionEnum?.code
        val originPlaceDto = getOriginPlace(importDto.originPlaceName, product)
        if (originPlaceDto.isNotEmpty() && originPlaceDto.toJson() != spuAe.originPropertyValueItems) {
            spuAe.originPropertyValueItems = originPlaceDto.toJson()
        }
        if (importDto.taxType.isNotBlank()) {
            spuAe.taxType = ProductAeTaxTypeEnum.getByDescription(importDto.taxType!!).value
        }
        if (importDto.productTitle.isNotBlank()) {
            val titleList = importDto.getParsedTitleList()
            if (titleList.isNotEmpty()) {
                val config = productTitleConfigService.findByPlatformId(AE.platformId)
                val configId = config?.productTitleConfigId
                val titleCount = config?.titleCount ?: titleList.size
                val titleItems = titleList.mapIndexed { index, title ->
                    ProductTitleConfigGenerationItemDTO(
                        index = index,
                        title = title,
                        ruleId = null,
                        ruleName = null,
                        isOverLength = title.length > ProductInnerConstant.getTitleMaxLength(AE),
                        missingFields = emptyList(),
                        removeFields = emptyList()
                    )
                }
                val multiTitleData = ProductTitleConfigGenerationDTO(
                    productTitleConfigId = configId,
                    titleCount = titleCount,
                    titles = titleItems,
                    lastGeneratedTime = LocalDateTime.now()
                )
                spuAe.setParsedGeneratedTitles(multiTitleData)
                spuAe.productTitle = titleList.first()
            } else {
                spuAe.productTitle = importDto.productTitle
            }
        }
        productTemplateAeSpuRepository.updateById(spuAe)

        // 处理SKU
        val selectSizeList = ImportAeProductDTO.getUniqueSizes(listOf(importDto))
        if (selectSizeList.isEmpty()) return

        val importShipsFromNameList = importDto.getParsedShipsFromList()
        val importRetailPriceList = importDto.getParsedRetailPriceList()
        val importSalePriceList = importDto.getParsedSalePriceList()
        val (isValidShipsFrom, errorMsg) = importDto.validateShipsFromAndPrices()
        if (!isValidShipsFrom) throw BusinessException(errorMsg ?: "发货地与价格数量存在不匹配，请检查导入数据")

        // ================= 发货地处理逻辑开始 =================
        // 1. 从数据库中获取该 SPU 下所有已启用的发货地属性值（attributeValueId 和名称）
        // 获取现有可用发货地
        val dbShipsFroms = productTemplateAeSkuRepository
            .listByAeSpuId(spuAe.aeSpuId!!, Bool.YES.code)
            .mapNotNull { it.getParsedShipsFromPropertyValueItem() }

        // 2. 将导入的发货地名称列表转换为 DTO，通过属性服务获取对应 attributeValueId，不存在则保留原名称
        // 处理导入发货地：通过 attributeValueId 比对
        val importShipsFroms: List<ProductAePropertyValueItemDTO> =
            if (importShipsFromNameList.isEmpty()) {
                // 如果导入的发货地列表为空，则使用配置的默认发货地
                listOf(productPublishAeHelper.getDefaultShipFromPropertyValueItemDTO())
            } else {
                val attResp = productAeService.getAttributes(ProductAeAttributeQueryReq().apply {
                    productId = product.productId
                    attributeType = ProductAeAttributeTypeEnum.SHIPS_FROM
                    categoryId = product.categoryId
                })

                importShipsFromNameList.map { name ->
                    buildShipsFromName(name, attResp)
                }
            }
        val unresolved = importShipsFroms.filter { it.attributeValueId == null }
        if (unresolved.isNotEmpty()) {
            val names = unresolved.map { it.attributeValueName ?: it.attributeName ?: "未知" }
            throw BusinessException("以下发货地未匹配到类目属性：${names.joinToString(", ")}")
        }

        // 3. 判断发货地是否变更，需要禁用旧 SKU
        val dbShipsFromValueIds = dbShipsFroms.mapNotNull { it.attributeValueId }.toSet()
        val importShipsFromValueIds = importShipsFroms.mapNotNull { it.attributeValueId }.toSet()
        val shouldDisableOldSkus =
            when {
                // 如果现有发货地列表和导入发货地列表不相等
                dbShipsFromValueIds == importShipsFromValueIds -> false
                else -> true
            }

        // 4. 如果发货地变更（新增或移除），则将原有所有 SKU 标记为禁用
        if (shouldDisableOldSkus) {
            val oldSkus = productTemplateAeSkuRepository.listByAeSkcIdList(
                aeSkcIdList = lastSkcAeList.orEmpty().map { it.aeSkcId!! },
                enableState = Bool.YES.code
            )

            if (oldSkus.isNotEmpty()) {
                val skusToDisable = oldSkus.map {
                    ProductTemplateAeSku().apply {
                        aeSkuId = it.aeSkuId
                        enableState = Bool.NO.code
                    }
                }

                productTemplateAeSkuRepository.updateBatchById(skusToDisable)
            }
            log.info { "发货地变更, ${spuAe.spuCode}, 已禁用 ${oldSkus.size} 个旧SKU" }
        }

        // 5. 构建最终发货地列表，简化为只使用导入的发货地列表（因为设置为空默认为中国发货地的逻辑）
        // ================= 发货地处理逻辑结束 =================
        val foreachShipsFrom = importShipsFroms

        //如指定店铺，则在店铺+(选中)发货地+(全部)目的地维度划线价更新；
        val shipsFromSize = foreachShipsFrom.size
        fun broadcastVec(src: List<String>): List<String?> = when {
            src.isEmpty() -> List(shipsFromSize) { null }
            src.size == 1 -> List(shipsFromSize) { src[0] }
            src.size == shipsFromSize -> src
            else -> throw BusinessException("价格数量需与发货地一致或为单值覆盖（size=1）")
        }

        val retailVec = broadcastVec(importRetailPriceList)
        val saleVec = broadcastVec(importSalePriceList)

        lastSkcAeList.orEmpty().forEach { skc ->
            selectSizeList.forEach { size ->
                val barCode = productBarCodeRepository.getBySpuCodeAndSkcAndSize(spuAe.spuCode, skc.skc, size)
                var newBarcode: String? = barCode?.barcode
                if (barCode == null) {
                    val barcodeReq = BatchCreateBarCodeReq().apply {
                        categoryCode = product.categoryCode
                        categoryName = product.categoryName
                        skcCode = skc.skc
                        color = skc.color
                        spuCode = product.spuCode
                        groupName = product.sizeGroupName
                        sourceGroupCode = product.sizeGroupCode
                        sizeValues = listOf(size)
                        inspiraImgUrl = product.inspiraImgUrl
                        supplyMode = product.supplyMode
                        localPrice = skc.localPrice
                        designImgUrl = skc.pictures
                        mainImgUrl = skc.pictures
                    }
                    val resp = barCodeService.createBarcodeByForce(listOf(barcodeReq))
                    if (resp.isNotEmpty()) newBarcode = resp[0].barcode
                }
                foreachShipsFrom.forEachIndexed { idx, shipsFrom ->
                    //需要支持SKU级别的店铺id查询
                    val skuAe = productTemplateAeSkuRepository.getByAeSkcIdAndSizeName(
                        skc.aeSkcId!!, size, shipsFrom.attributeValueId, templateSpu.shopId!! //这里需要匹配店铺ID
                    )
                    val retailPrice = retailVec[idx]
                    val salePrice = saleVec[idx]
                    if (skuAe != null) {
                        skuAe.enableState = Bool.YES.code
                        newBarcode?.let { skuAe.barcode = it }
                        importDto.stockQuantity?.let { skuAe.stockQuantity = it.toLong() }
                        if (!retailPrice.isNullOrBlank()) skuAe.retailPrice = retailPrice.toBigDecimal()
                        if (!salePrice.isNullOrBlank()) skuAe.salePrice = salePrice.toBigDecimal()
                        if (skuAe.stockQuantity.isNull()) skuAe.stockQuantity = lazadaDefaultProperties.defaultStock.toLong()
                        productTemplateAeSkuRepository.updateById(skuAe)
                    } else {
                        val newSku = ProductTemplateAeSku().apply {
                            aeSkuId = IdHelper.getId()
                            enableState = Bool.YES.code
                            aeSkcId = skc.aeSkcId
                            aeSpuId = spuAe.aeSpuId
                            barcode = newBarcode
                            stockQuantity = importDto.stockQuantity?.toLong() ?: lazadaDefaultProperties.defaultStock.toLong()
                            sizeName = size
                            if (!retailPrice.isNullOrBlank()) this.retailPrice = retailPrice.toBigDecimal()
                            if (!salePrice.isNullOrBlank()) this.salePrice = salePrice.toBigDecimal()
                            shipsFrom.let {
                                shipsFromAttributeId = it.attributeId
                                shipsFromAttributeName = it.attributeName
                                shipsFromAttributeValueId = it.attributeValueId
                                shipsFromAttributeValueName = it.attributeValueName
                            }
                            shopId = templateSpu.shopId
                        }
                        productTemplateAeSkuRepository.save(newSku)
                    }
                }
            }
        }
    }

    /**
     * 商品导入-新增更新商品-AE
     * @param templateSpu
     * @param importDto
     * @param imageMap
     * @return
     */
    private fun upsertProductByImportAe(
        templateSpu: ProductTemplateAeSpu,
        importDto: ImportAeProductDTO,
        imageMap: Map<String, ImageRepository>,
    ) {
        val upsertProduct = productRepository.getById(templateSpu.productId)

        // 更新值
        val imageRepository = imageMap[templateSpu.spuCode]
        if (imageRepository != null) {
            upsertProduct.mainImgUrl = imageRepository.mainUrl
        }
        val supplyModeEnum = SupplyModeEnum.getByName(importDto.supplyMode) ?: throw BaseBizException("找不到供给方式")
        upsertProduct.brandName = importDto.brandName
        upsertProduct.supplyMode = supplyModeEnum.dictCode
        upsertProduct.productTitle = importDto.productTitle
        upsertProduct.sizeGroupName = "字母码"
        upsertProduct.sizeGroupCode = ProductConstant.SOURCE_GROUP_CODE
        if (StringUtils.isNotBlank(importDto.packageWeight)) {
            upsertProduct.packageWeight = importDto.packageWeight
        }
        //设置定价类型和现货类型
        if (StringUtils.isNotBlank(importDto.pricingType)) {
            val pricingTypeEnum = ProductPricingTypeEnum.getByDesc(importDto.pricingType?.trim()) ?: throw BaseBizException("找不到定价类型")
            upsertProduct.pricingType = pricingTypeEnum.code
        }
        if (StringUtils.isNotBlank(importDto.spotType)) {
            val spotTypeOpsEnum = SpotTypeOpsEnum.getByDesc(importDto.spotType?.trim()) ?: throw BaseBizException("找不到现货类型")
            upsertProduct.spotTypeCode = spotTypeOpsEnum.dictCode
        }

        // 处理默认值
        if (upsertProduct.packageWeight == null) {
            upsertProduct.packageWeight = lazadaDefaultProperties.packageWeight
        }
        if (upsertProduct.packageDimensionsLength == null) {
            upsertProduct.packageDimensionsLength = lazadaDefaultProperties.packageDimensionsLength.toString()
        }
        if (upsertProduct.packageDimensionsWidth == null) {
            upsertProduct.packageDimensionsWidth = lazadaDefaultProperties.packageDimensionsWidth.toString()
        }
        if (upsertProduct.packageDimensionsHeight == null) {
            upsertProduct.packageDimensionsHeight = lazadaDefaultProperties.packageDimensionsHeight.toString()
        }

        // 导入更新
        productRepository.updateById(upsertProduct)

        //写库后再同步款式类型和图包状态
        productCommonComponent.initStyleTypeAndImagePackageState(upsertProduct)
    }

    /**
     * 导入:补充定价区域
     */
    private fun upsertPricingRegion(importDto: ImportAeProductDTO, templateSpu: ProductTemplateAeSpu) {
        val aeSkuList = productTemplateAeSkuRepository.listByAeSpuId(templateSpu.aeSpuId!!, Bool.YES.code)
        if (aeSkuList.isEmpty()) {
            return
        }

        // 获取物流配置(发货地-目的地)
        val logisticsRuleConfigMap = regionPriceRuleLogisticsRepository.getAeLogisticsCostConfig()
        aeSkuList.forEach { aeSku ->
            // 物流配置
            val configDtoList = logisticsRuleConfigMap[aeSku.shipsFromAttributeValueId]
            if (configDtoList.isEmpty()) {
                return@forEach
            }

            val newNaConfig = mutableListOf<AeNationalQuoteConfigDto>()

            // SKU上的区域定价配置
            val naConfig = aeSku.nationalQuoteConfig?.parseJsonList(AeNationalQuoteConfigDto::class.java)
            if (naConfig.isNotEmpty()) {
                // 转为map 方便比较
                val naConfigMap = naConfig?.associateBy { it.shipToCountry }

                // 循环物流配置
                configDtoList?.forEach { configDto ->
                    // 比较SKU是否有不存在的目的地, 若不存在, 则复制, 填充物流配置对应的目的地(发货地对应)
                    if (naConfigMap?.containsKey(configDto.receivingPlace) == true) {
                        // 存在, add
                        newNaConfig.add(naConfigMap[configDto.receivingPlace]!!)
                    } else {
                        // 不存在, 新增
                        newNaConfig.add(AeNationalQuoteConfigDto().apply {
                            this.shipToCountry = configDto.receivingPlace
                            this.defaultFLag = configDto.defaultRule ?: Bool.NO.code
                        })
                    }
                }
            } else {
                // 循环物流配置
                configDtoList?.forEach { configDto ->
                    // 新增
                    newNaConfig.add(AeNationalQuoteConfigDto().apply {
                        this.shipToCountry = configDto.receivingPlace
                        this.defaultFLag = configDto.defaultRule ?: Bool.NO.code
                    })
                }
            }
            // 更新区域定价字段
            productTemplateAeSkuRepository.ktUpdate()
                .set(ProductTemplateAeSku::nationalQuoteConfig, if (importDto.regionalPricingFlag == "是") newNaConfig.toJson() else null)
                .eq(ProductTemplateAeSku::aeSkuId, aeSku.aeSkuId)
                .update()
        }
    }

    /**
     * 获取产地
     *
     * @param importOriginPlaceName 格式: "中国大陆(Origin)-广东" 或者 "德国(Origin)" (只有中国有省份地区, 其他只有国家)
     * @param product
     */
    private fun getOriginPlace(importOriginPlaceName: String?, product: Product): List<ProductAePropertyValueItemDTO> {
        if (importOriginPlaceName.isNullOrBlank()) {
            log.info { "产地信息为空, productId: ${product.productId}" }
            return emptyList()
        }

        log.info { "开始处理产地信息: $importOriginPlaceName, productId: ${product.productId}" }
        val originPlaceDtoList = mutableListOf<ProductAePropertyValueItemDTO>()
        val originPlaceValueList = importOriginPlaceName.split("-")

        when {
            originPlaceValueList.isEmpty() -> {
                log.info { "产地信息格式异常，拆分后为空列表: $importOriginPlaceName" }
                return emptyList()
            }

            originPlaceValueList.size == 1 -> {
                // 处理只有国家的情况
                log.info { "处理国家级产地信息: ${originPlaceValueList.first()}" }
                findAndAddCountryOrigin(product, originPlaceValueList.first(), originPlaceDtoList)
            }

            originPlaceValueList.size == 2 -> {
                // 处理国家和地区的情况
                log.info { "处理国家和地区级产地信息: ${originPlaceValueList[0]}, ${originPlaceValueList[1]}" }
                findAndAddCountryOrigin(product, originPlaceValueList.first(), originPlaceDtoList)
                findAndAddRegionOrigin(product, originPlaceValueList[1], originPlaceDtoList)
            }

            else -> {
                val errorMsg = "产地格式异常, 期望格式: '国家(Origin)' 或 '国家(Origin)-地区', 实际值: $importOriginPlaceName"
                log.error { errorMsg }
                throw IllegalArgumentException(errorMsg)
            }
        }

        log.info { "产地信息处理完成，获取到 ${originPlaceDtoList.size} 个属性值" }
        return originPlaceDtoList
    }

    /**
     * 查找并添加国家级产地信息
     */
    private fun findAndAddCountryOrigin(
        product: Product,
        countryValue: String,
        resultList: MutableList<ProductAePropertyValueItemDTO>,
    ) {
        val attReq = ProductAeAttributeQueryReq().apply {
            productId = product.productId
            attributeType = ProductAeAttributeTypeEnum.ORIGIN
            categoryId = product.categoryId
        }

        productAeService.getOriginAttribute(attReq).let { attResp ->
            attResp.values
                .firstOrNull { it.chineseName?.let { name -> countryValue.contains(name) } == true }
                ?.let { originPlaceAttr ->
                    resultList.add(ProductAePropertyValueItemDTO().apply {
                        attributeId = attResp.attributeId
                        attributeName = attResp.attributeChineseName
                        attributeValueId = originPlaceAttr.id
                        attributeValueName = originPlaceAttr.chineseName
                    })
                    log.info { "已添加国家级产地: ${originPlaceAttr.chineseName}, id: ${originPlaceAttr.id}" }
                } ?: log.warn { "未找到匹配的国家级产地: $countryValue" }
        }
    }

    /**
     * 查找并添加地区级产地信息
     */
    private fun findAndAddRegionOrigin(
        product: Product,
        regionValue: String,
        resultList: MutableList<ProductAePropertyValueItemDTO>,
    ) {
        val attAreaReq = ProductAeAttributeQueryReq().apply {
            productId = product.productId
            attrValueId = aliexpressProperties.aePlatform.requiredProductAttributes.originAttribute.attributeValueId
            attributeType = ProductAeAttributeTypeEnum.ORIGIN
            categoryId = product.categoryId
        }

        productAeService.getOriginAttribute(attAreaReq).let { attAreaResp ->
            attAreaResp.values
                .firstOrNull { it.chineseName?.let { name -> regionValue.contains(name) } == true }
                ?.let { originPlaceAttr ->
                    resultList.add(ProductAePropertyValueItemDTO().apply {
                        attributeId = attAreaResp.attributeId
                        attributeName = attAreaResp.attributeChineseName
                        attributeValueId = originPlaceAttr.id
                        attributeValueName = originPlaceAttr.chineseName
                    })
                    log.info { "已添加地区级产地: ${originPlaceAttr.chineseName}, id: ${originPlaceAttr.id}" }
                } ?: log.warn { "未找到匹配的地区级产地: $regionValue" }
        }
    }


    /**
     * 根据名称构建商品属性值
     */
    private fun buildShipsFromName(
        name: String,
        attResp: ProductAeAttributeResp,
    ): ProductAePropertyValueItemDTO {
        val match = attResp.values.firstOrNull {
            it.name?.trim() == name || it.chineseName?.trim() == name
        }

        return ProductAePropertyValueItemDTO().apply {
            if (match != null) {
                attributeId = attResp.attributeId
                attributeName = attResp.attributeChineseName
                attributeValueId = match.id
                attributeValueName = match.name
            } else {
                attributeValueName = name
            }
        }
    }

    /**
     * 更新AE模板SKC和SKU
     */
    private fun upsertAeTemplateSpuSkc(product: Product, templateSpu: ProductTemplateAeSpu, skcList: List<ProductInitSkcDto>? = null): Pair<ProductTemplateAeSpu, List<ProductTemplateAeSkc>?> {
        val multiTitleResp = try {
            productTitleGenerateService.generateMultiTitlesByConfig(ProductTitleConfigMultiGenerateReq().apply {
                this.productId = product.productId
                this.product = product
                this.shopId = product.shopId
                this.platform = AE
            })
        } catch (e: Exception) {
            log.warn(e) { "生成多标题失败，回退到单标题生成: productId=${product.productId}" }
            null
        }

        // 内联 title 赋值逻辑
        fun setGeneratedTitle(spu: ProductTemplateAeSpu) {
            if (multiTitleResp?.titleData != null) {
                // 使用多标题结果
                val titleData = multiTitleResp.titleData
                spu.setParsedGeneratedTitles(titleData)

                // 使用第一个标题作为主标题
                val firstTitle = titleData.titles.firstOrNull()?.title
                if (firstTitle.isNotBlank()) {
                    spu.productTitle = firstTitle
                        .takeIf { it.isNotBlank() && it!!.length < ProductInnerConstant.getTitleMaxLength(AE) }
                        ?: spu.productTitle
                    spu.generatedTitleOverLengthFlag = if (firstTitle?.let { titleData.titles.firstOrNull()?.isOverLength } == true) YesOrNoEnum.YES.code else YesOrNoEnum.NO.code
                    spu.generatedTitleMissingFieldsJson = (titleData.titles.firstOrNull()?.missingFields ?: emptyList()).toJson()
                }
            }
        }

        // 处理SPU
        val spuAe = templateSpu

        // 更新
        spuAe.productTitle = product.productTitle
        spuAe.brandName = product.brandName
        spuAe.packageWeight = product.packageWeight
        spuAe.packageDimensionsLength = product.packageDimensionsLength
        spuAe.packageDimensionsHeight = product.packageDimensionsHeight
        spuAe.packageDimensionsWidth = product.packageDimensionsWidth
        spuAe.sizeGroupName = product.sizeGroupName
        spuAe.sizeGroupCode = product.sizeGroupCode

        setGeneratedTitle(spuAe)
        productTemplateAeSpuRepository.updateById(spuAe)

        // 处理SKC
        // 取出模板skc
        val skcAeList = productTemplateAeSkcRepository.listByAeSpuId(spuAe.aeSpuId!!)
        val skcMap = skcAeList.associateBy { it.skc }
        // 取出商品skc
        val productSkcList = productSkcRepository.getByProductId(product.productId!!)
        // 以模板skc为基准, aeSpuId+color为key, 遍历productSkcList, 存在则更新, 不存在则新增
        productSkcList.forEach {
            val skc = skcMap[it.skc]
            if (skc != null) {
                // 更新
                skc.productSkcId = it.productSkcId
                skc.skc = it.skc
                skc.color = it.color
                skc.platformColor = it.platformColor
                skc.colorCode = it.colorCode
                skc.colorAbbrCode = it.colorAbbrCode
                skc.pictures = it.pictures
                skc.state = it.state
                skc.cbPrice = it.cbPrice
                skc.localPrice = it.localPrice
                skc.purchasePrice = it.purchasePrice
                skc.costPrice = it.costPrice
                productTemplateAeSkcRepository.updateById(skc)
            } else {
                // 新增
                val skc = ProductTemplateAeSkc().apply {
                    this.aeSkcId = IdHelper.getId()
                    this.aeSpuId = spuAe.aeSpuId
                    this.productSkcId = it.productSkcId
                    this.skc = it.skc
                    this.color = it.color
                    this.platformColor = it.platformColor
                    this.colorCode = it.colorCode
                    this.colorAbbrCode = it.colorAbbrCode
                    this.pictures = it.pictures
                    this.state = it.state
                    this.cbPrice = it.cbPrice
                    this.localPrice = it.localPrice
                    this.purchasePrice = it.purchasePrice
                    this.costPrice = it.costPrice
                }
                productTemplateAeSkcRepository.save(skc)
            }
        }
        // 再获取一次SKC, 上面有新增和更新
        val lastSkcAeList = productTemplateAeSkcRepository.listByAeSpuId(spuAe.aeSpuId!!).filter { it.state == Bool.YES.code }

        val skuList = productTemplateAeSkuRepository.listByAeSpuId(spuAe.aeSpuId!!, Bool.YES.code)

        // 提取AE发货地
        val allShipsFrom = skuList.mapNotNull { it.getParsedShipsFromPropertyValueItem() }
        val foreachShipsFrom = mutableListOf<ProductAePropertyValueItemDTO?>()
        if (allShipsFrom.isEmpty()) {
            // 都是空, 则只有默认发货地
            foreachShipsFrom.add(ProductAePropertyValueItemDTO().apply {
                this.attributeId = aliexpressProperties.aePlatform.shipsFromPropertyId
                this.attributeName = aliexpressProperties.aePlatform.shipsFromPropertyName
                this.attributeValueId = aliexpressProperties.aePlatform.shipsFromPropertyValueId
                this.attributeValueName = aliexpressProperties.aePlatform.shipsFromPropertyValueName
            })
        } else {
            // 不为空, 则多发货地
            val tmpShipsFrom = allShipsFrom.associateBy { it.attributeValueId }
            if (tmpShipsFrom.values.isNotEmpty()) {
                foreachShipsFrom.addAll(tmpShipsFrom.values)
            } else {
                foreachShipsFrom.add(ProductAePropertyValueItemDTO().apply {
                    this.attributeId = aliexpressProperties.aePlatform.shipsFromPropertyId
                    this.attributeName = aliexpressProperties.aePlatform.shipsFromPropertyName
                    this.attributeValueId = aliexpressProperties.aePlatform.shipsFromPropertyValueId
                    this.attributeValueName = aliexpressProperties.aePlatform.shipsFromPropertyValueName
                })
            }
        }

        // 提取店铺
        val shop = shopRepository.getById(templateSpu.shopId!!)

        // 初始化尺码
        // skcList map key=skcCode
        val skcMapByCode = skcList?.groupBy { it.skcCode }
        lastSkcAeList.forEach { skc ->
            skcMapByCode?.get(skc.skc)?.forEach { skcDto ->
                skcDto.sizeList?.forEach { size ->
                    // 获取条码
                    val barCode = productBarCodeRepository.getBySpuCodeAndSkcAndSize(spuAe.spuCode, skc.skc, size)

                    // 循环发货地
                    foreachShipsFrom.forEach { shipsFrom ->
                        // 判断sku是否存在
                        val skuAe = productTemplateAeSkuRepository.getByAeSkcIdAndSizeName(
                            skc.aeSkcId!!,
                            size,
                            shipsFrom?.attributeValueId,
                            shop.shopId!!
                        )
                        if (skuAe == null) {
                            // 新增
                            val skuAe = ProductTemplateAeSku().apply {
                                this.aeSkuId = IdHelper.getId()
                                this.aeSkcId = skc.aeSkcId
                                this.aeSpuId = spuAe.aeSpuId
                                //                            this.sellerSku = importDto.skuCode
                                barCode?.let { this.barcode = it.barcode }
                                this.stockQuantity = lazadaDefaultProperties.defaultStock.toLong()
                                this.sizeName = size
                                this.shopId = shop.shopId
                                this.flagFrontend = Bool.YES.code
                                this.enableState = Bool.YES.code
                                if (shipsFrom != null) {
                                    this.shipsFromAttributeId = shipsFrom.attributeId
                                    this.shipsFromAttributeName = shipsFrom.attributeName
                                    this.shipsFromAttributeValueId = shipsFrom.attributeValueId
                                    this.shipsFromAttributeValueName = shipsFrom.attributeValueName
                                }
                            }
                            productTemplateAeSkuRepository.save(skuAe)
                        }
                    }
                }
            }
        }
        return Pair(spuAe, lastSkcAeList)
    }
}