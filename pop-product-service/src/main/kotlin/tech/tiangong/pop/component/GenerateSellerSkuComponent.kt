package tech.tiangong.pop.component

import org.apache.commons.lang3.StringUtils
import org.springframework.stereotype.Component
import team.aikero.blade.logging.core.annotation.Slf4j
import tech.tiangong.pop.common.enums.ChannelEnum
import tech.tiangong.pop.common.enums.ChannelEnum.ALIBABA
import tech.tiangong.pop.common.enums.PlatformEnum
import tech.tiangong.pop.common.enums.PlatformEnum.AE
import tech.tiangong.pop.common.enums.PlatformEnum.LAZADA
import tech.tiangong.pop.common.exception.PublishAscBizException
import tech.tiangong.pop.dao.entity.*
import tech.tiangong.pop.req.settings.SkuCodeGenerateReq
import tech.tiangong.pop.service.SkuCodeGenerateService

/**
 * 生成sku的组件
 */
@Component
@Slf4j
class GenerateSellerSkuComponent(private val skuCodeGenerateService: SkuCodeGenerateService) {

    /**
     * 生成sellerSku
     * @param product
     * @param saleGoods
     * @param saleSkc
     * @param saleSku
     * @return
     */
    fun generateSellerSku(product: Product, saleGoods: SaleGoods, saleSkc: SaleSkc, saleSku: SaleSku, comboColor: String? = null): String {
        val skuCodeGenerateReq = SkuCodeGenerateReq.builder()
            .channelId(ALIBABA.channelId)
            .platformId(LAZADA.platformId)
            .supplyMode(product.supplyMode)
            .spu(saleGoods.spuCode!!)
            .size(saleSku.sizeName)
            .color1((comboColor ?: saleSkc.colorCode!!).replace(" ", "_"))
            .color2(saleSkc.colorAbbrCode)
            .barcode(saleSku.barcode)
            .build()
        val sellerSku = skuCodeGenerateService.generateSkuCode(skuCodeGenerateReq)
        if (StringUtils.isBlank(sellerSku)) {
            throw PublishAscBizException("查询不到上架sku编码，请确认配置规则是否正确")
        }
        return sellerSku
    }

    /**
     * 生成sellerSku
     * @param product
     * @param saleGoods
     * @param saleSkc
     * @param saleSku
     * @return
     */
    fun generateSellerSku(product: Product, saleGoods: AeSaleGoods, saleSkc: AeSaleSkc, saleSku: AeSaleSku, comboColor: String? = null): String {
        val skuCodeGenerateReq = SkuCodeGenerateReq.builder()
            .channelId(ALIBABA.channelId)
            .platformId(AE.platformId)
            .supplyMode(product.supplyMode)
            .spu(saleGoods.spuCode!!)
            .size(saleSku.sizeName)
            .color1((comboColor ?: saleSkc.colorCode!!).replace(" ", "_"))
            .color2(saleSkc.colorAbbrCode)
            .barcode(saleSku.barcode)
            .build()
        val sellerSku = skuCodeGenerateService.generateSkuCode(skuCodeGenerateReq)
        if (StringUtils.isBlank(sellerSku)) {
            throw PublishAscBizException("查询不到上架sku编码，请确认配置规则是否正确")
        }
        return sellerSku
    }


    /**
     * 生成sellerSku
     * @param product
     * @param saleGoods
     * @param saleSkc
     * @param saleSku
     * @return
     */
    fun generateSellerSku(product: Product, saleGoods: TemuSaleGoods, saleSkc: TemuSaleSkc, saleSku: TemuSaleSku, comboColor: String? = null): String {
        val skuCodeGenerateReq = SkuCodeGenerateReq.builder()
            .channelId(ChannelEnum.OTHER.channelId)
            .platformId(PlatformEnum.TEMU.platformId)
            .supplyMode(product.supplyMode)
            .spu(saleGoods.spuCode!!)
            .size(saleSku.sizeName)
            .color1((comboColor ?: saleSkc.colorCode!!).replace(" ", "_"))
            .color2(saleSkc.colorAbbrCode)
            .barcode(saleSku.barcode)
            .build()
        val sellerSku = skuCodeGenerateService.generateSkuCode(skuCodeGenerateReq)
        if (StringUtils.isBlank(sellerSku)) {
            throw PublishAscBizException("查询不到上架sku编码，请确认配置规则是否正确")
        }
        return sellerSku
    }




    /**
     * 生成sellerSku
     * @param product
     * @param saleGoods
     * @param saleSkc
     * @param saleSku
     * @return
     */
    fun generateSellerSku(product: Product, templateTemuSpu: ProductTemplateTemuSpu, saleSkc:  ProductTemplateTemuSkc, saleSku: ProductTemplateTemuSku, comboColor: String? = null): String {
        val skuCodeGenerateReq = SkuCodeGenerateReq.builder()
            .channelId(ChannelEnum.OTHER.channelId)
            .platformId(PlatformEnum.TEMU.platformId)
            .supplyMode(product.supplyMode)
            .spu(templateTemuSpu.spuCode!!)
            .size(saleSku.sizeName)
            .color1((comboColor ?: saleSkc.colorCode!!).replace(" ", "_"))
            .color2(saleSkc.colorAbbrCode)
            .barcode(saleSku.barcode)
            .build()
        val sellerSku = skuCodeGenerateService.generateSkuCode(skuCodeGenerateReq)
        if (StringUtils.isBlank(sellerSku)) {
            throw PublishAscBizException("查询不到上架sku编码，请确认配置规则是否正确")
        }
        return sellerSku
    }
}