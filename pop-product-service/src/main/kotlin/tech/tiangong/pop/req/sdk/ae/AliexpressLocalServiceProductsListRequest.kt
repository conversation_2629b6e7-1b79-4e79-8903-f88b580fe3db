package tech.tiangong.pop.req.sdk.ae

import com.fasterxml.jackson.annotation.JsonProperty
import tech.tiangong.pop.resp.sdk.aliexpress.BaseAliexpressResponse
import java.io.Serializable


/**
 * 请求：海外托管商品列表查询
 * aliexpress.local.service.products.list
 */
@AliexpressApiName("aliexpress.local.service.products.list")
data class AliexpressLocalServiceProductsListRequest (
    /** 渠道seller id（LOCAL_SERVICE 店铺的 channel_seller_id） */
    @JsonProperty("channel_seller_id")
    val channelSellerId: Long,

    /** 渠道（通过 global.seller.relation.query 获取） */
    @JsonProperty("channel")
    val channel: String,

    /** 分页大小，最大 20 */
    @JsonProperty("page_size")
    val pageSize: Int,

    /** 查询条件对象 */
    @JsonProperty("search_condition_do")
    val searchCondition: SearchCondition,

    /** 当前页码 */
    @JsonProperty("current_page")
    val currentPage: Int
): BaseAliexpressRequest()


/**
 * 复合查询条件。
 * 接口示例将所有筛选字段打包进该对象。
 */
data class SearchCondition(
    /** 审核不通过原因枚举（可选） */
    @JsonProperty("audit_failure_reason")
    val auditFailureReason: String? = null,

    /** 更新时间 ≤ 指定时间；格式 2022-07-14 16:41:00（可选） */
    @JsonProperty("update_before")
    val updateBefore: String? = null,

    /** 创建时间 ≤ 指定时间（可选） */
    @JsonProperty("create_before")
    val createBefore: String? = null,

    /** 商品 ID（可选） */
    @JsonProperty("product_id")
    val productId: Long? = null,

    /** 创建时间 ≥ 指定时间（可选） */
    @JsonProperty("create_after")
    val createAfter: String? = null,

    /** 更新时间 ≥ 指定时间（可选） */
    @JsonProperty("update_after")
    val updateAfter: String? = null,

    /** 商品状态：ONLINE、PENDING_LAUNCH、PENDING_APPROVAL、VIOLATION_QC_FAILED、OFFLINE */
    @JsonProperty("product_status")

    val productStatus: String,
    /** 叶子类目 ID（可选） */
    @JsonProperty("leaf_category_id")
    val leafCategoryId: Long? = null,

    /** 欧盟责任人筛选，-2 表示未关联（可选） */
    @JsonProperty("msr_eu_id")
    val msrEuId: Long? = null,

    /** 制造商筛选，-2 表示未关联（可选） */
    @JsonProperty("manufacturer_id")
    val manufacturerId: Long? = null
): Serializable

/**
 * 响应：海外托管商品列表查询
 */
data class AliexpressLocalServiceProductsListResponse(
    /** 业务结果对象 */
    @JsonProperty("result")
    val result: Result,

) : BaseAliexpressResponse()

/** 查询结果 */
data class Result(
    /** 是否成功 */
    @JsonProperty("success")
    val success: Boolean,

    /** 总商品数 */
    @JsonProperty("total_count")
    val totalCount: Int,

    /** 总页数 */
    @JsonProperty("total_page")
    val totalPage: Int,

    /** 当前页 */
    @JsonProperty("current_page")
    val currentPage: Int,

    /** 每页商品数 */
    @JsonProperty("page_size")
    val pageSize: Int,

    /** 商品列表 */
    @JsonProperty("product_list")
    val productList: List<Product>
): Serializable

/** 商品信息 */
data class Product(
    /** 最大供货价 */
    @JsonProperty("product_max_price")
    val productMaxPrice: String,

    /** 创建时间，格式 yyyy-MM-dd HH:mm:ss */
    @JsonProperty("created_time")
    val createdTime: String,

    /** 最小供货价 */
    @JsonProperty("product_min_price")
    val productMinPrice: String,

    /** 商品主图 URL（逗号分隔） */
    @JsonProperty("image_urls")
    val imageUrls: String,

    /** 标题 */
    @JsonProperty("title")
    val title: String,

    /** 审核不通过原因明细（可选） */
    @JsonProperty("audit_failure_reason")
    val auditFailureReason: String? = null,

    /** 币种代码，如 CNY */
    @JsonProperty("currency_code")
    val currencyCode: String,

    /** 商品审核状态，1 表示通过 */
    @JsonProperty("product_audit_status")
    val productAuditStatus: Int,

    /** SKU 列表 */
    @JsonProperty("search_sku_info_list")
    val searchSkuInfoList: List<SkuInfo>,

    /** 运费模板 ID */
    @JsonProperty("shipping_template")
    val shippingTemplate: String,

    /** 修改时间 */
    @JsonProperty("modified_time")
    val modifiedTime: String,

    /** 叶子类目 ID */
    @JsonProperty("leaf_category_id")
    val leafCategoryId: Long,

    /** 商品 ID */
    @JsonProperty("product_id")
    val productId: Long,

    /** 商品总库存 */
    @JsonProperty("total_stocks")
    val totalStocks: Int,

    /** 审核不通过原因类型（可选） */
    @JsonProperty("audit_failure_type")
    val auditFailureType: String? = null
): Serializable

/** SKU 信息 */
data class SkuInfo(
    /** 审核不通过：建议标签（可选） */
    @JsonProperty("suggest_note")
    val suggestNote: String? = null,

    /** SKU 在各仓库的库存列表 */
    @JsonProperty("sku_warehouse_stock_list")
    val skuWarehouseStockList: List<QuerySkuWarehouseStock>,

    /** 当前生效供货价 */
    @JsonProperty("effective_supply_price")
    val effectiveSupplyPrice: String,

    /** 卖家自定义 SKU 编码 */
    @JsonProperty("seller_sku")
    val sellerSku: String,

    /** SKU 总库存 */
    @JsonProperty("sku_stock")
    val skuStock: Int,

    /** SKU 唯一标识 */
    @JsonProperty("sku_id")
    val skuId: Long,

    /** 供货价 */
    @JsonProperty("supply_price")
    val supplyPrice: String,

    /** 审核不通过：建议供货价（可选） */
    @JsonProperty("suggest_price")
    val suggestPrice: String? = null,

    /** SKU 审核状态，1 或空表示通过 */
    @JsonProperty("sku_audit_status")
    val skuAuditStatus: String,

    /** 属性串，例如 Color:Yellow */
    @JsonProperty("variation")
    val variation: String,

    /** 是否销售：active / inactive */
    @JsonProperty("status")
    val status: String
): Serializable

/** SKU 库存（按仓库维度） */
data class SkuWarehouseStock(
    /** 仓库类型，仅 jit_warehouse 可编辑库存 */
    @JsonProperty("warehouse_type")
    val warehouseType: String,

    /** 仓库名称 */
    @JsonProperty("warehouse_name")
    val warehouseName: String,

    /** 仓库编码 */
    @JsonProperty("warehouse_code")
    val warehouseCode: String,

    /** 可售库存数量 */
    @JsonProperty("sellable_quantity")
    val sellableQuantity: Int
): Serializable
