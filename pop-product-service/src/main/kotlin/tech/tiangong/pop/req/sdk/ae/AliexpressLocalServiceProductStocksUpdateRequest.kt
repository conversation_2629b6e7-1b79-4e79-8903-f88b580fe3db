package tech.tiangong.pop.req.sdk.ae

import com.fasterxml.jackson.annotation.JsonProperty
import tech.tiangong.pop.resp.sdk.aliexpress.BaseAliexpressResponse
import java.io.Serializable

/**
 * 请求：海外托管商品库存编辑
 * 接口名：aliexpress.local.service.product.stocks.update
 */
@AliexpressApiName("aliexpress.local.service.product.stocks.update")
data class AliexpressLocalServiceProductStocksUpdateRequest(

    /** 渠道 sellerId（LOCAL_SERVICE 店铺的 channel_seller_id） */
    @JsonProperty("channel_seller_id")
    val channelSellerId: Long,

    /** 商品 ID */
    @JsonProperty("product_id")
    val productId: Long,

    /** 渠道（通过 global.seller.relation.query 获取） */
    @JsonProperty("channel")
    val channel: String,

    /** 待更新的商品 SKU 库存列表 */
    @JsonProperty("product_sku_stock_list")
    val productSkuStockList: List<ProductSkuStockUpdate>
) : BaseAliexpressRequest() {
    companion object { private const val serialVersionUID = 1L }
}

/** 单个商品下的 SKU 库存编辑信息 */
data class ProductSkuStockUpdate(

    /** SKU 唯一标识，可通过 aliexpress.local.service.products.list 获取 */
    @JsonProperty("sku_id")
    val skuId: Long,

    /** 当前 SKU 在不同仓的库存更新列表 */
    @JsonProperty("sku_warehouse_stock_list")
    val skuWarehouseStockList: List<SkuWarehouseStockUpdate>
) : Serializable {
    companion object { private const val serialVersionUID = 1L }
}

/** SKU 的单仓库库存更新明细 */
data class SkuWarehouseStockUpdate(

    /** 仓库类型，仅 warehouse_type = dropshipping 的仓可编辑库存 */
    @JsonProperty("warehouse_type")
    val warehouseType: String,

    /** 仓库编码 */
    @JsonProperty("warehouse_code")
    val warehouseCode: String,

    /** 可售库存数量 */
    @JsonProperty("sellable_quantity")
    val sellableQuantity: Int
) : Serializable {
    companion object { private const val serialVersionUID = 1L }
}

/* ───────────────────────────────────────────────────────────────────────────── */

/**
 * 响应：海外托管商品库存编辑
 */
data class AliexpressLocalServiceProductStocksUpdateResponse(

    /** 当所有仓库库存均更新成功时为 true；部分失败需解析明细列表 */
    @JsonProperty("success")
    val success: Boolean,

    /** 商品 ID */
    @JsonProperty("product_id")
    val productId: Long? = null,

    /** 错误码（整体失败时给出） */
    @JsonProperty("error_code")
    val errorCode: String? = null,

    /** 各 SKU 的库存更新结果列表 */
    @JsonProperty("product_sku_stock_list")
    val productSkuStockList: List<ProductSkuStockResult> = emptyList()
) : BaseAliexpressResponse() {
    companion object { private const val serialVersionUID = 1L }
}

/** 单个 SKU 的库存更新执行结果 */
data class ProductSkuStockResult(

    /** SKU 唯一标识 */
    @JsonProperty("sku_id")
    val skuId: Long,

    /** 当前 SKU 在各仓库的更新结果列表 */
    @JsonProperty("sku_warehouse_stock_list")
    val skuWarehouseStockList: List<SkuWarehouseStockResult>
) : Serializable {
    companion object { private const val serialVersionUID = 1L }
}

/** SKU 在某仓库的库存更新结果 */
data class SkuWarehouseStockResult(

    /** 仓库编码 */
    @JsonProperty("warehouse_code")
    val warehouseCode: String,

    /** 单仓库库存是否更新成功 */
    @JsonProperty("success")
    val success: Boolean,

    /** 仓库级错误码（失败时给出） */
    @JsonProperty("error_code")
    val errorCode: String? = null,

    /** 仓库级错误信息（失败时给出） */
    @JsonProperty("error_message")
    val errorMessage: String? = null
) : Serializable {
    companion object { private const val serialVersionUID = 1L }
}
