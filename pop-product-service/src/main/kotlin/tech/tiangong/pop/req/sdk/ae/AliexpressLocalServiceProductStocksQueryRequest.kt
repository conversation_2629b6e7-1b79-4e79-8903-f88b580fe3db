package tech.tiangong.pop.req.sdk.ae

import com.fasterxml.jackson.annotation.JsonProperty
import tech.tiangong.pop.resp.sdk.aliexpress.BaseAliexpressResponse
import java.io.Serializable

/**
 * 请求：海外托管商品库存查询
 * 接口名：aliexpress.local.service.product.stocks.query
 */
@AliexpressApiName("aliexpress.local.service.product.stocks.query")
data class AliexpressLocalServiceProductStocksQueryRequest(

    /** 渠道 sellerId（全托管 ONE_STOP_SERVICE 店铺的 channel_seller_id） */
    @JsonProperty("channel_seller_id")
    val channelSellerId: Long,

    /** 商品 ID */
    @JsonProperty("product_id")
    val productId: Long,

    /** 渠道（可由 global.seller.relation.query 获取） */
    @JsonProperty("channel")
    val channel: String
) : BaseAliexpressRequest()

/**
 * 响应：海外托管商品库存查询
 */
data class AliexpressLocalServiceProductStocksQueryResponse(

    /** 请求是否成功 */
    @JsonProperty("success")
    val success: Boolean,

    /** 商品 SKU 库存列表 */
    @JsonProperty("product_sku_stock_list")
    val productSkuStockList: List<ProductSkuStock> = emptyList(),

    ) : BaseAliexpressResponse()

/** 商品下的 SKU 库存信息 */
data class ProductSkuStock(

    /** SKU 在不同仓库的库存列表 */
    @JsonProperty("sku_warehouse_stock_list")
    val querySkuWarehouseStockList: List<QuerySkuWarehouseStock> = emptyList(),

    /** SKU 唯一标识 */
    @JsonProperty("sku_id")
    val skuId: Long
) : Serializable

/** SKU 某仓库的库存详情 */
data class QuerySkuWarehouseStock(

    /** 仓库类型，仅类型 = dropshipping 的仓可编辑库存 */
    @JsonProperty("warehouse_type")
    val warehouseType: String,

    /** 仓库名称 */
    @JsonProperty("warehouse_name")
    val warehouseName: String,

    /** 仓库编码 */
    @JsonProperty("warehouse_code")
    val warehouseCode: String,

    /** 可售库存数量 */
    @JsonProperty("sellable_quantity")
    val sellableQuantity: Int,

    /** 是否允许编辑库存 */
    @JsonProperty("can_change_quantity")
    val canChangeQuantity: Boolean,

    /** 是否允许增加库存 */
    @JsonProperty("can_increase_quantity")
    val canIncreaseQuantity: Boolean,

    /** 是否允许减少库存 */
    @JsonProperty("can_decrease_quantity")
    val canDecreaseQuantity: Boolean
) : Serializable
