package tech.tiangong.pop.req.sdk.ae

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.databind.JsonNode
import com.global.iop.api.IopRequest
import team.aikero.blade.util.json.Json
import java.io.Serializable

/**
 * AE 速卖通API响应基类
 */
@JsonIgnoreProperties(ignoreUnknown = true)
open class BaseAliexpressRequest : Serializable {

    fun toRequest(): IopRequest {
        val request = IopRequest()
        request.apiName = javaClass.getAnnotation(AliexpressApiName::class.java)?.value
        // 1.如果是 dto 对象，转成 json 字符串
        // 2.如果为空，不添加参数
        val nodes = Json.instance.valueToTree<JsonNode>(this)
        nodes.fields().forEach { entry ->
            if (entry.value != null) {
                request.addApiParameter(entry.key, entry.value.asText())
            }
        }
        return request
    }

    companion object {
        private const val serialVersionUID = 1L  // 使用驼峰式命名更符合 Java 序列化约定
    }
}
