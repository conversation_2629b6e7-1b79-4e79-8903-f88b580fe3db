package tech.tiangong.pop.req.sdk.ae

import com.fasterxml.jackson.annotation.JsonProperty
import tech.tiangong.pop.resp.sdk.aliexpress.BaseAliexpressResponse
import java.io.Serializable

/* ----------------------------- 请求 ----------------------------- */

/**
 * 申请变更海外托管商品上下架状态 – Request
 */
@AliexpressApiName("aliexpress.local.service.product.status.update")
data class AliexpressLocalServiceProductStatusUpdateRequest(

    /** 渠道 sellerId，可通过 global.seller.relation.query 获取 */
    @JsonProperty("channel_seller_id")
    val channelSellerId: String,

    /** 商品 ID */
    @JsonProperty("product_id")
    val productId: String,

    /** 渠道代码，例如 AE_GLOBAL */
    @JsonProperty("channel")
    val channel: String,

    /** 商品操作类型：ON_SHELF / OFF_SHELF */
    @JsonProperty("product_opt_type")
    val productOptType: String
) : BaseAliexpressRequest()


/* ----------------------------- 响应 ----------------------------- */

/**
 * 申请变更海外托管商品上下架状态 – Response
 */
data class AliexpressLocalServiceProductStatusUpdateResponse(

    /** 商品 ID */
    @JsonProperty("product_id")
    val productId: Long? = null,

    /** 是否成功 */
    @JsonProperty("success")
    val success: Boolean? = null,

) : BaseAliexpressResponse(), Serializable
