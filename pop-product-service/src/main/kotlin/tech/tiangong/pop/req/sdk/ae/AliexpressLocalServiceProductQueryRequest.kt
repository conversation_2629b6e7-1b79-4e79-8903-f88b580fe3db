package tech.tiangong.pop.req.sdk.ae

import com.fasterxml.jackson.annotation.JsonProperty
import tech.tiangong.pop.resp.sdk.aliexpress.BaseAliexpressResponse
import java.io.Serializable

/**
 * 查询单个海外托管商品详情 – Request
 */
@AliexpressApiName("aliexpress.local.service.product.query")
data class AliexpressLocalServiceProductQueryRequest(

    /** 渠道 sellerId，可通过 global.seller.relation.query 获取 */
    @JsonProperty("channel_seller_id")
    val channelSellerId: Long,

    /** 商品 ID */
    @JsonProperty("product_id")
    val productId: Long,

    /** 渠道代码，例如 AE_GLOBAL */
    @JsonProperty("channel")
    val channel: String
) : BaseAliexpressRequest()

/**
 * 查询单个海外托管商品详情 – Response
 */
data class AliexpressLocalServiceProductQueryResponse(

    /** 调用是否成功 */
    @JsonProperty("success")
    val success: Boolean? = null,

    /** 商品模型 */
    @JsonProperty("local_service_product_dto")
    val product: AliexpressLocalServiceProductDto? = null,

) : BaseAliexpressResponse(), Serializable
