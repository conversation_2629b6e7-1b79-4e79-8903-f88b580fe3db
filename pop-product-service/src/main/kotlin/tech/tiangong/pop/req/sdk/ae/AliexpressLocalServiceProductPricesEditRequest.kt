package tech.tiangong.pop.req.sdk.ae

import com.fasterxml.jackson.annotation.JsonProperty
import tech.tiangong.pop.resp.sdk.aliexpress.BaseAliexpressResponse
import java.io.Serializable


/**
 * 编辑商品 SKU 供货价 – Request
 */
@AliexpressApiName("aliexpress.local.service.product.prices.edit")
data class AliexpressLocalServiceProductPricesEditRequest(

    /** 渠道 sellerId，可通过 global.seller.relation.query 获取 */
    @JsonProperty("channel_seller_id")
    val channelSellerId: Long,

    /** SKU 供货价列表 */
    @JsonProperty("sku_price_model_list")
    val skuPriceModelList: List<SkuPriceModelDto>,

    /** 商品 ID */
    @JsonProperty("product_id")
    val productId: Long,

    /** 渠道代码，例如 AE_GLOBAL */
    @JsonProperty("channel")
    val channel: String
) : BaseAliexpressRequest()

/**
 * SKU 供货价模型
 */
data class SkuPriceModelDto(

    /** SKU 唯一标识 */
    @JsonProperty("sku_id")
    val skuId: Long,

    /** SKU 供货价（必填） */
    @JsonProperty("supply_price")
    val supplyPrice: String,

    /** 分目的国家供货价列表 */
    @JsonProperty("dim_supply_price_list")
    val dimSupplyPriceList: List<DimSupplyPriceDto>? = null
) : Serializable

/**
 * 分国家供货价
 */
data class DimSupplyPriceDto(

    /** 国家代码，如 ES、KR 等 */
    @JsonProperty("country_code")
    val countryCode: String,

    /** 该国对应供货价 */
    @JsonProperty("country_supply_price")
    val countrySupplyPrice: String
) : BaseAliexpressRequest()



/**
 * 编辑商品 SKU 供货价 – Response
 */
data class AliexpressLocalServiceProductPricesEditResponse(

    /** 商品 ID */
    @JsonProperty("product_id")
    val productId: Long? = null,

    /** 是否成功（请求成功后会进入审核流程） */
    @JsonProperty("success")
    val success: String? = null,

    /** 错误码 */
    @JsonProperty("error_code")
    val errorCode: String? = null,

) : BaseAliexpressResponse()
