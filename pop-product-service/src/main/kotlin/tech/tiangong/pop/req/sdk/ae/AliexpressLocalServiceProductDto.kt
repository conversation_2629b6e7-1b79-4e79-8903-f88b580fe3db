package tech.tiangong.pop.req.sdk.ae

import com.fasterxml.jackson.annotation.JsonProperty
import java.io.Serializable

/**
 * 海外托管商品模型
 * */
data class AliexpressLocalServiceProductDto(

    /** 媒体信息（主图、营销图、视频等） */
    @JsonProperty("multimedia")
    val multimedia: MultimediaDto,

    /** SKU 列表 */
    @JsonProperty("product_sku_list")
    val productSkuList: List<ProductSkuDto>,

    /** 包裹信息 */
    @JsonProperty("package_dto")
    val packageDto: PackageDto,

    /** 商品基本信息 */
    @JsonProperty("product_info_dto")
    val productInfo: ProductInfoDto,

    /** 商品属性列表 */
    @JsonProperty("product_property_list")
    val productPropertyList: List<ProductPropertyDto>,

    /** 详描列表 */
    @JsonProperty("detail_source_list")
    val detailSourceList: List<DetailSourceDto>,

    /** 扩展信息 */
    @JsonProperty("product_ext_dto")
    val productExt: ProductExtDto
) : Serializable {
    companion object { private const val serialVersionUID = 1L }
}

/* 多媒体信息 */
data class MultimediaDto(

    /** 是否处理分国家主图 */
    @JsonProperty("process_customized_images")
    val processCustomizedImages: String? = null,

    /** 主图 URL 列表 */
    @JsonProperty("main_image_list")
    val mainImageList: List<String>,

    /** 营销图列表 */
    @JsonProperty("market_image_list")
    val marketImageList: List<MarketImageDto>,

    /** 视频列表 */
    @JsonProperty("video_list")
    val videoList: List<VideoDto>? = null
) : Serializable {
    companion object { private const val serialVersionUID = 1L }
}

data class MarketImageDto(
    /** 图片 URL */
    @JsonProperty("url")
    val url: String,

    /** 图片类型：1=长图 2=方图 */
    @JsonProperty("image_type")
    val imageType: Int
) : Serializable {
    companion object { private const val serialVersionUID = 1L }
}

data class VideoDto(
    /** 视频类型，示例 MAIN_IMAGE_VIDEO */
    @JsonProperty("media_type")
    val mediaType: String,

    /** 视频封面 URL */
    @JsonProperty("poster_url")
    val posterUrl: String,

    /** 媒体中心返回的 mediaId */
    @JsonProperty("media_id")
    val mediaId: Long
) : Serializable {
    companion object { private const val serialVersionUID = 1L }
}

/* SKU 信息 */
data class ProductSkuDto(

    /** 供货价 */
    @JsonProperty("supply_price")
    val supplyPrice: String,

    /** SKU 销售属性列表（最多 1-3 个） */
    @JsonProperty("sku_property_list")
    val skuPropertyList: List<SkuPropertyDto>? = null,

    /** SKU 编码 */
    @JsonProperty("sku_code")
    val skuCode: String? = null,

    /** SKU 状态：active / inactive */
    @JsonProperty("status")
    val status: String? = null,

    /** 包裹重量（KG 或磅/盎司） */
    @JsonProperty("package_weight")
    val packageWeight: String,

    /** 包裹尺寸（长宽高，单位 cm 或 inch） */
    @JsonProperty("package_length")
    val packageLength: String,
    @JsonProperty("package_width")
    val packageWidth: String,
    @JsonProperty("package_height")
    val packageHeight: String,

    /** 可售库存 */
    @JsonProperty("sellable_quantity")
    val sellableQuantity: Int,

    /** 分目的国供货价列表 */
    @JsonProperty("dest_country_supply_price_list")
    val destCountrySupplyPriceList: List<DestCountrySupplyPriceDto>? = null,

    /** 重量单位（美国卖家专用：1=磅 2=盎司） */
    @JsonProperty("package_weight_unit")
    val packageWeightUnit: String? = null
) : Serializable {
    companion object { private const val serialVersionUID = 1L }
}

data class SkuPropertyDto(
    /** 销售属性 ID */
    @JsonProperty("sku_property_id") val skuPropertyId: Long? = null,
    /** 销售属性名称 */
    @JsonProperty("sku_property_name") val skuPropertyName: String? = null,
    /** 销售属性值 */
    @JsonProperty("sku_property_value") val skuPropertyValue: String? = null,
    /** 销售属性值 ID */
    @JsonProperty("property_value_id") val propertyValueId: Long? = null,
    /** 销售属性别名 */
    @JsonProperty("property_value_definition_name") val propertyValueDefinitionName: String? = null,
    /** SKU 图片 */
    @JsonProperty("sku_image") val skuImage: String? = null
) : Serializable {
    companion object { private const val serialVersionUID = 1L }
}

data class DestCountrySupplyPriceDto(
    /** 国家编码，例如西班牙：ES */
    @JsonProperty("country_code") val countryCode: String,
    /** 对应目的国家的供货价 */
    @JsonProperty("money_value") val moneyValue: String
) : Serializable {
    companion object { private const val serialVersionUID = 1L }
}

/* 包裹信息 */
data class PackageDto(
    /** 每包的数量 */
    @JsonProperty("lot_num") val lotNum: Int? = null,
    /** 产品的单位 */
    @JsonProperty("product_unit") val productUnit: Int? = null,
    /** 打包销售: true 非打包销售:false */
    @JsonProperty("package_type") val packageType: Boolean? = null
) : Serializable {
    companion object { private const val serialVersionUID = 1L }
}

/* 商品基本信息 */
data class ProductInfoDto(
    /**商品标题*/
    @JsonProperty("subject_list") val subjectList: List<SubjectDto>,
    /** 商品分类 ID */
    @JsonProperty("category_id") val categoryId: Long,
    /** 运费模板 ID */
    @JsonProperty("postage_id") val postageId: Long,
    /**商品原发语种：en_US*/
    @JsonProperty("locale") val locale: String,
    /** 货币单位。如果不提供该值信息，则默认为"USD"；非俄罗斯卖家这个属性值可以不提供。对于俄罗斯海外卖家，该单位值必须提供，如: "RUB"。 */
    @JsonProperty("currency_code") val currencyCode: String? = null,
    /** 是否支持分目的国供货价设置，默认为false，如果需要设置分目的国家供货价，则传true，会取sku维度的dest_country_supply_price_list对应目的国家供货价 */
    @JsonProperty("support_country_supply_price") val supportCountrySupplyPrice: Boolean? = null
) : Serializable {
    companion object { private const val serialVersionUID = 1L }
}

data class SubjectDto(
    /** 语种 ：en_US */
    @JsonProperty("locale") val locale: String,
    /**多语言标题内容*/
    @JsonProperty("value") val value: String
) : Serializable {
    companion object { private const val serialVersionUID = 1L }
}

/* 商品属性 */
data class ProductPropertyDto(
    /** 属性名ID */
    @JsonProperty("attr_name_id") val attrNameId: Long? = null,
    /** 属性值ID */
    @JsonProperty("attr_value_id") val attrValueId: Long? = null,
    /** 属性名 */
    @JsonProperty("attr_name") val attrName: String? = null,
    /** 属性值名称 */
    @JsonProperty("attr_value") val attrValue: String? = null,
    /** 属性单位 */
    @JsonProperty("attr_value_unit") val attrValueUnit: String? = null,
    /** 属性值区间开始 */
    @JsonProperty("attr_value_start") val attrValueStart: String? = null,
    /** 属性值区间结束 */
    @JsonProperty("attr_value_end") val attrValueEnd: String? = null
) : Serializable {
    companion object { private const val serialVersionUID = 1L }
}

/* 详描 */
data class DetailSourceDto(
    /** 语种 ：en_US */
    @JsonProperty("locale") val locale: String,
    /** 移动端详描内容，具体格式请参考： https://open.aliexpress.com/doc/doc.htm#/?docId=725 */
    @JsonProperty("mobile_detail") val mobileDetail: String? = null,
    /** PC 端详描内容，注意!!! 对于PC端详描内容校验规则：关联模块(relation)数量至多1个，PC端详描下总信息模块数至多3个，当type=”html"并且其content中包含关联模块(relation)或自定义模块(custom)时，其中的关联模块和自定义模块也会被参与计数，请isv自行排查"html"模块中是否有关联模块(relation)和自定义模块(custom),请isv们做好前置校验，避免重复计数。具体格式请参考：https://open.aliexpress.com/doc/doc.htm#/?docId=725*/
    @JsonProperty("web_detail") val webDetail: String? = null
) : Serializable {
    companion object { private const val serialVersionUID = 1L }
}

/* 扩展信息 */
data class ProductExtDto(
    /** 制造商 ID */
    @JsonProperty("manufacturer_id") val manufacturerId: Long? = null,
    /** 欧盟责任人id，需要调用查询欧盟责任人列表接口：aliexpress.category.eu.responsible.persons.list，获取对应的欧盟责任人列表 */
    @JsonProperty("msr_eu_id") val msrEuId: Long? = null,
    /** 发货期，商家承诺的发货时间，目前取消发货期走白名单中 */
    @JsonProperty("delivery_time") val deliveryTime: Int? = null,
    /** 产品所关联的尺码模版ID */
    @JsonProperty("size_chart_id") val sizeChartId: Long? = null,
    /** 产品所关联的尺码模版ID列表 */
    @JsonProperty("size_chart_id_list") val sizeChartIdList: List<Long>? = null,
    /** 产品资质信息 */
    @JsonProperty("aeop_qualification_struct_list") val qualificationList: List<QualificationDto>? = null
) : Serializable {
    companion object { private const val serialVersionUID = 1L }
}

data class QualificationDto(
    /** 资质信息类型 ："image","text" */
    @JsonProperty("type") val type: String? = null,  // image / text
    /** 具体的资质信息：图片链接或文本 */
    @JsonProperty("value") val value: String? = null,
    /** 资质信息key, 每个类目下的key都不一样，请先调用资质信息查询接口：aliexpress.category.qualifications.list，查询对应的key和type */
    @JsonProperty("key") val key: String? = null
) : Serializable {
    companion object { private const val serialVersionUID = 1L }
}
