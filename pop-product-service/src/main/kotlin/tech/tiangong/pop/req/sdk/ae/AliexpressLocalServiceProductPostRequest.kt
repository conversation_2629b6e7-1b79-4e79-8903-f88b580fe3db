package tech.tiangong.pop.req.sdk.ae

import com.fasterxml.jackson.annotation.JsonProperty
import tech.tiangong.pop.resp.sdk.aliexpress.BaseAliexpressResponse

/**
 * 请求：海外托管店铺-商品发布
 */
@AliexpressApiName("aliexpress.local.service.product.post")
data class AliexpressLocalServiceProductPostRequest(

    /** 渠道 sellerId（LOCAL_SERVICE 店铺的 channel_seller_id） */
    @JsonProperty("channel_seller_id")
    val channelSellerId: Long,

    /** 商品主体信息 DTO */
    @JsonProperty("local_service_product_dto")
    val localServiceProduct: AliexpressLocalServiceProductDto,

    /** 渠道（通过 global.seller.relation.query 获取） */
    @JsonProperty("channel")
    val channel: String
) : BaseAliexpressRequest() {
    companion object { private const val serialVersionUID = 1L }
}

/**
 * 响应：海外托管店铺-商品发布
 */
data class AliexpressLocalServiceProductPostResponse(

    /** 接口调用是否成功 */
    @JsonProperty("success")
    val success: Boolean,

    /** 创建成功返回的商品 ID */
    @JsonProperty("product_id")
    val productId: Long? = null
) : BaseAliexpressResponse() {
    companion object { private const val serialVersionUID = 1L }
}
