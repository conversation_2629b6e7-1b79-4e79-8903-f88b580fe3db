package tech.tiangong.pop.req.sdk.ae

import com.fasterxml.jackson.annotation.JsonProperty
import tech.tiangong.pop.resp.sdk.aliexpress.BaseAliexpressResponse

@AliexpressApiName("aliexpress.local.service.product.edit")
data class AliexpressLocalServiceProductEditRequest(

    /** 渠道 sellerId，可通过 global.seller.relation.query 获取 */
    @JsonProperty("channel_seller_id")
    val channelSellerId: Long,

    /** 商品模型 */
    @JsonProperty("local_service_product_dto")
    val localServiceProduct: AliexpressLocalServiceProductDto,

    /** 渠道代码，例如 AE_GLOBAL */
    @JsonProperty("channel")
    val channel: String
) : BaseAliexpressRequest()


data class AliexpressLocalServiceProductEditResponse(

    /** 商品 ID */
    @JsonProperty("product_id")
    val productId: Long? = null,

    /** 操作是否成功 */
    @JsonProperty("success")
    val success: Boolean? = null
) : BaseAliexpressResponse()
