package tech.tiangong.pop.req.category

import jakarta.validation.constraints.NotEmpty
import jakarta.validation.constraints.NotNull

/**
 * 复制品类已关联属性值入参
 */

class CopyCategoryAttributeValueMappingReq {

    @field:NotNull(message = "源品类映射ID不能为空")
    var sourceCategoryMappingId: Long? = null
    @field:NotEmpty(message = "目标品类映射ID不能为空")
    var targetCategoryMappingIds: MutableList<Long>? = mutableListOf()
}
