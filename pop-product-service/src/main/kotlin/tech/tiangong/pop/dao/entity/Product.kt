package tech.tiangong.pop.dao.entity

import com.baomidou.mybatisplus.annotation.IdType
import com.baomidou.mybatisplus.annotation.TableField
import com.baomidou.mybatisplus.annotation.TableId
import com.baomidou.mybatisplus.annotation.TableName
import team.aikero.blade.util.json.parseJsonList
import tech.tiangong.pop.common.dto.ImageLabelInfoDto
import tech.tiangong.pop.dao.BaseEntity
import java.math.BigDecimal
import java.time.LocalDateTime

/**
 * 商品主表(Product)表名: product
 *
 * <AUTHOR>
 * @since 2025-02-18 15:23:12
 */
@TableName(value = "product")
data class Product(
    /**
     * 主键
     */
    @TableId(value = "product_id", type = IdType.ASSIGN_ID)
    var productId: Long? = null,

    /**
     * 灵感来源
     */
    @TableField(value = "inspira_source")
    var inspiraSource: String? = null,
    /**
     * 灵感来源ID
     */
    @TableField(value = "inspira_source_id")
    var inspiraSourceId: Long? = null,
    /**
     * 企划来源名
     */
    @TableField(value = "plan_source_name")
    var planSourceName: String? = null,
    /**
     * 企划来源ID
     */
    @TableField(value = "plan_source_id")
    var planSourceId: Long? = null,
    /**
     * 版型号
     */
    @TableField(value = "prototype_num")
    var prototypeNum: String? = null,
    /**
     * 商品标题
     */
    @TableField(value = "product_title")
    var productTitle: String? = null,

    /**
     * 定价类型【1按返单规则；2不返单规则 】
     */
    @TableField(value = "pricing_type")
    var pricingType: Int? = null,
    /**
     * 现货类型【1现货try on ；2ifashion】
     */
    @TableField(value = "spot_type")
    var spotType: Int? = null,

    /**
     * 现货类型OPS编码
     */
    @TableField(value = "spot_type_code")
    var spotTypeCode: String? = null,

    /**
     * 所有站点标题
     */
    @TableField(value = "all_country_title")
    var allCountryTitle: String? = null,
    /**
     * 选款人ID
     */
    @TableField(value = "select_style_id")
    var selectStyleId: Long? = null,
    /**
     * 货盘类型
     */
    @TableField(value = "goods_rep_type")
    var goodsRepType: String? = null,
    /**
     * 商品类型
     */
    @TableField(value = "goods_type")
    var goodsType: String? = null,
    /**
     * 选款人姓名
     */
    @TableField(value = "select_style_name")
    var selectStyleName: String? = null,
    /**
     * 灵感图URL
     */
    @TableField(value = "inspira_img_url")
    var inspiraImgUrl: String? = null,
    /**
     * 选款时间
     */
    @TableField(value = "select_style_time")
    var selectStyleTime: LocalDateTime? = null,
    /**
     * 供给方式
     */
    @TableField(value = "supply_mode")
    var supplyMode: String? = null,
    /**
     * 买手备注
     */
    @TableField(value = "buyer_remark")
    var buyerRemark: String? = null,
    /**
     * 主图url链接
     */
    @TableField(value = "main_img_url")
    var mainImgUrl: String? = null,
    /**
     * 设计师id
     */
    @TableField(value = "designer_id")
    var designerId: String? = null,
    /**
     * 设计师名称
     */
    @TableField(value = "design_name")
    var designName: String? = null,
    @TableField(value = "design_group")
    var designGroup: String? = null,
    @TableField(value = "brand_name")
    var brandName: String? = null,
    /**
     * 面料名称
     */
    @TableField(value = "fabric_name")
    var fabricName: String? = null,
    /**
     * 渠道ID
     */
    @TableField(value = "channel_id")
    var channelId: String? = null,
    /**
     * 平台ID
     */
    @TableField(value = "platform_id")
    var platformId: String? = null,
    /**
     * 上架人
     */
    @TableField(value = "publish_author")
    var publishAuthor: String? = null,
    /**
     * 价格是否异常(1异常)
     */
    @TableField(value = "price_exception")
    var priceException: Int? = null,
    /**
     * 波次
     */
    @TableField(value = "waves")
    var waves: String? = null,
    /**
     * spu编码
     */
    @TableField(value = "spu_code")
    var spuCode: String? = null,
    /**
     * 店铺ID
     */
    @TableField(value = "shop_id")
    var shopId: Long? = null,
    /**
     * 竞品销售价（美元）
     */
    @TableField(value = "cmp_sale_price")
    var cmpSalePrice: BigDecimal? = null,
    @TableField(value = "package_dimensions_length")
    var packageDimensionsLength: String? = null,
    @TableField(value = "package_weight")
    var packageWeight: String? = null,
    @TableField(value = "package_dimensions_height")
    var packageDimensionsHeight: String? = null,
    @TableField(value = "package_dimensions_width")
    var packageDimensionsWidth: String? = null,
    /**
     * 竞品划线价格（美元）
     */
    @TableField(value = "cmp_retail_price")
    var cmpRetailPrice: BigDecimal? = null,
    /**
     * 店铺名称
     */
    @TableField(value = "shop_name")
    var shopName: String? = null,
    /**
     * 是否同步过平台【0待发布；1已发布过】
     */
    @TableField(value = "is_sync_platform")
    var isSyncPlatform: Int? = null,
    /**
     * 是否更新【1有更新；0没有】
     */
    @TableField(value = "is_update")
    var isUpdate: Int? = null,
    /**
     * 是否初始化模板(待上架) 1是0否
     */
    @TableField(value = "init_template")
    var initTemplate: Int? = null,
    /**
     * 发布时间
     */
    @TableField(value = "publish_time")
    var publishTime: LocalDateTime? = null,
    /**
     * 首次上架用户ID
     */
    @TableField(value = "publish_user_id")
    var publishUserId: Long? = null,

    /**
     * 首次上架用户名称
     */
    @TableField(value = "publish_user_name")
    var publishUserName: String? = null,
    /**
     * 尺码组名称
     */
    @TableField(value = "size_group_name")
    var sizeGroupName: String? = null,
    /**
     * 尺码组编码
     */
    @TableField(value = "size_group_code")
    var sizeGroupCode: String? = null,
    /**
     * 商品上架状态【0待上架;1上架；2下架】
     */
    @TableField(value = "publish_state")
    var publishState: Int? = null,
    /**
     * 平台同步状态【0待同步;2同步中；1已同步；-1同步失败】
     */
    @TableField(value = "platform_sync_state")
    var platformSyncState: Int? = null,
    /**
     * 国家站点(多站点-分割)
     */
    @TableField(value = "countrys")
    var countrys: String? = null,
    /**
     * 品类编码（多级-分割）
     */
    @TableField(value = "category_code")
    var categoryCode: String? = null,
    /**
     * 品类名称（多级-分割）
     */
    @TableField(value = "category_name")
    var categoryName: String? = null,
    /**
     * 品类ID
     */
    @TableField(value = "category_id")
    var categoryId: Long? = null,
    /**
     * 标签(1是 0否):可用库存
     */
    @TableField(value = "tag_available_inv")
    var tagAvailableInv: Int? = null,
    /**
     * 标签(1是 0否):常规清仓
     */
    @TableField(value = "tag_reg_clear")
    var tagRegClear: Int? = null,
    /**
     * 提交平台json数组(platform_id)
     */
    @TableField(value = "submit_platform")
    var submitPlatform: String? = null,
    /**
     * 是否历史数据【1：是】
     */
    @TableField(value = "is_history")
    var isHistory: Int? = null,
    /**
     * 是否异常数据(1是0否)
     */
    @TableField(value = "is_error")
    var isError: Int? = null,
    /**
     * 异常类型json数组[[{"errorType":  1, "errorMsg":  "SKU为空"}]
     */
    @TableField(value = "error_info")
    var errorInfo: String? = null,
    /**
     * 款式风格名称
     */
    @TableField(value = "clothing_style_name")
    var clothingStyleName: String? = null,
    /**
     * 款式风格编码
     */
    @TableField(value = "clothing_style_code")
    var clothingStyleCode: String? = null,

    /**
     * 企划类型(1:企划内/2:企划外)
     */
    @TableField(value = "planning_type")
    var planningType: Int? = null,

    /**
     * 市场编码
     */
    @TableField(value = "market_code")
    var marketCode: String? = null,

    /**
     * 市场系列编码
     */
    @TableField(value = "market_series_code")
    var marketSeriesCode: String? = null,

    /**
     * 图片标签信息集合
     */
    @TableField(value = "image_label_info_list")
    var imageLabelInfoList: String? = null,
    /**
     * 印花类型
     * @see tech.tiangong.pop.common.enums.PrintTypeEnum
     */
    @TableField("print_type")
    var printType: String? = null,

    /**
     * 元素名称
     */
    @TableField(value = "style_element_name")
    var styleElementName: String? = null,

    /**
     * 元素编码
     */
    @TableField(value = "style_element_code")
    var styleElementCode: String? = null,

    /**
     * 季节json, 多选
     * List<tech.tiangong.sdp.design.vo.base.prototype.OpsObject>
     */
    @TableField(value = "style_season")
    var styleSeason: String? = null,

    /**
     * 合身编码(版型)-OPS
     */
    @TableField(value = "fit_code")
    var fitCode: String? = null,

    /**
     * 合身名称(版型)
     */
    @TableField(value = "fit_name")
    var fitName: String? = null,

    /** 款式类型1设计款2现货款3数据印花款 */
    @TableField(value = "style_type")
    var styleType: Int? = null,

    /** 图包状态1未完成2已完成3更新中4已更新
     * 参考 ProductImagePackageStateEnum
     * */
    @TableField(value = "image_package_state")
    var imagePackageState: Int? = null,
    /**
     * 上架时使用的图包版本
     */
    @TableField(value = "image_version_num")
    var imageVersionNum: Int? = null,

    /**
     * 企划审核状态0待审核1通过2不通过
     * 参考 ProductPlanAuditStateEnum
     */
    @TableField(value = "plan_audit_state")
    var planAuditState: Int? = null,
    /**
     * 企划审核人ID
     */
    @TableField(value = "plan_auditor_id")
    var planAuditorId: Long? = null,
    /**
     * 企划审核人
     */
    @TableField(value = "plan_auditor_name")
    var planAuditorName: String? = null,
    /**
     * 企划审核时间
     */
    @TableField(value = "plan_audit_time")
    var planAuditTime: LocalDateTime? = null,
    /**
     * 选款店铺毛利率
     */
    @TableField(value = "gross_margin")
    var grossMargin: BigDecimal? = null,

    /**
     * 毛利率是否应用到所有店铺，1是0否
     */
    @TableField(value = "gross_margin_use_all_shop")
    var grossMarginUseAllShop:Int? = null,
    /**
     * 商品所在店铺毛利率配置 JSON对象
     * 参考 ProductShopGrossMarginConfigDto
     */
    @TableField(value = "shop_gross_margin")
    var shopGrossMargin: String? = null,
    /**
     * 商品主题编码
     */
    @TableField(value = "product_theme_code")
    var productThemeCode: String? = null,

    /**
     * 商品主题名称
     */
    @TableField(value = "product_theme_name")
    var productThemeName: String? = null,

    /**
     * 商品来源1款式平台2手动导入
     * 参考 ProductSourceTypeEnum
     */
    @TableField(value = "source_type")
    var sourceType:Int? = null,

    /**
     * 商品分类1AI-时装2AI-现货3AI-POD
     * 参考 ProductTypeEnum
     */
    @TableField(value = "product_type")
    var productType:Int? = null,
    /**
     * 物流包装信息
     * 参考 ProductPackageInfoDto
     */
    @TableField(value = "package_info")
    var packageInfo: String? = null,

    /**
     * 电子说明书 上传文件JSON数组
     * 参考 FileUploadDTO
     *
     */
    @TableField(value = "instructions")
    var instructions: String? = null,
    /**
     * 商品资料完善状态 0待补全1已完善
     * -商品规格：颜色，供货价&采购价，尺码，尺寸表任一信息缺失即不完善，反之为完善
     * -商品属性：必填项均有值则为完善，反之为不完善；必填项=各平台必填属性已映射的内部属性并集
     * -物流包装：必填项均有值则为完善，反之为不完善
     * -图片&视频：主图&详情图有值即为完善，反之为不完善
     * -平台属性：必填项均有值则为完善，反之为不完善
     */
    @TableField(value = "resource_state")
    var resourceState: Int? = null,
    /**
     * 视频状态1未完成2已完成3更新中4已更新
     * 参考 ProductVideoStateEnum
     */
    @TableField(value = "video_state")
    var videoState: Int? = null,

    /**
     * 上游款式是否已取消1是0否
     */
    @TableField(value = "spu_canceled")
    var spuCanceled: Int? = null,

    /**
     * 买手id
     */
    @TableField(value = "buyer_id")
    var buyerId: Long? = null,

    /**
     * 买手名称
     */
    @TableField(value = "buyer_name")
    var buyerName: String? = null,

    /**
     * 场景名称(ops: JV_scene)
     */
    @TableField(value = "scene_name")
    var sceneName: String? = null,

    /**
     * 场景编码
     */
    @TableField(value = "scene_code")
    var sceneCode: String? = null,

    /**
     * 品质等级
     */
    @TableField(value = "quality_level")
    var qualityLevel: String? = null,

    /**
     * 品质等级编号
     */
    @TableField(value = "quality_level_code")
    var qualityLevelCode: String? = null,

    /**
     * 织造方式code
     */
    @TableField(value = "weave_mode_code")
    var weaveModeCode: String? = null,

    /**
     * 织造方式
     */
    @TableField(value = "weave_mode")
    var weaveMode: String? = null,
    /**
     * 灵感图来源编码
     */
    @TableField(value = "inspiration_image_source_code")
    var inspirationImageSourceCode: String? = null,

    /**
     * 灵感图来源
     */
    @TableField(value = "inspiration_image_source")
    var inspirationImageSource: String? = null,

    /**
     * 灵感源品牌编码
     */
    @TableField(value = "inspiration_brand_code")
    var inspirationBrandCode: String? = null,

    /**
     * 灵感源品牌
     */
    @TableField(value = "inspiration_brand")
    var inspirationBrand: String? = null,
    /**
     * 企划来源name
     */
    @TableField(value = "planning_source_name")
    var planningSourceName: String? = null,

    /**
     * 企划来源code
     */
    @TableField(value = "planning_source_code")
    var planningSourceCode: String? = null,
    /**
     * 商品链接
     */
    @TableField(value = "product_link")
    var productLink: String? = null,

    /**
     * 商品名（中文）
     */
    @TableField(value = "spu_name")
    var spuName: String? = null,

    /**
     * 商品名翻译
     */
    @TableField(value = "spu_name_trans")
    var spuNameTrans: String? = null,

) : BaseEntity() {
    fun setIsHistory(r: Int?) {
        isHistory = r
    }

    fun setIsSyncPlatform(r: Int?) {
        isSyncPlatform = r
    }

    fun setIsUpdate(r: Int?) {
        isUpdate = r
    }

    fun setIsError(r: Int?) {
        isError = r
    }

    fun getIsSyncPlatform(): Int? {
        return isSyncPlatform
    }

    fun getIsHistory(): Int? {
        return isHistory
    }

    fun getIsUpdate(): Int? {
        return isUpdate
    }

    fun getIsError(): Int? {
        return isError
    }

    fun getParsedImageLabelInfoList(): List<ImageLabelInfoDto> {
        return imageLabelInfoList?.parseJsonList(ImageLabelInfoDto::class.java) ?: emptyList()
    }
}
