package tech.tiangong.pop.dao.repository

import org.springframework.stereotype.Repository
import team.aikero.blade.data.mybatis.repository.BaseRepository
import tech.tiangong.pop.dao.entity.ProductPublishStore
import tech.tiangong.pop.dao.mapper.ProductPublishStoreMapper

@Repository
class ProductPublishStoreRepository : BaseRepository<ProductPublishStoreMapper, ProductPublishStore>() {

    fun getByProductIdAndShopId(productId: Long,shopId:Long): ProductPublishStore? {
        return ktQuery().eq(ProductPublishStore::productId,productId)
            .eq(ProductPublishStore::shopId,shopId).one()
    }

    fun listByProductIds(productIds: List<Long>): List<ProductPublishStore> {
        return ktQuery().`in`(ProductPublishStore::productId,productIds)
            .list()
    }
}
