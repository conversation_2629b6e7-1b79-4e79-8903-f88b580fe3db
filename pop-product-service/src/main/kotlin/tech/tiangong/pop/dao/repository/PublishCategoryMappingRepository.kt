package tech.tiangong.pop.dao.repository

import com.baomidou.mybatisplus.core.metadata.IPage
import com.baomidou.mybatisplus.extension.plugins.pagination.Page
import org.apache.commons.collections4.CollectionUtils
import org.springframework.stereotype.Repository
import team.aikero.blade.core.toolkit.isNotNull
import team.aikero.blade.data.mybatis.repository.BaseRepository
import tech.tiangong.pop.dao.entity.PublishCategoryMapping
import tech.tiangong.pop.dao.mapper.PublishCategoryMappingMapper
import tech.tiangong.pop.req.category.PublishCategoryMappingDetailQueryReq
import tech.tiangong.pop.req.category.PublishCategoryMappingQueryReq
import tech.tiangong.pop.resp.category.PublishCategoryMappingVo

/**
 * 品类映射表(PublishCategoryMapping)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-11-14 16:12:34
 */
@Repository
class PublishCategoryMappingRepository : BaseRepository<PublishCategoryMappingMapper, PublishCategoryMapping>() {

    fun pageQuery(query: PublishCategoryMappingQueryReq): IPage<PublishCategoryMappingVo> {
        return baseMapper.pageQuery(Page(query.pageNum.toLong(), query.pageSize.toLong()), query)
    }

    fun deleteByCategoryIds(categoryIds: List<Long>) {
        if (CollectionUtils.isEmpty(categoryIds)) {
            return
        }
        this.ktUpdate()
            .`in`(PublishCategoryMapping::publishCategoryId, categoryIds)
            .remove()
    }

    fun listByCategoryId(categoryId: Long):List<PublishCategoryMapping> {
        return this.ktQuery()
            .eq(PublishCategoryMapping::publishCategoryId, categoryId)
            .list()
    }

    fun getByPublishCategoryIdAndPlatformId(publishCategoryId: Long, platformId: Long): PublishCategoryMapping? {
        return ktQuery().eq(PublishCategoryMapping::publishCategoryId, publishCategoryId)
            .eq(PublishCategoryMapping::platformId, platformId)
            .one()
    }

    fun getByPublishCategoryIdsAndPlatformId(publishCategoryIds: List<Long>, platformId: Long): List<PublishCategoryMapping> {
        return ktQuery().`in`(PublishCategoryMapping::publishCategoryId, publishCategoryIds)
            .eq(PublishCategoryMapping::platformId, platformId)
            .list()
    }

    fun getByPlatformCategoryId(
        platformCategoryId: String,
        platformId: Long,
        channelId: Long,
        country: String?,
    ): PublishCategoryMapping? {
        return ktQuery().eq(PublishCategoryMapping::platformCategoryId, platformCategoryId)
            .eq(PublishCategoryMapping::platformId, platformId)
            .eq(PublishCategoryMapping::channelId, channelId)
            .eq(country.isNotNull(), PublishCategoryMapping::country, country)
            .list().firstOrNull()
    }

    fun getByPlatformCategoryId(
        platformCategoryId: String,
        platformId: Long,
        channelId: Long,
    ): PublishCategoryMapping? {
        return getByPlatformCategoryId(
            platformCategoryId,
            platformId,
            channelId,
            null
        )
    }

    fun listByPlatformCategoryIdAndPlatformIdAndCountry(
        platformCategoryId: String,
        platformId: Long,
        country: String,
    ): List<PublishCategoryMapping>? {
        return ktQuery().eq(PublishCategoryMapping::platformCategoryId, platformCategoryId)
            .eq(PublishCategoryMapping::platformId, platformId)
            .eq(PublishCategoryMapping::country, country)
            .list()
    }

    fun listByPlatformCategoryIds(
        platformCategoryIds: List<String>,
        platformId: Long,
        channelId: Long,
    ): List<PublishCategoryMapping>? {
        return ktQuery()
            .`in`(PublishCategoryMapping::platformCategoryId, platformCategoryIds)
            .eq(PublishCategoryMapping::platformId, platformId)
            .eq(PublishCategoryMapping::channelId, channelId)
            .list()
    }

    fun getByPublishCategoryId(
        publishCategoryId: Long,
        platformId: Long,
        channelId: Long,
        country: String?,
    ): PublishCategoryMapping? {
        return ktQuery().eq(PublishCategoryMapping::publishCategoryId, publishCategoryId)
            .eq(PublishCategoryMapping::platformId, platformId)
            .eq(PublishCategoryMapping::channelId, channelId)
            .eq(country.isNotNull(), PublishCategoryMapping::country, country)
            .list().firstOrNull()
    }

    fun getByPublishCategoryId(
        publishCategoryId: Long,
        platformId: Long,
        channelId: Long,
    ): PublishCategoryMapping? {
        return getByPublishCategoryId(
            publishCategoryId,
            platformId,
            channelId,
            null
        )
    }

    fun queryPublishCategoryByPlatformCategoryId(req: PublishCategoryMappingDetailQueryReq): List<PublishCategoryMapping> {
        return ktQuery()
            .eq(PublishCategoryMapping::platformId, req.platformId)
            .`in`(
                CollectionUtils.isNotEmpty(req.platformCategoryIds),
                PublishCategoryMapping::platformCategoryId,
                req.platformCategoryIds
            )
            .list()
    }

    fun getByPlatformId(platformId: Long): List<PublishCategoryMapping> {
        return ktQuery().eq(PublishCategoryMapping::platformId, platformId).list()
    }
}
