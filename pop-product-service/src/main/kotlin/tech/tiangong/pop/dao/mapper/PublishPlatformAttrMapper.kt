package tech.tiangong.pop.dao.mapper

import com.baomidou.mybatisplus.core.mapper.BaseMapper
import org.apache.ibatis.annotations.Param
import tech.tiangong.pop.dao.entity.PublishPlatformAttr
import tech.tiangong.pop.resp.category.AttributeMapperResp
import tech.tiangong.pop.resp.category.CountAttributeMappingByCategoryIdResp
import tech.tiangong.pop.resp.category.CountAttributeMappingByCategoryMapperIdResp

/**
 * 业务属性与平台属性的映射(PublishPlatformAttr)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-11-14 16:12:36
 */
interface PublishPlatformAttrMapper : BaseMapper<PublishPlatformAttr>{

    fun listPlatformAttributeMapperByCategoryMappingId(@Param("categoryMappingId") categoryMappingId: Long): List<AttributeMapperResp>

    fun countAttributeMappingByCategoryMappingId(@Param("categoryMappingIds") categoryMappingIds: List<Long>): List<CountAttributeMappingByCategoryMapperIdResp>

    fun countAttributeMappingByCategoryId(@Param("categoryIds") categoryIds: List<Long>,
                                          @Param("categoryMappingIds") categoryMappingIds: List<Long>?): List<CountAttributeMappingByCategoryIdResp>

}

