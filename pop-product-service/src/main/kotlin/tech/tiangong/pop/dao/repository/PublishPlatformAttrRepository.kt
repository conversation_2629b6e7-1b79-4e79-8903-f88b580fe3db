package tech.tiangong.pop.dao.repository

import org.springframework.stereotype.Repository
import org.springframework.util.CollectionUtils
import team.aikero.blade.data.mybatis.repository.BaseRepository
import tech.tiangong.pop.dao.entity.PublishPlatformAttr
import tech.tiangong.pop.dao.mapper.PublishPlatformAttrMapper
import tech.tiangong.pop.resp.category.AttributeMapperResp
import tech.tiangong.pop.resp.category.AttributeValueMapperResp
import tech.tiangong.pop.resp.category.CountAttributeMappingByCategoryIdResp
import tech.tiangong.pop.resp.category.CountAttributeMappingByCategoryMapperIdResp

/**
 * 业务属性与平台属性的映射(PublishPlatformAttr)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-11-14 16:12:37
 */
@Repository
class PublishPlatformAttrRepository(
    private val publishPlatformAttrValueRepository:PublishPlatformAttrValueRepository
) : BaseRepository<PublishPlatformAttrMapper, PublishPlatformAttr>() {

    fun hasUsedAttrIds(attrIds: List<Long>): Boolean {
        if (CollectionUtils.isEmpty(attrIds)) {
            return false
        }

        return ktQuery().`in`(PublishPlatformAttr::attributeId, attrIds)
            .count() > 0
    }

    fun deleteByPlatformAttrId(platformAttrIds: List<Long>) {
        if (CollectionUtils.isEmpty(platformAttrIds)) {
            return
        }
        platformAttrIds.forEach {
            logicDelete(it)
        }
    }

    fun listByAttributeIds(attributeIds: Set<Long>): List<PublishPlatformAttr> {
        if (CollectionUtils.isEmpty(attributeIds)) {
            return emptyList()
        }

        return ktQuery().`in`(PublishPlatformAttr::attributeId, attributeIds)
            .list()
    }

    fun listByCategoryIds(categoryIds: List<Long>): List<PublishPlatformAttr> {
        if (CollectionUtils.isEmpty(categoryIds)) {
            return emptyList()
        }
        return ktQuery().`in`(PublishPlatformAttr::categoryId, categoryIds)
            .list()
    }

    fun listByCategoryMappingIds(categoryMappingIds: List<Long>): List<PublishPlatformAttr> {
        if (CollectionUtils.isEmpty(categoryMappingIds)) {
            return emptyList()
        }
        return ktQuery().`in`(PublishPlatformAttr::categoryMappingId, categoryMappingIds)
            .list()
    }

    fun listPlatformAttributeMappingByCategoryMappingId(categoryMappingId: Long): List<AttributeMapperResp> {
        var list = baseMapper.listPlatformAttributeMapperByCategoryMappingId(categoryMappingId)
        var platformAttrIds = list.mapNotNull { it.platformAttrId }.toList()
        var platformAttributeValueMappingList = publishPlatformAttrValueRepository.listByPublicPlatformAttrIds(platformAttrIds)
        var platformAttrIdToValuesMap = platformAttributeValueMappingList.groupBy { it.publishPlatformAttrId }
        list.forEach {
            var values = platformAttrIdToValuesMap[it.platformAttrId]
            it.attributeValueList = values?.mapNotNull {
                AttributeValueMapperResp().apply {
                    this.attributeValueId = it.attributeId
                    this.attributeValue = it.attributeValue
                    this.platformAttributeCode = it.platformAttributeCode
                    this.attributeId = it.attributeId
                    this.platformAttributeValue = it.platformAttributeValue
                    this.platformAttrValueId = it.platformAttrValueId
                }
            }
        }
        return list
    }

    fun countAttributeMappingByCategoryMappingId(categoryMappingIds: List<Long>?): List<CountAttributeMappingByCategoryMapperIdResp> {
        if(categoryMappingIds.isNullOrEmpty()){
            return mutableListOf()
        }
        return baseMapper.countAttributeMappingByCategoryMappingId(categoryMappingIds)
    }

    fun countAttributeMappingByCategoryId(categoryIds: List<Long>?,categoryMappingIds: List<Long>?): List<CountAttributeMappingByCategoryIdResp> {
        if(categoryIds.isNullOrEmpty()){
            return mutableListOf()
        }
        return baseMapper.countAttributeMappingByCategoryId(categoryIds,categoryMappingIds)
    }
    fun deleteByCategoryIds(categoryIds: List<Long>) {
        if (CollectionUtils.isEmpty(categoryIds)) {
            return
        }
        this.ktUpdate()
            .eq(PublishPlatformAttr::categoryId, categoryIds)
            .remove()
    }
}
