package tech.tiangong.pop.dao.entity

import com.baomidou.mybatisplus.annotation.IdType
import com.baomidou.mybatisplus.annotation.TableField
import com.baomidou.mybatisplus.annotation.TableId
import com.baomidou.mybatisplus.annotation.TableName
import com.fasterxml.jackson.annotation.JsonIgnore
import team.aikero.blade.util.json.parseJson
import team.aikero.blade.util.json.parseJsonList
import team.aikero.blade.util.json.toJson
import tech.tiangong.pop.dao.BaseEntity
import tech.tiangong.pop.dto.product.ProductAePropertyValueItemDTO
import tech.tiangong.pop.dto.product.ProductTitleConfigGenerationDTO
import tech.tiangong.pop.req.product.ae.AeHsCodePropertyValueItem
import java.time.LocalDateTime

/**
 * 商品模板AE-SPU(ProductTemplateAeSpu)表名: product_template_ae_spu
 *
 * <AUTHOR>
 * @since 2025-04-01 18:41:32
 */
@TableName(value = "product_template_ae_spu")
data class ProductTemplateAeSpu(
    /**
     * SPU模板id
     */
    @TableId(value = "ae_spu_id", type = IdType.ASSIGN_ID)
    var aeSpuId: Long? = null,

    /**
     * 商品id
     */
    @TableField(value = "product_id")
    var productId: Long? = null,
    /**
     * 店铺id
     */
    @TableField(value = "shop_id")
    var shopId: Long? = null,
    /**
     * spu编码
     */
    @TableField(value = "spu_code")
    var spuCode: String? = null,
    /**
     * -2 取消上架, -1上架失败, 0 待开始, 1 进行中, 2 已完成
     */
    @TableField(value = "task_status")
    var taskStatus: Int? = null,
    /**
     * 取消原因
     */
    @TableField(value = "cancel_reason")
    var cancelReason: String? = null,
    /**
     * 上架任务执行人员id
     */
    @TableField(value = "task_executor_id")
    var taskExecutorId: Long? = null,
    /**
     * 上架任务执行人员name
     */
    @TableField(value = "task_executor_name")
    var taskExecutorName: String? = null,
    /**
     * 任务完成时间
     */
    @TableField(value = "task_complete_time")
    var taskCompleteTime: LocalDateTime? = null,
    /**
     * 商品标题
     */
    @TableField(value = "product_title")
    var productTitle: String? = null,
    /**
     * 品牌ID
     */
    @TableField(value = "brand_id")
    var brandId: Int? = null,
    /**
     * 品牌名称
     */
    @TableField(value = "brand_name")
    var brandName: String? = null,
    /**
     * 包装规格-重量
     */
    @TableField(value = "package_weight")
    var packageWeight: String? = null,
    /**
     * 包装规格-长度
     */
    @TableField(value = "package_dimensions_length")
    var packageDimensionsLength: String? = null,
    /**
     * 包装规格-高度
     */
    @TableField(value = "package_dimensions_height")
    var packageDimensionsHeight: String? = null,
    /**
     * 包装规格-宽度
     */
    @TableField(value = "package_dimensions_width")
    var packageDimensionsWidth: String? = null,
    /**
     * 是否包含关税(1：不含关税报价 2：含关税报价)
     */
    @TableField(value = "tax_type")
    var taxType: Int? = null,
    /**
     * 尺码组名称
     */
    @TableField(value = "size_group_name")
    var sizeGroupName: String? = null,
    /**
     * 尺码组编码
     */
    @TableField(value = "size_group_code")
    var sizeGroupCode: String? = null,
    /**
     * 库存扣减方式(1:下单扣库存, 2:付款扣库存)
     */
    @TableField(value = "inv_deduction")
    var invDeduction: Int? = null,
    /**
     * 产地(国家/地区)
     */
    @TableField(value = "origin_place_name")
    var originPlaceName: String? = null,

    /**
     * 税号
     */
    @TableField(value = "hs_code")
    var hsCode: String? = null,

    /**
     * 税号监管属性列表（存储为JSON）
     */
    @TableField(value = "hs_pv_list")
    var hsPvList: String? = null,

    /**
     * 税号扩展字段（存储为JSON）
     */
    @TableField(value = "hs_extend_info")
    var hsExtendInfo: String? = null,

    /**
     * 原产地属性值JSON
     */
    @TableField(value = "origin_property_value_items")
    var originPropertyValueItems: String? = null,

    /**
     * 发货地属性值JSON
     */
    @TableField(value = "ships_from_property_value_item")
    var shipsFromPropertyValueItem: String? = null,

    /**
     * 标题字段缺失信息，存储为JSON数组，例如：["HOT_WORD","PRINT_ATTRIBUTE_EXTEND1"]
     */
    @TableField(value = "generated_title_missing_fields")
    var generatedTitleMissingFieldsJson: String? = null,

    /**
     * 标题是否过长：0-否，1-是
     */
    @TableField(value = "generated_title_over_length")
    var generatedTitleOverLengthFlag: Int? = 0,

    /**
     * 多标题生成数据，存储为JSON格式
     */
    @TableField(value = "generated_titles_json")
    var generatedTitlesJson: String? = null,

//    /**
//     * 价格计算规则
//     * 参考 PriceCalculateRuleEnum
//     */
//    @TableField(value = "price_calculate_rule")
//    var priceCalculateRule: String? = null,
) : BaseEntity() {

    @JsonIgnore
    fun getParsedHsPvList(): List<AeHsCodePropertyValueItem>? {
        return hsPvList?.parseJsonList(AeHsCodePropertyValueItem::class.java)
    }

    fun setParsedHsPvList(hsPvList: List<AeHsCodePropertyValueItem>?) {
        this.hsPvList = hsPvList?.toJson()
    }

    /**
     * 获取原产地属性值列表
     */
    @JsonIgnore
    fun getParsedOriginPropertyValueItems(): List<ProductAePropertyValueItemDTO>? {
        return originPropertyValueItems?.parseJsonList(ProductAePropertyValueItemDTO::class.java)
    }

    /**
     * 设置原产地属性值列表
     */
    fun setParsedOriginPropertyValueItems(items: List<ProductAePropertyValueItemDTO>?) {
        this.originPropertyValueItems = items?.toJson()
    }

    /**
     * 获取发货地属性值
     */
    @JsonIgnore
    fun getParsedShipsFromPropertyValueItem(): ProductAePropertyValueItemDTO? {
        return shipsFromPropertyValueItem?.parseJson(ProductAePropertyValueItemDTO::class.java)
    }

    /**
     * 设置发货地属性值
     */
    fun setParsedShipsFromPropertyValueItem(item: ProductAePropertyValueItemDTO?) {
        this.shipsFromPropertyValueItem = item?.toJson()
    }

    /**
     * 获取缺失字段列表
     */
    @JsonIgnore
    fun getParsedGeneratedTitleMissingFields(): List<String> {
        return generatedTitleMissingFieldsJson?.parseJsonList(String::class.java) ?: emptyList()
    }

    /**
     * 获取多标题生成数据
     */
    @JsonIgnore
    fun getParsedGeneratedTitles(): ProductTitleConfigGenerationDTO? {
        return generatedTitlesJson?.parseJson(ProductTitleConfigGenerationDTO::class.java)
    }

    /**
     * 设置多标题生成数据
     */
    fun setParsedGeneratedTitles(titleData: ProductTitleConfigGenerationDTO?) {
        this.generatedTitlesJson = titleData?.toJson()
    }
}


