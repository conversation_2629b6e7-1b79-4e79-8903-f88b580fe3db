package tech.tiangong.pop.dao.entity

import com.baomidou.mybatisplus.annotation.IdType
import com.baomidou.mybatisplus.annotation.TableField
import com.baomidou.mybatisplus.annotation.TableId
import com.baomidou.mybatisplus.annotation.TableName
import tech.tiangong.pop.dao.BaseEntity
import java.math.BigDecimal

@TableName(value = "gross_margin_record")
class GrossMarginRecord(
    /**
     * 主键
     */
    @TableId(value = "record_id", type = IdType.ASSIGN_ID)
    var recordId: Long? = null,

    /**
     * 商品ID
     */
    @TableField(value = "product_id")
    var productId: Long? = null,
    /**
     * 店铺ID
     */
    @TableField(value = "shop_id")
    var shopId: Long? = null,
    /**
     * 毛利率
     */
    @TableField(value = "gross_margin")
    var grossMargin: BigDecimal? = null,
) : BaseEntity()  {
}