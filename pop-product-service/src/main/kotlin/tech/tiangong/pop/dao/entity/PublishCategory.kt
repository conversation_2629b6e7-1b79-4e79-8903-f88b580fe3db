package tech.tiangong.pop.dao.entity

import com.baomidou.mybatisplus.annotation.IdType
import com.baomidou.mybatisplus.annotation.TableField
import com.baomidou.mybatisplus.annotation.TableId
import com.baomidou.mybatisplus.annotation.TableName
import tech.tiangong.pop.dao.BaseEntity

/**
 * 上架品类表(PublishCategory)表名: publish_category
 *
 * <AUTHOR>
 * @since 2024-11-14 16:12:32
 */
@TableName(value = "publish_category")
data class PublishCategory(
    /**
     * 品类id
     */
    @TableId(value = "publish_category_id", type = IdType.ASSIGN_ID)
    var publishCategoryId: Long? = null,

    /**
     * 父品类ID
     */
    @TableField(value = "parent_category_id")
    var parentCategoryId: Long? = null,

    /**
     * 品类编码
     */
    @TableField(value = "cateory_code")
    var cateoryCode: String? = null,

    /**
     * 父节点ID，多个用英文逗号隔开
     */
    @TableField(value = "parent_paths")
    var parentPaths: String? = null,
    /**
     * 节点所在层级
     */
    @TableField(value = "level")
    var level: Int? = null,
    /**
     * 品类名称
     */
    @TableField(value = "category_name")
    var categoryName: String? = null,
    /**
     * 序号;排序
     */
    @TableField(value = "sort_num")
    var sortNum: Int? = null,
    /**
     * 品类状态1启用0停用
     */
    @TableField(value = "state")
    var state: Int? = null,
    /**
     * 属性关联状态0无配置1已配置
     */
    @TableField(value = "attribute_associate_state")
    var attributeAssociateState: Int? = null,

    /**
     * 平台关联状态0未关联平台品类，1已关联平台品类，2已关联平台品类属性
     * 参考 CategoryAssociatePlatformStateEnum
     */
    @TableField(value = "platform_associate_state")
    var platformAssociateState: Int? = null,

    /**
     * 包装配置状态0无配置1已配置
     */
    @TableField(value = "package_config_state")
    var packageConfigState: Int? = null,
) : BaseEntity()


