package tech.tiangong.pop.dao.repository

import org.springframework.stereotype.Repository
import team.aikero.blade.data.mybatis.repository.BaseRepository
import tech.tiangong.pop.dao.entity.GrossMarginRecord
import tech.tiangong.pop.dao.mapper.GrossMarginRecordMapper

@Repository
class GrossMarginRecordRepository : BaseRepository<GrossMarginRecordMapper, GrossMarginRecord>()  {

    fun listByProductId(productId:Long?):List<GrossMarginRecord>?{
        if(productId==null){
            return mutableListOf()
        }
        return this.ktQuery().eq(GrossMarginRecord::productId,productId).list()
    }
}