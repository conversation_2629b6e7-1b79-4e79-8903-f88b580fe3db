package tech.tiangong.pop.dao.entity

import com.baomidou.mybatisplus.annotation.IdType
import com.baomidou.mybatisplus.annotation.TableField
import com.baomidou.mybatisplus.annotation.TableId
import com.baomidou.mybatisplus.annotation.TableName
import com.fasterxml.jackson.annotation.JsonIgnore
import team.aikero.blade.util.json.parseJson
import team.aikero.blade.util.json.parseJsonList
import team.aikero.blade.util.json.toJson
import tech.tiangong.pop.dao.BaseEntity
import tech.tiangong.pop.dto.product.ProductAeGroupDTO
import tech.tiangong.pop.dto.product.ProductAePropertyValueItemDTO
import tech.tiangong.pop.req.product.ae.AeHsCodePropertyValueItem
import java.math.BigDecimal
import java.time.LocalDateTime

/**
 * 销售商品表-AE(AeSaleGoods)表名: ae_sale_goods
 *
 * <AUTHOR>
 * @since 2025-04-19 15:58:47
 */
@TableName(value = "ae_sale_goods")
data class AeSaleGoods(
    /**
     * 主键
     */
    @TableId(value = "sale_goods_id", type = IdType.ASSIGN_ID)
    var saleGoodsId: Long? = null,

    /**
     * 商品表ID
     */
    @TableField(value = "product_id")
    var productId: Long? = null,
    /**
     * 平台商品ID
     */
    @TableField(value = "platform_product_id")
    var platformProductId: Long? = null,
    /**
     * 平台ID
     */
    @TableField(value = "platform_id")
    var platformId: Long? = null,
    /**
     * 渠道ID
     */
    @TableField(value = "channel_id")
    var channelId: Long? = null,
    /**
     * 商品标题
     */
    @TableField(value = "product_title")
    var productTitle: String? = null,
    /**
     * spu编码
     */
    @TableField(value = "spu_code")
    var spuCode: String? = null,
    /**
     * 店铺ID
     */
    @TableField(value = "shop_id")
    var shopId: Long? = null,
    /**
     * 店铺名称
     */
    @TableField(value = "shop_name")
    var shopName: String? = null,
    /**
     * 品牌ID
     */
    @TableField(value = "brand_id")
    var brandId: Long? = null,
    /**
     * 品牌名称
     */
    @TableField(value = "brand_name")
    var brandName: String? = null,
    /**
     * 品类编码
     */
    @TableField(value = "category_code")
    var categoryCode: String? = null,
    /**
     * 平台品类唯一标识
     */
    @TableField(value = "platform_category_id")
    var platformCategoryId: String? = null,
    /**
     * 平台品类名
     */
    @TableField(value = "platform_category_name")
    var platformCategoryName: String? = null,
    /**
     * 库存类型: 1期货;2现货;3海外仓
     */
    @TableField(value = "stock_type")
    var stockType: Int? = null,
    /**
     * 发货期
     */
    @TableField(value = "delay_delivery_days")
    var delayDeliveryDays: Int? = null,
    /**
     * 库存扣减方式(1:下单扣库存, 2:付款扣库存)
     */
    @TableField(value = "inv_deduction")
    var invDeduction: Int? = null,
    /**
     * 产地(国家/地区)
     */
    @TableField(value = "origin_place_name")
    var originPlaceName: String? = null,
    /**
     * 包装规格-重量
     */
    @TableField(value = "package_weight")
    var packageWeight: String? = null,
    /**
     * 包装规格-长度
     */
    @TableField(value = "package_dimensions_length")
    var packageDimensionsLength: String? = null,
    /**
     * 包装规格-高度
     */
    @TableField(value = "package_dimensions_height")
    var packageDimensionsHeight: String? = null,
    /**
     * 包装规格-宽度
     */
    @TableField(value = "package_dimensions_width")
    var packageDimensionsWidth: String? = null,
    /**
     * 是否包含关税(1：不含关税报价 2：含关税报价)
     */
    @TableField(value = "tax_type")
    var taxType: Int? = null,
    /**
     * 商品上架状态【1上架；2下架】
     */
    @TableField(value = "publish_state")
    var publishState: Int? = null,
    /**
     * 是否更新【1有更新；0没有】
     */
    @TableField(value = "update_state")
    var updateState: Int? = null,
    /**
     * 供货价是否更新【1有更新；0没有】
     */
    @TableField(value = "cost_price_update_state")
    var costPriceUpdateState: Int? = null,
    /**
     * 系统更新平台失败(1是 0否)
     */
    @TableField(value = "system_update_platform_fail")
    var systemUpdatePlatformFail: Int? = null,
    /**
     * 平台同步状态【0待同步;2同步中；1已同步；-1同步失败】
     */
    @TableField(value = "platform_sync_state")
    var platformSyncState: Int? = null,
    /**
     * 首次上架时间
     */
    @TableField(value = "publish_time")
    var publishTime: LocalDateTime? = null,
    /**
     * 首次发布人id
     */
    @TableField(value = "publish_user_id")
    var publishUserId: Long? = null,
    /**
     * 首次发布人名称
     */
    @TableField(value = "publish_user_name")
    var publishUserName: String? = null,
    /**
     * 最近上架时间
     */
    @TableField(value = "latest_publish_time")
    var latestPublishTime: LocalDateTime? = null,
    /**
     * 最近下架人id
     */
    @TableField(value = "latest_offline_user_id")
    var latestOfflineUserId: Long? = null,
    /**
     * 最近上架人id
     */
    @TableField(value = "latest_publish_user_id")
    var latestPublishUserId: Long? = null,
    /**
     * 最近下架人名称
     */
    @TableField(value = "latest_offline_user_name")
    var latestOfflineUserName: String? = null,
    /**
     * 最近上架人名称
     */
    @TableField(value = "latest_publish_user_name")
    var latestPublishUserName: String? = null,
    /**
     * 最近下架时间
     */
    @TableField(value = "latest_offline_time")
    var latestOfflineTime: LocalDateTime? = null,
    /**
     * 是否历史数据【1：是】
     */
    @TableField(value = "is_history")
    var isHistory: Int? = null,

    /**
     * 是否异常数据(1是0否)
     */
    @TableField(value = "error_state")
    var errorState: Int? = null,

    /**
     * 异常类型json数组[[{"errorType":  1, "errorMsg":  "SKU为空"}]
     */
    @TableField(value = "error_info")
    var errorInfo: String? = null,

    /**
     * AE服务模板ID
     */
    @TableField(value = "promise_template_id")
    var promiseTemplateId: Long? = null,

    /**
     * AE服务模板名称
     */
    @TableField(value = "promise_template_name")
    var promiseTemplateName: String? = null,

    /**
     * AE运费模板ID
     */
    @TableField(value = "freight_template_id")
    var freightTemplateId: Long? = null,

    /**
     * AE运费模板名称
     */
    @TableField(value = "freight_template_name")
    var freightTemplateName: String? = null,

    /**
     * AE欧盟责任人ID
     */
    @TableField(value = "msr_id")
    var msrId: Long? = null,

    /**
     * AE欧盟责任人名称
     */
    @TableField(value = "msr_name")
    var msrName: String? = null,

    /**
     * AE制造商ID
     */
    @TableField(value = "manufacture_id")
    var manufactureId: Long? = null,

    /**
     * AE制造商名称
     */
    @TableField(value = "manufacture_name")
    var manufactureName: String? = null,

    /**
     * AE产品分组JSON数据
     */
    @TableField(value = "product_groups")
    var productGroups: String? = null,

    /**
     * 税号
     */
    @TableField(value = "hs_code")
    var hsCode: String? = null,

    /**
     * 税号监管属性列表（存储为JSON）
     */
    @TableField(value = "hs_pv_list")
    var hsPvList: String? = null,

    /**
     * 税号扩展字段（存储为JSON）
     */
    @TableField(value = "hs_extend_info")
    var hsExtendInfo: String? = null,

    /**
     * 原产地属性值JSON
     */
    @TableField(value = "origin_property_value_items")
    var originPropertyValueItems: String? = null,

    /**
     * 发货地属性值JSON
     */
    @TableField(value = "ships_from_property_value_item")
    var shipsFromPropertyValueItem: String? = null,

    /**
     * 最近定时任务同步更新时间
     * AeProductScheduledSyncComponent
     */
    @TableField(value = "last_scheduled_sync_time")
    var lastScheduledSyncTime: LocalDateTime? = null,

    /**
     * AE平台已删除/未找到，1表示未找到，0表示正常
     */
    @TableField(value = "product_not_found")
    var productNotFound: Int? = null,

    /**
     * 图包规则ID
     */
    @TableField(value = "image_pack_rule_id")
    var imagePackRuleId: Long? = null,

    /**
     * 使用时的规则版本号，时间戳格式
     */
    @TableField(value = "image_pack_rule_version")
    var imagePackRuleVersion: String? = null,

    /**
     * 价格计算规则
     * 参考 PriceCalculateRuleEnum
     */
    @TableField(value = "price_calculate_rule")
    var priceCalculateRule: String? = null,

    /**
     * 毛利率
     */
    @TableField(value = "gross_margin")
    var grossMargin: BigDecimal? = null,
) : BaseEntity() {
    /**
     * 获取产品分组列表(用于展示)
     */
    @JsonIgnore
    fun getProductGroupList(): List<ProductAeGroupDTO>? {
        return productGroups?.parseJsonList(ProductAeGroupDTO::class.java)
    }

    /**
     * 设置产品分组列表
     */
    fun setProductGroupList(groupList: List<ProductAeGroupDTO>?) {
        this.productGroups = groupList?.toJson()
    }

    @JsonIgnore
    fun getParsedHsPvList(): List<AeHsCodePropertyValueItem>? {
        return hsPvList?.parseJsonList(AeHsCodePropertyValueItem::class.java)
    }

    fun setParsedHsPvList(hsPvList: List<AeHsCodePropertyValueItem>?) {
        this.hsPvList = hsPvList?.toJson()
    }

    /**
     * 获取原产地属性值列表
     */
    @JsonIgnore
    fun getParsedOriginPropertyValueItems(): List<ProductAePropertyValueItemDTO>? {
        return originPropertyValueItems?.parseJsonList(ProductAePropertyValueItemDTO::class.java)
    }

    /**
     * 设置原产地属性值列表
     */
    fun setParsedOriginPropertyValueItems(items: List<ProductAePropertyValueItemDTO>?) {
        this.originPropertyValueItems = items?.toJson()
    }

    /**
     * 获取发货地属性值
     */
    @JsonIgnore
    fun getParsedShipsFromPropertyValueItem(): ProductAePropertyValueItemDTO? {
        return shipsFromPropertyValueItem?.parseJson(ProductAePropertyValueItemDTO::class.java)
    }
}


