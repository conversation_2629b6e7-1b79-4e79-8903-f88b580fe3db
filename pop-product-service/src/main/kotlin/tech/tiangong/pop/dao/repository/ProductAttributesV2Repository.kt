package tech.tiangong.pop.dao.repository

import org.springframework.stereotype.Repository
import team.aikero.blade.data.mybatis.repository.BaseRepository
import tech.tiangong.pop.dao.entity.ProductAttributesV2
import tech.tiangong.pop.dao.mapper.ProductAttributesV2Mapper

@Repository
class ProductAttributesV2Repository : BaseRepository<ProductAttributesV2Mapper, ProductAttributesV2>() {

    fun listByProductId(productId: Long): List<ProductAttributesV2>?{
        return this.ktQuery().eq(ProductAttributesV2::productId,productId).list()
    }

    fun listByProductIdAndAttrId(productId: Long, attrId: Long): ProductAttributesV2?{
        return this.ktQuery()
            .eq(ProductAttributesV2::productId,productId)
            .eq(ProductAttributesV2::attributeId,attrId)
            .list()
            .firstOrNull()
    }
}
