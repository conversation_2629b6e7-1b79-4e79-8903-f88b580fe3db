package tech.tiangong.pop.dao.entity

import com.baomidou.mybatisplus.annotation.IdType
import com.baomidou.mybatisplus.annotation.TableId
import com.baomidou.mybatisplus.annotation.TableName
import tech.tiangong.pop.dao.BaseEntity
import java.math.BigDecimal

@TableName("product_skc")
data class ProductSkc(
    /**
     * 主键ID
     */
    @TableId(value = "product_skc_id", type = IdType.ASSIGN_ID)
    var productSkcId: Long? = null,

    /**
     * 商品id
     */
    var productId: Long? = null,

    /**
     * SKC
     */
    var skc: String? = null,

    /**
     * 内部颜色
     */
    var color: String? = null,

    /**
     * 颜色编码（如White）
     */
    var colorCode: String? = null,

    /**
     * 颜色缩写编码（如 "WH" ）
     */
    var colorAbbrCode: String? = null,

    /**
     * 平台颜色
     */
    var platformColor: String? = null,

    /**
     * SKC状态【1可用；0取消】
     */
    var state: Int? = null,

    /**
     *  导入标识【1为导入】
     */
    var importFlag: Int? = null,

    /**
     * skc图片（多个用英文逗号分割）
     */
    var pictures: String? = null,

    /**
     * 跨境价
     */
    var cbPrice: BigDecimal? = null,

    /**
     * 本土价（供货价）
     */
    var localPrice: BigDecimal? = null,

    /**
     * 采购价
     */
    var purchasePrice: BigDecimal? = null,

    /**
     * 定价成本
     */
    var costPrice: BigDecimal? = null,

    /**
     * 核价类型：1.精准核价 2.预估核价
     */
    var checkPriceType: Int? = null,

    /**
     * 核价师
     */
    var pricerId: Long? = null,

    /**
     * 核价师
     */
    var pricerName: String? = null,

    /**
     *  尺码名JSON数组[S,X,2XL]
     */
    var sizeNames: String? = null,
    /**
     *  特殊尺码名JSON数组[加大码]
     */
    var specialSizeNames: String? = null,
) : BaseEntity()