package tech.tiangong.pop.service.product

import team.aikero.blade.core.protocol.PageVo
import tech.tiangong.pop.req.product.ae.*
import tech.tiangong.pop.req.product.lazada.UpdateSkuByUpdateStockPriceReq
import tech.tiangong.pop.resp.product.ae.*
import tech.tiangong.pop.resp.sdk.aliexpress.AliexpressCategoryAttributeResponse

/**
 * 商品-AE
 * <AUTHOR>
 * @date 2025-2-12 10:31:11
 */
interface ProductAeService {

    /**
     * 拉取Ae商品详情
     * @param req
     * @return
     */
    fun detail(req: ProductAeDetailReq): ProductAeDetailResp

    /**
     * 分页查询Ae商品
     * @param req
     * @return
     */
    fun page(req: ProductAePageQueryReq): PageVo<ProductAePageResp>

    /**
     * AE-统计数量
     */
    fun getProductCounts(req: ProductAePageQueryReq): ProductAeCountResp

    /**
     * 更新Ae商品
     * @param req
     */
    fun update(req: ProductAePublishedUpdateReq)

    /**
     * 批量下架-弹窗-商品SKU列表查询
     *
     * @param request
     */
    fun pageOnlineSkuList(request: ProductAeOnlineSkuListReq): PageVo<ProductAeOnlineSkuListResp>

    /**
     * 批量下架-弹窗-商品SKU列表查询-统计数量
     *
     * @param req
     */
    fun countOnlineSku(req: ProductAeOfflineSubmitReq): ProductAeOnlineSkuCountResp

    /**
     * 批量下架
     */
    fun batchOffline(req: ProductAeOfflineSubmitReq)

    /**
     * 定时刷新AE商品状态(审核中)
     */
    fun scheduleRefreshProductStatus()

    /**
     * 查询速卖通用户运费模板列表
     *
     * @param shopId 店铺ID（必需）
     * @param channelSellerId 渠道卖家ID（可选）
     * @return AliexpressFreightTemplateResponse 运费模板列表响应
     */
    fun queryFreightTemplateList(
        shopId: Long,
        channelSellerId: String? = null,
    ): List<ProductAeFreightTemplateResp>

    /**
     * 查询速卖通服务模板
     *
     * @param shopId 店铺ID（必需）
     * @param templateId 服务模板ID（可选，默认为-1，查询所有模板）
     * @return AliexpressPromiseTemplateResponse 服务模板查询响应
     */
    fun queryPromiseTemplates(
        shopId: Long,
        templateId: Long = -1L,
    ): List<ProductAePromiseTemplateResp>

    /**
     * 查询欧盟责任人列表
     *
     * @param shopId 店铺ID（必需）
     * @param channelSellerId 渠道seller id（可选）
     * @param channel 渠道（可选）
     * @return AliexpressMsrListResponse 欧盟责任人列表响应
     */
    fun queryMsrList(
        shopId: Long,
        channelSellerId: Long? = null,
        channel: String? = null,
    ): List<ProductAeMsrItemResp>

    /**
     * 获取制造商信息列表
     *
     * @param shopId 店铺ID（必需）
     * @param channelSellerId 渠道seller id（可选）
     * @param channel 渠道（可选）
     * @return AliexpressManufactureListResponse 制造商信息列表响应
     */
    fun queryManufactureList(
        shopId: Long,
        channelSellerId: Long? = null,
        channel: String? = null,
    ): List<ProductAeManufactureItemResp>

    /**
     * 获取当前会员的产品分组
     *
     * @param shopId 店铺ID（必需）
     * @return AliexpressProductGroupsResponse 产品分组响应
     */
    fun queryProductGroups(shopId: Long): List<ProductAeProductGroupResp>

    /**
     * 弹窗查询-编辑库存/价格
     *
     * @param req
     */
    fun querySkuByUpdateStockPrice(req: AeQuerySkuByUpdateStockPriceReq): List<AeQuerySkuByUpdateStockPriceResp>

    /**
     * 编辑库存/价格
     *
     * @param req
     */
    fun updateSkuByUpdateStockPrice(req: List<UpdateSkuByUpdateStockPriceReq>)

    /**
     * 失效指定店铺的所有缓存
     */
    fun invalidateAllShopCache(shopId: Long)

    /**
     * 失效所有缓存
     */
    fun invalidateAllCache()

    /**
     * 批量同步其他店铺
     */
    fun syncOtherShop(req: AeCloneProductToOtherShopReq)

    /**
     * 批量修改库存
     */
    fun batchStockQuantity(req: AeBatchUpdateQuantityReq)

    /**
     * 批量修改分组/模板等
     */
    fun batchUpdateAeInfo(req: AeBatchUpdateAeInfoReq)

    /**
     * 获取产地属性列表
     * @param req 请求参数，包含可选的属性值ID
     * @return 产地属性值列表
     */
    fun getOriginAttribute(req: ProductAeAttributeQueryReq): ProductAeAttributeResp

    /**
     * 获取AE商品属性
     * @param req 属性查询请求
     * @return 属性响应
     */
    fun getAttributes(req: ProductAeAttributeQueryReq): ProductAeAttributeResp

    /**
     * 获取品类所有属性
     */
    fun getAllAttributes(req: ProductAeCategoryAttributesQueryReq): AliexpressCategoryAttributeResponse

    /**
     * 修改AE商品的毛利率配置
     */
    fun updateProductGrossMargin(req: UpdateAEProductGrossMarginReq)
}
