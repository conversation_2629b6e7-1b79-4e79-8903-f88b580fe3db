package tech.tiangong.pop.service.product.update.temu

import org.apache.commons.collections4.CollectionUtils
import org.springframework.stereotype.Service
import team.aikero.blade.core.enums.Bool
import team.aikero.blade.core.toolkit.isEmpty
import team.aikero.blade.core.toolkit.isNotEmpty
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.sequence.id.IdHelper
import team.aikero.blade.util.json.toJson
import tech.tiangong.pop.common.dto.ProductUpdateDto
import tech.tiangong.pop.common.enums.PlatformEnum
import tech.tiangong.pop.common.req.BatchCreateBarCodeReq
import tech.tiangong.pop.config.TemuProperties
import tech.tiangong.pop.dao.entity.ProductSkc
import tech.tiangong.pop.dao.entity.ProductTemplateTemuSkc
import tech.tiangong.pop.dao.entity.ProductTemplateTemuSku
import tech.tiangong.pop.dao.repository.*
import tech.tiangong.pop.service.product.BarCodeService
import tech.tiangong.pop.service.product.price.ProductPricingServiceSelector
import tech.tiangong.pop.service.product.update.PlatformTemplateProductUpdateService

/**
 * 模板相关表更新-Temu
 */
@Service
@Slf4j
class TemuTemplateUpdateServiceImpl(
    private val productTemplateTemuSkcRepository: ProductTemplateTemuSkcRepository,
    private val productTemplateTemuSkuRepository: ProductTemplateTemuSkuRepository,
    private val productTemplateTemuSpuRepository: ProductTemplateTemuSpuRepository,
    private val productBarCodeRepository: ProductBarCodeRepository,
    private val productRepository: ProductRepository,
    private val barCodeService: BarCodeService,
    private val temuProperties: TemuProperties,
    private val productSkcRepository: ProductSkcRepository,
    private val productPricingServiceSelector: ProductPricingServiceSelector,
) : PlatformTemplateProductUpdateService {

    /**
     * 获取平台
     */
    override fun getPlatform(): PlatformEnum {
        return PlatformEnum.TEMU
    }

    /**
     * 取消SKC
     */
    override fun cancel(data: ProductUpdateDto.Data) {
        val spuList = productTemplateTemuSpuRepository.listNoCompletedBySpuCode(data.spuCode!!)
        if (spuList.isEmpty()) {
            log.warn { "待上架-取消SKC-错误，找不到spuCode, dto=${data.toJson()}" }
            return
        }
        spuList?.forEach { spu ->
            val productSkcList = productTemplateTemuSkcRepository.getByTemuSpuId(spu.temuSpuId!!)
            if (CollectionUtils.isEmpty(productSkcList)) {
                return@forEach
            }
            val updateSkcList = productSkcList
                ?.filter { it.skc == data.skc }
                ?.map {
                    ProductTemplateTemuSkc().apply {
                        this.temuSkcId = it.temuSkcId
                        this.state = Bool.NO.code
                    }
                } ?: emptyList()
            // 批量更新SKC
            if (updateSkcList.isNotEmpty()) {
                productTemplateTemuSkcRepository.updateBatchById(updateSkcList)

                // 批量更新SKC下的SKU
                val skcIdList = updateSkcList.map { it.temuSkcId!! }
                val skuList = productTemplateTemuSkuRepository.getByTemuSkcId(skcIdList)
                if (skuList.isNotEmpty()) {
                    val updateSkuList = skuList.map {
                        ProductTemplateTemuSku().apply {
                            this.temuSkuId = it.temuSkuId
                            this.enableState = Bool.NO.code
                        }
                    }
                    // 批量更新
                    if (updateSkuList.isNotEmpty()) {
                        productTemplateTemuSkuRepository.updateBatchById(updateSkuList)
                    }
                }
            }
        }
    }

    /**
     * 供货价更新
     */
    override fun updatePrice(data: ProductUpdateDto.Data) {
        val spuList = productTemplateTemuSpuRepository.listNoCompletedBySpuCode(data.spuCode!!)
        if (spuList.isEmpty()) {
            log.warn { "待上架-供货价更新-错误，找不到spuCode, dto=${data.toJson()}" }
            return
        }
        spuList?.forEach { spu ->
            val productSkcList = productTemplateTemuSkcRepository.getByTemuSpuId(spu.temuSpuId!!)
            if (CollectionUtils.isEmpty(productSkcList)) {
                return@forEach
            }

            val productSkcIdMap = mutableMapOf<Long, ProductSkc>()
            val productSkcIds = productSkcList?.mapNotNull { it.productSkcId }?.distinct()
            if (productSkcIds.isNotEmpty()) {
                val productSkcList = productSkcRepository.listByIds(productSkcIds)
                if (productSkcList.isNotEmpty()) {
                    productSkcList.forEach {
                        productSkcIdMap[it.productSkcId!!] = it
                    }
                }
            }

            // 更新供货价和定价成本
            productSkcList
                ?.filter { it.skc == data.skc }
                ?.forEach {
                    val productSkc = productSkcIdMap[it.productSkcId]
                    if (productSkc != null) {
                        val updateSkc = ProductTemplateTemuSkc().apply {
                            this.temuSkcId = it.temuSkcId
                            this.localPrice = data.price
                            this.costPrice = productSkc.costPrice
                        }
                        productTemplateTemuSkcRepository.updateById(updateSkc)
                    }
                }

            // 所有skc更新定价成本
            productSkcList?.forEach {
                val productSkc = productSkcIdMap[it.productSkcId]
                if (productSkc != null) {
                    val updateSkc = ProductTemplateTemuSkc().apply {
                        this.temuSkcId = it.temuSkcId
                        this.costPrice = productSkc.costPrice
                    }
                    productTemplateTemuSkcRepository.updateById(updateSkc)
                }
            }
            /*
            注意: 因为Temu配置店铺计算价格, 待上架是没有店铺, 因此不计算售价
             */
//        // 重算价格(里面使用product_skc来计算)
//        val autoCulPriceList = productPricingServiceSelector
//            .getSalePricingHandler(getPlatform())
//            .autoCalPriceByTemplate(spu.temuSpuId!!, AliexpressConstants.DEFAULT_COUNTRY.map { it.code })
//        // 重新查一次skc, 所有skc重算售价
//        val updateSkuList = mutableListOf<ProductTemplateTemuSku>()
//        productTemplateTemuSkcRepository.listByTemuSpuId(spu.temuSpuId!!)
//            ?.forEach {
//                val productSkcIdMap = autoCulPriceList.associateBy({ it.skc }, { it })
//                val skuList = productTemplateTemuSkuRepository.getByTemuSkcId(listOf(it.temuSkcId!!))
//                if (skuList.isNotEmpty()) {
//                    skuList.forEach { sku ->
//                        val price = productSkcIdMap[it.skc]?.countryPriceList?.find { it.country == AliexpressConstants.DEFAULT_COUNTRY.first().code }
//                        if (price != null) {
//                            val updateSku = ProductTemplateTemuSku().apply {
//                                this.temuSkuId = sku.temuSkuId
//                                // TEMU计算价格需要转币种, 再更新到json字符串内
////                                this.price = price.salePrice
////                                this.retailPrice = price.retailPrice
//                            }
//                            updateSkuList.add(updateSku)
//                        }
//                    }
//                }
//            }
//
//        if (updateSkuList.isNotEmpty()) {
//            productTemplateTemuSkuRepository.updateBatchById(updateSkuList)
//        }
        }
    }

    /**
     * 新增SKC
     */
    override fun addSkc(data: ProductUpdateDto.Data) {
        // 新增SKC使用create来源, 那里有模板init操作
    }

    /**
     * 创建商品
     */
    override fun create(data: ProductUpdateDto.Data) {

    }

    /**
     * 更新品类
     */
    override fun updateCategory(data: ProductUpdateDto.Data) {
        // 模板表没有品类, product表记录
    }

    /**
     * 更新颜色
     */
    override fun updateColor(data: ProductUpdateDto.Data) {
        val spuList = productTemplateTemuSpuRepository.listNoCompletedBySpuCode(data.spuCode!!)
        if (spuList.isEmpty()) {
            log.warn { "待上架-更新颜色-错误，找不到spuCode, dto=${data.toJson()}" }
            return
        }
        spuList?.forEach { spu ->
            val productSkcList = productTemplateTemuSkcRepository.getByTemuSpuId(spu.temuSpuId!!)
            if (CollectionUtils.isEmpty(productSkcList)) {
                return@forEach
            }
            productSkcList
                ?.filter { it.skc == data.skc }
                ?.forEach {
                    val updateSkc = ProductTemplateTemuSkc().apply {
                        this.temuSkcId = it.temuSkcId
                        this.color = data.color
                        this.platformColor = data.colorCode
                        this.colorCode = data.colorCode
                        this.colorAbbrCode = data.colorAbbrCode
                    }
                    productTemplateTemuSkcRepository.updateById(updateSkc)
                }
        }
    }

    /**
     * 更新采购价
     */
    override fun updatePurchasesPrice(data: ProductUpdateDto.Data) {
        val spuList = productTemplateTemuSpuRepository.listNoCompletedBySpuCode(data.spuCode!!)
        if (spuList.isEmpty()) {
            log.warn { "待上架-更新采购价-错误，找不到spuCode, dto=${data.toJson()}" }
            return
        }
        spuList?.forEach { spu ->
            val productSkcList = productTemplateTemuSkcRepository.listByTemuSpuId(spu.temuSpuId!!)
            if (CollectionUtils.isEmpty(productSkcList)) {
                return@forEach
            }

            val productSkcIdMap = mutableMapOf<Long, ProductSkc>()
            val productSkcIds = productSkcList?.mapNotNull { it.productSkcId }?.distinct()
            if (productSkcIds.isNotEmpty()) {
                val productSkcList = productSkcRepository.listByIds(productSkcIds)
                if (productSkcList.isNotEmpty()) {
                    productSkcList.forEach {
                        productSkcIdMap[it.productSkcId!!] = it
                    }
                }
            }

            productSkcList
                ?.filter { it.skc == data.skc }
                ?.forEach {
                    val productSkc = productSkcIdMap[it.productSkcId]
                    if (productSkc != null) {
                        val updateSkc = ProductTemplateTemuSkc().apply {
                            this.temuSkcId = it.temuSkcId
                            this.purchasePrice = data.purchasePrice
                            this.costPrice = productSkc.costPrice
                        }
                        productTemplateTemuSkcRepository.updateById(updateSkc)
                    }
                }

            // 所有skc更新定价成本
            productSkcList?.forEach {
                val productSkc = productSkcIdMap[it.productSkcId]
                if (productSkc != null) {
                    val updateSkc = ProductTemplateTemuSkc().apply {
                        this.temuSkcId = it.temuSkcId
                        this.costPrice = productSkc.costPrice
                    }
                    productTemplateTemuSkcRepository.updateById(updateSkc)
                }
            }

            /*
              注意: 因为Temu配置店铺计算价格, 待上架是没有店铺, 因此不计算售价
               */
//        // 重算价格(里面使用product_skc来计算)
//        val autoCulPriceList = productPricingServiceSelector
//            .getSalePricingHandler(getPlatform())
//            .autoCalPriceByTemplate(spu.temuSpuId!!, AliexpressConstants.DEFAULT_COUNTRY.map { it.code })
//        // 重新查一次skc, 所有skc重算售价
//        val updateSkuList = mutableListOf<ProductTemplateTemuSku>()
//        productTemplateTemuSkcRepository.listByTemuSpuId(spu.temuSpuId!!)
//            ?.forEach {
//                val productSkcIdMap = autoCulPriceList.associateBy({ it.skc }, { it })
//                val skuList = productTemplateTemuSkuRepository.getByTemuSkcId(listOf(it.temuSkcId!!))
//                if (skuList.isNotEmpty()) {
//                    skuList.forEach { sku ->
//                        val price = productSkcIdMap[it.skc]?.countryPriceList?.find { it.country == AliexpressConstants.DEFAULT_COUNTRY.first().code }
//                        if (price != null) {
//                            val updateSku = ProductTemplateTemuSku().apply {
//                                this.temuSkuId = sku.temuSkuId
//                                // TEMU计算价格需要转币种, 再更新到json字符串内
////                                this.salePrice = price.salePrice
////                                this.retailPrice = price.retailPrice
//                            }
//                            updateSkuList.add(updateSku)
//                        }
//                    }
//                }
//            }
//        if (updateSkuList.isNotEmpty()) {
//            productTemplateTemuSkuRepository.updateBatchById(updateSkuList)
//        }
        }
    }

    /**
     * 更新尺码信息
     */
    override fun updateSize(data: ProductUpdateDto.Data) {
        if (CollectionUtils.isEmpty(data.sizeNameList)) {
            return
        }
        val spuList = productTemplateTemuSpuRepository.listNoCompletedBySpuCode(data.spuCode!!)
        if (spuList.isEmpty()) {
            log.warn { "待上架-更新尺码信息-错误，找不到spuCode, dto=${data.toJson()}" }
            return
        }
        spuList?.forEach { spu ->
            val productSkcList = productTemplateTemuSkcRepository.getByTemuSpuId(spu.temuSpuId!!)
            if (CollectionUtils.isEmpty(productSkcList)) {
                return@forEach
            }
            productSkcList
                ?.filter { it.skc == data.skc }
                ?.forEach { skc ->
                    val skuList = productTemplateTemuSkuRepository.getByTemuSkcId(listOf(skc.temuSkcId!!))
                    if (skuList.isNotEmpty()) {
                        /*
                        以模板sku为基准, 匹配data尺码, 模板不存在data内, 删除
                         */
                        skuList
                            .filter { !data.sizeNameList!!.contains(it.sizeName) }
                            .forEach { sku ->
                                val updateSku = ProductTemplateTemuSku().apply {
                                    this.temuSkuId = sku.temuSkuId
                                    this.enableState = Bool.NO.code
                                }
                                productTemplateTemuSkuRepository.updateById(updateSku)
                            }
                        /*
                        以data尺码为基准, 匹配模板sku
                        1. 模板不存在data内, 新增
                        2. 模板存在data内, enable 改为 1
                         */
                        data.sizeNameList!!.forEach { sizeName ->

                            if (!skuList.map { it.sizeName }.contains(sizeName)) {
                                // 模板不存在data内
                                var newBarcode: String? = null
                                val barCode = productBarCodeRepository.getBySpuCodeAndSkcAndSize(spu.spuCode, skc.skc, sizeName)
                                if (barCode == null) {
                                    // 添加条码
                                    val product = productRepository.getById(spu.productId)
                                    val barcodeReq = BatchCreateBarCodeReq().apply {
                                        this.categoryCode = product.categoryCode
                                        this.categoryName = product.categoryName
                                        this.skcCode = skc.skc
                                        this.color = skc.color
                                        this.spuCode = product.spuCode
                                        this.groupName = product.sizeGroupName
                                        this.sourceGroupCode = product.sizeGroupCode
                                        this.sizeValues = listOf(sizeName)
                                        this.inspiraImgUrl = product.inspiraImgUrl
                                        this.supplyMode = product.supplyMode
                                        this.localPrice = skc.localPrice
                                        this.designImgUrl = skc.pictures
                                        this.mainImgUrl = skc.pictures
                                    }
                                    val barcodeResp = barCodeService.createBarcodeByForce(listOf(barcodeReq))
                                    if (CollectionUtils.isNotEmpty(barcodeResp)) {
                                        newBarcode = barcodeResp[0].barcode
                                    }
                                } else {
                                    newBarcode = barCode.barcode
                                }

                                val sku = ProductTemplateTemuSku().apply {
                                    this.temuSkuId = IdHelper.getId()
                                    this.temuSkcId = skc.temuSkcId
                                    this.temuSpuId = spu.temuSpuId
//                        this.sellerSku =
                                    this.barcode = newBarcode
                                    this.stockQuantity = temuProperties.defaultStockQuantity
                                    this.sizeName = sizeName
                                    this.flagFrontend = Bool.YES.code
                                    this.enableState = Bool.YES.code
                                }
                                productTemplateTemuSkuRepository.save(sku)
                            } else {
                                val updateSkuList = skuList
                                    .filter { it.sizeName == sizeName }
                                    .map { sku ->
                                        val updateSku = ProductTemplateTemuSku().apply {
                                            this.temuSkuId = sku.temuSkuId
                                            this.enableState = Bool.YES.code
                                        }
                                        updateSku
                                    }
                                if (CollectionUtils.isNotEmpty(updateSkuList)) {
                                    productTemplateTemuSkuRepository.updateBatchById(updateSkuList)
                                }
                            }
                        }
                    }
                }
        }
    }

    /**
     * 更新定价类型
     */
    override fun updatePricingType(data: ProductUpdateDto.Data) {
        // 模板表没有定价类型, product表记录
    }

    /**
     * 更新款式风格
     */
    override fun updateClothingStyle(data: ProductUpdateDto.Data) {
        // 模板表没有款式风格, product表记录
    }
    /**
     * 更新商品属性
     */
    override fun updateAttributes(data: ProductUpdateDto.Data){

    }
}