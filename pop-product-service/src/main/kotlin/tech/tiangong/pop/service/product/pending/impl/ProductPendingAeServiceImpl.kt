package tech.tiangong.pop.service.product.pending.impl

import com.alibaba.excel.EasyExcel
import com.baomidou.mybatisplus.extension.kotlin.KtQueryWrapper
import com.baomidou.mybatisplus.extension.plugins.pagination.Page
import org.apache.commons.collections4.CollectionUtils
import org.apache.commons.lang3.ObjectUtils
import org.springframework.stereotype.Service
import org.springframework.transaction.PlatformTransactionManager
import org.springframework.transaction.annotation.Transactional
import org.springframework.transaction.support.TransactionTemplate
import org.springframework.web.multipart.MultipartFile
import team.aikero.blade.core.enums.Bool
import team.aikero.blade.core.exception.BusinessException
import team.aikero.blade.core.protocol.PageVo
import team.aikero.blade.core.toolkit.isNotBlank
import team.aikero.blade.core.toolkit.isNotEmpty
import team.aikero.blade.core.toolkit.isNotNull
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.sequence.id.IdHelper
import team.aikero.blade.util.json.toJson
import tech.tiangong.pop.common.enums.PlatformEnum
import tech.tiangong.pop.common.enums.PlatformEnum.AE
import tech.tiangong.pop.common.enums.YesOrNoEnum
import tech.tiangong.pop.common.exception.BaseBizException
import tech.tiangong.pop.common.req.BatchCreateBarCodeReq
import tech.tiangong.pop.component.MarketStyleComponent
import tech.tiangong.pop.component.ae.AeProductManageComponent
import tech.tiangong.pop.config.AliexpressProperties
import tech.tiangong.pop.config.LazadaDefaultProperties
import tech.tiangong.pop.constant.ProductInnerConstant
import tech.tiangong.pop.dao.entity.*
import tech.tiangong.pop.dao.repository.*
import tech.tiangong.pop.dto.TagContextDto
import tech.tiangong.pop.enums.ImportDataTypeEnum
import tech.tiangong.pop.enums.ImportSourceEnum
import tech.tiangong.pop.enums.PendingCancelReasonEnum
import tech.tiangong.pop.enums.ProductPendingTaskStatusEnum
import tech.tiangong.pop.external.InspirationClientExternal
import tech.tiangong.pop.helper.PageRespHelper
import tech.tiangong.pop.helper.ProductAttributesAeHelper
import tech.tiangong.pop.listeners.ImportAeProductExcelListener
import tech.tiangong.pop.req.product.ProductCreatePublishTaskReq
import tech.tiangong.pop.req.product.ProductPendingPublishLogPageReq
import tech.tiangong.pop.req.product.ProductTitleConfigMultiGenerateReq
import tech.tiangong.pop.req.product.ae.*
import tech.tiangong.pop.req.product.ae.ProductPendingAeTaskChangeReq.PendingAeTaskChangeUserReq
import tech.tiangong.pop.resp.product.ProductPendingPageShopResp
import tech.tiangong.pop.resp.product.ProductPendingPublishLogPageResp
import tech.tiangong.pop.resp.product.ProductPendingPublishLogStatisticsResp
import tech.tiangong.pop.resp.product.ae.ProductPendingAeDetailResp
import tech.tiangong.pop.resp.product.ae.ProductPendingAePageResp
import tech.tiangong.pop.resp.product.ae.ProductPendingAeStatisticsResp
import tech.tiangong.pop.service.product.BarCodeService
import tech.tiangong.pop.service.product.ProductTitleGenerateService
import tech.tiangong.pop.service.product.pending.ProductPendingAeService
import java.time.LocalDate
import java.util.*

/**
 * 待上架商品-ae服务实现类
 */
@Service
@Slf4j
class ProductPendingAeServiceImpl(
    private val productTemplateAeSpuRepository: ProductTemplateAeSpuRepository,
    private val productTemplateAeSkcRepository: ProductTemplateAeSkcRepository,
    private val productTemplateAeSkuRepository: ProductTemplateAeSkuRepository,
    private val shopRepository: ShopRepository,
    private val productRepository: ProductRepository,
    private val productSkcRepository: ProductSkcRepository,
    private val imageRepositoryRepository: ImageRepositoryRepository,
    private val productTagRepository: ProductTagRepository,
    private val marketStyleComponent: MarketStyleComponent,
    private val inspirationClientExternal: InspirationClientExternal,
    private val aeProductManageComponent: AeProductManageComponent,
    private val productAttributesRepository: ProductAttributesRepository,
    private val productPictureRepository: ProductPictureRepository,
    private val productAttributesAeHelper: ProductAttributesAeHelper,
    private val productBarCodeRepository: ProductBarCodeRepository,
    private val barCodeService: BarCodeService,
    private val importProductRecordRepository: ImportProductRecordRepository,
    private val aliexpressProperties: AliexpressProperties,
    private val lazadaDefaultProperties: LazadaDefaultProperties,
    private val transactionManager: PlatformTransactionManager,
    private val productTitleGenerateService: ProductTitleGenerateService,
) : ProductPendingAeService {

    private val platform = PlatformEnum.AE

    /**
     * 创建待上架商品
     */
    override fun createPendingTask(req: ProductCreatePublishTaskReq.PendingAeTaskChangeUserReq?, productList: List<Product>, shop: Shop) {

        productList.forEach { product ->

            val multiTitleResp = try {
                productTitleGenerateService.generateMultiTitlesByConfig(ProductTitleConfigMultiGenerateReq().apply {
                    this.productId = product.productId
                    this.product = product
                    this.shopId = product.shopId
                    this.platform = AE
                })
            } catch (e: Exception) {
                log.warn(e) { "生成多标题失败，回退到单标题生成: productId=${product.productId}" }
                null
            }

            // 内联 title 赋值逻辑
            fun setGeneratedTitle(spu: ProductTemplateAeSpu) {
                if (multiTitleResp?.titleData != null) {
                    // 使用多标题结果
                    val titleData = multiTitleResp.titleData
                    spu.setParsedGeneratedTitles(titleData)

                    // 使用第一个标题作为主标题
                    val firstTitle = titleData.titles.firstOrNull()?.title
                    if (firstTitle.isNotBlank()) {
                        spu.productTitle = firstTitle
                            .takeIf { it.isNotBlank() && it!!.length < ProductInnerConstant.getTitleMaxLength(AE) }
                            ?: spu.productTitle
                        spu.generatedTitleOverLengthFlag = if (firstTitle?.let { titleData.titles.firstOrNull()?.isOverLength } == true) YesOrNoEnum.YES.code else YesOrNoEnum.NO.code
                        spu.generatedTitleMissingFieldsJson = (titleData.titles.firstOrNull()?.missingFields ?: emptyList()).toJson()
                    }
                }
            }

            // 手动事务
            TransactionTemplate(transactionManager).execute { status ->

                try {
                    // 新增
                    val spuAe = ProductTemplateAeSpu().apply {
                        this.aeSpuId = IdHelper.getId()
                        this.productId = product.productId
                        this.spuCode = product.spuCode
                        this.productTitle = product.productTitle
                        this.brandName = product.brandName
                        this.packageWeight = product.packageWeight
                        this.packageDimensionsLength = product.packageDimensionsLength ?: lazadaDefaultProperties.packageDimensionsLength.toString()
                        this.packageDimensionsHeight = product.packageDimensionsHeight ?: lazadaDefaultProperties.packageDimensionsHeight.toString()
                        this.packageDimensionsWidth = product.packageDimensionsWidth ?: lazadaDefaultProperties.packageDimensionsWidth.toString()
                        this.sizeGroupName = product.sizeGroupName
                        this.sizeGroupCode = product.sizeGroupCode
                        // 任务信息
                        this.taskExecutorId = req?.userId
                        this.taskExecutorName = req?.userName
                        this.taskStatus = ProductPendingTaskStatusEnum.PENDING.code
                    }
                    setGeneratedTitle(spuAe)
                    productTemplateAeSpuRepository.save(spuAe)

                    val productSkcList = productSkcRepository.getByProductId(product.productId!!)
                    val newSkcList = mutableListOf<ProductTemplateAeSkc>()
                    val newSkuList = mutableListOf<ProductTemplateAeSku>()
                    productSkcList.forEach { skc ->
                        val newSkc = ProductTemplateAeSkc().apply {
                            this.aeSkcId = IdHelper.getId()
                            this.aeSpuId = spuAe.aeSpuId
                            this.productSkcId = skc.productSkcId
                            this.skc = skc.skc
                            this.color = skc.color
                            this.platformColor = skc.platformColor
                            this.colorCode = skc.colorCode
                            this.colorAbbrCode = skc.colorAbbrCode
                            this.pictures = skc.pictures
                            this.state = skc.state
                            this.cbPrice = skc.cbPrice
                            this.localPrice = skc.localPrice
                            this.purchasePrice = skc.purchasePrice
                            this.costPrice = skc.costPrice
                        }
                        newSkcList.add(newSkc)

                        val sizeList = listOf<String>() // TODO 需要product_skc获取尺码, 暂无该字段
                        sizeList.forEach { size ->
                            // 获取条码
                            val barCode = productBarCodeRepository.getBySpuCodeAndSkcAndSize(spuAe.spuCode, skc.skc, size)

                            // 新增
                            val skuAe = ProductTemplateAeSku().apply {
                                this.aeSkuId = IdHelper.getId()
                                this.aeSkcId = newSkc.aeSkcId
                                this.aeSpuId = spuAe.aeSpuId
                                //                            this.sellerSku = importDto.skuCode
                                barCode?.let { this.barcode = it.barcode }
                                this.stockQuantity = lazadaDefaultProperties.defaultStock.toLong()
                                this.sizeName = size
                                this.shopId = shop.shopId
                                this.flagFrontend = Bool.YES.code
                                this.enableState = Bool.YES.code
                                this.shipsFromAttributeId = aliexpressProperties.aePlatform.shipsFromPropertyId
                                this.shipsFromAttributeName = aliexpressProperties.aePlatform.shipsFromPropertyName
                                this.shipsFromAttributeValueId = aliexpressProperties.aePlatform.shipsFromPropertyValueId
                                this.shipsFromAttributeValueName = aliexpressProperties.aePlatform.shipsFromPropertyValueName
                            }
                            newSkuList.add(skuAe)
                        }
                    }
                    if (newSkcList.isNotEmpty()) {
                        productTemplateAeSkcRepository.saveBatch(newSkcList)
                    }
                    if (newSkcList.isNotEmpty()) {
                        productTemplateAeSkuRepository.saveBatch(newSkuList)
                    }
                } catch (e: Exception) {
                    log.error(e) { "商品中心-创建AE待上架-异常 productId: ${product.productId}, shopId: ${shop.shopId}" }
                    // 手动标记事务回滚
                    status.setRollbackOnly()
                }
            }
        }
    }

    /**
     * 商品列表-分页
     */
    override fun page(req: ProductPendingAePageReq): PageVo<ProductPendingAePageResp> {
        val page = productTemplateAeSpuRepository.pendingPage(Page(req.pageNum.toLong(), req.pageSize.toLong()), req)
        val templateSpuList = page.records
        if (CollectionUtils.isEmpty(templateSpuList)) {
            return PageRespHelper.empty()
        }
        // 获取product
        val productIds = templateSpuList.mapNotNull { it.productId }
        if (productIds.isEmpty()) {
            return PageRespHelper.empty()
        }
        val products = productRepository.listByIds(productIds)
        if (productIds.isEmpty()) {
            return PageRespHelper.empty()
        }

        // 获取主图
        val spuMainImageMap = mutableMapOf<String, String?>()
        val spuCodes = products.mapNotNull { it.spuCode }.distinct()
        if (spuCodes.isNotEmpty()) {
            imageRepositoryRepository.listBySpuCodes(spuCodes)
                ?.filter { image -> image.spuCode.isNotBlank() }
                ?.forEach { image ->
                    spuMainImageMap[image.spuCode!!] = image.mainUrl
                }
        }

        val shopMap = shopRepository.listByIds(products.mapNotNull { it.shopId })
            ?.associateBy { it.shopId }

        // 获取标签
        val tagContext = collectTagData(templateSpuList)

        val productMap = products.associateBy { it.productId }
        val productsResp = templateSpuList.mapNotNull { templateSpu ->

            val product = productMap[templateSpu.productId] ?: return@mapNotNull null
            val tagCodes = TagContextDto.getCombinedTags(templateSpu.productId!!, templateSpu.aeSpuId!!, tagContext)

            ProductPendingAePageResp().apply {
                this.aeSpuId = templateSpu.aeSpuId
                this.productId = templateSpu.productId
                this.listingShopId = templateSpu.shopId
                this.listingShopName = shopMap?.get(templateSpu.shopId!!)?.shopName
                this.mainImgUrl = spuMainImageMap[product.spuCode] ?: product.mainImgUrl
                this.supplyMode = product.supplyMode
                this.waves = product.waves
                this.spuCode = product.spuCode
                this.tagCodes = tagCodes
                this.shopList = mutableListOf<ProductPendingPageShopResp>().apply {
                    this.add(ProductPendingPageShopResp().apply {
                        this.shopId = product.shopId
                        this.shopName = product.shopName
                    })
                }.toList()
                this.countryList = product.countrys?.let { c -> listOf(c) }
                this.categoryId = product.categoryId
                this.categoryCode = product.categoryCode
                this.categoryName = product.categoryName
                this.creatorId = product.creatorId
                this.creatorName = product.creatorName
                this.createdTime = product.createdTime
                this.reviserId = product.reviserId
                this.reviserName = product.reviserName
                this.revisedTime = product.revisedTime
                this.styleType = product.styleType
                this.imagePackageState = product.imagePackageState
                this.planAuditState = product.planAuditState
                this.planAuditorId = product.planAuditorId
                this.planAuditorName = product.planAuditorName
                this.planAuditTime = product.planAuditTime
                this.listingUserId = templateSpu.taskExecutorId
                this.listingUserName = templateSpu.taskExecutorName
                this.taskStatus = templateSpu.taskStatus
            }
        }
        return PageRespHelper.of(page.current.toInt(), page.total, productsResp)
    }

    /**
     * 商品列表-统计
     */
    override fun statistics(req: ProductPendingAePageReq): ProductPendingAeStatisticsResp {
        // 状态统计
        val taskStatusStats = productTemplateAeSpuRepository.statistics(req)
        return ProductPendingAeStatisticsResp().apply {
            this.pendingCount = taskStatusStats.pendingCount ?: 0
            this.startingCount = taskStatusStats.startingCount ?: 0
            this.failedCount = taskStatusStats.failedCount ?: 0
            this.missingInfoCount = taskStatusStats.missingInfoCount ?: 0
            this.completeInfoCount = taskStatusStats.completeInfoCount ?: 0
        }
    }

    /**
     * 商品详情
     */
    override fun detail(req: ProductPendingAeDetailReq): ProductPendingAeDetailResp {
        val templateSpu = productTemplateAeSpuRepository.getById(req.aeSpuId) ?: throw IllegalArgumentException("商品不存在")
        val product = productRepository.getById(templateSpu.productId) ?: throw IllegalArgumentException("商品不存在")

        // 市场(一级节点)
        val marketMap = marketStyleComponent.getMarketNodeMap(1)
        // 系列(二级节点) 市场-系列
        val marketSeriesMap = marketStyleComponent.getMarketNodeMap(2)
        // 风格(三级节点) 市场-系列-风格
        val marketStyleMap = marketStyleComponent.getMarketNodeMap(3)
        // 获取图片
        val image = imageRepositoryRepository.getBySpuCode(product.spuCode!!)

        return ProductPendingAeDetailResp().apply {
            this.aeSpuId = templateSpu.aeSpuId
            this.productId = templateSpu.productId
            this.shopId = product.shopId
            this.shopName = product.shopName
            this.mainImgUrl = image?.mainUrl ?: product.mainImgUrl
            this.spuCode = product.spuCode
            this.supplyMode = product.supplyMode
            this.spotTypeCode = product.spotTypeCode
            this.clothingStyleName = product.clothingStyleName ?: marketStyleMap?.get(product.clothingStyleCode)
            this.planningType = product.planningType
            this.marketCode = product.marketCode
            this.marketName = marketMap?.get(product.marketCode)
            this.marketSeriesCode = product.marketSeriesCode
            this.marketSeriesName = marketSeriesMap?.get(product.marketSeriesCode)
            this.planSourceName = product.planSourceName
            this.goodsRepType = product.goodsRepType
            this.categoryId = product.categoryId
            this.categoryCode = product.categoryCode
            this.categoryName = product.categoryName
            this.inspiraSource = product.inspiraSource
            this.inspiraImgUrl = product.inspiraImgUrl
            this.selectStyleName = product.selectStyleName
            this.selectStyleTime = product.selectStyleTime
            this.buyerRemark = product.buyerRemark
            this.creatorName = product.creatorName
            this.createdTime = product.createdTime
            this.prototypeNum = product.prototypeNum
            this.goodsType = product.goodsType
            this.pricingType = product.pricingType
            this.spotType = product.spotType
            this.waves = product.waves
            this.printType = product.printType
            if (product.inspiraSourceId != null) {
                // 获取灵感详情
                try {
                    val data = inspirationClientExternal.getByInspirationOrPickingId(product.inspiraSourceId!!)
                    if (data?.inspirationInfo != null) {
                        this.inspiraSource = data.inspirationInfo?.inspirationImageSource
                        this.inspiraCountry = data.inspirationInfo?.countrySiteName
                        this.planSourceName = data.inspirationInfo?.planningSourceName
                        this.inspiraImgUrl = data.inspirationInfo?.inspirationImage
                    }
                    if (data?.pickingInfo != null) {
                        this.countryList = data.pickingInfo?.countrySiteName?.let { listOf(it) }
                        this.shopId = data.pickingInfo?.shopId
                        this.shopName = data.pickingInfo?.shopName
                        this.selectStyleTime = data.pickingInfo?.pickingTime
                        this.buyerRemark = data.pickingInfo?.remark
                    }
                } catch (e: Exception) {
                    log.error { "getByInspirationOrPickingId 获取灵感详情报错或者数据为空，Exception=${e.message}," }
                }
            }
            this.aeDetail = aeProductManageComponent.detailV2(templateSpu, product)
            this.styleType = product.styleType
            this.planAuditState = product.planAuditState
            this.imagePackageState = product.imagePackageState
        }
    }

    /**
     * 商品更新
     */
    @Transactional(rollbackFor = [Exception::class])
    override fun update(req: ProductPendingAeUpdateReq) {
        // sku(存在: 更新, 不存在: 新增), 新增时id为空, 也要判断尺码条件, 可能已经存在, 但可用状态是0
        val aeSpu = productTemplateAeSpuRepository.getById(req.aeSpuId!!)
        requireNotNull(aeSpu) { "AE spu不存在 ${req.aeSpuId}" }

        val product = productRepository.getById(aeSpu.productId!!)

        //更新图库
        savePicture(product)

        //设置属性
        if (CollectionUtils.isNotEmpty(req.attributes)) {
            val platformEnum = AE
            //先删除已有属性
            productAttributesRepository.removeByProductIdAndPlatformId(product.productId!!, platformEnum.platformId)
            productAttributesRepository.saveBatch(
                req.attributes?.map { k ->
                    ProductAttributes().apply {
                        this.attributeId = k.attributeId
                        this.attributeValueId = k.attributeValueId
                        this.productId = product.productId
                        this.categoryId = product.categoryId
                        this.platformId = platformEnum.platformId
                    }
                })
        }

        // 更新SPU (每个字段判断, 有不一致的才更新)
        updateAeSpu(aeSpu, req)
        // 更新SKC 和 SKU
        updateAeSkcAndSku(aeSpu.aeSpuId!!, req.skcList)
    }

    /**
     * 更新Ae spu
     * @param aeSpu
     * @param req
     */
    private fun updateAeSpu(aeSpu: ProductTemplateAeSpu, req: ProductPendingAeUpdateReq) {
        var update = false
        if (req.productTitle?.isNotBlank() == true && ObjectUtils.notEqual(req.productTitle, aeSpu.productTitle)) {
            aeSpu.productTitle = req.productTitle
            update = true
        }
        if (req.invDeduction != null && ObjectUtils.notEqual(req.invDeduction, aeSpu.invDeduction)) {
            aeSpu.invDeduction = req.invDeduction
            update = true
        }
        if (req.originPlaceName != null && ObjectUtils.notEqual(req.originPlaceName, aeSpu.originPlaceName)) {
            aeSpu.originPlaceName = req.originPlaceName
            update = true
        }
        if (req.packageDimensionsLength?.isNotBlank() == true && ObjectUtils.notEqual(req.packageDimensionsLength, aeSpu.packageDimensionsLength)) {
            aeSpu.packageDimensionsLength = req.packageDimensionsLength
            update = true
        }
        if (req.packageDimensionsWidth?.isNotBlank() == true && ObjectUtils.notEqual(req.packageDimensionsWidth, aeSpu.packageDimensionsWidth)) {
            aeSpu.packageDimensionsWidth = req.packageDimensionsWidth
            update = true
        }
        if (req.packageDimensionsHeight?.isNotBlank() == true && ObjectUtils.notEqual(req.packageDimensionsHeight, aeSpu.packageDimensionsHeight)) {
            aeSpu.packageDimensionsHeight = req.packageDimensionsHeight
            update = true
        }
        if (req.packageWeight?.isNotBlank() == true && ObjectUtils.notEqual(req.packageWeight, aeSpu.packageWeight)) {
            aeSpu.packageWeight = req.packageWeight
            update = true
        }
        if (req.taxType?.isNotNull() == true && ObjectUtils.notEqual(req.taxType, aeSpu.taxType)) {
            aeSpu.taxType = req.taxType
            update = true
        }
        if (req.hsCode.isNotBlank() && ObjectUtils.notEqual(req.hsCode, aeSpu.hsCode)) {
            aeSpu.hsCode = req.hsCode
            update = true
        }
        if (req.hsPvList.isNotEmpty() && ObjectUtils.notEqual(req.hsPvList, aeSpu.getParsedHsPvList())) {
            aeSpu.setParsedHsPvList(req.hsPvList)
            update = true
        }
        if (req.hsExtendInfo.isNotBlank() && ObjectUtils.notEqual(req.hsExtendInfo, aeSpu.hsExtendInfo)) {
            aeSpu.hsExtendInfo = req.hsExtendInfo
            update = true
        }
        val originPropertyValueItems = productAttributesAeHelper.processOriginAttributes(req.originPropertyValueItems)
        if (originPropertyValueItems.isNotEmpty() && ObjectUtils.notEqual(originPropertyValueItems, aeSpu.getParsedOriginPropertyValueItems())) {
            aeSpu.setParsedOriginPropertyValueItems(originPropertyValueItems)
            update = true
        }
        if (req.generatedTitleMissingFieldsJson.isNotEmpty() && ObjectUtils.notEqual(req.generatedTitleMissingFieldsJson, aeSpu.getParsedGeneratedTitleMissingFields())) {
            aeSpu.generatedTitleMissingFieldsJson = req.generatedTitleMissingFieldsJson!!.toJson()
            update = true
        }
        if (req.generatedTitleOverLengthFlag != null && ObjectUtils.notEqual(req.generatedTitleOverLengthFlag, aeSpu.generatedTitleOverLengthFlag)) {
            aeSpu.generatedTitleOverLengthFlag = req.generatedTitleOverLengthFlag
            update = true
        }
        if (req.generatedTitlesJson != null && ObjectUtils.notEqual(req.generatedTitlesJson, aeSpu.getParsedGeneratedTitles())) {
            aeSpu.setParsedGeneratedTitles(req.generatedTitlesJson)
            update = true
        }
        if (update) {
            productTemplateAeSpuRepository.updateById(aeSpu)
        }
    }

    /**
     * 更新AE skc和sku
     * @param aeSpuId
     * @param req
     */
    private fun updateAeSkcAndSku(aeSpuId: Long, req: List<ProductPendingUpdateAeSkcReq>?) {
        if (req.isNullOrEmpty()) {
            return
        }
        val spuLazada = productTemplateAeSpuRepository.getById(aeSpuId)
        req.forEach { skcReq ->
            // 处理SKC
            var aeSkc: ProductTemplateAeSkc? = null
            if (skcReq.aeSkcId == null) {
                // 是否组合商品
                if (skcReq.combo == Bool.YES.code) {
                    if (skcReq.colorCode.isNullOrBlank()) {
                        throw IllegalArgumentException("组合商品必须有颜色")
                    }

                    // 组合商品, 创建SKC
                    aeSkc = ProductTemplateAeSkc().apply {
                        this.aeSkcId = IdHelper.getId()
                        this.aeSpuId = aeSpuId
                        this.color = getComboColorByAe(skcReq)
                        this.colorCode = skcReq.colorCode?.trim()
                        this.platformColor = skcReq.platformColor
//                        this.colorAbbrCode = skcReq.colorAbbrCode
//                        this.pictures = skcReq.pictures
                        this.combo = Bool.YES.code
                        this.state = Bool.YES.code
//                        this.cbPrice = it.cbPrice
//                        this.localPrice = it.localPrice
//                        this.purchasePrice = it.purchasePrice
//                        this.costPrice = it.costPrice
                    }
                    productTemplateAeSkcRepository.save(aeSkc)
                }
            } else {
                aeSkc = productTemplateAeSkcRepository.getByAeSpuIdAndAeSkcId(aeSpuId, skcReq.aeSkcId!!)
            }

            requireNotNull(aeSkc) { "AE skc不存在 ${skcReq.aeSkcId}" }

            if (skcReq.combo == Bool.YES.code && aeSkc.combo == Bool.YES.code) {
                // 组合颜色可以改colorCode
                aeSkc.color = getComboColorByAe(skcReq)
                aeSkc.colorCode = skcReq.colorCode
            }
            aeSkc.platformColor = skcReq.platformColor
            productTemplateAeSkcRepository.updateById(aeSkc)

            // 处理SKU
            val saveSkuList = mutableListOf<ProductTemplateAeSku>()
            val updateSkuList = mutableListOf<ProductTemplateAeSku>()
            skcReq.skuList?.forEach { skuReq ->
                var aeSku: ProductTemplateAeSku? = null
                if (skuReq.aeSkuId == null) {
                    // 发货地
                    val shipsFromDto = productAttributesAeHelper.processShipsFromAttribute(skuReq.shipsFromPropertyValueItem)

//                     skuId为空, 可能新增/状态更新, 先用尺码和国家判断是否存在
//                    aeSku = productTemplateAeSkuRepository.getByAeSpuIdAndAeSkcIdAndSizeName(aeSpuId, aeSkc.aeSkcId!!, skuReq.sizeName!!, shipsFromDto?.attributeValueId)
//                    if (aeSku == null) {
                    // 不存在, 新增

                    // 判断尺码是否有条码
                    val skcAe = productTemplateAeSkcRepository.getByAeSpuIdAndAeSkcId(aeSpuId, aeSkc.aeSkcId!!)
                    val barCode = productBarCodeRepository.getBySpuCodeAndSkcAndSize(spuLazada.spuCode, skcAe!!.skc, skuReq.sizeName!!)
                    var newBarcode: String? = null
                    if (barCode == null && skcReq.combo != Bool.YES.code) {
                        // 添加条码
                        val product = productRepository.getById(spuLazada.productId)
                        val barcodeReq = BatchCreateBarCodeReq().apply {
                            this.categoryCode = product.categoryCode
                            this.categoryName = product.categoryName
                            this.skcCode = skcAe.skc
                            this.color = skcAe.color
                            this.spuCode = product.spuCode
                            this.groupName = product.sizeGroupName
                            this.sourceGroupCode = product.sizeGroupCode
                            this.sizeValues = listOf(skuReq.sizeName!!)
                            this.inspiraImgUrl = product.inspiraImgUrl
                            this.supplyMode = product.supplyMode
                            this.localPrice = skcAe.localPrice
                            this.designImgUrl = skcAe.pictures
                            this.mainImgUrl = skcAe.pictures
                        }
                        val barcodeResp = barCodeService.createBarcodeByForce(listOf(barcodeReq))
                        if (CollectionUtils.isNotEmpty(barcodeResp)) {
                            newBarcode = barcodeResp[0].barcode
                        }
                    } else {
                        newBarcode = barCode?.barcode
                    }
                    saveSkuList.add(ProductTemplateAeSku().apply {
                        this.aeSkuId = IdHelper.getId()
                        this.aeSkcId = aeSkc.aeSkcId
                        this.aeSpuId = spuLazada.aeSpuId
                        this.shopId = skuReq.shopId
                        this.barcode = newBarcode
                        this.flagFrontend = skuReq.flagFrontend
                        this.stockQuantity = skuReq.stockQuantity
                        this.sizeName = skuReq.sizeName
                        this.salePrice = skuReq.salePrice ?: skuReq.retailPrice
                        this.retailPrice = skuReq.retailPrice
                        this.lastSalePrice = skuReq.lastSalePrice
                        this.lastRetailPrice = skuReq.lastRetailPrice
                        this.nationalQuoteConfig = skuReq.nationalQuoteConfigList?.toJson()
                        this.purchaseSalePrice = skuReq.purchaseSalePrice
                        this.purchaseRetailPrice = skuReq.purchaseRetailPrice
                        this.regularSalePrice = skuReq.regularSalePrice
                        this.regularRetailPrice = skuReq.regularRetailPrice
                        this.enableState = Bool.YES.code
                        if (skcReq.combo == Bool.YES.code) {
                            if (skuReq.barcodeList.isNullOrEmpty()) {
                                throw IllegalArgumentException("组合商品 ${skuReq.sizeName} 必须有条码")
                            }
                            this.barcodes = skuReq.barcodeList?.toJson()
                        }
                        if (shipsFromDto != null) {
                            this.shipsFromAttributeId = shipsFromDto.attributeId
                            this.shipsFromAttributeName = shipsFromDto.attributeName
                            this.shipsFromAttributeValueId = shipsFromDto.attributeValueId
                            this.shipsFromAttributeValueName = shipsFromDto.attributeValueName
                        }
                    })
//                    } // 存在, 状态更新(更新逻辑)
                } else {
                    // skuId不为空, 正常更新(更新逻辑)
                    aeSku = productTemplateAeSkuRepository.getByAeSpuIdAndAeSkcIdAndAeSkuId(aeSpuId, aeSkc.aeSkcId!!, skuReq.aeSkuId!!)
                }

                if (aeSku != null) {
                    aeSku.flagFrontend = skuReq.flagFrontend
                    aeSku.stockQuantity = skuReq.stockQuantity
                    aeSku.lastSalePrice = aeSku.salePrice
                    aeSku.lastRetailPrice = aeSku.retailPrice
                    aeSku.salePrice = skuReq.salePrice ?: skuReq.retailPrice
                    aeSku.retailPrice = skuReq.retailPrice
                    aeSku.lastSalePrice = skuReq.lastSalePrice
                    aeSku.lastRetailPrice = skuReq.lastRetailPrice
                    aeSku.nationalQuoteConfig = skuReq.nationalQuoteConfigList?.toJson()
                    aeSku.purchaseSalePrice = skuReq.purchaseSalePrice
                    aeSku.purchaseRetailPrice = skuReq.purchaseRetailPrice
                    aeSku.regularSalePrice = skuReq.regularSalePrice
                    aeSku.regularRetailPrice = skuReq.regularRetailPrice
                    aeSku.enableState = Bool.YES.code
                    if (skcReq.combo == Bool.YES.code) {
                        aeSku.barcodes = skuReq.barcodeList?.toJson()
                    }
                    val shipsFromDto = productAttributesAeHelper.processShipsFromAttribute(skuReq.shipsFromPropertyValueItem)
                    if (shipsFromDto != null) {
                        aeSku.shipsFromAttributeId = shipsFromDto.attributeId
                        aeSku.shipsFromAttributeName = shipsFromDto.attributeName
                        aeSku.shipsFromAttributeValueId = shipsFromDto.attributeValueId
                        aeSku.shipsFromAttributeValueName = shipsFromDto.attributeValueName
                    }
                    updateSkuList.add(aeSku)
                }
            }

            if (saveSkuList.isNotEmpty()) {
                productTemplateAeSkuRepository.saveBatch(saveSkuList)
            }
            if (updateSkuList.isNotEmpty()) {
                productTemplateAeSkuRepository.updateBatchById(updateSkuList)
            }

            // 不在req的sku, 标记为enable=0
            val existSkuList = productTemplateAeSkuRepository.list(
                KtQueryWrapper(ProductTemplateAeSku::class.java)
                    .eq(ProductTemplateAeSku::aeSpuId, aeSpuId)
                    .eq(ProductTemplateAeSku::aeSkcId, aeSkc.aeSkcId)
                    .eq(ProductTemplateAeSku::enableState, Bool.YES.code)
            )
            val saveAeSkuId = (saveSkuList + updateSkuList).map { it.aeSkuId }
            val noEnableSkuList = mutableListOf<ProductTemplateAeSku>()
            existSkuList
                .filter { existSku -> !saveAeSkuId.contains(existSku.aeSkuId) }
                .forEach { existSku ->
                    // 是否存在于req
                    var exist = false
                    skcReq.skuList
                        ?.filter { skuReq -> skuReq.aeSkuId != null }
                        ?.forEach innerForEach@{ skuReq ->
                            if (Objects.equals(skuReq.aeSkcId, existSku.aeSkcId)
                                && Objects.equals(skuReq.sizeName, existSku.sizeName)
                                && existSku.enableState == Bool.YES.code
                            ) {
                                exist = true
                                return@innerForEach
                            }
                        }
                    if (!exist) {
                        // 不存在的标记enable=0
                        existSku.enableState = Bool.NO.code
                        noEnableSkuList.add(existSku)
                    }
                }
            if (noEnableSkuList.isNotEmpty()) {
                productTemplateAeSkuRepository.updateBatchById(noEnableSkuList)
            }
        }
    }

    /**
     * 获取组合颜色
     */
    private fun getComboColorByAe(skcReq: ProductPendingUpdateAeSkcReq): String {
        // 提取sku下的所有barcode
        val barcodeList = skcReq.skuList?.mapNotNull { it.barcodeList }?.flatten()?.mapNotNull { it.barcode }?.distinct()
        require(!(barcodeList.isNullOrEmpty())) { "组合商品必须有条码" }
        val barcodeInfoList = productBarCodeRepository.getListByBarcodes(barcodeList)
        val skcCodeList = barcodeInfoList.mapNotNull { it.skc }.distinct()
        val productSkcList = productSkcRepository.listBySkc(skcCodeList)
        return productSkcList.mapNotNull { it.color?.trim() }.distinct().sorted().joinToString("+")
    }

    private fun savePicture(product: Product) {
        val imageRepository = imageRepositoryRepository.getBySpuCode(product.spuCode!!)
        if (Objects.isNull(imageRepository)) {
            return
        }
        val picture = productPictureRepository.getOneByProductId(product.productId!!)
        if (Objects.isNull(picture)) {
            val productPicture = ProductPicture().apply {
                this.productId = product.productId
                this.spuCode = product.spuCode
                this.sourceImageUrl = imageRepository!!.mainUrl
            }
            productPictureRepository.save(productPicture)
        }
    }

    /**
     * 任务操作-开始任务
     */
    @Transactional(rollbackFor = [Exception::class])
    override fun start(aeSpuIds: List<Long>) {
        val templateAeSpuList = productTemplateAeSpuRepository.listByIds(aeSpuIds)
            .filter { it.taskStatus == ProductPendingTaskStatusEnum.PENDING.code }
        if (templateAeSpuList.isEmpty()) {
            throw IllegalArgumentException("商品不存在或已被处理")
        }
        val updateSpuList = templateAeSpuList.map { templateAeSpu ->
            ProductTemplateAeSpu().apply {
                this.aeSpuId = templateAeSpu.aeSpuId
                this.taskStatus = ProductPendingTaskStatusEnum.STARTING.code
            }
        }
        productTemplateAeSpuRepository.updateBatchById(updateSpuList)
    }

    /**
     * 任务操作-更换人员
     */
    override fun change(req: ProductPendingAeTaskChangeReq) {
        // 获取合法的商品SPU
        val templateAeSpuList = productTemplateAeSpuRepository.listByIds(req.aeSpuIds)
            .filter {
                it.taskStatus == ProductPendingTaskStatusEnum.FAILED.code
                        || it.taskStatus == ProductPendingTaskStatusEnum.PENDING.code
                        || it.taskStatus == ProductPendingTaskStatusEnum.STARTING.code
            }
        if (templateAeSpuList.isEmpty()) {
            throw IllegalArgumentException("商品不存在或已被处理")
        }
        // 多人员则平均分配
        val userSpuMap = mutableMapOf<PendingAeTaskChangeUserReq, MutableList<ProductTemplateAeSpu>>()
        if (req.users.size > templateAeSpuList.size) {
            log.info { "人数 > 商品数" }
            // 前spuList.size个用户各分配一个商品，剩下的用户分配空列表
            for (i in req.users.indices) {
                val userId = req.users[i]
                val spuListForUser = if (i < templateAeSpuList.size) {
                    // 为前N个用户分配一个商品（N等于商品数量）
                    mutableListOf(templateAeSpuList[i])
                } else {
                    mutableListOf()
                }
                userSpuMap[userId] = spuListForUser
            }

        } else if (req.users.size < templateAeSpuList.size) {
            log.info { "人数 < 商品数" }
            // 计算基本分配数量和需要多分配一个商品的用户数
            val baseCount = templateAeSpuList.size / req.users.size
            val extraCount = templateAeSpuList.size % req.users.size

            var currentIndex = 0
            for (i in req.users.indices) {
                val userId = req.users[i]
                // 前extraCount个用户多分配一个商品
                val count = if (i < extraCount) baseCount + 1 else baseCount
                // 截取对应数量的商品
                val spuListForUser = templateAeSpuList.subList(currentIndex, currentIndex + count).toMutableList()
                userSpuMap[userId] = spuListForUser
                currentIndex += count
            }

        } else {
            log.info { "人数 = 商品数" }
            // 一对一分配
            for (i in req.users.indices) {
                userSpuMap[req.users[i]] = mutableListOf(templateAeSpuList[i])
            }
        }
        if (userSpuMap.isEmpty()) {
            throw BusinessException("平均分配失败")
        }
        userSpuMap.forEach { (user, spuList) ->
            val updateSpuList = spuList.map { templateAeSpu ->
                ProductTemplateAeSpu().apply {
                    this.aeSpuId = templateAeSpu.aeSpuId
                    this.taskExecutorId = user.userId
                    this.taskExecutorName = user.userName
                }
            }
            productTemplateAeSpuRepository.updateBatchById(updateSpuList)
        }
    }

    /**
     * 任务操作-取消任务
     */
    override fun cancel(req: ProductPendingAeTaskCancelReq) {
        if (req.aeSpuIds.isEmpty()) {
            throw IllegalArgumentException("请选择商品")
        }
        val cancel = PendingCancelReasonEnum.getByCode(req.cancelReason) ?: throw IllegalArgumentException("取消原因不存在")
        val templateAeSpuList = productTemplateAeSpuRepository.listByIds(req.aeSpuIds)
            .filter {
                it.taskStatus == ProductPendingTaskStatusEnum.FAILED.code
                        || it.taskStatus == ProductPendingTaskStatusEnum.PENDING.code
                        || it.taskStatus == ProductPendingTaskStatusEnum.STARTING.code
            }
        if (templateAeSpuList.isEmpty()) {
            throw IllegalArgumentException("商品不存在或已被处理")
        }
        val updateSpuList = templateAeSpuList.map { templateAeSpu ->
            ProductTemplateAeSpu().apply {
                this.aeSpuId = templateAeSpu.aeSpuId
                this.taskStatus = ProductPendingTaskStatusEnum.CANCELED.code
                this.cancelReason = cancel.desc
            }
        }
        productTemplateAeSpuRepository.updateBatchById(updateSpuList)
    }

    /**
     * 上架记录-统计
     */
    override fun publishLogStatistics(): ProductPendingPublishLogStatisticsResp {
        // 状态统计
        val taskStatusStats = productTemplateAeSpuRepository.taskStatusStats()
        // 今天创建的数量
        val todayNew = productTemplateAeSpuRepository.ktQuery()
            .between(ProductTemplateAeSpu::createdTime, LocalDate.now().atStartOfDay(), LocalDate.now())
            .count()
        // 今天发布的数量
        val todayPublished = productTemplateAeSpuRepository.ktQuery()
            .eq(ProductTemplateAeSpu::taskStatus, ProductPendingTaskStatusEnum.COMPLETED.code)
            .between(ProductTemplateAeSpu::taskCompleteTime, LocalDate.now().atStartOfDay(), LocalDate.now())
            .count()
        return ProductPendingPublishLogStatisticsResp().apply {
            this.pendingCount = taskStatusStats.find { it.taskStatus == ProductPendingTaskStatusEnum.PENDING.code }?.taskStatusCount?.toInt() ?: 0
            this.startingCount = taskStatusStats.find { it.taskStatus == ProductPendingTaskStatusEnum.STARTING.code }?.taskStatusCount?.toInt() ?: 0
            this.failedCount = taskStatusStats.find { it.taskStatus == ProductPendingTaskStatusEnum.FAILED.code }?.taskStatusCount?.toInt() ?: 0
            this.todayNewCount = todayNew?.toInt() ?: 0
            this.todayPublishedCount = todayPublished?.toInt() ?: 0
        }
    }

    /**
     * 上架记录-分页列表
     */
    override fun publishLogPage(req: ProductPendingPublishLogPageReq): PageVo<ProductPendingPublishLogPageResp> {
        val page = productTemplateAeSpuRepository.page(
            Page(req.pageNum.toLong(), req.pageSize.toLong()),
            KtQueryWrapper(ProductTemplateAeSpu::class.java)
                .eq(req.taskState != null, ProductTemplateAeSpu::taskStatus, req.taskState)
                .orderByDesc(ProductTemplateAeSpu::createdTime)
        )
        if (page.records.isEmpty()) {
            return PageRespHelper.of(page.current.toInt(), page.total, emptyList())
        }
        val shopMap = shopRepository.listByIds(page.records.mapNotNull { it.shopId })
            ?.associateBy { it.shopId }
        val result = page.records.map { templateSpu ->
            ProductPendingPublishLogPageResp().apply {
                this.shopName = shopMap?.get(templateSpu.shopId)?.shopName
                this.spuCode = templateSpu.spuCode
                this.creatorId = templateSpu.creatorId
                this.creatorName = templateSpu.creatorName
                this.createdTime = templateSpu.createdTime
                this.taskStatus = templateSpu.taskStatus
                this.listingUserName = templateSpu.taskExecutorName
                this.publishTime = templateSpu.taskCompleteTime
                this.operationCount = 0
                this.reason = templateSpu.cancelReason
            }
        }
        return PageRespHelper.of(page.current.toInt(), page.total, result)
    }

    /**
     * 导入-待上架-商品数据
     */
    override fun importExcel(file: MultipartFile) {
        val listener = ImportAeProductExcelListener()
        EasyExcel.read(file.inputStream, listener).doReadAll()

        val importList = listener.getProductList()
            .asSequence()
            .filter { it.supplyMode.isNotBlank() }
            .filter { it.productTitle.isNotBlank() }
            .filter { it.spuCode.isNotBlank() }
            .filter { it.categoryName.isNotBlank() }
            .filter { it.skcCode.isNotBlank() }
            .filter { it.originPlaceName.isNotBlank() }
            .toList()
        if (CollectionUtils.isEmpty(importList)) {
            throw BaseBizException("导入AE数据为空")
        }

        // 检查一个SPU只能有一个品类, 否则报错
        val spuCodeMap = importList.groupBy { it.spuCode }
        spuCodeMap.forEach { (spuCode, list) ->
            if (list.filter { it.categoryName.isNotBlank() }.mapNotNull { it.categoryName }.distinct().size > 1) {
                throw BaseBizException("SPU: $spuCode 有多个品类, 请检查")
            }
        }

        // 检查店铺
        val shopNameList = importList.map { it.listingShopName }.distinct()
        if (shopNameList.isEmpty()) {
            throw BaseBizException("店铺不能为空")
        }
        val existsShopList = shopRepository.ktQuery()
            .`in`(Shop::shopName, shopNameList)
            .eq(Shop::platformId, platform.platformId)
            .list()
            .mapNotNull { it.shopName }
        shopNameList.forEach { shopName ->
            if (!existsShopList.contains(shopName)) {
                throw BaseBizException("店铺: $shopName 不存在")
            }
        }

        val list = importList.map {
            ImportProductRecord().apply {
                this.supplyMode = it.supplyMode
                this.spuCode = it.spuCode
                this.importDataType = ImportDataTypeEnum.ALL.code
                this.importSource = ImportSourceEnum.PENDING_UPLOAD.code
                this.platformId = platform.platformId
                this.importData = it.toJson()
                this.skc = it.skcCode
            }
        }
        importProductRecordRepository.saveBatch(list, 100)
    }

    /**
     * 收集给定产品ID的所有标签相关数据
     */
    private fun collectTagData(templateSpuList: List<ProductTemplateAeSpu>): TagContextDto {
        val productIds = templateSpuList.mapNotNull { it.productId }.distinct()

        // 1. 获取产品级别的标签
        val productIdTagMap = productTagRepository.getTagMapByProductIds(productIds)

        // 获取SPU数据
        val spuList: List<ProductTemplateAeSpu> = templateSpuList
        val spuIds: List<Long> = spuList.mapNotNull { it.aeSpuId }.distinct()
        val productIdToSpuIds: Map<Long, List<Long>> = spuList
            .filter { it.productId.isNotNull() }
            .groupBy { it.productId!! }
            .mapValues { entry ->
                entry.value.mapNotNull { it.aeSpuId }
            }

        val aeSpuIdTagMap = if (spuIds.isNotEmpty()) {
            productTagRepository.getTagMapByAeSpuIds(spuIds)
        } else {
            emptyMap()
        }

        return TagContextDto(
            productIdTagMap,
            productIdToSpuIds,
            aeSpuIdTagMap,
        )
    }
}