package tech.tiangong.pop.service.product.pending.impl

import com.alibaba.excel.EasyExcel
import com.baomidou.mybatisplus.extension.kotlin.KtQueryWrapper
import com.baomidou.mybatisplus.extension.plugins.pagination.Page
import org.apache.commons.collections4.CollectionUtils
import org.apache.commons.lang3.ObjectUtils
import org.springframework.stereotype.Service
import org.springframework.transaction.PlatformTransactionManager
import org.springframework.transaction.annotation.Transactional
import org.springframework.transaction.support.TransactionTemplate
import org.springframework.web.multipart.MultipartFile
import team.aikero.blade.core.enums.Bool
import team.aikero.blade.core.exception.BusinessException
import team.aikero.blade.core.protocol.PageVo
import team.aikero.blade.core.toolkit.isNotBlank
import team.aikero.blade.core.toolkit.isNotEmpty
import team.aikero.blade.core.toolkit.isNotNull
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.sequence.id.IdHelper
import team.aikero.blade.util.json.parseJsonList
import team.aikero.blade.util.json.toJson
import tech.tiangong.pop.common.constant.LazadaConstants
import tech.tiangong.pop.common.enums.LazadaCountryEnum
import tech.tiangong.pop.common.enums.PlatformEnum
import tech.tiangong.pop.common.enums.PlatformEnum.LAZADA
import tech.tiangong.pop.common.enums.YesOrNoEnum
import tech.tiangong.pop.common.exception.BaseBizException
import tech.tiangong.pop.common.req.BatchCreateBarCodeReq
import tech.tiangong.pop.component.MarketStyleComponent
import tech.tiangong.pop.config.LazadaDefaultProperties
import tech.tiangong.pop.constant.ProductInnerConstant
import tech.tiangong.pop.dao.entity.*
import tech.tiangong.pop.dao.repository.*
import tech.tiangong.pop.dto.TagContextDto
import tech.tiangong.pop.dto.image.ImageCollectionDTO
import tech.tiangong.pop.dto.product.ComboBarcodeInfoDto
import tech.tiangong.pop.enums.ImportDataTypeEnum
import tech.tiangong.pop.enums.ImportSourceEnum
import tech.tiangong.pop.enums.PendingCancelReasonEnum
import tech.tiangong.pop.enums.ProductPendingTaskStatusEnum
import tech.tiangong.pop.external.InspirationClientExternal
import tech.tiangong.pop.helper.ImageCollectionHelper
import tech.tiangong.pop.helper.PageRespHelper
import tech.tiangong.pop.listeners.ImportProductExcelListener
import tech.tiangong.pop.req.product.ProductCreatePublishTaskReq
import tech.tiangong.pop.req.product.ProductPendingPublishLogPageReq
import tech.tiangong.pop.req.product.ProductTitleGenerateReq
import tech.tiangong.pop.req.product.ae.ProductPendingAePageReq
import tech.tiangong.pop.req.product.lazada.*
import tech.tiangong.pop.resp.category.PublishAttributePairResp
import tech.tiangong.pop.resp.product.ProductPendingPageShopResp
import tech.tiangong.pop.resp.product.ProductPendingPublishLogPageResp
import tech.tiangong.pop.resp.product.ProductPendingPublishLogStatisticsResp
import tech.tiangong.pop.resp.product.ProductTitleResp
import tech.tiangong.pop.resp.product.ae.ProductPendingAeStatisticsResp
import tech.tiangong.pop.resp.product.lazada.ProductPendingLazadaDetailDataResp
import tech.tiangong.pop.resp.product.lazada.ProductPendingLazadaDetailResp
import tech.tiangong.pop.resp.product.lazada.ProductPendingLazadaPageResp
import tech.tiangong.pop.service.product.BarCodeService
import tech.tiangong.pop.service.product.ProductTitleGenerateService
import tech.tiangong.pop.service.product.pending.ProductPendingLazadaService
import tech.tiangong.pop.service.product.price.ProductPricingServiceSelector
import java.time.LocalDate
import java.util.*

/**
 * 待上架商品-Lazada服务实现类
 */
@Service
@Slf4j
class ProductPendingLazadaServiceImpl(
    private val productTemplateLazadaSpuRepository: ProductTemplateLazadaSpuRepository,
    private val productTemplateLazadaSkcRepository: ProductTemplateLazadaSkcRepository,
    private val productTemplateLazadaSkuRepository: ProductTemplateLazadaSkuRepository,
    private val shopRepository: ShopRepository,
    private val productRepository: ProductRepository,
    private val productSkcRepository: ProductSkcRepository,
    private val imageRepositoryRepository: ImageRepositoryRepository,
    private val productTagRepository: ProductTagRepository,
    private val marketStyleComponent: MarketStyleComponent,
    private val inspirationClientExternal: InspirationClientExternal,
    private val productAttributesRepository: ProductAttributesRepository,
    private val productPictureRepository: ProductPictureRepository,
    private val productBarCodeRepository: ProductBarCodeRepository,
    private val barCodeService: BarCodeService,
    private val imageCollectionHelper: ImageCollectionHelper,
    private val publishCategoryMappingRepository: PublishCategoryMappingRepository,
    private val importProductRecordRepository: ImportProductRecordRepository,
    private val lazadaDefaultProperties: LazadaDefaultProperties,
    private val productPricingServiceSelector: ProductPricingServiceSelector,
    private val transactionManager: PlatformTransactionManager,
    private val productTitleGenerateService: ProductTitleGenerateService,
) : ProductPendingLazadaService {

    private val platform = PlatformEnum.LAZADA

    /**
     * 创建待上架商品
     */
    override fun createPendingTask(req: ProductCreatePublishTaskReq.PendingAeTaskChangeUserReq?, productList: List<Product>, shop: Shop) {

        // 取出所有站点
        val countryList = LazadaCountryEnum.getCountryList()

        productList.forEach { product ->

            val generatedTitleResp = productTitleGenerateService.previewTitle(ProductTitleGenerateReq().apply {
                this.productId = product.productId
                this.product = product
                this.shopId = product.shopId
            })

            // 内联 title 赋值逻辑
            fun setGeneratedTitle(spu: ProductTemplateLazadaSpu) {
                if (generatedTitleResp.productTitleRuleId.isNotNull()) {
                    spu.productTitle = generatedTitleResp.title
                        .takeIf { it.isNotBlank() && it!!.length < ProductInnerConstant.getTitleMaxLength(LAZADA) }
                        ?: spu.productTitle
                    spu.generatedTitleOverLengthFlag = if (generatedTitleResp.isOverLength) YesOrNoEnum.YES.code else YesOrNoEnum.NO.code
                    spu.generatedTitleMissingFieldsJson = generatedTitleResp.missingFields.toJson()
                }
            }

            // 手动事务
            TransactionTemplate(transactionManager).execute { status ->

                try {
                    // 新增
                    val spuLazada = ProductTemplateLazadaSpu().apply {
                        this.lazadaSpuId = IdHelper.getId()
                        this.productId = product.productId
                        this.spuCode = product.spuCode
                        this.productTitle = product.productTitle
                        this.brandName = product.brandName
                        this.packageWeight = product.packageWeight
                        this.packageDimensionsLength = product.packageDimensionsLength ?: lazadaDefaultProperties.packageDimensionsLength.toString()
                        this.packageDimensionsHeight = product.packageDimensionsHeight ?: lazadaDefaultProperties.packageDimensionsHeight.toString()
                        this.packageDimensionsWidth = product.packageDimensionsWidth ?: lazadaDefaultProperties.packageDimensionsWidth.toString()
                        this.sizeGroupName = product.sizeGroupName
                        this.sizeGroupCode = product.sizeGroupCode
                        // 任务信息
                        this.taskExecutorId = req?.userId
                        this.taskExecutorName = req?.userName
                        this.taskStatus = ProductPendingTaskStatusEnum.PENDING.code
                    }

                    // 生成标题
                    setGeneratedTitle(spuLazada)
                    productTemplateLazadaSpuRepository.save(spuLazada)

                    // 重算价格(里面使用product_skc来计算)
                    val autoCulPriceList = productPricingServiceSelector
                        .getSalePricingHandler(LAZADA)
                        .autoCalPriceByTemplate(spuLazada.lazadaSpuId!!, LazadaConstants.LAZADA_DEFAULT_COUNTRY.map { it.code })
                    val productSkcIdMap = autoCulPriceList.associateBy({ it.skc }, { it })

                    val productSkcList = productSkcRepository.getByProductId(product.productId!!)
                    val newSkcList = mutableListOf<ProductTemplateLazadaSkc>()
                    val newSkuList = mutableListOf<ProductTemplateLazadaSku>()
                    productSkcList.forEach { skc ->
                        val newSkc = ProductTemplateLazadaSkc().apply {
                            this.lazadaSkcId = IdHelper.getId()
                            this.lazadaSpuId = spuLazada.lazadaSpuId
                            this.productSkcId = skc.productSkcId
                            this.skc = skc.skc
                            this.color = skc.color
                            this.platformColor = skc.platformColor
                            this.colorCode = skc.colorCode
                            this.colorAbbrCode = skc.colorAbbrCode
                            this.pictures = skc.pictures
                            this.state = skc.state
                            this.cbPrice = skc.cbPrice
                            this.localPrice = skc.localPrice
                            this.purchasePrice = skc.purchasePrice
                            this.costPrice = skc.costPrice
                        }
                        newSkcList.add(newSkc)

                        val sizeList = listOf<String>() // TODO 需要product_skc获取尺码, 暂无该字段
                        sizeList.forEach { size ->
                            // 获取条码
                            val barCode = productBarCodeRepository.getBySpuCodeAndSkcAndSize(spuLazada.spuCode, skc.skc, size)
                            countryList.forEach { country ->

                                // 新增
                                val skuLazada = ProductTemplateLazadaSku().apply {
                                    this.lazadaSkuId = IdHelper.getId()
                                    this.lazadaSkcId = newSkc.lazadaSkcId
                                    this.lazadaSpuId = spuLazada.lazadaSpuId
                                    barCode?.let { this.barcode = it.barcode }
                                    this.stockQuantity = lazadaDefaultProperties.defaultStock.toLong()
                                    this.sizeName = size
                                    this.country = country
                                    this.enableState = Bool.YES.code

                                    val price = productSkcIdMap[skc.skc]?.countryPriceList?.firstOrNull { it.country == country }
                                    if (price != null) {
                                        this.salePrice = price.salePrice
                                        this.retailPrice = price.retailPrice
                                    }
                                }
                                newSkuList.add(skuLazada)
                            }
                        }
                    }
                    if (newSkcList.isNotEmpty()) {
                        productTemplateLazadaSkcRepository.saveBatch(newSkcList)
                    }
                    if (newSkcList.isNotEmpty()) {
                        productTemplateLazadaSkuRepository.saveBatch(newSkuList)
                    }
                } catch (e: Exception) {
                    log.error(e) { "商品中心-创建Lazada待上架-异常 productId: ${product.productId}, shopId: ${shop.shopId}" }
                    // 手动标记事务回滚
                    status.setRollbackOnly()
                }
            }
        }
    }

    /**
     * 商品列表-分页
     */
    override fun page(req: ProductPendingLazadaPageReq): PageVo<ProductPendingLazadaPageResp> {
        val page = productTemplateLazadaSpuRepository.pendingPage(Page(req.pageNum.toLong(), req.pageSize.toLong()), req)
        val templateSpuList = page.records
        if (CollectionUtils.isEmpty(templateSpuList)) {
            return PageRespHelper.empty()
        }
        // 获取product
        val productIds = templateSpuList.mapNotNull { it.productId }
        if (productIds.isEmpty()) {
            return PageRespHelper.empty()
        }
        val products = productRepository.listByIds(productIds)
        if (productIds.isEmpty()) {
            return PageRespHelper.empty()
        }

        // 获取主图
        val spuMainImageMap = mutableMapOf<String, String?>()
        val spuCodes = products.mapNotNull { it.spuCode }.distinct()
        if (spuCodes.isNotEmpty()) {
            imageRepositoryRepository.listBySpuCodes(spuCodes)
                ?.filter { image -> image.spuCode.isNotBlank() }
                ?.forEach { image ->
                    spuMainImageMap[image.spuCode!!] = image.mainUrl
                }
        }

        val shopMap = shopRepository.listByIds(products.mapNotNull { it.shopId })
            ?.associateBy { it.shopId }

        // 获取标签
        val tagContext = collectTagData(templateSpuList)

        val productMap = products.associateBy { it.productId }
        val productsResp = templateSpuList.mapNotNull { templateSpu ->

            val product = productMap[templateSpu.productId] ?: return@mapNotNull null
            val tagCodes = TagContextDto.getCombinedTags(templateSpu.productId!!, templateSpu.lazadaSpuId!!, tagContext)

            ProductPendingLazadaPageResp().apply {
                this.lazadaSpuId = templateSpu.lazadaSpuId
                this.productId = templateSpu.productId
                this.listingShopId = templateSpu.shopId
                this.listingShopName = shopMap?.get(templateSpu.shopId!!)?.shopName
                this.mainImgUrl = spuMainImageMap[product.spuCode] ?: product.mainImgUrl
                this.supplyMode = product.supplyMode
                this.waves = product.waves
                this.spuCode = product.spuCode
                this.tagCodes = tagCodes
                this.shopList = mutableListOf<ProductPendingPageShopResp>().apply {
                    this.add(ProductPendingPageShopResp().apply {
                        this.shopId = product.shopId
                        this.shopName = product.shopName
                    })
                }.toList()
                this.countryList = product.countrys?.let { c -> listOf<String>(c) }
                this.categoryId = product.categoryId
                this.categoryCode = product.categoryCode
                this.categoryName = product.categoryName
                this.creatorId = product.creatorId
                this.creatorName = product.creatorName
                this.createdTime = product.createdTime
                this.reviserId = product.reviserId
                this.reviserName = product.reviserName
                this.revisedTime = product.revisedTime
                this.styleType = product.styleType
                this.imagePackageState = product.imagePackageState
                this.planAuditState = product.planAuditState
                this.planAuditorId = product.planAuditorId
                this.planAuditorName = product.planAuditorName
                this.planAuditTime = product.planAuditTime
                this.listingUserId = templateSpu.taskExecutorId
                this.listingUserName = templateSpu.taskExecutorName
                this.taskStatus = templateSpu.taskStatus
            }
        }
        return PageRespHelper.of(page.current.toInt(), page.total, productsResp)
    }

    /**
     * 商品列表-统计
     */
    override fun statistics(req: ProductPendingAePageReq): ProductPendingAeStatisticsResp {
        // 状态统计
        val taskStatusStats = productTemplateLazadaSpuRepository.statistics(req)
        return ProductPendingAeStatisticsResp().apply {
            this.pendingCount = taskStatusStats.pendingCount ?: 0
            this.startingCount = taskStatusStats.startingCount ?: 0
            this.failedCount = taskStatusStats.failedCount ?: 0
            this.missingInfoCount = taskStatusStats.missingInfoCount ?: 0
            this.completeInfoCount = taskStatusStats.completeInfoCount ?: 0
        }
    }

    /**
     * 商品详情
     */
    override fun detail(req: ProductPendingLazadaDetailReq): ProductPendingLazadaDetailResp {
        val templateSpu = productTemplateLazadaSpuRepository.getById(req.lazadaSpuId) ?: throw IllegalArgumentException("商品不存在")
        val product = productRepository.getById(templateSpu.productId) ?: throw IllegalArgumentException("商品不存在")

        // 市场(一级节点)
        val marketMap = marketStyleComponent.getMarketNodeMap(1)
        // 系列(二级节点) 市场-系列
        val marketSeriesMap = marketStyleComponent.getMarketNodeMap(2)
        // 风格(三级节点) 市场-系列-风格
        val marketStyleMap = marketStyleComponent.getMarketNodeMap(3)
        // 获取图片
        val image = imageRepositoryRepository.getBySpuCode(product.spuCode!!)

        return ProductPendingLazadaDetailResp().apply {
            this.lazadaSpuId = templateSpu.lazadaSpuId
            this.productId = templateSpu.productId
            this.shopId = product.shopId
            this.shopName = product.shopName
            this.mainImgUrl = image?.mainUrl ?: product.mainImgUrl
            this.spuCode = product.spuCode
            this.supplyMode = product.supplyMode
            this.spotTypeCode = product.spotTypeCode
            this.clothingStyleName = product.clothingStyleName ?: marketStyleMap?.get(product.clothingStyleCode)
            this.planningType = product.planningType
            this.marketCode = product.marketCode
            this.marketName = marketMap?.get(product.marketCode)
            this.marketSeriesCode = product.marketSeriesCode
            this.marketSeriesName = marketSeriesMap?.get(product.marketSeriesCode)
            this.planSourceName = product.planSourceName
            this.goodsRepType = product.goodsRepType
            this.categoryId = product.categoryId
            this.categoryCode = product.categoryCode
            this.categoryName = product.categoryName
            this.inspiraSource = product.inspiraSource
            this.inspiraImgUrl = product.inspiraImgUrl
            this.selectStyleName = product.selectStyleName
            this.selectStyleTime = product.selectStyleTime
            this.buyerRemark = product.buyerRemark
            this.creatorName = product.creatorName
            this.createdTime = product.createdTime
            this.prototypeNum = product.prototypeNum
            this.goodsType = product.goodsType
            this.pricingType = product.pricingType
            this.spotType = product.spotType
            this.waves = product.waves
            this.printType = product.printType
            if (product.inspiraSourceId != null) {
                // 获取灵感详情
                try {
                    val data = inspirationClientExternal.getByInspirationOrPickingId(product.inspiraSourceId!!)
                    if (data?.inspirationInfo != null) {
                        this.inspiraSource = data.inspirationInfo?.inspirationImageSource
                        this.inspiraCountry = data.inspirationInfo?.countrySiteName
                        this.planSourceName = data.inspirationInfo?.planningSourceName
                        this.inspiraImgUrl = data.inspirationInfo?.inspirationImage
                    }
                    if (data?.pickingInfo != null) {
                        this.countryList = data.pickingInfo?.countrySiteName?.let { listOf(it) }
                        this.shopId = data.pickingInfo?.shopId
                        this.shopName = data.pickingInfo?.shopName
                        this.selectStyleTime = data.pickingInfo?.pickingTime
                        this.buyerRemark = data.pickingInfo?.remark
                    }
                } catch (e: Exception) {
                    log.error { "getByInspirationOrPickingId 获取灵感详情报错或者数据为空，Exception=${e.message}," }
                }
            }
            this.styleType = product.styleType
            this.planAuditState = product.planAuditState
            this.imagePackageState = product.imagePackageState
            this.lazadaDetail = ProductPendingLazadaDetailDataResp().apply {

                val platformEnum = LAZADA

                //设置商品属性
                val attributes = productAttributesRepository.listByProductId(product.productId!!, platformEnum.platformId)

                this.lazadaSpuId = templateSpu.lazadaSpuId
                this.productTitle = templateSpu.productTitle
                this.productTitleList = templateSpu.allCountryTitle?.parseJsonList(ProductTitleResp::class.java) ?: LazadaCountryEnum.entries.map {
                    ProductTitleResp().apply {
                        this.country = it.code
                        this.countryName = it.desc
                    }
                }
                this.brandId = templateSpu.brandId
                this.brandName = templateSpu.brandName
                this.sizeGroupName = templateSpu.sizeGroupName
                this.sizeGroupCode = templateSpu.sizeGroupCode
                this.categoryId = product.categoryId
                val platformCategory = getPlatformCategory(product, platformEnum.platformId)
                this.categoryMappingId = platformCategory?.categoryMappingId
                this.platformCategoryName = platformCategory?.platformCategoryName
                this.platformName = platformEnum.platformName
                this.packageDimensionsLength = templateSpu.packageDimensionsLength
                this.packageDimensionsWidth = templateSpu.packageDimensionsWidth
                this.packageDimensionsHeight = templateSpu.packageDimensionsHeight
                this.packageWeight = templateSpu.packageWeight
                this.attributes = attributes.map {
                    PublishAttributePairResp().apply {
                        this.attributeId = it.attributeId
                        this.attributeValueId = it.attributeValueId
                    }
                }
                val imageCollection = getImages(product.spuCode!!)
                this.mainUrlList = imageCollection.limitedMainImages()
                this.detailImageList = imageCollection.detailImages
                this.generatedTitleMissingFieldsJson = templateSpu.getParsedGeneratedTitleMissingFields()
                this.generatedTitleOverLengthFlag = templateSpu.generatedTitleOverLengthFlag

                val skcLazadaList = productTemplateLazadaSkcRepository.listByLazadaSpuId(templateSpu.lazadaSpuId!!)
                    .filter { skcLazada ->
                        skcLazada.state == Bool.YES.code
                    }
                if (CollectionUtils.isNotEmpty(skcLazadaList)) {
                    val lazadaSkcIdList = skcLazadaList.mapNotNull { it.lazadaSkcId }
                    val skuLazadaList = productTemplateLazadaSkuRepository.listByLazadaSkcIdList(lazadaSkcIdList)

                    this.skcList = skcLazadaList.map { skcLazada ->
                        ProductPendingLazadaDetailDataResp.ProductPendingLazadaSkcResp().apply {
                            this.lazadaSkcId = skcLazada.lazadaSkcId
                            this.lazadaSpuId = skcLazada.lazadaSpuId
                            this.skc = skcLazada.skc
                            this.color = skcLazada.color
                            this.colorCode = skcLazada.colorCode
                            this.platformColor = skcLazada.platformColor
                            this.pictures = skcLazada.pictures
                            this.cbPrice = skcLazada.cbPrice
                            this.localPrice = skcLazada.localPrice
                            this.purchasePrice = skcLazada.purchasePrice
                            this.costPrice = skcLazada.costPrice
                            this.combo = skcLazada.combo
                            this.skuList = skuLazadaList
                                .filter { skuLazada -> skuLazada.lazadaSkcId == skcLazada.lazadaSkcId }
                                .groupBy { it.sizeName }
                                .map { (size, skuLazada) ->
                                    ProductPendingLazadaDetailDataResp.ProductPendingLazadaSkuResp().apply {
                                        this.sizeName = size
                                        if (skuLazada.isNotEmpty()) {
                                            this.sellerSku = skuLazada.first().sellerSku
                                            this.barcode = skuLazada.first().barcode
                                            this.barcodes = skuLazada.first().barcodes?.parseJsonList(ComboBarcodeInfoDto::class.java)
                                            this.flagFrontend = skuLazada.first().flagFrontend
                                            this.stockQuantity = skuLazada.first().stockQuantity
                                            this.countryPriceList = skuLazada.map { cp ->
                                                ProductPendingLazadaDetailDataResp.ProductPendingLazadaCountrySkuResp().apply {
                                                    this.lazadaSkuId = cp.lazadaSkuId
                                                    this.lazadaSkcId = cp.lazadaSkcId
                                                    this.lazadaSpuId = cp.lazadaSpuId
                                                    this.country = cp.country
                                                    this.stockQuantity = cp.stockQuantity
                                                    this.sizeName = cp.sizeName
                                                    this.salePrice = cp.salePrice
                                                    this.retailPrice = cp.retailPrice
                                                    this.lastSalePrice = cp.lastSalePrice
                                                    this.lastRetailPrice = cp.lastRetailPrice
                                                    this.purchaseSalePrice = cp.purchaseSalePrice
                                                    this.purchaseRetailPrice = cp.purchaseRetailPrice
                                                    this.regularSalePrice = cp.regularSalePrice
                                                    this.regularRetailPrice = cp.regularRetailPrice
                                                    this.enable = cp.enableState
                                                }
                                            }
                                        }
                                    }
                                }
                        }
                    }
                }
            }
        }
    }

    /**
     * 商品更新
     */
    @Transactional(rollbackFor = [Exception::class])
    override fun update(req: ProductPendingUpdateLazadaReq) {
        // sku(存在: 更新, 不存在: 新增), 新增时id为空, 也要判断尺码条件, 可能已经存在, 但可用状态是0
        val lazadaSpu = productTemplateLazadaSpuRepository.getById(req.lazadaSpuId!!)
        requireNotNull(lazadaSpu) { "Lazada spu不存在 ${req.lazadaSpuId}" }

        val product = productRepository.getById(lazadaSpu.productId!!)

        //更新图库
        savePicture(product)

        //设置属性
        if (CollectionUtils.isNotEmpty(req.attributes)) {
            val platformEnum = LAZADA
            //先删除已有属性
            productAttributesRepository.removeByProductIdAndPlatformId(product.productId!!, platformEnum.platformId)
            productAttributesRepository.saveBatch(
                req.attributes?.map { k ->
                    ProductAttributes().apply {
                        this.attributeId = k.attributeId
                        this.attributeValueId = k.attributeValueId
                        this.productId = product.productId
                        this.categoryId = product.categoryId
                        this.platformId = platformEnum.platformId
                    }
                })
        }

        // 更新SPU (每个字段判断, 有不一致的才更新)
        updateLazadaSpu(lazadaSpu, req)
        // 更新SKC 和 SKU
        updateLazadaSkcAndSku(lazadaSpu.lazadaSpuId!!, req.skcList)
    }

    /**
     * 任务操作-开始任务
     */
    @Transactional(rollbackFor = [Exception::class])
    override fun start(lazadaSpuIds: List<Long>) {
        val templateSpuList = productTemplateLazadaSpuRepository.listByIds(lazadaSpuIds)
            .filter { it.taskStatus == ProductPendingTaskStatusEnum.PENDING.code }
        if (templateSpuList.isEmpty()) {
            throw IllegalArgumentException("商品不存在或已被处理")
        }
        val updateSpuList = templateSpuList.map { templateSpu ->
            ProductTemplateLazadaSpu().apply {
                this.lazadaSpuId = templateSpu.lazadaSpuId
                this.taskStatus = ProductPendingTaskStatusEnum.STARTING.code
            }
        }
        productTemplateLazadaSpuRepository.updateBatchById(updateSpuList)
    }

    /**
     * 任务操作-更换人员
     */
    override fun change(req: ProductPendingLazadaTaskChangeReq) {
        // 获取合法的商品SPU
        val templateSpuList = productTemplateLazadaSpuRepository.listByIds(req.lazadaSpuIds)
            .filter {
                it.taskStatus == ProductPendingTaskStatusEnum.FAILED.code
                        || it.taskStatus == ProductPendingTaskStatusEnum.PENDING.code
                        || it.taskStatus == ProductPendingTaskStatusEnum.STARTING.code
            }
        if (templateSpuList.isEmpty()) {
            throw IllegalArgumentException("商品不存在或已被处理")
        }
        // 多人员则平均分配
        val userSpuMap = mutableMapOf<ProductPendingLazadaTaskChangeReq.PendingLazadaTaskChangeUserReq, MutableList<ProductTemplateLazadaSpu>>()
        if (req.users.size > templateSpuList.size) {
            log.info { "人数 > 商品数" }
            // 前spuList.size个用户各分配一个商品，剩下的用户分配空列表
            for (i in req.users.indices) {
                val userId = req.users[i]
                val spuListForUser = if (i < templateSpuList.size) {
                    // 为前N个用户分配一个商品（N等于商品数量）
                    mutableListOf(templateSpuList[i])
                } else {
                    mutableListOf()
                }
                userSpuMap[userId] = spuListForUser
            }

        } else if (req.users.size < templateSpuList.size) {
            log.info { "人数 < 商品数" }
            // 计算基本分配数量和需要多分配一个商品的用户数
            val baseCount = templateSpuList.size / req.users.size
            val extraCount = templateSpuList.size % req.users.size

            var currentIndex = 0
            for (i in req.users.indices) {
                val userId = req.users[i]
                // 前extraCount个用户多分配一个商品
                val count = if (i < extraCount) baseCount + 1 else baseCount
                // 截取对应数量的商品
                val spuListForUser = templateSpuList.subList(currentIndex, currentIndex + count).toMutableList()
                userSpuMap[userId] = spuListForUser
                currentIndex += count
            }

        } else {
            log.info { "人数 = 商品数" }
            // 一对一分配
            for (i in req.users.indices) {
                userSpuMap[req.users[i]] = mutableListOf(templateSpuList[i])
            }
        }
        if (userSpuMap.isEmpty()) {
            throw BusinessException("平均分配失败")
        }
        userSpuMap.forEach { (user, spuList) ->
            val updateSpuList = spuList.map { templateSpu ->
                ProductTemplateLazadaSpu().apply {
                    this.lazadaSpuId = templateSpu.lazadaSpuId
                    this.taskExecutorId = user.userId
                    this.taskExecutorName = user.userName
                }
            }
            productTemplateLazadaSpuRepository.updateBatchById(updateSpuList)
        }
    }

    /**
     * 任务操作-取消任务
     */
    override fun cancel(req: ProductPendingLazadaTaskCancelReq) {
        if (req.lazadaSpuIds.isEmpty()) {
            throw IllegalArgumentException("请选择商品")
        }
        val cancel = PendingCancelReasonEnum.getByCode(req.cancelReason) ?: throw IllegalArgumentException("取消原因不存在")
        val templateSpuList = productTemplateLazadaSpuRepository.listByIds(req.lazadaSpuIds)
            .filter {
                it.taskStatus == ProductPendingTaskStatusEnum.FAILED.code
                        || it.taskStatus == ProductPendingTaskStatusEnum.PENDING.code
                        || it.taskStatus == ProductPendingTaskStatusEnum.STARTING.code
            }
        if (templateSpuList.isEmpty()) {
            throw IllegalArgumentException("商品不存在或已被处理")
        }
        val updateSpuList = templateSpuList.map { templateSpu ->
            ProductTemplateLazadaSpu().apply {
                this.lazadaSpuId = templateSpu.lazadaSpuId
                this.taskStatus = ProductPendingTaskStatusEnum.CANCELED.code
                this.cancelReason = cancel.desc
            }
        }
        productTemplateLazadaSpuRepository.updateBatchById(updateSpuList)
    }

    /**
     * 上架记录-统计
     */
    override fun publishLogStatistics(): ProductPendingPublishLogStatisticsResp {
        // 状态统计
        val taskStatusStats = productTemplateLazadaSpuRepository.taskStatusStats()
        // 今天创建的数量
        val todayNew = productTemplateLazadaSpuRepository.ktQuery()
            .between(ProductTemplateLazadaSpu::createdTime, LocalDate.now().atStartOfDay(), LocalDate.now())
            .count()
        // 今天发布的数量
        val todayPublished = productTemplateLazadaSpuRepository.ktQuery()
            .eq(ProductTemplateLazadaSpu::taskStatus, ProductPendingTaskStatusEnum.COMPLETED.code)
            .between(ProductTemplateLazadaSpu::taskCompleteTime, LocalDate.now().atStartOfDay(), LocalDate.now())
            .count()
        return ProductPendingPublishLogStatisticsResp().apply {
            this.pendingCount = taskStatusStats.find { it.taskStatus == ProductPendingTaskStatusEnum.PENDING.code }?.taskStatusCount?.toInt() ?: 0
            this.startingCount = taskStatusStats.find { it.taskStatus == ProductPendingTaskStatusEnum.STARTING.code }?.taskStatusCount?.toInt() ?: 0
            this.failedCount = taskStatusStats.find { it.taskStatus == ProductPendingTaskStatusEnum.FAILED.code }?.taskStatusCount?.toInt() ?: 0
            this.todayNewCount = todayNew?.toInt() ?: 0
            this.todayPublishedCount = todayPublished?.toInt() ?: 0
        }
    }

    /**
     * 上架记录-分页列表
     */
    override fun publishLogPage(req: ProductPendingPublishLogPageReq): PageVo<ProductPendingPublishLogPageResp> {
        val page = productTemplateLazadaSpuRepository.page(
            Page(req.pageNum.toLong(), req.pageSize.toLong()),
            KtQueryWrapper(ProductTemplateLazadaSpu::class.java)
                .eq(req.taskState != null, ProductTemplateLazadaSpu::taskStatus, req.taskState)
                .orderByDesc(ProductTemplateLazadaSpu::createdTime)
        )
        if (page.records.isEmpty()) {
            return PageRespHelper.of(page.current.toInt(), page.total, emptyList())
        }
        val shopMap = shopRepository.listByIds(page.records.mapNotNull { it.shopId })
            ?.associateBy { it.shopId }
        val result = page.records.map { templateSpu ->
            ProductPendingPublishLogPageResp().apply {
                this.shopName = shopMap?.get(templateSpu.shopId)?.shopName
                this.spuCode = templateSpu.spuCode
                this.creatorId = templateSpu.creatorId
                this.creatorName = templateSpu.creatorName
                this.createdTime = templateSpu.createdTime
                this.taskStatus = templateSpu.taskStatus
                this.listingUserName = templateSpu.taskExecutorName
                this.publishTime = templateSpu.taskCompleteTime
                this.operationCount = 0
                this.reason = templateSpu.cancelReason
            }
        }
        return PageRespHelper.of(page.current.toInt(), page.total, result)
    }

    /**
     * 导入-待上架-商品数据
     */
    override fun importExcel(file: MultipartFile) {
        val listener = ImportProductExcelListener()
        EasyExcel.read(file.inputStream, listener).doReadAll()

        val importList = listener.getProductList()
            .asSequence()
            .filter { it.supplyMode.isNotBlank() }
            .filter { it.productTitle.isNotBlank() }
            .filter { it.spuCode.isNotBlank() }
            .filter { it.categoryName.isNotBlank() }
            .filter { it.skcCode.isNotBlank() }
            .toList()
        if (CollectionUtils.isEmpty(importList)) {
            throw BaseBizException("导入Lazada数据为空")
        }

        // 检查店铺
        val shopNameList = importList.map { it.listingShopName }.distinct()
        if (shopNameList.isEmpty()) {
            throw BaseBizException("店铺不能为空")
        }
        val existsShopList = shopRepository.ktQuery()
            .`in`(Shop::shopName, shopNameList)
            .eq(Shop::platformId, platform.platformId)
            .list()
            .mapNotNull { it.shopName }
        shopNameList.forEach { shopName ->
            if (!existsShopList.contains(shopName)) {
                throw BaseBizException("店铺: $shopName 不存在")
            }
        }

        val list = importList.map {
            ImportProductRecord().apply {
                this.supplyMode = it.supplyMode
                this.spuCode = it.spuCode
                this.importDataType = ImportDataTypeEnum.ALL.code
                this.importSource = ImportSourceEnum.PENDING_UPLOAD.code
                this.platformId = platform.platformId
                this.importData = it.toJson()
                this.skc = it.skcCode
            }
        }
        importProductRecordRepository.saveBatch(list, 100)
    }

    /**
     * 收集给定产品ID的所有标签相关数据
     */
    private fun collectTagData(templateSpuList: List<ProductTemplateLazadaSpu>): TagContextDto {

        val productIds = templateSpuList.mapNotNull { it.productId }.distinct()

        // 1. 获取产品级别的标签
        val productIdTagMap = productTagRepository.getTagMapByProductIds(productIds)

        // 获取SPU数据
        val spuList: List<ProductTemplateLazadaSpu> = templateSpuList
        val spuIds: List<Long> = spuList.mapNotNull { it.lazadaSpuId }.distinct()
        val productIdToSpuIds: Map<Long, List<Long>> = spuList
            .filter { it.productId.isNotNull() }
            .groupBy { it.productId!! }
            .mapValues { entry ->
                entry.value.mapNotNull { it.lazadaSpuId }
            }

        val spuIdTagMap = if (spuIds.isNotEmpty()) {
            productTagRepository.getTagMapByLazadaSpuIds(spuIds)
        } else {
            emptyMap()
        }

        return TagContextDto(
            productIdTagMap,
            productIdToSpuIds,
            spuIdTagMap,
        )
    }

    /**
     * 获取图片分类集合
     * @param spuCode
     */
    private fun getImages(spuCode: String): ImageCollectionDTO {
        val imageRepository = imageRepositoryRepository.getBySpuCode(spuCode)
        return if (imageRepository != null) {
            imageCollectionHelper.buildImageCollection(spuCode, imageRepository)
        } else {
            ImageCollectionDTO()
        }
    }

    /**
     * 获取映射的平台品类
     *
     * @param product
     * @param platformId
     * @return
     */
    private fun getPlatformCategory(product: Product, platformId: Long): PublishCategoryMapping? {
        if (product.categoryId != null) {
            val categoryMapping = publishCategoryMappingRepository.getByPublishCategoryIdAndPlatformId(product.categoryId!!, platformId)
            if (categoryMapping != null) {
                return categoryMapping
            }
        }
        return null
    }

    private fun savePicture(product: Product) {
        val imageRepository = imageRepositoryRepository.getBySpuCode(product.spuCode!!)
        if (Objects.isNull(imageRepository)) {
            return
        }
        val picture = productPictureRepository.getOneByProductId(product.productId!!)
        if (Objects.isNull(picture)) {
            val productPicture = ProductPicture().apply {
                this.productId = product.productId
                this.spuCode = product.spuCode
                this.sourceImageUrl = imageRepository!!.mainUrl
            }
            productPictureRepository.save(productPicture)
        }
    }

    /**
     * 更新Lazada spu
     * @param lazadaSpu
     * @param req
     */
    private fun updateLazadaSpu(lazadaSpu: ProductTemplateLazadaSpu, req: ProductPendingUpdateLazadaReq) {
        var update = false
        if (req.productTitle?.isNotBlank() == true && ObjectUtils.notEqual(req.productTitle, lazadaSpu.productTitle)) {
            lazadaSpu.productTitle = req.productTitle
            update = true
        }
        val allTitle = req.productTitleList
        if (allTitle != null && allTitle.isNotEmpty() && ObjectUtils.notEqual(allTitle.toJson(), lazadaSpu.allCountryTitle)) {
            lazadaSpu.allCountryTitle = allTitle.toJson()
            update = true
        }
        if (req.packageDimensionsLength?.isNotBlank() == true && ObjectUtils.notEqual(req.packageDimensionsLength, lazadaSpu.packageDimensionsLength)) {
            lazadaSpu.packageDimensionsLength = req.packageDimensionsLength
            update = true
        }
        if (req.packageDimensionsWidth?.isNotBlank() == true && ObjectUtils.notEqual(req.packageDimensionsWidth, lazadaSpu.packageDimensionsWidth)) {
            lazadaSpu.packageDimensionsWidth = req.packageDimensionsWidth
            update = true
        }
        if (req.packageDimensionsHeight?.isNotBlank() == true && ObjectUtils.notEqual(req.packageDimensionsHeight, lazadaSpu.packageDimensionsHeight)) {
            lazadaSpu.packageDimensionsHeight = req.packageDimensionsHeight
            update = true
        }
        if (req.packageWeight?.isNotBlank() == true && ObjectUtils.notEqual(req.packageWeight, lazadaSpu.packageWeight)) {
            lazadaSpu.packageWeight = req.packageWeight
            update = true
        }
        if (req.generatedTitleMissingFieldsJson.isNotEmpty() && ObjectUtils.notEqual(req.generatedTitleMissingFieldsJson, lazadaSpu.getParsedGeneratedTitleMissingFields())) {
            lazadaSpu.generatedTitleMissingFieldsJson = req.generatedTitleMissingFieldsJson!!.toJson()
            update = true
        }
        if (req.generatedTitleOverLengthFlag != null && ObjectUtils.notEqual(req.generatedTitleOverLengthFlag, lazadaSpu.generatedTitleOverLengthFlag)) {
            lazadaSpu.generatedTitleOverLengthFlag = req.generatedTitleOverLengthFlag
            update = true
        }
        if (req.generatedTitlesJson != null && ObjectUtils.notEqual(req.generatedTitlesJson, lazadaSpu.getParsedGeneratedTitles())) {
            lazadaSpu.setParsedGeneratedTitles(req.generatedTitlesJson)
            update = true
        }
        if (update) {
            productTemplateLazadaSpuRepository.updateById(lazadaSpu)
        }
    }

    /**
     * 更新Lazada skc和sku
     * @param lazadaSpuId
     * @param req
     */
    private fun updateLazadaSkcAndSku(lazadaSpuId: Long, req: List<ProductPendingUpdateSkcReq>?) {
        if (req.isNullOrEmpty()) {
            return
        }
        val spuLazada = productTemplateLazadaSpuRepository.getById(lazadaSpuId)
        req.forEach { skcReq ->
            // 处理SKC
            var lazadaSkc: ProductTemplateLazadaSkc? = null
            if (skcReq.lazadaSkcId == null) {
                // 是否组合商品
                if (skcReq.combo == Bool.YES.code) {

                    if (skcReq.colorCode.isNullOrBlank()) {
                        throw IllegalArgumentException("组合商品必须有颜色")
                    }
                    // 组合商品, 创建SKC
                    lazadaSkc = ProductTemplateLazadaSkc().apply {
                        this.lazadaSkcId = IdHelper.getId()
                        this.lazadaSpuId = lazadaSpuId
                        this.color = getComboColorByLazada(skcReq)
                        this.colorCode = skcReq.colorCode?.trim()
                        this.platformColor = skcReq.platformColor
                        this.combo = Bool.YES.code
                        this.state = Bool.YES.code
                    }
                    productTemplateLazadaSkcRepository.save(lazadaSkc)
                }
            } else {
                lazadaSkc = productTemplateLazadaSkcRepository.getByLazadaSpuIdAndLazadaSkcId(lazadaSpuId, skcReq.lazadaSkcId!!)
            }

            requireNotNull(lazadaSkc) { "Lazada skc不存在 ${skcReq.lazadaSkcId}" }

            if (skcReq.combo == Bool.YES.code && lazadaSkc.combo == Bool.YES.code) {
                // 组合颜色可以改colorCode
                lazadaSkc.color = getComboColorByLazada(skcReq)
                lazadaSkc.colorCode = skcReq.colorCode
            }
            lazadaSkc.platformColor = skcReq.platformColor
            productTemplateLazadaSkcRepository.updateById(lazadaSkc)

            // 处理SKU
            val saveSkuList = mutableListOf<ProductTemplateLazadaSku>()
            val updateSkuList = mutableListOf<ProductTemplateLazadaSku>()
            skcReq.skuList?.forEach { skuReq ->
                val lazadaSku: ProductTemplateLazadaSku?
                if (skuReq.lazadaSkuId == null) {
                    // skuId为空, 可能新增/状态更新, 先用尺码和国家判断是否存在
                    lazadaSku = productTemplateLazadaSkuRepository.getByLazadaSpuIdAndLazadaSkcIdAndSizeNameAndCountry(lazadaSpuId, lazadaSkc.lazadaSkcId!!, skuReq.sizeName!!, skuReq.country!!)
                    if (lazadaSku == null) {
                        // 不存在, 新增

                        // 判断尺码是否有条码
                        val skcLazada = productTemplateLazadaSkcRepository.getByLazadaSpuIdAndLazadaSkcId(lazadaSpuId, lazadaSkc.lazadaSkcId!!)
                        val barCode = productBarCodeRepository.getBySpuCodeAndSkcAndSize(spuLazada.spuCode, skcLazada!!.skc, skuReq.sizeName!!)
                        var newBarcode: String? = null
                        if (barCode == null && skcReq.combo != Bool.YES.code) {
                            // 添加条码
                            val product = productRepository.getById(spuLazada.productId)
                            val barcodeReq = BatchCreateBarCodeReq().apply {
                                this.categoryCode = product.categoryCode
                                this.categoryName = product.categoryName
                                this.skcCode = skcLazada.skc
                                this.color = skcLazada.color
                                this.spuCode = product.spuCode
                                this.groupName = product.sizeGroupName
                                this.sourceGroupCode = product.sizeGroupCode
                                this.sizeValues = listOf(skuReq.sizeName!!)
                                this.inspiraImgUrl = product.inspiraImgUrl
                                this.supplyMode = product.supplyMode
                                this.localPrice = skcLazada.localPrice
                                this.designImgUrl = skcLazada.pictures
                                this.mainImgUrl = skcLazada.pictures
                            }
                            val barcodeResp = barCodeService.createBarcodeByForce(listOf(barcodeReq))
                            if (CollectionUtils.isNotEmpty(barcodeResp)) {
                                newBarcode = barcodeResp[0].barcode
                            }
                        } else {
                            newBarcode = barCode?.barcode
                        }
                        saveSkuList.add(ProductTemplateLazadaSku().apply {
                            this.lazadaSkuId = IdHelper.getId()
                            this.lazadaSkcId = lazadaSkc.lazadaSkcId
                            this.lazadaSpuId = spuLazada.lazadaSpuId
                            this.barcode = newBarcode
                            this.country = skuReq.country
                            this.flagFrontend = skuReq.flagFrontend
                            this.stockQuantity = skuReq.stockQuantity
                            this.sizeName = skuReq.sizeName
                            this.salePrice = skuReq.salePrice
                            this.retailPrice = skuReq.retailPrice
                            this.lastSalePrice = skuReq.lastSalePrice
                            this.lastRetailPrice = skuReq.lastRetailPrice
                            this.purchaseSalePrice = skuReq.purchaseSalePrice
                            this.purchaseRetailPrice = skuReq.purchaseRetailPrice
                            this.regularSalePrice = skuReq.regularSalePrice
                            this.regularRetailPrice = skuReq.regularRetailPrice
                            this.enableState = Bool.YES.code
                            if (skcReq.combo == Bool.YES.code) {
                                if (skuReq.barcodeList.isNullOrEmpty()) {
                                    throw IllegalArgumentException("组合商品 ${skuReq.sizeName} 必须有条码")
                                }
                                this.barcodes = skuReq.barcodeList?.toJson()
                            }
                        })
                    } // 存在, 状态更新(更新逻辑)
                } else {
                    // skuId不为空, 正常更新(更新逻辑)
                    lazadaSku = productTemplateLazadaSkuRepository.getByLazadaSpuIdAndLazadaSkcIdAndLazadaSkuId(lazadaSpuId, lazadaSkc.lazadaSkcId!!, skuReq.lazadaSkuId!!)
                }

                if (lazadaSku != null) {
                    lazadaSku.flagFrontend = skuReq.flagFrontend
                    lazadaSku.stockQuantity = skuReq.stockQuantity
                    lazadaSku.lastSalePrice = lazadaSku.salePrice
                    lazadaSku.lastRetailPrice = lazadaSku.retailPrice
                    lazadaSku.salePrice = skuReq.salePrice
                    lazadaSku.retailPrice = skuReq.retailPrice
                    lazadaSku.lastSalePrice = skuReq.lastSalePrice
                    lazadaSku.lastRetailPrice = skuReq.lastRetailPrice
                    lazadaSku.purchaseSalePrice = skuReq.purchaseSalePrice
                    lazadaSku.purchaseRetailPrice = skuReq.purchaseRetailPrice
                    lazadaSku.regularSalePrice = skuReq.regularSalePrice
                    lazadaSku.regularRetailPrice = skuReq.regularRetailPrice
                    lazadaSku.enableState = Bool.YES.code
                    if (skcReq.combo == Bool.YES.code) {
                        lazadaSku.barcodes = skuReq.barcodeList?.toJson()
                    }
                    updateSkuList.add(lazadaSku)
                }
            }

            if (saveSkuList.isNotEmpty()) {
                productTemplateLazadaSkuRepository.saveBatch(saveSkuList)
            }
            if (updateSkuList.isNotEmpty()) {
                productTemplateLazadaSkuRepository.updateBatchById(updateSkuList)
            }

            // 不在req的sku, 标记为enable=0
            val existSkuList = productTemplateLazadaSkuRepository.list(
                KtQueryWrapper(ProductTemplateLazadaSku::class.java)
                    .eq(ProductTemplateLazadaSku::lazadaSpuId, lazadaSpuId)
                    .eq(ProductTemplateLazadaSku::lazadaSkcId, lazadaSkc.lazadaSkcId)
                    .eq(ProductTemplateLazadaSku::enableState, Bool.YES.code)
            )
            val saveLazadaSkuId = saveSkuList.map { it.lazadaSkuId }
            val noEnableSkuList = mutableListOf<ProductTemplateLazadaSku>()
            existSkuList
                .filter { existSku -> !saveLazadaSkuId.contains(existSku.lazadaSkuId) }
                .forEach { existSku ->
                    // 是否存在于req
                    var exist = false
                    skcReq.skuList
                        ?.filter { skuReq -> skuReq.lazadaSkuId != null }
                        ?.forEach innerForEach@{ skuReq ->
                            if (Objects.equals(skuReq.lazadaSkcId, existSku.lazadaSkcId)
                                && Objects.equals(skuReq.country, existSku.country)
                                && Objects.equals(skuReq.sizeName, existSku.sizeName)
                                && existSku.enableState == Bool.YES.code
                            ) {
                                exist = true
                                return@innerForEach
                            }
                        }
                    if (!exist) {
                        // 不存在的标记enable=0
                        existSku.enableState = Bool.NO.code
                        noEnableSkuList.add(existSku)
                    }
                }
            if (noEnableSkuList.isNotEmpty()) {
                productTemplateLazadaSkuRepository.updateBatchById(noEnableSkuList)
            }
        }
    }

    /**
     * 获取组合颜色
     */
    private fun getComboColorByLazada(skcReq: ProductPendingUpdateSkcReq): String {
        // 提取sku下的所有barcode
        val barcodeList = skcReq.skuList?.mapNotNull { it.barcodeList }?.flatten()?.mapNotNull { it.barcode }?.distinct()
        require(!(barcodeList.isNullOrEmpty())) { "组合商品必须有条码" }
        val barcodeInfoList = productBarCodeRepository.getListByBarcodes(barcodeList)
        val skcCodeList = barcodeInfoList.mapNotNull { it.skc }.distinct()
        val productSkcList = productSkcRepository.listBySkc(skcCodeList)
        return productSkcList.mapNotNull { it.color?.trim() }.distinct().sorted().joinToString("+")
    }

}