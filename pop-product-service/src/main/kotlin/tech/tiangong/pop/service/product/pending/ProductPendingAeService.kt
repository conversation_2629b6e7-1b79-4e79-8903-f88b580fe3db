package tech.tiangong.pop.service.product.pending

import org.springframework.web.multipart.MultipartFile
import team.aikero.blade.core.protocol.PageVo
import tech.tiangong.pop.dao.entity.Product
import tech.tiangong.pop.dao.entity.Shop
import tech.tiangong.pop.req.product.ProductCreatePublishTaskReq
import tech.tiangong.pop.req.product.ProductPendingPublishLogPageReq
import tech.tiangong.pop.req.product.ae.ProductPendingAeDetailReq
import tech.tiangong.pop.req.product.ae.ProductPendingAePageReq
import tech.tiangong.pop.req.product.ae.ProductPendingAeTaskCancelReq
import tech.tiangong.pop.req.product.ae.ProductPendingAeTaskChangeReq
import tech.tiangong.pop.req.product.ae.ProductPendingAeUpdateReq
import tech.tiangong.pop.resp.product.ProductPendingPublishLogPageResp
import tech.tiangong.pop.resp.product.ProductPendingPublishLogStatisticsResp
import tech.tiangong.pop.resp.product.ae.ProductPendingAeDetailResp
import tech.tiangong.pop.resp.product.ae.ProductPendingAePageResp
import tech.tiangong.pop.resp.product.ae.ProductPendingAeStatisticsResp

/**
 * 待上架商品-AE服务
 */
interface ProductPendingAeService {

    /**
     * 创建待上架商品
     */
    fun createPendingTask(req: ProductCreatePublishTaskReq.PendingAeTaskChangeUserReq?, productList: List<Product>, shop: Shop)

    /**
     * 商品列表-分页
     */
    fun page(req: ProductPendingAePageReq): PageVo<ProductPendingAePageResp>

    /**
     * 商品列表-统计
     */
    fun statistics(req: ProductPendingAePageReq): ProductPendingAeStatisticsResp

    /**
     * 商品详情
     */
    fun detail(req: ProductPendingAeDetailReq): ProductPendingAeDetailResp

    /**
     * 商品更新
     */
    fun update(req: ProductPendingAeUpdateReq)

    /**
     * 任务操作-开始任务
     */
    fun start(aeSpuIds: List<Long>)

    /**
     * 任务操作-更换人员
     */
    fun change(req: ProductPendingAeTaskChangeReq)

    /**
     * 任务操作-取消任务
     */
    fun cancel(req: ProductPendingAeTaskCancelReq)

    /**
     * 上架记录-统计
     */
    fun publishLogStatistics(): ProductPendingPublishLogStatisticsResp

    /**
     * 上架记录-分页列表
     */
    fun publishLogPage(req: ProductPendingPublishLogPageReq): PageVo<ProductPendingPublishLogPageResp>

    /**
     * 导入-待上架-商品数据
     */
    fun importExcel(file: MultipartFile)
}
