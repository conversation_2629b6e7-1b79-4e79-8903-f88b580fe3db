package tech.tiangong.pop.service.category.impl

import com.lazada.lazop.util.ApiException
import jakarta.servlet.http.HttpServletResponse
import org.apache.commons.lang3.StringUtils
import org.springframework.beans.BeanUtils
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import org.springframework.util.Assert
import org.springframework.util.CollectionUtils
import org.springframework.web.multipart.MultipartFile
import team.aikero.blade.core.enums.Bool
import team.aikero.blade.core.exception.BusinessException
import team.aikero.blade.core.toolkit.isNotBlank
import team.aikero.blade.core.toolkit.isNotEmpty
import team.aikero.blade.core.toolkit.isNull
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.sequence.id.IdHelper
import team.aikero.blade.util.json.parseJsonList
import team.aikero.blade.util.json.toJson
import tech.tiangong.pop.common.enums.CountryEnum
import tech.tiangong.pop.common.enums.LazadaCountryEnum
import tech.tiangong.pop.common.enums.PlatformEnum
import tech.tiangong.pop.common.enums.PlatformEnum.AE
import tech.tiangong.pop.common.enums.PlatformEnum.LAZADA
import tech.tiangong.pop.constant.RedisConstants.CATEGORY_ATTR_COPY_LOCK
import tech.tiangong.pop.constant.RedisConstants.CATEGORY_ATTR_MAPPING_UPDATE_LOCK
import tech.tiangong.pop.core.lock.LockComponent
import tech.tiangong.pop.dao.BaseEntity
import tech.tiangong.pop.dao.entity.*
import tech.tiangong.pop.dao.repository.*
import tech.tiangong.pop.enums.CategoryAssociatePlatformStateEnum
import tech.tiangong.pop.helper.cache.AliexpressServiceHelper
import tech.tiangong.pop.req.category.*
import tech.tiangong.pop.resp.category.*
import tech.tiangong.pop.service.TemuService
import tech.tiangong.pop.service.category.PublishCategoryAttributeService
import tech.tiangong.pop.service.lazada.LazadaApiService
import tech.tiangong.pop.utils.CategoryUtils
import tech.tiangong.pop.resp.category.PlatformAttributeResp
import tech.tiangong.pop.service.category.PublishCategoryService

@Slf4j
@Service
class PublishCategoryAttributeServiceImpl(
    private val publishCategoryAttrRepository: PublishCategoryAttrRepository,
    private val publishAttributeValueRepository: PublishAttributeValueRepository,
    private val publishAttributeRepository:PublishAttributeRepository,
    private val publishAttributeGroupRepository:PublishAttributeGroupRepository,
    private val publishCategoryMappingRepository: PublishCategoryMappingRepository,
    private val publishPlatformAttrValueRepository: PublishPlatformAttrValueRepository,
    private val publishPlatformAttrRepository: PublishPlatformAttrRepository,
    private val lazadaApiService: LazadaApiService,
    private val aliexpressServiceHelper: AliexpressServiceHelper,
    private val lockComponent: LockComponent,
    private val publishCategoryRepository: PublishCategoryRepository,
    private val temuService: TemuService,
    private val publishCategoryService:PublishCategoryService
) : PublishCategoryAttributeService {

    override fun removePlatformAttr(categoryMappingId: Long) {
        val categoryMapping = publishCategoryMappingRepository.getById(categoryMappingId)
            ?: throw BusinessException("找不到平台和品类映射记录")

        val platformAttrList = publishPlatformAttrRepository.ktQuery()
            .eq(PublishPlatformAttr::categoryMappingId, categoryMappingId)
            .list()

        // 删除关联的属性
        publishPlatformAttrRepository.ktUpdate()
            .eq(PublishPlatformAttr::categoryMappingId, categoryMapping.categoryMappingId)
            .remove()

        // 删除关联的属性值
        if (!platformAttrList.isNullOrEmpty()) {
            val attrIds = platformAttrList.map { it.platformAttrId }
            publishPlatformAttrValueRepository.ktUpdate()
                .`in`(PublishPlatformAttrValue::publishPlatformAttrId, attrIds)
                .remove()
        }
    }

    @Transactional(rollbackFor = [Exception::class])
    override fun refCategoryAttribute(req: CategoryAttributeEditReq) {
        // 检查属性值是否重复
        for (attributeMapper in req.attributeMapperList!!) {
            if (CategoryAttributeEditReq.hasDuplicatePlatformAttributeValue(attributeMapper.attributeValueList!!)) {
                // throw new BaseBizException("操作错误，属性值重复！");
            }
        }

        val categoryMappingId = req.attributeMapperList!!.first().categoryMappingId!!
        val lockKey = "$CATEGORY_ATTR_MAPPING_UPDATE_LOCK$categoryMappingId"

        // 使用 lockComponent 替代直接使用 redissonClient
        lockComponent.doInLock(
            lockKey,
            BusinessException("操作错误，有其他操作正在进行中！")
        ) {
            processCategoryAttributeUpdate(req, categoryMappingId)
        }
    }

    @Throws(ApiException::class)
    override fun findByCategoryAttrIds(req: CategoryAttrListReq): CategoryAttributeRefResp {
        val categoryMapping = publishCategoryMappingRepository.getById(req.categoryMappingId)
            ?: throw BusinessException("找不到平台和品类映射记录")

        val platform = PlatformEnum.getByPlatformId(categoryMapping.platformId!!)
            ?: throw BusinessException("找不到平台信息")
        //查询已关联属性
        var attributeMapperList = publishCategoryAttrRepository.findByCategoryAttrIds(categoryMapping.categoryMappingId!!, req.categoryId!!)

        if (req.allPlatform == null || req.allPlatform != Bool.YES.code) {
            // 不展示所有平台, 只展示当前平台
            if (categoryMapping.platformId != null) {
                attributeMapperList = attributeMapperList.filter { it.platformId == categoryMapping.platformId }
            }
        }

        if (attributeMapperList.isEmpty()) {
            throw BusinessException("该品类未关联属性！")
        }
        //补充平台ID值
        attributeMapperList.forEach { it.platformId = platform.platformId }
        //查询本地属性的属性值
        val attributeValues = publishAttributeValueRepository.listByAttrIds(attributeMapperList.map { it.attributeId }.toSet())
            .filter { it.state == Bool.YES.code }

        if (attributeValues.isNotEmpty()) {
            for (attributeMapperResp in attributeMapperList) {
                attributeMapperResp.categoryId = req.categoryId
                attributeMapperResp.categoryMappingId = req.categoryMappingId

                val valueList = attributeValues.filter { it.attributeId == attributeMapperResp.attributeId }
                val attributeValueList = mutableListOf<AttributeValueMapperResp>()

                for (publishAttributeValue in valueList) {
                    attributeValueList.add(AttributeValueMapperResp().apply {
                        attributeId = attributeMapperResp.attributeId
                        attributeValueId = publishAttributeValue.attributeValueId
                        attributeValue = publishAttributeValue.attributeValue
                    })
                }

                attributeMapperResp.attributeValueList = attributeValueList
            }
        }

        // 查看关联平台的属性值
        val platformAttrIdSet = attributeMapperList
            .mapNotNull { it.platformAttrId }
            .toSet()

        if (platformAttrIdSet.isNotEmpty()) {
            // 获取属性值并将其映射到对应的属性中
            val platformAttrValueList = publishPlatformAttrValueRepository.ktQuery()
                .`in`(PublishPlatformAttrValue::publishPlatformAttrId, platformAttrIdSet)
                .list()

            val attributeValueMap = platformAttrValueList
                .map { platformAttrValue ->
                    AttributeValueMapperResp().apply {
                        this.attributeValueId = platformAttrValue.attributeValueId
                        this.attributeId = platformAttrValue.attributeId
                        this.attributeValue = platformAttrValue.attributeValue
                        this.platformAttributeCode = platformAttrValue.platformAttributeCode
                        this.platformAttrValueId = platformAttrValue.platformAttrValueId
                        this.platformAttributeValue = platformAttrValue.platformAttributeValue
                    }
                }
                .groupBy { it.attributeId }
            //将已选中的平台属性值映射到对应的本地属性值中
            for (attributeMapper in attributeMapperList) {
                for (attributeValueMapperResp in attributeMapper.attributeValueList ?: emptyList()) {
                    attributeValueMap[attributeMapper.attributeId]?.let { mappedValues ->
                        mappedValues.find { it.attributeValueId == attributeValueMapperResp.attributeValueId }?.let { match ->
                            attributeValueMapperResp.platformAttributeValue = match.platformAttributeValue
                            attributeValueMapperResp.platformAttrValueId = match.platformAttrValueId
                            attributeValueMapperResp.platformAttributeCode = match.platformAttributeCode
                        }
                    }
                }
            }
        }

        val platformAttributeList = listPlatformCategoryAttribute(platform,categoryMapping.platformCategoryId!!,categoryMapping.country!!)

        return CategoryAttributeRefResp().apply {
            this.attributeMapperList = attributeMapperList
            this.platformAttributeList = platformAttributeList
        }
    }

    override fun getPlatformAttributeMappingDetailByCategoryMappingId(categoryMappingId:Long):PlatformCategoryAttributeMappingDetailResp{
        val categoryMapping = publishCategoryMappingRepository.getById(categoryMappingId)
            ?: throw BusinessException("找不到平台和品类映射记录")

        val platform = PlatformEnum.getByPlatformId(categoryMapping.platformId!!)
            ?: throw BusinessException("找不到平台信息")
        return PlatformCategoryAttributeMappingDetailResp().apply {
            //已关联的属性值
            this.attributeMapperList = publishPlatformAttrRepository.listPlatformAttributeMappingByCategoryMappingId(categoryMapping.categoryMappingId!!)
            //品类属性
            this.categoryAttributeList = getCategoryAttributeWithValuesByCategoryId(categoryMapping.publishCategoryId!!)
            //平台属性
            this.platformAttributeList = listPlatformCategoryAttribute(platform,categoryMapping.platformCategoryId!!,categoryMapping.country!!)
        }
    }

    @Transactional(rollbackFor = [Exception::class])
    override fun updatePlatformAttributeRequestFlag(categoryMappingId: Long) {
        val categoryMapping = publishCategoryMappingRepository.getById(categoryMappingId)
        //找出第三方平台属性
        val platformAttributeList = listPlatformCategoryAttribute(PlatformEnum.getByPlatformId(categoryMapping.platformId!!)!!,categoryMapping.platformCategoryId!!,categoryMapping.country!!)
        val attributeNameToRequestMap = platformAttributeList.associateBy({it.name}, {it.requestFlag})
        //找出品类已关联的第三方平台属性
        val list = publishPlatformAttrRepository.listPlatformAttributeMappingByCategoryMappingId(categoryMappingId)
        //判断第三方平台是否要求必填
        val updatePlatformAttrList = list.map {
            PublishPlatformAttr().apply {
                this.platformAttrId = it.platformAttrId
                this.requestFlag = attributeNameToRequestMap[it.platformAttributeKeyName]?:Bool.NO.code
            }
        }
        publishPlatformAttrRepository.updateBatchById(updatePlatformAttrList)
    }

    @Transactional(rollbackFor = [Exception::class])
    override fun saveCategoryAttributeValueMapping(req: SaveCategoryAttributeValueMappingReq) {
        val categoryMappingId = req.categoryMappingId
        val lockKey = "$CATEGORY_ATTR_MAPPING_UPDATE_LOCK$categoryMappingId"
        // 使用 lockComponent 替代直接使用 redissonClient
        lockComponent.doInLock(
            lockKey,
            BusinessException("操作错误，有其他操作正在进行中！")
        ) {
            handleCategoryAttributeValueMapping(req)
        }
    }

    override fun copyCategoryAttributeValueMapping(req: CopyCategoryAttributeValueMappingReq){
        if(req.sourceCategoryMappingId==null || req.targetCategoryMappingId==null){
            throw BusinessException("参数不能为空")
        }
        //判断源品类和目标品类是否都是映射到同一个平台同一个站点
        val sourceCategoryMapping = publishCategoryMappingRepository.getById(req.sourceCategoryMappingId!!)
        val targetCategoryMapping = publishCategoryMappingRepository.getById(req.targetCategoryMappingId!!)
        Assert.isTrue(sourceCategoryMapping!=null && targetCategoryMapping!=null,"需要复制的品类映射不存在")
        Assert.isTrue(sourceCategoryMapping.platformId==targetCategoryMapping.platformId
                && sourceCategoryMapping.country==targetCategoryMapping.country,"源品类和目标品类不是关联同一个平台同一个站点")
        //找出源品类的第三方属性映射
        val sourceList = publishPlatformAttrRepository.listPlatformAttributeMappingByCategoryMappingId(sourceCategoryMapping.categoryMappingId!!)
        Assert.isTrue(sourceList.isNotEmpty(),"源品类未配置第三方平台属性映射")
        //找出源品类的第三方属性值映射
        val sourceAttributeValueMappingList = publishPlatformAttrValueRepository.listByPublicPlatformAttrIds(sourceList.mapNotNull { it.platformAttrId })
        val sourcePlatformAttrIdToValueMapping = sourceAttributeValueMappingList.groupBy { it.publishPlatformAttrId }

        //同步源品类的属性到目标品类
        val sourceCategoryAttributeList = publishCategoryAttrRepository.listByCategoryId(sourceCategoryMapping.publishCategoryId)
        val targetCategoryAttributeList = publishCategoryAttrRepository.listByCategoryId(targetCategoryMapping.publishCategoryId)
        val saveCategoryAttributeList: MutableList<PublishCategoryAttr> = mutableListOf()
        //如果目标品类还没关联本地属性，则同步源品类的属性给它
        if(targetCategoryAttributeList.isEmpty()){
            saveCategoryAttributeList.addAll(sourceCategoryAttributeList.map {
                PublishCategoryAttr().apply {
                    this.categoryAttrId = IdHelper.getId()
                    this.categoryId = targetCategoryMapping.publishCategoryId
                    this.attributeId = it.attributeId
                    this.requestFlag = it.requestFlag
                }
            })
        }else{
            val targetAttributeIds = targetCategoryAttributeList.map { it.attributeId }.toSet()
            sourceCategoryAttributeList.forEach {
                if(!targetAttributeIds.contains(it.attributeId)){
                    saveCategoryAttributeList.add(PublishCategoryAttr().apply {
                            this.categoryAttrId = IdHelper.getId()
                            this.categoryId = targetCategoryMapping.publishCategoryId
                            this.attributeId = it.attributeId
                            this.requestFlag = it.requestFlag
                    })
                }
            }
        }
        //目标品类的第三方属性映射
        val targetList = publishPlatformAttrRepository.listPlatformAttributeMappingByCategoryMappingId(targetCategoryMapping.categoryMappingId!!)
        //目标品类的第三方属性值映射
        val targetAttributeValueMappingList = publishPlatformAttrValueRepository.listByPublicPlatformAttrIds(targetList.mapNotNull { it.platformAttrId })
        val targetPlatformAttrIdToValueMapping = targetAttributeValueMappingList.groupBy { it.publishPlatformAttrId }
        //目标品类的第三方属性映射（本地属性ID-平台属性ID）
        val targetExistMapping = targetList.associateBy { "${it.attributeId}-${it.platformAttributeKeyName}" }

        var savePublishPlatformAttrList: MutableList<PublishPlatformAttr> = mutableListOf<PublishPlatformAttr>()
        var savePublishPlatformAttrValueList: MutableList<PublishPlatformAttrValue> = mutableListOf<PublishPlatformAttrValue>()
        var deletePublishPlatformAttrValueIdList: MutableList<Long> = mutableListOf<Long>()
        sourceList.forEach {sourceAttributeMappingResp->
            //判断属性是否已映射，没有则创建
            var key = "${sourceAttributeMappingResp.attributeId}-${sourceAttributeMappingResp.platformAttributeKeyName}"
            if(targetList.isNotEmpty() || !targetExistMapping.contains(key)){
                val targetAttrMapping = PublishPlatformAttr().apply {
                    this.platformAttrId = IdHelper.getId()
                    this.categoryId = targetCategoryMapping.publishCategoryId
                    this.platformId = targetCategoryMapping.platformId
                    this.categoryMappingId = targetCategoryMapping.categoryMappingId
                    this.platformAttributeLabelName = sourceAttributeMappingResp.platformAttributeLabelName
                    this.platformAttributeKeyName = sourceAttributeMappingResp.platformAttributeKeyName
                    this.attributeId = sourceAttributeMappingResp.attributeId
                    this.country = targetCategoryMapping.country
                    this.requestFlag = sourceAttributeMappingResp.requestFlag
                }
                savePublishPlatformAttrList.add(targetAttrMapping)
                val sourceValueMappingList = sourcePlatformAttrIdToValueMapping[sourceAttributeMappingResp.platformAttrId]
                sourceValueMappingList?.mapNotNull {sourceValueMapping->
                    savePublishPlatformAttrValueList.add(PublishPlatformAttrValue().apply {
                        this.platformAttrValueId = IdHelper.getId()
                        this.publishPlatformAttrId = targetAttrMapping.platformAttrId
                        this.platformAttributeValue = sourceValueMapping.platformAttributeValue
                        this.platformAttributeCode = sourceValueMapping.platformAttributeCode
                        this.attributeValueId = sourceValueMapping.attributeValueId
                        this.attributeValue = sourceValueMapping.attributeValue
                        this.attributeId = sourceValueMapping.attributeId
                    })
                }
            }
            //已存在属性映射，则更新值映射
            else{
                var targetAttributeMapperResp = targetExistMapping[key]
                //判断是否有值，有则按源数据更新，没有则创建
                val targetValueMappingList = targetPlatformAttrIdToValueMapping[targetAttributeMapperResp!!.platformAttrId]
                val targetPlatformAttributeCodeAndAttributeIdToValueMapping = targetValueMappingList?.groupBy { "${it.platformAttributeCode}-${it.attributeId}" }
                //以源品类属性映射值为准
                val sourceValueMappingList = sourcePlatformAttrIdToValueMapping[sourceAttributeMappingResp.platformAttrId]
                val sourcePlatformAttributeCodeAndAttributeIdToValueMapping = sourceValueMappingList?.groupBy { "${it.platformAttributeCode}-${it.attributeId}" }
                sourcePlatformAttributeCodeAndAttributeIdToValueMapping?.forEach { (key,sourceValues)->{
                    var targetValues = targetPlatformAttributeCodeAndAttributeIdToValueMapping?.get(key)
                    //目标属性没有设置值映射，创建
                    if(targetValues.isNullOrEmpty()){
                        targetValues = sourceValues.map {sourceValueMapping->
                            PublishPlatformAttrValue().apply {
                                this.platformAttrValueId = IdHelper.getId()
                                this.publishPlatformAttrId = targetAttributeMapperResp.platformAttrId
                                this.platformAttributeValue = sourceValueMapping.platformAttributeValue
                                this.platformAttributeCode = sourceValueMapping.platformAttributeCode
                                this.attributeValueId = sourceValueMapping.attributeValueId
                                this.attributeValue = sourceValueMapping.attributeValue
                                this.attributeId = sourceValueMapping.attributeId
                            }
                        }
                        savePublishPlatformAttrValueList.addAll(targetValues)
                    }
                    //更新目标属性值
                    else{
                        val sourcePlatformAttributeValueToValueMapping = sourceValues.groupBy { "${it.platformAttributeCode}-${it.platformAttributeValue}" }
                        val targetPlatformAttributeValueToValueMapping = targetValues.groupBy { "${it.platformAttributeCode}-${it.platformAttributeValue}" }
                        sourcePlatformAttributeValueToValueMapping.forEach { (key,sourceValueMappingList)->{
                            var targetValueMappingList = targetPlatformAttributeValueToValueMapping[key]
                            //删除原来的值
                            if(!targetValueMappingList.isNullOrEmpty()) {
                                deletePublishPlatformAttrValueIdList.addAll(targetValueMappingList.mapNotNull { it.platformAttrValueId })
                            }
                            val newTargetValueMappingList = sourceValueMappingList.map {sourceValueMapping->
                                PublishPlatformAttrValue().apply {
                                    this.platformAttrValueId = IdHelper.getId()
                                    this.publishPlatformAttrId = targetAttributeMapperResp.platformAttrId
                                    this.platformAttributeValue = sourceValueMapping.platformAttributeValue
                                    this.platformAttributeCode = sourceValueMapping.platformAttributeCode
                                    this.attributeValueId = sourceValueMapping.attributeValueId
                                    this.attributeValue = sourceValueMapping.attributeValue
                                    this.attributeId = sourceValueMapping.attributeId
                                }
                            }
                            savePublishPlatformAttrValueList.addAll(newTargetValueMappingList)
                        } }
                    }
                } }
            }
        }
        if(savePublishPlatformAttrList.isNotEmpty()){
            publishPlatformAttrRepository.saveBatch(savePublishPlatformAttrList)
        }
        if(savePublishPlatformAttrValueList.isNotEmpty()){
            publishPlatformAttrValueRepository.saveBatch(savePublishPlatformAttrValueList)
        }
        if(deletePublishPlatformAttrValueIdList.isNotEmpty()){
            publishPlatformAttrValueRepository.removeBatchByIds(deletePublishPlatformAttrValueIdList)
        }
        if(saveCategoryAttributeList.isNotEmpty()){
            publishCategoryAttrRepository.saveBatch(saveCategoryAttributeList)
        }
        //更新品类关联平台状态
        val updateCategory = PublishCategory().apply {
            this.publishCategoryId = targetCategoryMapping.publishCategoryId
            this.platformAssociateState = if(savePublishPlatformAttrList.isNotEmpty()){
                CategoryAssociatePlatformStateEnum.ASSOCIATE_ATTRIBUTE.code
            }else{
                CategoryAssociatePlatformStateEnum.ASSOCIATE_CATEGORY.code
            }
            if(saveCategoryAttributeList.isNotEmpty()){
                this.attributeAssociateState = Bool.YES.code
            }
        }
        publishCategoryRepository.updateById(updateCategory)
    }

    override fun importCategoryAttributeMapping(file: MultipartFile,categoryMappingId:Long){
        //TODO
        //更新品类关联平台状态
    }

    override fun exportCategoryAttributeMapping(categoryMappingId:Long,response: HttpServletResponse){
        //TODO
    }

    private fun listPlatformCategoryAttribute(platform: PlatformEnum,platformCategoryId:String,country:String): MutableList<PlatformAttributeResp>{
        val platformAttributeList = mutableListOf<PlatformAttributeResp>()
        if (LAZADA == platform) {
            val categoryAttributes = lazadaApiService.getCategoryAttributes(
                country,
                platformCategoryId
            )

            if (!categoryAttributes.isSuccess || categoryAttributes.data.isNullOrEmpty()) {
                throw BusinessException("查询${LAZADA.platformName}获取属性报错:${categoryAttributes.message}")
            }

            // 获取销售属性
            val data = categoryAttributes.data?.filter { it.isSaleProp == "0" } ?: emptyList()

            for (lzdAttrResp in data) {
                val platformAttribute = PlatformAttributeResp().apply {
                    label = lzdAttrResp.label
                    inputType = lzdAttrResp.inputType
                    name = lzdAttrResp.name
                    requestFlag = if(lzdAttrResp.isMandatory.isNullOrBlank()){
                        Bool.NO.code
                    }else if(lzdAttrResp.isMandatory == "1"){
                        Bool.YES.code
                    }else{
                        Bool.NO.code
                    }
                }

                if (!lzdAttrResp.options.isNullOrEmpty()) {
                    platformAttribute.optionList = lzdAttrResp.options!!.toJson().parseJsonList(PlatformAttributeResp.PlatformAttributeOption::class.java)
                }

                platformAttributeList.add(platformAttribute)
            }
        } else if (AE == platform) {
            val categoryAttributes = aliexpressServiceHelper.getCachedCategoryAttributes(
                shopId = null,
                platformCategoryId.toLong()
            )

            if (!categoryAttributes.isSuccess()) {
                throw BusinessException("查询${AE.platformName}获取属性报错:${categoryAttributes.errorMessage}")
            }

            // 获取销售属性
            val data = categoryAttributes.result?.attributes?.filter { it.visible == true } ?: emptyList()

            for (aeAttrResp in data) {
                val platformAttribute = PlatformAttributeResp().apply {
                    label = aeAttrResp.getName()
                    inputType = aeAttrResp.inputType
                    name = aeAttrResp.id.toString()
                    requestFlag = if(aeAttrResp.required.isNull()){
                        Bool.NO.code
                    }else if(aeAttrResp.required==true){
                        Bool.YES.code
                    }else{
                        Bool.NO.code
                    }
                }
                if (!aeAttrResp.values.isNullOrEmpty()) {
                    platformAttribute.optionList = aeAttrResp.values.map {
                        PlatformAttributeResp.PlatformAttributeOption().apply {
                            this.name = it.getName() + if (it.getChineseName().isNotBlank() && it.getChineseName() != it.getName()) "(${it.getChineseName()})" else ""
                            this.enName = it.getName()
                            this.id = it.id.toString()
                        }
                    }
                }

                platformAttributeList.add(platformAttribute)
            }
        } else if (PlatformEnum.TEMU == platform) {

            val attrsResponse = temuService.getAttrsByCategoryWithCache(platformCategoryId.toInt())
            if (attrsResponse != null) {
                attrsResponse.properties?.forEach {
                    val platformAttribute = PlatformAttributeResp().apply {
                        label = it.name
                        /** 属性值类型
                        控件类型： INPUT(0, "可输入"), CHOOSE(1, "可勾选"), INPUT_CHOOSE(3, "可输入又可勾选"),
                        SINGLE_YMD_DATE(5, "单项时间选择器-年月日"), MULTIPLE_YMD_DATE(6, "双项时间选择器-年月日"),
                        SINGLE_YM_DATE(7, "单项时间选择器-年月"), MULTIPLE_YM_DATE(8, "双项时间选择器-年月"), COLOR_SELECTOR(9, "调色盘"),
                        SIZE_SELECTOR(10, "尺码选择器"), NUMBER_RANGE(11, "输入数值范围"), NUMBER_PRODUCT_DOUBLE(12, "输入数值乘积-2维"),
                        NUMBER_PRODUCT_TRIPLE(13, "输入数值乘积-3维"), AUTO_COMPUTE(14, "自动计算框"), REGION_CHOOSE(15, "地区选择器"),
                        PROPERTY_CHOOSE_AND_INPUT(16, "属性勾选和数值录入"),
                         **/
                        inputType = it.controlType.toString()
                        name = it.pid.toString() + "-" + it.templatePid
                        requestFlag = if(it.required.isNull()){
                            Bool.NO.code
                        }else if(it.required==true){
                            Bool.YES.code
                        }else{
                            Bool.NO.code
                        }

                    }

                    if (it.values != null && it.values.isNotEmpty()) {
                        platformAttribute.optionList = it.values!!.map { v ->
                            PlatformAttributeResp.PlatformAttributeOption().apply {
                                name = v.value
                                id = v.vid.toString()
                            }
                        }
                    }
                    platformAttributeList.add(platformAttribute)

                }
            }
        }
        return platformAttributeList
    }

    override fun invalidateAllAliexpressCategoryAttributes() {
        aliexpressServiceHelper.invalidateAllCachedCategoryAttributes()
    }

    @Transactional(rollbackFor = [Exception::class])
    override fun copyAttributes(req: CategoryAttributeCopyReq): CategoryAttributeCopyResp {
        log.info { "开始处理品类属性复制请求: ${req.mappingList.size}条映射" }
        val categoryList = publishCategoryRepository.listWithCache()
        return CategoryAttributeCopyResp(req.mappingList.map { processCopyCategoryMapping(categoryList, it) })
    }

    @Transactional(rollbackFor = [Exception::class])
    override fun copyAttributesById(req: CategoryAttributeCopyByIdReq): CategoryAttributeCopyByIdResp {
        log.info { "开始处理品类属性复制请求(ID): ${req.mappingList.size}条映射" }
        return CategoryAttributeCopyByIdResp(req.mappingList.map { processCategoryIdMapping(it) })
    }

    override fun getCategoryAttributeWithValuesByCategoryId(categoryId:Long):List<CategoryAttributeWithValuesResp>{
        var categoryAttributeList = publishCategoryAttrRepository.listByCategoryId(categoryId)
        var attributeIds = categoryAttributeList.mapNotNull { it.attributeId }.toList()
        if(attributeIds.isEmpty()){
            return mutableListOf()
        }

        var attributeList = publishAttributeRepository.listByIds(attributeIds)
        val groupIdList = attributeList.map { it.attributeGroupId }
        val attributeGroupList = publishAttributeGroupRepository.listByIds(groupIdList)
        val groupIdToMap = attributeGroupList.associateBy { it.attributeGroupId }
        var attributeIdToMap =  attributeList.associateBy { it.attributeId }
        var attributeValueList = publishAttributeValueRepository.listByAttrIds(attributeIds)
        var attributeIdToValueListMap = attributeValueList.groupBy { it.attributeId }
        return categoryAttributeList.map {
            CategoryAttributeWithValuesResp().apply {
                this.attributeId = it.attributeId
                var attribute = attributeIdToMap[it.attributeId]
                if(attribute!=null){
                    this.attributeGroupId = attribute.attributeGroupId
                    this.attributeName = attribute.attributeName
                    this.state = attribute.state
                    this.attributeCode = attribute.attributeCode
                    this.showType = attribute.showType
                    groupIdToMap[attribute.attributeGroupId]?.let {
                        this.platformFlag = it.platformFlag
                    }
                }

                var valueList = attributeIdToValueListMap[it.attributeId]
                this.attributeValueList = valueList?.mapNotNull { v ->
                    PublishAttributeValueResp().apply {
                        this.attributeValueId = v.attributeValueId
                        this.attributeId = v.attributeId
                        this.attributeValue = v.attributeValue
                        this.state = v.state
                        this.defaultFlag = v.defaultFlag
                        this.unit = v.unit
                    }
                }
            }
        }
    }

    private fun handleCategoryAttributeValueMapping(req: SaveCategoryAttributeValueMappingReq){
        val categoryMapping = publishCategoryMappingRepository.getById(req.categoryMappingId)
            ?: throw BusinessException("找不到平台和品类映射记录")

        val platform = PlatformEnum.getByPlatformId(categoryMapping.platformId!!)
            ?: throw BusinessException("找不到平台信息")
        try{
            // 获取请求中的平台属性映射ID
            val updatePlatformAttrIdList = req.attributeMapperList?.mapNotNull { it.platformAttrId } ?: emptyList()

            // 获取当前品类在当前平台所关联的属性
            val allPlatformAttrList = publishPlatformAttrRepository.ktQuery()
                .eq(PublishPlatformAttr::categoryMappingId, categoryMapping.categoryMappingId)
                .eq(PublishPlatformAttr::platformId,platform.platformId)
                .list()
            // 处理需要删除的平台属性
            allPlatformAttrList.filter { !updatePlatformAttrIdList.contains(it.platformAttrId) }
                .map { it.platformAttrId }
                .takeIf { it.isNotEmpty() }
                ?.let { removePlatformAttrIds ->
                    // 先删除关联的属性值
                    publishPlatformAttrValueRepository.ktUpdate()
                        .`in`(PublishPlatformAttrValue::publishPlatformAttrId, removePlatformAttrIds)
                        .remove()

                    // 再删除平台属性
                    publishPlatformAttrRepository.removeByIds(removePlatformAttrIds)
                    log.info { "删除了 ${removePlatformAttrIds.size} 个平台属性及其属性值" }
                }
            // 处理需要更新的平台属性映射对象
            val updateAttrMappers = req.attributeMapperList?.filter { it.platformAttrId != null } ?: emptyList()
            // 批量处理属性值更新和删除
            handlePlatformAttributeUpdates(updateAttrMappers)

            // 处理需要新增的平台属性
            val addAttributeList = req.attributeMapperList?.filter { it.platformAttrId == null } ?: emptyList()
            if (addAttributeList.isNotEmpty()) {
                handleAddNewPlatformAttributes(addAttributeList,categoryMapping)
            }

            //更新品类关联平台状态
            changePublishCategoryPlatformAssociateState(categoryMapping.publishCategoryId!!)
        } catch (e: Exception) {
            log.error(e) { "关联属性失败: ${e.message}" }
            throw BusinessException("关联属性失败")
        }
    }

    @Transactional(rollbackFor = [Exception::class])
    override fun changePublishCategoryPlatformAssociateState(categoryId:Long){
        //更新品类关联平台状态
        val attrMappings: List<PublishPlatformAttr?> =
            publishPlatformAttrRepository.listByCategoryIds(mutableListOf(categoryId))
        val categoryMappings: List<PublishCategoryMapping?> =
            publishCategoryMappingRepository.listByCategoryId(categoryId)
        val updateCategory = PublishCategory()
        updateCategory.publishCategoryId = categoryId
        updateCategory.platformAssociateState =
            if (CollectionUtils.isEmpty(attrMappings)) {
                if (CollectionUtils.isEmpty(categoryMappings)){
                    CategoryAssociatePlatformStateEnum.NO_ASSOCIATE.code
                } else {
                    CategoryAssociatePlatformStateEnum.ASSOCIATE_CATEGORY.code
                }
            }else {
                CategoryAssociatePlatformStateEnum.ASSOCIATE_ATTRIBUTE.code
            }
        publishCategoryRepository.updateById(updateCategory)
    }

    /**
     * 添加新的平台属性及其属性值
     */
    private fun handleAddNewPlatformAttributes(attributeMappers: List<SaveCategoryAttributeValueMappingReq.AttributeMapper>,
                                               categoryMapping: PublishCategoryMapping) {
        // 批量收集所有新增属性
        val attrToAdd = mutableListOf<PublishPlatformAttr>()
        // 批量收集所有新增属性值
        val attrValuesToAdd = mutableListOf<PublishPlatformAttrValue>()

        attributeMappers.forEach { mapper ->
            // 创建新平台属性
            val newAttrId = IdHelper.getId()
            val newAttr = PublishPlatformAttr().apply {
                platformAttrId = newAttrId
                categoryId = categoryMapping.publishCategoryId
                platformId = categoryMapping.platformId
                categoryMappingId = categoryMapping.categoryMappingId
                platformAttributeLabelName = mapper.platformAttributeLabelName
                platformAttributeKeyName = mapper.platformAttributeKeyName
                attributeId = mapper.attributeId
                // 设置国家/地区信息
                country = categoryMapping.country
                requestFlag = mapper.requestFlag
            }
            attrToAdd.add(newAttr)

            // 创建属性对应的属性值
            mapper.attributeValueList?.forEach { valueMapper ->
                val newValue = PublishPlatformAttrValue().apply {
                    platformAttrValueId = IdHelper.getId()
                    publishPlatformAttrId = newAttrId
                    platformAttributeValue = valueMapper.platformAttributeValue
                    platformAttributeCode = valueMapper.platformAttributeCode
                    attributeValueId = valueMapper.attributeValueId
                    attributeValue = valueMapper.attributeValue
                    attributeId = mapper.attributeId
                }
                attrValuesToAdd.add(newValue)
            }
        }

        // 批量保存新增属性
        if (attrToAdd.isNotEmpty()) {
            publishPlatformAttrRepository.saveBatch(attrToAdd)
            log.info { "新增了 ${attrToAdd.size} 个平台属性" }
        }

        // 批量保存新增属性值
        if (attrValuesToAdd.isNotEmpty()) {
            publishPlatformAttrValueRepository.saveBatch(attrValuesToAdd)
            log.info { "新增了 ${attrValuesToAdd.size} 个平台属性值" }
        }
    }

    /**
     * 处理平台属性更新
     */
    private fun handlePlatformAttributeUpdates(attributeMappers: List<SaveCategoryAttributeValueMappingReq.AttributeMapper>) {
        if (attributeMappers.isEmpty()) return

        // 批量收集所有需要更新的属性
        val attrUpdates = mutableListOf<PublishPlatformAttr>()

        // 批量收集所有需要添加的属性值
        val attrValuesToAdd = mutableListOf<PublishPlatformAttrValue>()

        // 批量收集所有需要删除的属性值ID
        val attrValuesToDelete = mutableListOf<Long>()

        // 批量收集所有需要更新的属性值
        val attrValuesToUpdate = mutableListOf<PublishPlatformAttrValue>()

        for (mapper in attributeMappers) {
            // 更新平台属性基础信息
            val platformAttr = PublishPlatformAttr().apply {
                platformAttrId = mapper.platformAttrId
                platformAttributeKeyName = mapper.platformAttributeKeyName
                platformAttributeLabelName = mapper.platformAttributeLabelName
                requestFlag = mapper.requestFlag
            }
            attrUpdates.add(platformAttr)

            val reqAttributeValueList = mapper.attributeValueList ?: continue

            // 如果属性值列表为空，删除所有相关属性值
            if (reqAttributeValueList.isEmpty()) {
                publishPlatformAttrValueRepository.ktUpdate()
                    .eq(PublishPlatformAttrValue::publishPlatformAttrId, mapper.platformAttrId)
                    .remove()
                continue
            }

            // 获取数据库中当前的属性值
            val existingAttrValues = publishPlatformAttrValueRepository.ktQuery()
                .eq(PublishPlatformAttrValue::publishPlatformAttrId, mapper.platformAttrId)
                .list()
                .associateBy { it.platformAttrValueId }

            // 处理现有属性值的更新
            reqAttributeValueList.filter { it.platformAttrValueId != null }
                .forEach { valueMapper ->
                    val existingValue = existingAttrValues[valueMapper.platformAttrValueId]
                    if (existingValue != null) {
                        val updatedValue = existingValue.copy(
                            platformAttributeValue = valueMapper.platformAttributeValue,
                            platformAttributeCode = valueMapper.platformAttributeCode,
                        )
                        attrValuesToUpdate.add(updatedValue)
                    }
                }

            // 找出需要删除的属性值ID
            val reqValueIds = reqAttributeValueList
                .mapNotNull { it.platformAttrValueId }
                .toSet()

            existingAttrValues.keys
                .filterNotNull()
                .filter { !reqValueIds.contains(it) }
                .let { attrValuesToDelete.addAll(it) }

            // 收集新增的属性值
            reqAttributeValueList
                .filter { it.platformAttrValueId == null && !StringUtils.isBlank(it.platformAttributeCode) }
                .mapTo(attrValuesToAdd) { valueMapper ->
                    PublishPlatformAttrValue().apply {
                        platformAttrValueId = IdHelper.getId()
                        publishPlatformAttrId = mapper.platformAttrId
                        platformAttributeValue = valueMapper.platformAttributeValue
                        platformAttributeCode = valueMapper.platformAttributeCode
                        attributeValueId = valueMapper.attributeValueId
                        attributeValue = valueMapper.attributeValue
                        attributeId = mapper.attributeId
                    }
                }
        }

        // 批量更新平台属性
        if (attrUpdates.isNotEmpty()) {
            attrUpdates.forEach { attr ->
                publishPlatformAttrRepository.ktUpdate()
                    .eq(PublishPlatformAttr::platformAttrId, attr.platformAttrId)
                    .set(PublishPlatformAttr::platformAttributeKeyName, attr.platformAttributeKeyName)
                    .set(PublishPlatformAttr::platformAttributeLabelName, attr.platformAttributeLabelName)
                    .set(PublishPlatformAttr::requestFlag, attr.requestFlag)
                    .update()
            }
            log.info { "更新了 ${attrUpdates.size} 个平台属性" }
        }

        // 批量删除不需要的属性值
        if (attrValuesToDelete.isNotEmpty()) {
            publishPlatformAttrValueRepository.removeBatchByIds(attrValuesToDelete)
            log.info { "删除了 ${attrValuesToDelete.size} 个平台属性值" }
        }

        // 批量更新现有属性值
        if (attrValuesToUpdate.isNotEmpty()) {
            attrValuesToUpdate.forEach { value ->
                publishPlatformAttrValueRepository.ktUpdate()
                    .eq(PublishPlatformAttrValue::platformAttrValueId, value.platformAttrValueId)
                    .set(PublishPlatformAttrValue::platformAttributeValue, value.platformAttributeValue)
                    .set(PublishPlatformAttrValue::platformAttributeCode, value.platformAttributeCode)
                    .update()
            }
            log.info { "更新了 ${attrValuesToUpdate.size} 个平台属性值" }
        }

        // 批量保存新增属性值
        if (attrValuesToAdd.isNotEmpty()) {
            publishPlatformAttrValueRepository.saveBatch(attrValuesToAdd)
            log.info { "新增了 ${attrValuesToAdd.size} 个平台属性值" }
        }
    }

    private fun processCategoryAttributeUpdate(req: CategoryAttributeEditReq, categoryMappingId: Long) {
        try {
            // 获取所有现有平台属性
            val allPlatformAttrList = publishPlatformAttrRepository.ktQuery()
                .eq(PublishPlatformAttr::categoryMappingId, categoryMappingId)
                .list()

            // 获取请求中的平台属性ID列表
            val updatePlatformAttrIdList = req.attributeMapperList?.mapNotNull { it.platformAttrId } ?: emptyList()

            // 处理需要删除的平台属性
            allPlatformAttrList.filter { !updatePlatformAttrIdList.contains(it.platformAttrId) }
                .map { it.platformAttrId }
                .takeIf { it.isNotEmpty() }
                ?.let { removePlatformAttrIds ->
                    // 先删除关联的属性值
                    publishPlatformAttrValueRepository.ktUpdate()
                        .`in`(PublishPlatformAttrValue::publishPlatformAttrId, removePlatformAttrIds)
                        .remove()

                    // 再删除平台属性
                    publishPlatformAttrRepository.removeByIds(removePlatformAttrIds)
                    log.info { "删除了 ${removePlatformAttrIds.size} 个平台属性及其属性值" }
                }

            // 处理需要更新的平台属性
            val updateAttrMappers = req.attributeMapperList?.filter { it.platformAttrId != null } ?: emptyList()

            // 批量处理属性值更新和删除
            processPlatformAttributeUpdates(updateAttrMappers)

            // 处理需要新增的平台属性
            val addAttributeList = req.attributeMapperList?.filter { it.platformAttrId == null } ?: emptyList()
            if (addAttributeList.isNotEmpty()) {
                addNewPlatformAttributes(addAttributeList)
            }

        } catch (e: Exception) {
            log.error(e) { "关联属性失败: ${e.message}" }
            throw BusinessException("关联属性失败")
        }
    }

    /**
     * 处理平台属性更新
     */
    private fun processPlatformAttributeUpdates(attributeMappers: List<CategoryAttributeEditReq.AttributeMapper>) {
        if (attributeMappers.isEmpty()) return

        // 批量收集所有需要更新的属性
        val attrUpdates = mutableListOf<PublishPlatformAttr>()

        // 批量收集所有需要添加的属性值
        val attrValuesToAdd = mutableListOf<PublishPlatformAttrValue>()

        // 批量收集所有需要删除的属性值ID
        val attrValuesToDelete = mutableListOf<Long>()

        // 批量收集所有需要更新的属性值
        val attrValuesToUpdate = mutableListOf<PublishPlatformAttrValue>()

        for (mapper in attributeMappers) {
            // 更新平台属性基础信息
            val platformAttr = PublishPlatformAttr().apply {
                platformAttrId = mapper.platformAttrId
                platformAttributeKeyName = mapper.platformAttributeKeyName
                platformAttributeLabelName = mapper.platformAttributeLabelName
            }
            attrUpdates.add(platformAttr)

            val reqAttributeValueList = mapper.attributeValueList ?: continue

            // 如果属性值列表为空，删除所有相关属性值
            if (reqAttributeValueList.isEmpty()) {
                publishPlatformAttrValueRepository.ktUpdate()
                    .eq(PublishPlatformAttrValue::publishPlatformAttrId, mapper.platformAttrId)
                    .remove()
                continue
            }

            // 获取数据库中当前的属性值
            val existingAttrValues = publishPlatformAttrValueRepository.ktQuery()
                .eq(PublishPlatformAttrValue::publishPlatformAttrId, mapper.platformAttrId)
                .list()
                .associateBy { it.platformAttrValueId }

            // 处理现有属性值的更新
            reqAttributeValueList.filter { it.platformAttrValueId != null }
                .forEach { valueMapper ->
                    val existingValue = existingAttrValues[valueMapper.platformAttrValueId]
                    if (existingValue != null) {
                        val updatedValue = existingValue.copy(
                            platformAttributeValue = valueMapper.platformAttributeValue,
                            platformAttributeCode = valueMapper.platformAttributeCode
                        )
                        attrValuesToUpdate.add(updatedValue)
                    }
                }

            // 找出需要删除的属性值ID
            val reqValueIds = reqAttributeValueList
                .mapNotNull { it.platformAttrValueId }
                .toSet()

            existingAttrValues.keys
                .filterNotNull()
                .filter { !reqValueIds.contains(it) }
                .let { attrValuesToDelete.addAll(it) }

            // 收集新增的属性值
            reqAttributeValueList
                .filter { it.platformAttrValueId == null && !StringUtils.isBlank(it.platformAttributeCode) }
                .mapTo(attrValuesToAdd) { valueMapper ->
                    PublishPlatformAttrValue().apply {
                        platformAttrValueId = IdHelper.getId()
                        publishPlatformAttrId = mapper.platformAttrId
                        platformAttributeValue = valueMapper.platformAttributeValue
                        platformAttributeCode = valueMapper.platformAttributeCode
                        attributeValueId = valueMapper.attributeValueId
                        attributeValue = valueMapper.attributeValue
                        attributeId = valueMapper.attributeId
                    }
                }
        }

        // 批量更新平台属性
        if (attrUpdates.isNotEmpty()) {
            attrUpdates.forEach { attr ->
                publishPlatformAttrRepository.ktUpdate()
                    .eq(PublishPlatformAttr::platformAttrId, attr.platformAttrId)
                    .set(PublishPlatformAttr::platformAttributeKeyName, attr.platformAttributeKeyName)
                    .set(PublishPlatformAttr::platformAttributeLabelName, attr.platformAttributeLabelName)
                    .update()
            }
            log.info { "更新了 ${attrUpdates.size} 个平台属性" }
        }

        // 批量删除不需要的属性值
        if (attrValuesToDelete.isNotEmpty()) {
            publishPlatformAttrValueRepository.removeBatchByIds(attrValuesToDelete)
            log.info { "删除了 ${attrValuesToDelete.size} 个平台属性值" }
        }

        // 批量更新现有属性值
        if (attrValuesToUpdate.isNotEmpty()) {
            attrValuesToUpdate.forEach { value ->
                publishPlatformAttrValueRepository.ktUpdate()
                    .eq(PublishPlatformAttrValue::platformAttrValueId, value.platformAttrValueId)
                    .set(PublishPlatformAttrValue::platformAttributeValue, value.platformAttributeValue)
                    .set(PublishPlatformAttrValue::platformAttributeCode, value.platformAttributeCode)
                    .update()
            }
            log.info { "更新了 ${attrValuesToUpdate.size} 个平台属性值" }
        }

        // 批量保存新增属性值
        if (attrValuesToAdd.isNotEmpty()) {
            publishPlatformAttrValueRepository.saveBatch(attrValuesToAdd)
            log.info { "新增了 ${attrValuesToAdd.size} 个平台属性值" }
        }
    }

    /**
     * 添加新的平台属性及其属性值
     */
    private fun addNewPlatformAttributes(attributeMappers: List<CategoryAttributeEditReq.AttributeMapper>) {
        // 批量收集所有新增属性
        val attrToAdd = mutableListOf<PublishPlatformAttr>()
        // 批量收集所有新增属性值
        val attrValuesToAdd = mutableListOf<PublishPlatformAttrValue>()

        attributeMappers.forEach { mapper ->
            // 创建新平台属性
            val newAttrId = IdHelper.getId()
            val newAttr = PublishPlatformAttr().apply {
                platformAttrId = newAttrId
                categoryId = mapper.categoryId
                platformId = mapper.platformId
                categoryMappingId = mapper.categoryMappingId
                platformAttributeLabelName = mapper.platformAttributeLabelName
                platformAttributeKeyName = mapper.platformAttributeKeyName
                attributeId = mapper.attributeId

                // 设置国家/地区信息
                country = when (mapper.platformId) {
                    LAZADA.platformId -> LazadaCountryEnum.PH.code
                    AE.platformId -> CountryEnum.US.code
                    PlatformEnum.TEMU.platformId -> CountryEnum.CN.code
                    else -> null
                }
            }
            attrToAdd.add(newAttr)

            // 创建属性对应的属性值
            mapper.attributeValueList?.forEach { valueMapper ->
                val newValue = PublishPlatformAttrValue().apply {
                    platformAttrValueId = IdHelper.getId()
                    publishPlatformAttrId = newAttrId
                    platformAttributeValue = valueMapper.platformAttributeValue
                    platformAttributeCode = valueMapper.platformAttributeCode
                    attributeValueId = valueMapper.attributeValueId
                    attributeValue = valueMapper.attributeValue
                    attributeId = valueMapper.attributeId
                }
                attrValuesToAdd.add(newValue)
            }
        }

        // 批量保存新增属性
        if (attrToAdd.isNotEmpty()) {
            publishPlatformAttrRepository.saveBatch(attrToAdd)
            log.info { "新增了 ${attrToAdd.size} 个平台属性" }
        }

        // 批量保存新增属性值
        if (attrValuesToAdd.isNotEmpty()) {
            publishPlatformAttrValueRepository.saveBatch(attrValuesToAdd)
            log.info { "新增了 ${attrValuesToAdd.size} 个平台属性值" }
        }
    }

    private fun processCategoryIdMapping(mapping: CategoryCopyIdMapping): IdCopyResult {
        try {
            // 验证品类ID有效性
            when {
                mapping.sourceCategoryId == mapping.targetCategoryId ->
                    return IdCopyResult(mapping.sourceCategoryId, mapping.targetCategoryId, false, "源品类和目标品类相同，无需复制")
            }

            // 使用分布式锁确保操作的原子性
            lockComponent.doInLock("$CATEGORY_ATTR_COPY_LOCK${mapping.targetCategoryId}", BusinessException("目标品类正在被其他操作处理，请稍后重试")) {
                copyAttributes(mapping.sourceCategoryId, mapping.targetCategoryId)
            }

            return IdCopyResult(mapping.sourceCategoryId, mapping.targetCategoryId, true, null)
        } catch (e: Exception) {
            log.error(e) { "复制品类属性失败, 源品类ID: ${mapping.sourceCategoryId}, 目标品类ID: ${mapping.targetCategoryId}" }
            return IdCopyResult(mapping.sourceCategoryId, mapping.targetCategoryId, false, e.message ?: "未知错误")
        }
    }

    private fun processCopyCategoryMapping(
        categoryList: List<PublishCategory>,
        mapping: CategoryCopyMapping,
    ): CopyResult {
        try {
            // 获取源品类ID和目标品类ID
            val sourceCategoryId = CategoryUtils.findPublishCategoryIdByPath(categoryList, mapping.sourceCategoryPath)
            val targetCategoryId = CategoryUtils.findPublishCategoryIdByPath(categoryList, mapping.targetCategoryPath)

            // 验证品类ID有效性
            when {
                sourceCategoryId == null -> return CopyResult(mapping.sourceCategoryPath, mapping.targetCategoryPath, false, "源品类路径未找到匹配的品类ID")
                targetCategoryId == null -> return CopyResult(mapping.sourceCategoryPath, mapping.targetCategoryPath, false, "目标品类路径未找到匹配的品类ID")
                sourceCategoryId == targetCategoryId -> return CopyResult(mapping.sourceCategoryPath, mapping.targetCategoryPath, false, "源品类和目标品类相同，无需复制")
            }

            // 使用分布式锁确保操作的原子性
            lockComponent.doInLock(
                "$CATEGORY_ATTR_COPY_LOCK$targetCategoryId",
                BusinessException("目标品类正在被其他操作处理，请稍后重试")
            ) {
                copyAttributes(sourceCategoryId!!, targetCategoryId!!)
            }

            return CopyResult(mapping.sourceCategoryPath, mapping.targetCategoryPath, true, null)
        } catch (e: Exception) {
            log.error(e) { "复制品类属性失败, 源品类: ${mapping.sourceCategoryPath}, 目标品类: ${mapping.targetCategoryPath}" }
            return CopyResult(mapping.sourceCategoryPath, mapping.targetCategoryPath, false, e.message ?: "未知错误")
        }
    }

    private fun copyAttributes(sourceCategoryId: Long, targetCategoryId: Long) {
        log.info { "开始从品类ID: $sourceCategoryId 复制属性到品类ID: $targetCategoryId" }

        // 先清理目标品类在所有三个表中的数据
        clearTargetCategoryData(targetCategoryId)

        // 复制品类属性、映射关系及平台属性
        copyPublishCategoryAttributes(sourceCategoryId, targetCategoryId)
        val mappingPairs = copyPublishCategoryMappings(sourceCategoryId, targetCategoryId)
        mappingPairs.forEach { (sourceMappingId, targetMappingId) ->
            copyPlatformAttributes(sourceMappingId, targetMappingId, sourceCategoryId, targetCategoryId)
        }

        log.info { "完成从品类ID: $sourceCategoryId 到品类ID: $targetCategoryId 的属性复制" }
    }

    private fun clearTargetCategoryData(targetCategoryId: Long) {
        log.info { "开始清理目标品类 $targetCategoryId 的相关数据" }

        // 获取目标品类的所有映射
        val mappings = publishCategoryMappingRepository.ktQuery()
            .eq(PublishCategoryMapping::publishCategoryId, targetCategoryId)
            .list()

        if (mappings.isNotEmpty()) {
            val categoryMappingIds = mappings.mapNotNull { it.categoryMappingId }

            // 获取所有相关平台属性
            val platformAttrs = publishPlatformAttrRepository.ktQuery()
                .`in`(PublishPlatformAttr::categoryMappingId, categoryMappingIds)
                .list()

            // 批量删除平台属性值
            if (platformAttrs.isNotEmpty()) {
                val platformAttrIds = platformAttrs.mapNotNull { it.platformAttrId }
                publishPlatformAttrValueRepository.ktUpdate()
                    .`in`(PublishPlatformAttrValue::publishPlatformAttrId, platformAttrIds)
                    .remove()
                log.info { "已删除目标品类 $targetCategoryId 关联的平台属性值, 影响属性数: ${platformAttrIds.size}" }
            }

            // 批量删除平台属性
            if (categoryMappingIds.isNotEmpty()) {
                publishPlatformAttrRepository.ktUpdate()
                    .`in`(PublishPlatformAttr::categoryMappingId, categoryMappingIds)
                    .remove()
                log.info { "已删除目标品类 $targetCategoryId 关联的平台属性, 影响映射数: ${categoryMappingIds.size}" }
            }

            // 批量删除品类映射
            publishCategoryMappingRepository.ktUpdate()
                .eq(PublishCategoryMapping::publishCategoryId, targetCategoryId)
                .remove()
            log.info { "已删除目标品类 $targetCategoryId 的品类映射, 数量: ${mappings.size}" }
        }

        // 清理品类属性
        publishCategoryAttrRepository.ktUpdate()
            .eq(PublishCategoryAttr::categoryId, targetCategoryId)
            .remove()
        log.info { "已删除目标品类 $targetCategoryId 的品类属性" }
    }

    private fun copyPublishCategoryAttributes(sourceCategoryId: Long, targetCategoryId: Long) {
        val sourceAttrs = publishCategoryAttrRepository.ktQuery()
            .eq(PublishCategoryAttr::categoryId, sourceCategoryId)
            .list()

        if (sourceAttrs.isEmpty()) {
            log.info { "源品类 $sourceCategoryId 没有属性关联，跳过复制" }
            return
        }

        val targetAttrs = sourceAttrs.map { sourceAttr ->
            PublishCategoryAttr().apply {
                categoryAttrId = IdHelper.getId()
                categoryId = targetCategoryId
                attributeId = sourceAttr.attributeId
            }
        }

        publishCategoryAttrRepository.saveBatch(targetAttrs)
        log.info { "复制品类属性关联: 从品类 $sourceCategoryId 到 $targetCategoryId, 共 ${targetAttrs.size} 条记录" }
    }

    private fun copyPublishCategoryMappings(sourceCategoryId: Long, targetCategoryId: Long): List<Pair<Long, Long>> {
        val sourceMappings = publishCategoryMappingRepository.ktQuery()
            .eq(PublishCategoryMapping::publishCategoryId, sourceCategoryId)
            .list()

        if (sourceMappings.isEmpty()) {
            log.info { "源品类 $sourceCategoryId 没有平台映射关系，跳过复制" }
            return emptyList()
        }

        val mappingPairs = mutableListOf<Pair<Long, Long>>()
        val targetMappings = sourceMappings.map { sourceMapping ->
            val newMappingId = IdHelper.getId()
            mappingPairs.add(Pair(sourceMapping.categoryMappingId!!, newMappingId))

            sourceMapping.copy(
                categoryMappingId = newMappingId,
                publishCategoryId = targetCategoryId
            ).also {
                BaseEntity.reset(it)
            }
        }

        publishCategoryMappingRepository.saveBatch(targetMappings)
        log.info { "复制品类平台映射: 从品类 $sourceCategoryId 到 $targetCategoryId, 共 ${targetMappings.size} 条记录" }
        return mappingPairs
    }

    private fun copyPlatformAttributes(sourceMappingId: Long, targetMappingId: Long, sourceCategoryId: Long, targetCategoryId: Long) {
        val sourceAttrs = publishPlatformAttrRepository.ktQuery()
            .eq(PublishPlatformAttr::categoryMappingId, sourceMappingId)
            .list()

        if (sourceAttrs.isEmpty()) {
            log.info { "源映射 $sourceMappingId 没有平台属性，跳过" }
            return
        }

        // 创建属性ID映射和目标属性列表
        val attrIdMapping = mutableMapOf<Long, Long>()
        val targetAttrs = sourceAttrs.map { sourceAttr ->
            val newAttrId = IdHelper.getId()
            attrIdMapping[sourceAttr.platformAttrId!!] = newAttrId

            sourceAttr.copy(
                platformAttrId = newAttrId,
                categoryId = targetCategoryId,
                categoryMappingId = targetMappingId
            ).also {
                BaseEntity.reset(it)
            }
        }

        // 批量保存平台属性
        publishPlatformAttrRepository.saveBatch(targetAttrs)
        log.info { "复制平台属性: 从映射 $sourceMappingId 到 $targetMappingId, 共 ${targetAttrs.size} 条记录" }

        // 批量获取所有源属性的属性值
        val allSourceAttributeIds = attrIdMapping.keys.toList()
        if (allSourceAttributeIds.isEmpty()) return

        val allSourceValues = publishPlatformAttrValueRepository.ktQuery()
            .`in`(PublishPlatformAttrValue::publishPlatformAttrId, allSourceAttributeIds)
            .list()

        // 批量创建所有目标属性值
        val allTargetValues = mutableListOf<PublishPlatformAttrValue>()

        allSourceValues.forEach { sourceValue ->
            val targetAttrId = attrIdMapping[sourceValue.publishPlatformAttrId]
            if (targetAttrId != null) {
                allTargetValues.add(
                    sourceValue.copy(
                        platformAttrValueId = IdHelper.getId(),
                        publishPlatformAttrId = targetAttrId
                    ).also {
                        BaseEntity.reset(it)
                    }
                )
            }
        }

        // 批量保存所有属性值
        if (allTargetValues.isNotEmpty()) {
            publishPlatformAttrValueRepository.saveBatch(allTargetValues)
            log.info { "批量复制平台属性值: 共 ${allTargetValues.size} 条记录" }
        }
    }

}