package tech.tiangong.pop.service.category

import org.springframework.web.multipart.MultipartFile
import tech.tiangong.pop.req.category.PublishCategoryTreeQueryReq
import tech.tiangong.pop.req.category.SavePublishCategoryReq
import tech.tiangong.pop.resp.category.PublishCategoryMappingTreeNodeVo
import tech.tiangong.pop.resp.category.PublishCategoryTreeNodeVo

/**
 * 上架品类
 */
interface PublishCategoryService {
    /**
     * 保存品类
     */
    fun save(req: SavePublishCategoryReq)

    /**
     * 查询品类树
     */
    fun tree(req: PublishCategoryTreeQueryReq): List<PublishCategoryTreeNodeVo>

    /**
     * 根据已知品类映射ID查询已映射到同平台同站点同第三方品类的内部品类
     */
    fun getMappingCategoryTree(categoryMappingId:Long):List<PublishCategoryMappingTreeNodeVo>
    /**
     * 导入品类
     */
    fun importCategory(file: MultipartFile): List<String>

    /**
     * 品类删除
     */
    fun deleteCategory(publishCategoryIds: List<Long>?)
}
