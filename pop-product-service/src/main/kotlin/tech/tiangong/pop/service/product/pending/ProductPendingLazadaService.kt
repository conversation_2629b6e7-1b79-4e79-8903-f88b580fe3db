package tech.tiangong.pop.service.product.pending

import org.springframework.web.multipart.MultipartFile
import team.aikero.blade.core.protocol.PageVo
import tech.tiangong.pop.dao.entity.Product
import tech.tiangong.pop.dao.entity.Shop
import tech.tiangong.pop.req.product.ProductCreatePublishTaskReq
import tech.tiangong.pop.req.product.ProductPendingPublishLogPageReq
import tech.tiangong.pop.req.product.ae.ProductPendingAePageReq
import tech.tiangong.pop.req.product.lazada.*
import tech.tiangong.pop.resp.product.ProductPendingPublishLogPageResp
import tech.tiangong.pop.resp.product.ProductPendingPublishLogStatisticsResp
import tech.tiangong.pop.resp.product.ae.ProductPendingAeStatisticsResp
import tech.tiangong.pop.resp.product.lazada.ProductPendingLazadaDetailResp
import tech.tiangong.pop.resp.product.lazada.ProductPendingLazadaPageResp

/**
 * 待上架商品-Lazada服务
 */
interface ProductPendingLazadaService {

    /**
     * 创建待上架商品
     */
    fun createPendingTask(req: ProductCreatePublishTaskReq.PendingAeTaskChangeUserReq?, productList: List<Product>, shop: Shop)

    /**
     * 商品列表-分页
     */
    fun page(req: ProductPendingLazadaPageReq): PageVo<ProductPendingLazadaPageResp>

    /**
     * 商品列表-统计
     */
    fun statistics(req: ProductPendingAePageReq): ProductPendingAeStatisticsResp

    /**
     * 商品详情
     */
    fun detail(req: ProductPendingLazadaDetailReq): ProductPendingLazadaDetailResp

    /**
     * 商品更新
     */
    fun update(req: ProductPendingUpdateLazadaReq)

    /**
     * 任务操作-开始任务
     */
    fun start(lazadaSpuIds: List<Long>)

    /**
     * 任务操作-更换人员
     */
    fun change(req: ProductPendingLazadaTaskChangeReq)

    /**
     * 任务操作-取消任务
     */
    fun cancel(req: ProductPendingLazadaTaskCancelReq)

    /**
     * 上架记录-统计
     */
    fun publishLogStatistics(): ProductPendingPublishLogStatisticsResp

    /**
     * 上架记录-分页列表
     */
    fun publishLogPage(req: ProductPendingPublishLogPageReq): PageVo<ProductPendingPublishLogPageResp>

    /**
     * 导入-待上架-商品数据
     */
    fun importExcel(file: MultipartFile)
}
