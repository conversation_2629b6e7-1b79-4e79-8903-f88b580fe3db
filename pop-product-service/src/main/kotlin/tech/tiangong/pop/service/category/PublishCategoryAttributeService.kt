package tech.tiangong.pop.service.category

import jakarta.servlet.http.HttpServletResponse
import org.springframework.web.multipart.MultipartFile
import tech.tiangong.pop.req.category.CategoryAttrListReq
import tech.tiangong.pop.req.category.CategoryAttributeCopyByIdReq
import tech.tiangong.pop.req.category.CategoryAttributeCopyReq
import tech.tiangong.pop.req.category.CategoryAttributeEditReq
import tech.tiangong.pop.req.category.CopyCategoryAttributeValueMappingReq
import tech.tiangong.pop.req.category.SaveCategoryAttributeValueMappingReq
import tech.tiangong.pop.resp.category.CategoryAttributeCopyByIdResp
import tech.tiangong.pop.resp.category.CategoryAttributeCopyResp
import tech.tiangong.pop.resp.category.CategoryAttributeRefResp
import tech.tiangong.pop.resp.category.CategoryAttributeWithValuesResp
import tech.tiangong.pop.resp.category.PlatformCategoryAttributeMappingDetailResp
import tech.tiangong.pop.resp.category.PublishCategoryMappingTreeNodeVo

/**
 * 品类属性关联service
 */
interface PublishCategoryAttributeService {
    fun removePlatformAttr(categoryMappingId: Long)

    @Deprecated(message = "用saveCategoryAttributeValueMapping代替")
    fun refCategoryAttribute(req: CategoryAttributeEditReq)
    @Deprecated(message = "用getPlatformCategoryAttributeWithMappingValuesByCategoryMappingId代替")
    fun findByCategoryAttrIds(req: CategoryAttrListReq): CategoryAttributeRefResp

    /**
     * 失效所有的aliexpress类目属性缓存
     */
    fun invalidateAllAliexpressCategoryAttributes()

    /**
     * 复制品类属性
     *
     * @param req 品类属性复制请求
     * @return 品类属性复制响应
     */
    fun copyAttributes(req: CategoryAttributeCopyReq): CategoryAttributeCopyResp

    /**
     * 复制品类属性（基于ID）
     */
    fun copyAttributesById(req: CategoryAttributeCopyByIdReq): CategoryAttributeCopyByIdResp

    /**
     * 根据本地品类ID查询关联的属性及属性值
     */
    fun getCategoryAttributeWithValuesByCategoryId(categoryId:Long):List<CategoryAttributeWithValuesResp>
    /**
     * 根据已关联品类ID，查询第三方平台的品类属性,本地品类的属性及已映射第三方平台的属性值
     */
    fun getPlatformAttributeMappingDetailByCategoryMappingId(categoryMappingId:Long): PlatformCategoryAttributeMappingDetailResp

    /**
     * 更新第三方平台属性必填字段
     */
    fun updatePlatformAttributeRequestFlag(categoryMappingId: Long)
    /**
     * 保存本地属性值与平台属性值的映射关系
     */
    fun saveCategoryAttributeValueMapping(req: SaveCategoryAttributeValueMappingReq)

    /**
     * 复制本地属性值与平台属性值的映射关系
     */
    fun copyCategoryAttributeValueMapping(req: CopyCategoryAttributeValueMappingReq)
    /**
     * 导入本地品类属性对应平台品类属性值的映射
     */
    fun importCategoryAttributeMapping(file: MultipartFile,categoryMappingId:Long)

    /**
     * 导出本地品类属性对应平台品类属性值的映射
     */
    fun exportCategoryAttributeMapping(categoryMappingId:Long,response: HttpServletResponse)

    /**
     * 更新品类平台关联状态
     */
    fun changePublishCategoryPlatformAssociateState(categoryId:Long);
}
