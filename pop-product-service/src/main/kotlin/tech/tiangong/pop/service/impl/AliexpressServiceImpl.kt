package tech.tiangong.pop.service.impl

import com.aliexpress.open.domain.AliexpressPostproductRedefiningFindproductinfolistqueryAeopAEProductListQuery
import com.aliexpress.open.request.*
import com.aliexpress.open.response.AliexpressPostproductRedefiningEditsimpleproductfiledResponse
import com.aliexpress.open.response.AliexpressPostproductRedefiningOnlineaeproductResponse
import com.global.iop.api.IopClient
import com.global.iop.api.IopClientImpl
import com.global.iop.api.IopRequest
import com.global.iop.domain.Protocol
import com.global.iop.util.FileItem
import org.apache.commons.lang3.StringUtils
import org.springframework.stereotype.Service
import team.aikero.blade.core.enums.Bool
import team.aikero.blade.core.toolkit.isNotBlank
import team.aikero.blade.core.toolkit.isNotNull
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.util.json.parseJson
import team.aikero.blade.util.json.toJson
import tech.tiangong.pop.aspect.annotation.ExternalLogApiCall
import tech.tiangong.pop.common.enums.PlatformEnum
import tech.tiangong.pop.common.exception.AliexpressApiException
import tech.tiangong.pop.common.exception.BaseBizException
import tech.tiangong.pop.config.AliexpressProperties
import tech.tiangong.pop.dao.entity.ProductSyncLog
import tech.tiangong.pop.dao.repository.AeSaleGoodsRepository
import tech.tiangong.pop.dao.repository.ProductSyncLogRepository
import tech.tiangong.pop.dao.repository.ShopRepository
import tech.tiangong.pop.enums.PlatformOperatorTypeEnum
import tech.tiangong.pop.enums.PlatformSyncStateEnum
import tech.tiangong.pop.external.ExternalApiExecutor
import tech.tiangong.pop.req.sdk.ae.AliexpressProductInfoRequest
import tech.tiangong.pop.req.sdk.ae.AliexpressQueryRegulatoryAttributesInfoRequest
import tech.tiangong.pop.req.sdk.ae.AliexpressSelectRegulatoryAttributesOptionsRequest
import tech.tiangong.pop.req.sdk.ae.AliexpressSetProductGroupsRequest
import tech.tiangong.pop.resp.sdk.aliexpress.*
import tech.tiangong.pop.service.AliexpressService
import tech.tiangong.pop.utils.parseAliResponse
import java.io.File


@Slf4j
@Service
class AliexpressServiceImpl(
    private val aliexpressProperties: AliexpressProperties,
    private val shopRepository: ShopRepository,
    private val productSyncLogRepository: ProductSyncLogRepository,
    private val aeSaleGoodsRepository: AeSaleGoodsRepository,
) : AliexpressService {

    private val iopClient: IopClient by lazy {
        IopClientImpl(
            aliexpressProperties.aePlatform.domain,
            aliexpressProperties.aePlatform.appKey,
            aliexpressProperties.aePlatform.appSecret
        )
    }

    @ExternalLogApiCall(businessIdParamName = "productId")
    override fun postProduct(
        productId: Long,
        accessToken: String,
        productInfo: AliexpressProductInfoRequest,
    ): AliexpressProductEditResponse {
        val request = AliexpressOfferProductPostRequest()
        request.addApiParameter("aeop_a_e_product", productInfo.toJson())
        ExternalApiExecutor.executeAliexpressApi("postProduct") {
            val response = iopClient.execute(request, accessToken, Protocol.TOP)
            saveSyncLog(productId, request.toJson(), response.gopResponseBody!!, PlatformOperatorTypeEnum.ACTIVE, response.gopErrorSubMsg)
            return response.parseAliResponse()
        }
    }

    @ExternalLogApiCall(businessIdParamName = "productId")
    override fun editProduct(productId: Long, accessToken: String, productInfo: AliexpressProductInfoRequest): AliexpressProductEditResponse {
        val request = AliexpressOfferProductEditRequest()
        request.addApiParameter("aeop_a_e_product", productInfo.toJson())
        ExternalApiExecutor.executeAliexpressApi("editProduct") {
            val response = iopClient.execute(request, accessToken, Protocol.TOP)
            saveSyncLog(productId, request.toJson(), response.gopResponseBody!!, PlatformOperatorTypeEnum.UPDATE_PRODUCT, response.gopErrorSubMsg)
            return response.parseAliResponse()
        }
    }

    override fun queryProduct(platformProductId: Long, accessToken: String): AliexpressProductQueryResponse {
        val request = AliexpressOfferProductQueryRequest().apply {
            this.productId = platformProductId
        }
        ExternalApiExecutor.executeAliexpressApi("queryProduct") {
            val response = iopClient.execute(request, accessToken, Protocol.TOP)
            if (response.gopErrorCode == "ApiCallLimit" || response.gopErrorSubCode == "ApiCallLimit") {
                log.warn { "查询商品列表接口调用次数超限 platformProductId=$platformProductId, 休眠1秒重试1次" }
                Thread.sleep(1000)
                val response = iopClient.execute(request, accessToken, Protocol.TOP)
                return response.parseAliResponse()
            }
            return response.parseAliResponse()
        }
    }

    /**
     * 分页查询商品列表
     *
     * @param req 分页参数
     * @param accessToken 访问令牌
     * @return 商品列表响应
     */
    override fun pageProduct(req: AliexpressPostproductRedefiningFindproductinfolistqueryAeopAEProductListQuery, accessToken: String): AliexpressProductPageResponse {
        val request = AliexpressPostproductRedefiningFindproductinfolistqueryRequest().apply {
            this.aeopAEProductListQuery = req
        }
        ExternalApiExecutor.executeAliexpressApi("pageProduct") {
            val resp = iopClient.execute(request, accessToken, Protocol.TOP)
            return resp.parseAliResponse()
        }
    }

    override fun queryCategoryTreeList(
        accessToken: String?,
        channelSellerId: Long?,
        onlyWithPermission: Boolean,
        channel: String?,
        categoryId: Long?,
    ): AliexpressCategoryTreeListResponse {
        var innerAccessToken = accessToken
        if (StringUtils.isBlank(accessToken)) {
            val shop = shopRepository.getById(aliexpressProperties.aePlatform.tokenShopId)
            innerAccessToken = shop?.token
                ?: throw BaseBizException("查询速卖通品类时获取速卖通令牌异常")
        }

        val request = AliexpressCategoryTreeListRequest().apply {
            this.channelSellerId = channelSellerId
            this.onlyWithPermission = onlyWithPermission
            this.channel = channel
            this.categoryId = categoryId
        }

        ExternalApiExecutor.executeAliexpressApi("queryCategoryTreeList") {
            val response = iopClient.execute(request, innerAccessToken, Protocol.GOP)
            return response.parseAliResponse()
        }
    }

    override fun queryCategoryAttributes(
        accessToken: String?,
        categoryId: Long,
        param2: String?,
        locale: String?,
        channel: String?,
        productType: String?,
    ): AliexpressCategoryAttributeResponse {
        var innerAccessToken = accessToken
        if (StringUtils.isBlank(accessToken)) {
            val shop = shopRepository.getById(aliexpressProperties.aePlatform.tokenShopId)
            innerAccessToken = shop?.token
                ?: throw BaseBizException("查询速卖通品类属性时获取速卖通令牌异常")
        }

        val request = AliexpressCategoryRedefiningGetchildattributesresultbypostcateidandpathRequest().apply {
            // param1是必填的，对应categoryId
            this.param1 = categoryId
            if (param2.isNotBlank()) this.param2 = listOf(param2)
            // 以下是可选参数，根据需要设置
            this.locale = locale
            this.channel = channel
            this.productType = productType
        }

        ExternalApiExecutor.executeAliexpressApi("queryCategoryAttributes") {
            val response = iopClient.execute(request, innerAccessToken, Protocol.GOP)
            return response.parseAliResponse()
        }
    }

    override fun refreshToken(refreshToken: String): AliexpressAuthTokenRefreshResponse {
        val request = AuthTokenRefreshRequest().apply {
            this.refreshToken = refreshToken
        }

        ExternalApiExecutor.executeAliexpressApi("refreshToken") {
            val response = iopClient.execute(request, Protocol.GOP)
            return response.parseAliResponse()
        }
    }

    override fun createToken(code: String): AliexpressAuthTokenResponse {
        val request = AuthTokenCreateRequest().apply {
            this.code = code
        }
        ExternalApiExecutor.executeAliexpressApi("createToken") {
            val response = iopClient.execute(request, Protocol.GOP)
            return response.parseAliResponse()
        }
    }

    override fun uploadImageFileToTempDirectory(
        accessToken: String?,
        file: File,
    ): AliexpressImageUploadResponse {
        var innerAccessToken = accessToken
        if (StringUtils.isBlank(accessToken)) {
            val shop = shopRepository.getById(aliexpressProperties.aePlatform.tokenShopId)
            innerAccessToken = shop?.token
                ?: throw BaseBizException("查询上传图片到速卖通临时目录时获取速卖通令牌异常")
        }

        val fileItem = FileItem(file)
        val request = AliexpressImageRedefiningUploadtempimageforsdkRequest().apply {
            this.fileData = fileItem
            this.srcFileName = file.name
        }

        ExternalApiExecutor.executeAliexpressApi("uploadImageFileToTempDirectory") {
            val response = iopClient.execute(request, innerAccessToken, Protocol.TOP)

            return response.parseAliResponse()
        }
    }

    override fun queryFreightTemplateList(
        accessToken: String,
        channelSellerId: String?,
    ): AliexpressFreightTemplateResponse {
        val request = AliexpressFreightRedefiningListfreighttemplateRequest().apply {
            if (!channelSellerId.isNullOrBlank()) {
                this.addApiParameter("channel_seller_id", channelSellerId)
            }
        }
        ExternalApiExecutor.executeAliexpressApi("queryFreightTemplateList") {
            val response = iopClient.execute(request, accessToken, Protocol.TOP)

            return response.parseAliResponse()
        }
    }

    override fun queryPromiseTemplates(
        accessToken: String,
        templateId: Long,
    ): AliexpressPromiseTemplateResponse {
        val request = AliexpressPostproductRedefiningQuerypromisetemplatebyidRequest().apply {
            this.templateId = templateId
        }

        ExternalApiExecutor.executeAliexpressApi("queryPromiseTemplates") {
            val response = iopClient.execute(request, accessToken, Protocol.GOP)

            return response.parseAliResponse()
        }
    }

    /**
     * 通过模板ID获取单个运费模板内容
     *
     * @param accessToken 访问令牌（必需）
     * @param templateId 服务模板ID（必须）
     * @return 服务模板查询响应
     */
    override fun getFreightSettingByTemplateQuery(
        accessToken: String,
        templateId: Long,
    ): AliexpressFreightTemplateDetailResponse {
        log.info { "调用AE接口 通过模板ID获取单个运费模板内容[getFreightSettingByTemplateQuery] 请求: templateId=$templateId" }
        ExternalApiExecutor.executeAliexpressApi("getFreightSettingByTemplateQuery") {
            val request = IopRequest()
            request.apiName = "aliexpress.freight.redefining.getfreightsettingbytemplatequery"
            request.addApiParameter("template_id", templateId.toString())
            val response = iopClient.execute(request, accessToken, Protocol.TOP)
            log.info { "调用AE接口 通过模板ID获取单个运费模板内容[getFreightSettingByTemplateQuery] 响应: ${response.toJson()}" }
            return if (response.isSuccess) {
                if (response.gopResponseBody.isNotBlank()) {
                    response.gopResponseBody.parseJson(AliexpressFreightTemplateDetailResponse::class.java)
                } else {
                    AliexpressFreightTemplateDetailResponse(
                        templateId = templateId.toString(),
                        isSuccess = true,
                        errorDesc = "调用AE接口 通过模板ID获取单个运费模板内容[getFreightSettingByTemplateQuery] 失败, 响应结果为空",
                    )
                }
            } else {
                throw AliexpressApiException("调用AE接口 通过模板ID获取单个运费模板内容[getFreightSettingByTemplateQuery] 失败 " + response.toJson())
            }
        }
    }

    override fun setSizeChart(
        accessToken: String,
        productId: Long,
        sizeChartId: Long,
        channel: String?,
        channelSellerId: String?,
    ): AliexpressSetSizeChartResponse {
        val request = AliexpressPostproductRedefiningSetsizechartRequest().apply {
            this.productId = productId
            this.sizechartId = sizeChartId

            // 设置可选参数
            if (!channel.isNullOrBlank()) {
                this.channel = channel
            }
            if (!channelSellerId.isNullOrBlank()) {
                this.channelSellerId = channelSellerId
            }
        }

        ExternalApiExecutor.executeAliexpressApi("setSizeChart") {
            val response = iopClient.execute(request, accessToken, Protocol.GOP)

            return response.parseAliResponse()
        }
    }

    override fun onlineAeProduct(
        accessToken: String,
        platformProductIds: List<String>,
    ): AliexpressPostproductRedefiningOnlineaeproductResponse {
        // 描述：上架一个或者多个商品，待上架的产品ID通过参数productIds指定，产品ID之间使用英文分号(;)隔开, 最多一次只能上架50个商品
        if (platformProductIds.size > 50) {
            throw BaseBizException("AE: 一次最多只能上架50个商品")
        }
        val productIds = platformProductIds.joinToString(",")
        val request = AliexpressPostproductRedefiningOnlineaeproductRequest().apply {
            this.productIds = productIds
        }
        ExternalApiExecutor.executeAliexpressApi(request.apiName) {
            val response = iopClient.execute(request, accessToken, Protocol.TOP)
            platformProductIds.forEach { productId ->
                saveSyncLog(productId.toLong(), request.toJson(), response.gopResponseBody!!, PlatformOperatorTypeEnum.ACTIVE, response.gopErrorSubMsg)
            }
            if (!response.isSuccess) {
                throw AliexpressApiException("调用AE接口 [${request.apiName}] 失败: ${response.gopResponseBody?.toJson()}")
            }
            return response.gopResponseBody.parseJson(AliexpressPostproductRedefiningOnlineaeproductResponse::class.java)
        }
    }

    override fun offlineAeProduct(
        accessToken: String,
        platformProductIds: List<String>,
    ): AeOfflineProductResp {
        // 描述：商品下架接口。需要下架的商品的通过productIds参数指定，多个商品之间用英文分号隔开。
        val productIds = platformProductIds.joinToString(";")
        val request = AliexpressPostproductRedefiningOfflineaeproductRequest().apply {
            this.productIds = productIds
        }
        ExternalApiExecutor.executeAliexpressApi(request.apiName) {
            val response = iopClient.execute(request, accessToken, Protocol.TOP)
            log.info { "调用AE接口 [${request.apiName}] 响应: ${response.gopResponseBody}" }
            platformProductIds.forEach { productId ->
                saveSyncLog(productId.toLong(), request.toJson(), response.gopResponseBody!!, PlatformOperatorTypeEnum.INACTIVE, response.gopErrorSubMsg)
            }
            if (!response.isSuccess) {
                throw AliexpressApiException("调用AE接口 [${request.apiName}] 失败: ${response.gopResponseBody?.toJson()}")
            }
            val resultStr = response.gopResponseBody
            if (resultStr.isNullOrBlank()) {
                throw AliexpressApiException("调用AE接口 [${request.apiName}] 失败: 响应结果为空")
            }
            val result = resultStr.parseJson(AeOfflineProductResp::class.java)
            val dto = result.result
            if (dto != null) {
                val errorList = dto.errorDetails
                if (!errorList.isNullOrEmpty()) {
                    log.error { "调用AE接口 [${request.apiName}] 失败, 下架数量: ${platformProductIds.size}, 成功更新数量: ${dto.modifyCount} resp: ${errorList.toJson()}" }
                }
            }
            return result
        }
    }

    override fun querySizeTemplatesByCategory(
        accessToken: String,
        leafCategoryId: Long,
        currentPage: Long,
        channel: String?,
        channelSellerId: String?,
    ): AliexpressSizeTemplatesResponse {
        if (StringUtils.isBlank(accessToken)) {
            throw BaseBizException("查询类目尺码模板列表访问令牌不能为空")
        }

        val request = AliexpressOfferRedefiningGetsizetemplatesbycategoryidRequest().apply {
            this.leafCategoryId = leafCategoryId
            this.currentPage = currentPage
            if (!channel.isNullOrBlank()) {
                this.channel = channel
            }
            if (!channelSellerId.isNullOrBlank()) {
                this.channelSellerId = channelSellerId
            }
        }

        ExternalApiExecutor.executeAliexpressApi("querySizeTemplatesByCategory") {
            val response = iopClient.execute(request, accessToken, Protocol.GOP)

            return response.parseAliResponse()
        }
    }

    override fun queryMsrList(
        accessToken: String,
        channelSellerId: Long?,
        channel: String?,
    ): AliexpressMsrListResponse {
        val request = IopRequest().apply {
            apiName = "aliexpress.merchant.msr.list"
            channelSellerId?.let {
                addApiParameter("channel_seller_id", it.toString())
            }
            channel?.let {
                addApiParameter("channel", it)
            }
        }
        ExternalApiExecutor.executeAliexpressApi("queryMsrList") {
            val response = iopClient.execute(request, accessToken, Protocol.GOP)

            return response.parseAliResponse()
        }
    }

    override fun queryManufactureDetail(
        accessToken: String,
        manufactureId: Long,
        channelSellerId: Long?,
        channel: String?,
    ): AliexpressManufactureDetailResponse {
        val request = IopRequest().apply {
            apiName = "aliexpress.merchant.manufacture.detail"
            addApiParameter("id", manufactureId.toString())
            channelSellerId?.let {
                addApiParameter("channel_seller_id", it.toString())
            }
            channel?.let {
                addApiParameter("channel", it)
            }
        }
        ExternalApiExecutor.executeAliexpressApi("queryManufactureDetail") {
            val response = iopClient.execute(request, accessToken, Protocol.GOP)
            return response.parseAliResponse()
        }

    }

    override fun queryProductStatus(
        accessToken: String,
        productId: Long,
    ): AliexpressProductStatusResponse {
        val request = AliexpressPostproductRedefiningFindaeproductstatusbyidRequest().apply {
            this.productId = productId
        }
        ExternalApiExecutor.executeAliexpressApi("queryProductStatus") {
            val response = iopClient.execute(request, accessToken, Protocol.TOP)

            return response.parseAliResponse()
        }
    }

    override fun queryManufactureList(
        accessToken: String,
        channelSellerId: Long?,
        channel: String?,
    ): AliexpressManufactureListResponse {
        val request = IopRequest().apply {
            apiName = "aliexpress.merchant.manufacture.list"
            channelSellerId?.let {
                addApiParameter("channel_seller_id", it.toString())
            }
            channel?.let {
                addApiParameter("channel", it)
            }
        }
        ExternalApiExecutor.executeAliexpressApi("queryManufactureList") {
            val response = iopClient.execute(request, accessToken, Protocol.GOP)

            return response.parseAliResponse()
        }
    }

    override fun querySellerRelations(token: String): AliexpressSellerRelationResponse {
        val request = GlobalSellerRelationQueryRequest()

        val response = iopClient.execute(request, token, Protocol.GOP)

        return response.parseAliResponse()
    }

    override fun queryProductGroups(accessToken: String): AliexpressProductGroupsResponse {
        val request = AliexpressProductProductgroupsGetRequest()

        ExternalApiExecutor.executeAliexpressApi("queryProductGroups") {
            val response = iopClient.execute(request, accessToken, Protocol.TOP)

            return response.parseAliResponse()
        }
    }

    /**
     * 编辑单商品多sku价格
     *
     * @param accessToken 访问令牌（必需）
     * @param productId 商品ID（必需）
     * @param skuPrices SKU价格映射（必需）{"skuId 1": "价格", "skuId 2": "价格"}
     */
    override fun updateProductSkuPrices(
        accessToken: String,
        productId: Long,
        skuPrices: Map<String, String>,
    ): AeUpdateSkuPricesResp {
        val request = AliexpressOfferProductSkupricesEditRequest().apply {
            this.productId = productId
            val skuIdPriceMap: HashMap<String, Any> = HashMap()
            skuPrices.forEach { (skuId, price) ->
                skuIdPriceMap[skuId] = price
            }
            this.skuIdPriceMap = skuIdPriceMap
        }
        ExternalApiExecutor.executeAliexpressApi(request.apiName) {
            log.info { "调用AE接口 [${request.apiName}] 请求: ${request.toJson()}" }
            val response = iopClient.execute(request, accessToken, Protocol.TOP)
            log.info { "调用AE接口 [${request.apiName}] 响应: ${response.gopResponseBody}" }
            saveSyncLog(productId, request.toJson(), response.gopResponseBody!!, PlatformOperatorTypeEnum.MODIFY_PRICE, response.gopErrorSubMsg)
            if (!response.isSuccess) {
                throw AliexpressApiException("调用AE接口 [${request.apiName}] 失败: ${response.gopResponseBody?.toJson()}")
            }
            val resultStr = response.gopResponseBody
            if (resultStr.isNullOrBlank()) {
                throw AliexpressApiException("调用AE接口 [${request.apiName}] 失败: 响应结果为空")
            }
            val result = resultStr.parseJson(AeUpdateSkuPricesResp::class.java)
            val dto = result.result
            if (dto != null) {
                val errorList = dto.errorDetails
                if (!dto.errorDetails.isNullOrEmpty()) {
                    log.error { "调用AE接口 [${request.apiName}] 失败, 修改价格SKU数量: ${skuPrices.size}, 成功更新数量: ${dto.modifyCount} resp: ${errorList?.toJson()}" }
                }
            }
            return result
        }
    }

    /**

     * 编辑单商品多sku库存
     *
     * @param accessToken 访问令牌（必需）
     * @param productId 商品ID（必需）
     * @param skuStocks SKU库存映射（必需）{"skuId 1": "库存", "skuId 2": "库存"}
     */
    override fun updateProductSkuStocks(
        accessToken: String,
        productId: Long,
        skuStocks: Map<String, Long>,
    ): AeUpdateSkuStocksResp {
        val request = AliexpressPostproductRedefiningEditmutilpleskustocksRequest().apply {
            this.productId = productId
            val skuIdStocksMap: HashMap<String, Any> = HashMap()
            skuStocks.forEach { (skuId, stock) ->
                skuIdStocksMap[skuId] = stock
            }
            this.skuStocks = skuIdStocksMap
        }
        ExternalApiExecutor.executeAliexpressApi(request.apiName) {
            log.info { "调用AE接口 [${request.apiName}] 请求: ${request.toJson()}" }
            val response = iopClient.execute(request, accessToken, Protocol.TOP)
            log.info { "调用AE接口 [${request.apiName}] 响应: ${response.gopResponseBody}" }
            saveSyncLog(productId, request.toJson(), response.gopResponseBody!!, PlatformOperatorTypeEnum.MODIFY_STOCK, response.gopErrorSubMsg)
            if (!response.isSuccess) {
                throw AliexpressApiException("调用AE接口 [${request.apiName}] 失败: ${response.gopResponseBody?.toJson()}")
            }
            val resultStr = response.gopResponseBody
            if (resultStr.isNullOrBlank()) {
                throw AliexpressApiException("调用AE接口 [${request.apiName}] 失败: 响应结果为空")
            }
            val result = resultStr.parseJson(AeUpdateSkuStocksResp::class.java)
            val dto = result.result
            if (dto != null) {
                val errorList = dto.errorDetails
                if (!dto.errorDetails.isNullOrEmpty()) {
                    log.error { "调用AE接口 [${request.apiName}] 失败, 修改库存SKU数量: ${skuStocks.size}, 成功更新数量: ${dto.modifyCount} resp: ${errorList?.toJson()}" }
                }
            }
            return result
        }
    }

    override fun queryRegulatoryAttributesInfo(
        accessToken: String,
        request: AliexpressQueryRegulatoryAttributesInfoRequest,
    ): AliexpressQueryRegulatoryAttributesInfoResponse {
        val apiRequest = IopRequest().apply {
            apiName = "aliexpress.trade.tax.hscode.queryRegulatoryAttributesInfo"
        }

        // 添加必要参数
        apiRequest.addApiParameter("language", request.language ?: "zh_CN")
        apiRequest.addApiParameter("source", request.source ?: "ISV")
        apiRequest.addApiParameter("title", request.title)
        apiRequest.addApiParameter("scene", request.scene)
        apiRequest.addApiParameter("item_id", request.itemId)
        apiRequest.addApiParameter("seller_id", request.sellerId.toString())
        apiRequest.addApiParameter("image_url", request.imageUrl)
        apiRequest.addApiParameter("tax_type", request.taxType ?: "DUTY")

        // 添加JSON格式参数
        request.extendInfo?.let {
            apiRequest.addApiParameter("extend_info", it)
        }

        request.elementMap?.let {
            apiRequest.addApiParameter("element_map", it.toJson())
        }

        // 添加可选参数
        request.hsCode?.let {
            apiRequest.addApiParameter("hs_code", it)
        }

        // 处理类目列表（二选一）
        if (request.categoryListJson.isNotBlank()) {
            apiRequest.addApiParameter("category_list_json", request.categoryListJson)
        } else {
            apiRequest.addApiParameter("category_list", request.categoryList!!.toJson())
        }

        // 处理已选属性值列表（二选一）
        if (request.selectedPropertyValueListJson.isNotBlank()) {
            apiRequest.addApiParameter("selected_property_value_list_json", request.selectedPropertyValueListJson)
        } else if (request.selectedPropertyValueList != null) {
            apiRequest.addApiParameter("selected_property_value_list", request.selectedPropertyValueList!!.toJson())
        }

        ExternalApiExecutor.executeAliexpressApi("queryRegulatoryAttributesInfo") {
            val response = iopClient.execute(apiRequest, accessToken, Protocol.TOP)
            return response.parseAliResponse()
        }
    }

    override fun batchQueryRegulatoryAttributesInfo(
        accessToken: String,
        requests: List<AliexpressQueryRegulatoryAttributesInfoRequest>,
    ): AliexpressQueryRegulatoryAttributesInfoResponse {
        require(requests.size <= 10) { "批量查询请求数量不能超过10条" }

        val apiRequest = IopRequest().apply {
            apiName = "aliexpress.trade.tax.hscode.batchQueryRegulatoryAttributesInfo"
        }

        val batchRequestsJson = requests.map { request ->
            mapOf(
                "language" to (request.language ?: "zh_CN"),
                "source" to (request.source ?: "ISV"),
                "title" to request.title,
                "scene" to request.scene,
                "item_id" to request.itemId,
                "seller_id" to request.sellerId,
                "image_url" to request.imageUrl,
                "tax_type" to (request.taxType ?: "DUTY"),
                "extend_info" to (request.extendInfo),
                "element_map" to (request.elementMap?.toJson()),
                "hs_code" to request.hsCode,
                "category_list_json" to request.categoryListJson,
                "selected_property_value_list_json" to request.selectedPropertyValueListJson,
                "category_list" to (if (request.categoryListJson.isNullOrEmpty() && request.categoryList != null)
                    request.categoryList!!.toJson() else null),
                "selected_property_value_list" to (if (request.selectedPropertyValueListJson.isNullOrEmpty() && request.selectedPropertyValueList != null)
                    request.selectedPropertyValueList!!.toJson() else null)
            ).filterValues { it != null }
        }.toJson()

        // TODO 确认这里如何设置入参变量名
        apiRequest.addApiParameter("batch_requests", batchRequestsJson)

        ExternalApiExecutor.executeAliexpressApi("batchQueryRegulatoryAttributesInfo") {
            val response = iopClient.execute(apiRequest, accessToken, Protocol.TOP)
            return response.parseAliResponse()
        }
    }

    override fun selectRegulatoryAttributesOptions(
        accessToken: String,
        request: AliexpressSelectRegulatoryAttributesOptionsRequest,
    ): AliexpressSelectRegulatoryAttributesOptionsResponse {
        val apiRequest = IopRequest().apply {
            apiName = "aliexpress.trade.tax.hscode.selectRegulatoryAttributesOptions"
        }

        // 添加必要参数
        apiRequest.addApiParameter("language", request.language ?: "zh_CN")
        apiRequest.addApiParameter("source", request.source ?: "ISV")
        apiRequest.addApiParameter("title", request.title)
        apiRequest.addApiParameter("scene", request.scene)
        apiRequest.addApiParameter("item_id", request.itemId)
        apiRequest.addApiParameter("seller_id", request.sellerId.toString())
        apiRequest.addApiParameter("image_url", request.imageUrl)
        apiRequest.addApiParameter("tax_type", request.taxType ?: "DUTY")

        // 处理扩展信息
        request.extendInfo?.let {
            apiRequest.addApiParameter("extend_info", it)
        }

        // 处理商品属性信息
        request.elementMap?.let {
            apiRequest.addApiParameter("element_map", it.toJson())
        }

        // 处理类目列表（二选一）
        if (request.categoryList.isNotNull()) {
            apiRequest.addApiParameter("category_list", request.categoryList!!.toJson())
        } else if (request.categoryListJson.isNotBlank()) {
            apiRequest.addApiParameter("category_list_json", request.categoryListJson)
        }

        // 处理已选属性值列表（二选一）
        if (request.selectedPropertyValueList.isNotNull()) {
            apiRequest.addApiParameter("selected_property_value_list", request.selectedPropertyValueList!!.toJson())
        } else if (request.selectedPropertyValueListJson.isNotBlank()) {
            apiRequest.addApiParameter("selected_property_value_list_json", request.selectedPropertyValueListJson)
        }

        ExternalApiExecutor.executeAliexpressApi("selectRegulatoryAttributesOptions") {
            val response = iopClient.execute(apiRequest, accessToken, Protocol.TOP)
            return response.parseAliResponse()
        }
    }

    @ExternalLogApiCall(businessIdParamName = "productId")
    override fun setProductGroups(productId: Long, request: AliexpressSetProductGroupsRequest, accessToken: String): AliexpressSetProductGroupsResponse {
        val validationError = request.validate()
        if (validationError != null) {
            throw IllegalArgumentException("设置商品分组参数验证失败: $validationError")
        }

        val aliRequest = AliexpressPostproductRedefiningSetgroupsRequest().apply {
            this.productId = request.platformProductId
            this.groupIds = request.getGroupIdsString()
        }

        ExternalApiExecutor.executeAliexpressApi("setProductGroups") {
            val response = iopClient.execute(aliRequest, accessToken, Protocol.TOP)
            return response.parseAliResponse<AliexpressSetProductGroupsResponse>()
        }
    }

    /**
     * 编辑商品的单个字段
     * @see tech.tiangong.pop.enums.ae.AeEditFiedEnum
     *
     * @param req.productId 商品ID（必需） 日志记录关联业务ID
     * @param req.fiedName （必需） AeEditFiedEnum
     * @param req.fiedValue （必需） AeEditFiedEnum不同类型, 不同value
     * @param accessToken 访问令牌（必需）
     * @return
     */
    override fun editSimpleProductFiled(req: AliexpressPostproductRedefiningEditsimpleproductfiledRequest, accessToken: String): AliexpressPostproductRedefiningEditsimpleproductfiledResponse {
        ExternalApiExecutor.executeAliexpressApi("editSimpleProductFiled") {
            return iopClient.execute(req, accessToken, Protocol.TOP)
        }
    }

    /**
     * 添加成功同步日志
     */
     fun saveSyncLog(pid: Long, reqStr: String?, respStr: String?, opType: PlatformOperatorTypeEnum?, errorMsg: String? = null) {
        try {
            val saleGoods = aeSaleGoodsRepository.getByPlatformProductId(pid)
            if (saleGoods != null) {
                if (StringUtils.isNotBlank(errorMsg)) {
                    // 更新状态为失败
                    saleGoods.platformSyncState = PlatformSyncStateEnum.FAILURE.code
                    aeSaleGoodsRepository.updateById(saleGoods)
                }

                val productSyncLog = ProductSyncLog().apply {
                    this.productId = saleGoods.productId
                    this.saleGoodId = saleGoods.saleGoodsId
                    this.opType = opType?.code
                    this.logType = if (StringUtils.isNotBlank(errorMsg)) Bool.NO.code else Bool.YES.code
                    this.platformName = PlatformEnum.AE.platformName
                    this.shopName = saleGoods.shopName
                    this.platformRequestParams = reqStr
                    this.platformHttpResp = respStr
                    this.errorMsg = errorMsg
                }
                productSyncLogRepository.save(productSyncLog)
            }
        } catch (e: Exception) {
            log.error(e) { "记录同步日志异常 pid: $pid, req:$reqStr, resp:$respStr" }
        }
    }
}
