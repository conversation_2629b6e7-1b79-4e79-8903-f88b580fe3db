package tech.tiangong.pop.service.product

import org.springframework.web.multipart.MultipartFile
import team.aikero.blade.core.protocol.PageVo
import tech.tiangong.pop.common.dto.CreateProductDto
import tech.tiangong.pop.dao.entity.ImageRepository
import tech.tiangong.pop.dao.entity.PublishCategory
import tech.tiangong.pop.dto.product.ImportAeProductDTO
import tech.tiangong.pop.dto.product.ImportProductDTO
import tech.tiangong.pop.dto.product.ImportTemuProductDTO
import tech.tiangong.pop.dto.product.ProductInitDto
import tech.tiangong.pop.dto.product.ProductInitDto.ProductInitSkcDto
import tech.tiangong.pop.req.product.*
import tech.tiangong.pop.req.product.manage.ProductManagePageReq
import tech.tiangong.pop.resp.product.ColorMapResp
import tech.tiangong.pop.resp.product.GrossMarginRecordResp
import tech.tiangong.pop.resp.product.PublishErrorLogResp
import tech.tiangong.pop.resp.product.UpdateProductGrossMarginResp
import tech.tiangong.pop.resp.product.manage.ProductManagePageResp

/**
 * 商品管理
 *
 * <AUTHOR>
 * @date 2025-3-29 21:15:30
 */
interface ProductManageService {
    /**
     * 商品列表-分页
     * @param req
     * @return
     */
    fun page(req: ProductManagePageReq): PageVo<ProductManagePageResp>

    /**
     * 初始化模板表
     * @param initDto
     */
    fun initTemplateByProduct(initDto: ProductInitDto)

    /**
     * 初始化模板
     */
    fun initTemplate(dto: CreateProductDto): ProductInitDto {
        return ProductInitDto().apply {
            this.spuCode = dto.spuCode
            this.skcList = dto.dataList?.map { data ->
                ProductInitSkcDto().apply {
                    this.skcCode = data.skc
                    this.sizeList = data.skuList?.mapNotNull { it.sizeName }
                }
            }
        }
    }

    /**
     * 补录商品的款式类型
     */
    fun fillStyleType(req: FillProductStyleTypeReq)

    /**
     * 刷新商品的图包状态
     */
    fun refreshImagePackageState(req: RefreshProductImagePackageStateReq)

    fun initHisDataV2(file: MultipartFile)

    /**
     * 企划审核
     */
    fun planAudit(req: ProductPlanAuditReq)

    /**
     * 修改商品的毛利率配置
     */
    fun updateProductGrossMargin(req: UpdateProductGrossMarginReq): UpdateProductGrossMarginResp

    /**
     * 修改商品指定商品的毛利率配置
     */
    fun updateProductGrossMarginByShop(req: UpdateProductGrossMarginByShopReq)

    /**
     * 待上架查询上架失败原因
     */
    fun publishErrorLog(req: PendingErrorLogQueryReq): PageVo<PublishErrorLogResp>

    /**
     * 查询商品的毛利率调整记录
     */
    fun listGrossMarginRecordByProductId(productId:Long):List<GrossMarginRecordResp>
}
