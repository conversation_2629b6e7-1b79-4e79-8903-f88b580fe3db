package tech.tiangong.pop.service.product.impl

import com.alibaba.excel.EasyExcel
import com.baomidou.mybatisplus.extension.kotlin.KtQueryWrapper
import com.baomidou.mybatisplus.extension.plugins.pagination.Page
import org.apache.commons.collections4.CollectionUtils
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import org.springframework.web.multipart.MultipartFile
import team.aikero.blade.core.enums.Bool
import team.aikero.blade.core.exception.BusinessException
import team.aikero.blade.core.protocol.PageVo
import team.aikero.blade.core.toolkit.*
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.sequence.id.IdHelper
import team.aikero.blade.user.holder.CurrentUserHolder
import team.aikero.blade.util.assertion.Assert
import team.aikero.blade.util.json.parseJson
import team.aikero.blade.util.json.parseJsonList
import team.aikero.blade.util.json.toJson
import tech.tiangong.eis.temu.enums.TemuSiteEnum
import tech.tiangong.pop.common.constant.LazadaConstants
import tech.tiangong.pop.common.enums.*
import tech.tiangong.pop.common.enums.PlatformEnum.LAZADA
import tech.tiangong.pop.component.GenerateSellerSkuComponent
import tech.tiangong.pop.component.MarketStyleComponent
import tech.tiangong.pop.config.AliexpressProperties
import tech.tiangong.pop.config.CommonProperties
import tech.tiangong.pop.config.LazadaDefaultProperties
import tech.tiangong.pop.dao.entity.*
import tech.tiangong.pop.dao.entity.dto.ProductShopGrossMarginConfigDto
import tech.tiangong.pop.dao.entity.dto.ProductShopGrossMarginConfigDto.ShopGrossMarginConfigDto
import tech.tiangong.pop.dao.repository.*
import tech.tiangong.pop.dto.product.ProductAePropertyValueItemDTO
import tech.tiangong.pop.dto.product.ProductInitDto
import tech.tiangong.pop.dto.product.ProductInitDto.ProductInitSkcDto
import tech.tiangong.pop.dto.product.StyleHisDataUploadExcelDTO
import tech.tiangong.pop.enums.*
import tech.tiangong.pop.external.DesignClientExternal
import tech.tiangong.pop.helper.PageRespHelper
import tech.tiangong.pop.req.product.*
import tech.tiangong.pop.req.product.manage.ProductManagePageReq
import tech.tiangong.pop.resp.product.PublishErrorLogResp
import tech.tiangong.pop.resp.product.UpdateProductGrossMarginResp
import tech.tiangong.pop.resp.product.UpdateProductGrossMarginResp.RetailPriceDto
import tech.tiangong.pop.resp.product.UpdateProductGrossMarginResp.SalePriceDto
import tech.tiangong.pop.resp.product.center.ProductCenterPageShopResp
import tech.tiangong.pop.resp.product.manage.*
import tech.tiangong.pop.service.product.ProductManageService
import tech.tiangong.pop.service.product.ProductTitleGenerateService
import tech.tiangong.pop.service.product.price.ProductPricingServiceSelector
import tech.tiangong.pop.service.regionpricerule.RegionPriceRuleService
import tech.tiangong.pop.service.settings.CurrencyExchangeRateService
import tech.tiangong.pop.service.v2.pricing.AeTemplatePricingService
import tech.tiangong.pop.utils.ProductSizeUtils
import java.io.IOException
import java.math.BigDecimal
import java.math.RoundingMode
import java.time.LocalDateTime
import java.time.LocalTime
import java.util.*

/**
 * 商品管理
 *
 * <AUTHOR>
 * @date 2025-3-29 21:15:30
 */
@Service
@Slf4j
class ProductManageServiceImpl(
    private val productRepository: ProductRepository,
    private val productSkcRepository: ProductSkcRepository,
    private val shopRepository: ShopRepository,
    private val productBarCodeRepository: ProductBarCodeRepository,
    private val imageRepositoryRepository: ImageRepositoryRepository,
    private val productTemplateLazadaSkcRepository: ProductTemplateLazadaSkcRepository,
    private val productTemplateLazadaSkuRepository: ProductTemplateLazadaSkuRepository,
    private val productTemplateLazadaSpuRepository: ProductTemplateLazadaSpuRepository,
    private val productTemplateAeSkcRepository: ProductTemplateAeSkcRepository,
    private val productTemplateAeSkuRepository: ProductTemplateAeSkuRepository,
    private val productTemplateAeSpuRepository: ProductTemplateAeSpuRepository,
    private val lazadaDefaultProperties: LazadaDefaultProperties,
    private val productPricingServiceSelector: ProductPricingServiceSelector,
    private val aliexpressProperties: AliexpressProperties,
    private val productTagRepository: ProductTagRepository,
    private val productTemplateTemuSpuRepository: ProductTemplateTemuSpuRepository,
    private val productTemplateTemuSkuRepository: ProductTemplateTemuSkuRepository,
    private val productTemplateTemuSkcRepository: ProductTemplateTemuSkcRepository,
    private val generateSellerSkuComponent: GenerateSellerSkuComponent,
    private val productTitleGenerateService: ProductTitleGenerateService,
    private val marketStyleComponent: MarketStyleComponent,
    private val designClientExternal: DesignClientExternal,
    private val regionPriceRuleService: RegionPriceRuleService,
    private val aeTemplatePricingService: AeTemplatePricingService,
    private val commonProperties: CommonProperties,
    private val currencyExchangeRateService: CurrencyExchangeRateService,
    private val productSyncLogRepository: ProductSyncLogRepository,
    private val productPublishStoreRepository: ProductPublishStoreRepository,
) : ProductManageService {
    /**
     * 商品列表-分页
     * @param req
     * @return
     */
    override fun page(req: ProductManagePageReq): PageVo<ProductManagePageResp> {
        if (Objects.equals(Bool.YES.code, req.today)) {
            req.createdTimeStart = LocalDateTime.now().with(LocalTime.MIN)
            req.createdTimeEnd = LocalDateTime.now().with(LocalTime.MAX)
        }
        val page = productRepository.getManagePage(Page(req.pageNum.toLong(), req.pageSize.toLong()), req)
        val products = page.records
        if (CollectionUtils.isEmpty(products)) {
            return PageRespHelper.empty()
        }
        // 市场(一级节点)
        val marketMap = marketStyleComponent.getMarketNodeMap(1)

        val spuMainImageMap = mutableMapOf<String, String?>()
        val spuCodes = products.mapNotNull { it.spuCode }.distinct()
        if (spuCodes.isNotEmpty()) {
            val images = imageRepositoryRepository.listBySpuCodes(spuCodes)
            images
                ?.filter { image -> image.spuCode.isNotBlank() }
                ?.forEach { image ->
                    spuMainImageMap[image.spuCode!!] = image.mainUrl
                }
        }
        //准备店铺信息
        val shopList = shopRepository.list()
        val shopIdToMap = if(!shopList.isNullOrEmpty()){
            shopList.associateBy { it.shopId }
        }else{
            mutableMapOf()
        }

        val productIds = products.mapNotNull { it.productId }.distinct()
        //已上架店铺
        val productIdToPublishStoreMap = getPublishStoreByProductIds(productIds,shopIdToMap);
        // 提取标签处理逻辑到单独的方法
        val tagContext = collectTagData(productIds)
        val result = products.map { product ->
            val productId = product.productId!!
            val tagCodes = getCombinedTags(productId, tagContext)
            val resp = ProductManagePageResp().apply {
                this.productId = product.productId
                // 先找图库301, 空则用product的主图
                this.mainImgUrl = spuMainImageMap[product.spuCode] ?: product.mainImgUrl
                this.productType = product.productType
                this.supplyMode = product.supplyMode
                this.waves = product.waves
                this.spuCode = product.spuCode
                this.marketCode = product.marketCode
                this.marketName = marketMap?.get(product.marketCode)
                this.tagCodes = tagCodes
                this.shopList = mutableListOf<ProductManagePageShopResp>().apply {
                    this.add(ProductManagePageShopResp().apply {
                        this.shopId = product.shopId
                        this.shopName = product.shopName
                    })
                }.toList()
                this.countryList = product.countrys?.split("-")?.toList()
                this.categoryId = product.categoryId
                this.categoryCode = product.categoryCode
                this.categoryName = product.categoryName
                this.creatorId = product.creatorId
                this.creatorName = product.creatorName
                this.createdTime = product.createdTime
                this.reviserId = product.reviserId
                this.reviserName = product.reviserName
                this.revisedTime = product.revisedTime
                this.update = product.isUpdate
                this.priceException = product.priceException
                this.platformUpdateState = product.platformSyncState
                this.submitStatus = product.isSyncPlatform
                this.styleType = product.styleType
                this.imagePackageState = product.imagePackageState
                this.planAuditState = product.planAuditState
                this.planAuditorId = product.planAuditorId
                this.planAuditorName = product.planAuditorName
                this.planAuditTime = product.planAuditTime

                this.submitPlatformNameList = if (productIdToPublishStoreMap.isEmpty()
                    || productIdToPublishStoreMap[productId].isNullOrEmpty()
                ) {
                    product.submitPlatform?.parseJsonList(Long::class.java)?.mapNotNull { platformId ->
                        PlatformEnum.getByPlatformId(platformId)?.platformName
                    } ?: emptyList()
                } else {
                    productIdToPublishStoreMap[productId]!!.mapNotNull { it ->
                        PlatformEnum.getByPlatformId(it.platformId!!)?.platformName
                    }
                }
                this.submitShopList = mutableListOf<ProductCenterPageShopResp>().apply {
                    productIdToPublishStoreMap[productId]?.let {
                        this.addAll(it.map {
                            ProductCenterPageShopResp().apply {
                                this.shopId = product.shopId
                                this.shopName = product.shopName
                            }
                        })
                    }
                }.toList()
                //毛利率记录
                this.shopGrossMargin = product.shopGrossMargin?.parseJson<ProductShopGrossMarginConfigDto>().let {
                    it?.shopGrossMarginConfigList?.map {
                        ShopGrossMarginConfigResp().apply {
                            this.grossMargin = it.grossMargin
                            this.shopId = it.shopId
                            this.shopName = shopIdToMap[it.shopId]?.shopName
                        }
                    }
                }
                this.sourceType = product.sourceType
            }

            //查询区域定价相关字段
            val productRegionPriceRuleDto = regionPriceRuleService.getProductRegionPriceRule(product)
            //设置区域定价相关字段
            productRegionPriceRuleDto.let {
                resp.salePrice = SalePriceResp(it.salePrice?.salePriceMax, it.salePrice?.salePriceMin, it.salePrice?.salePriceUSDMax, it.salePrice?.salePriceUSDMin)
                resp.retailPrice = RetailPriceResp(it.retailPrice?.retailPriceMax, it.retailPrice?.retailPriceMin, it.retailPrice?.retailPriceUSDMax, it.retailPrice?.retailPriceUSDMin)
                resp.productCost = it.productCost
                resp.logisticsCost = LogisticsCostResp(it.logisticsCost?.logisticsCostMax, it.logisticsCost?.logisticsCostMin)
                resp.storageCost = StorageCostResp(it.storageCost?.storageCostMax, it.storageCost?.storageCostMin)
                resp.taxRate = it.taxRate
                resp.rejectRate = it.rejectRate
                resp.adCost = it.adCost
                resp.commission = it.commission
                resp.withdraw = it.withdraw
                resp.grossMargin = it.grossMargin
                resp.marketing = it.marketing
                resp.freightRate = FreightRateResp(it.freightRate?.freightRateMax, it.freightRate?.freightRateMin)
                resp.discountRate = it.discountRate
            }
            resp
        }
        return PageRespHelper.of(page.current.toInt(), page.total, result)
    }

    private fun getPublishStoreByProductIds(productIds: List<Long>, shopIdToMap: Map<Long?, Shop?>): MutableMap<Long, List<Shop>> {
        val productIdToPublishShopMap: MutableMap<Long, List<Shop>> = mutableMapOf()
        val publishStoreList = productPublishStoreRepository.listByProductIds(productIds)
        if (publishStoreList.isNotEmpty()) {
            val productIdToPublishStoreListMap = publishStoreList.groupBy { it.productId }
            productIdToPublishStoreListMap.forEach { (productId, publishStoreList) ->
                productIdToPublishShopMap.put(productId!!, publishStoreList.mapNotNull {
                    shopIdToMap[it.shopId]
                })
            }
        }
        return productIdToPublishShopMap
    }

    /**
     * 更新Lazada模板SKC和SKU
     */
    private fun upsertLazadaTemplateNewSkc(product: Product, skcList: List<ProductInitSkcDto>?) {
        if (skcList.isNullOrEmpty()) {
            log.error { "上游新增SKC, 尺码为空" }
            return
        }
        val templateSpuList = productTemplateLazadaSpuRepository.listNoCompletedBySpuCode(product.spuCode!!)
        if (templateSpuList.isEmpty()) {
            log.error { "上游新增SKC, Lazada待上架无待处理SPU" }
            return
        }

        templateSpuList?.forEach { templateSpu ->
            // 重算价格(里面使用product_skc来计算)
            val autoCulPriceList = productPricingServiceSelector
                .getSalePricingHandler(LAZADA)
                .autoCalPriceByTemplate(templateSpu.lazadaSpuId!!, LazadaConstants.LAZADA_DEFAULT_COUNTRY.map { it.code })
            val productSkcIdMap = autoCulPriceList.associateBy({ it.skc }, { it })
            // 处理SKC
            // 取出模板skc
            val skcLazadaList = productTemplateLazadaSkcRepository.listByLazadaSpuId(templateSpu.lazadaSpuId!!)
            val skcMap = skcLazadaList.associateBy { it.skc }
            // 取出商品skc
            val productSkcList = productSkcRepository.getByProductId(product.productId!!)
            // 以模板skc为基准, lazadaSpuId+color为key, 遍历productSkcList, 存在则更新, 不存在则新增
            productSkcList.forEach {
                val skc = skcMap[it.skc]
                if (skc != null) {
                    // 更新
                    skc.productSkcId = it.productSkcId
                    skc.skc = it.skc
                    skc.color = it.color
                    skc.platformColor = it.platformColor
                    skc.colorCode = it.colorCode
                    skc.colorAbbrCode = it.colorAbbrCode
                    skc.pictures = it.pictures
                    skc.state = it.state
                    skc.cbPrice = it.cbPrice
                    skc.localPrice = it.localPrice
                    skc.purchasePrice = it.purchasePrice
                    skc.costPrice = it.costPrice
                    productTemplateLazadaSkcRepository.updateById(skc)
                } else {
                    // 新增
                    val skc = ProductTemplateLazadaSkc().apply {
                        this.lazadaSkcId = IdHelper.getId()
                        this.lazadaSpuId = templateSpu.lazadaSpuId
                        this.productSkcId = it.productSkcId
                        this.skc = it.skc
                        this.color = it.color
                        this.platformColor = it.platformColor
                        this.colorCode = it.colorCode
                        this.colorAbbrCode = it.colorAbbrCode
                        this.pictures = it.pictures
                        this.state = it.state
                        this.cbPrice = it.cbPrice
                        this.localPrice = it.localPrice
                        this.purchasePrice = it.purchasePrice
                        this.costPrice = it.costPrice
                    }
                    productTemplateLazadaSkcRepository.save(skc)
                }
            }
            // 再获取一次SKC, 上面有新增和更新
            val lastSkcLazadaList = productTemplateLazadaSkcRepository.listByLazadaSpuId(templateSpu.lazadaSpuId!!)

            // 初始化尺码
            // 取出所有站点
            val countryList = LazadaCountryEnum.getCountryList()
            // skcList map key=skcCode
            val skcMapByCode = skcList?.groupBy { it.skcCode }
            lastSkcLazadaList.forEach { skc ->

                skcMapByCode?.get(skc.skc)?.forEach { skcDto ->
                    skcDto.sizeList?.forEach { size ->
                        // 获取条码
                        val barCode = productBarCodeRepository.getBySpuCodeAndSkcAndSize(templateSpu.spuCode, skc.skc, size)

                        countryList.forEach { country ->

                            val price = productSkcIdMap[skc.skc]?.countryPriceList?.firstOrNull { it.country == country }

                            // 判断sku是否存在
                            val skuLazada = productTemplateLazadaSkuRepository.getByLazadaSkcIdAndSizeNameAndCountry(
                                skc.lazadaSkcId!!,
                                size,
                                country
                            )
                            if (skuLazada == null) {
                                // 新增
                                val skuLazada = ProductTemplateLazadaSku().apply {
                                    this.lazadaSkuId = IdHelper.getId()
                                    this.lazadaSkcId = skc.lazadaSkcId
                                    this.lazadaSpuId = templateSpu.lazadaSpuId
                                    //                            this.sellerSku = importDto.skuCode
                                    barCode?.let { this.barcode = it.barcode }
                                    this.stockQuantity = lazadaDefaultProperties.defaultStock.toLong()
                                    this.sizeName = size
                                    this.country = country
                                    this.enableState = Bool.YES.code
                                    if (price != null) {
                                        this.salePrice = price.salePrice
                                        this.retailPrice = price.retailPrice
                                    }
                                }
                                productTemplateLazadaSkuRepository.save(skuLazada)
                            } else {
                                if (price != null) {
                                    skuLazada.salePrice = price.salePrice
                                    skuLazada.retailPrice = price.retailPrice
                                }
                                productTemplateLazadaSkuRepository.updateById(skuLazada)
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * 更新AE模板SKC和SKU
     */
    private fun upsertAeTemplateNewSkc(product: Product, skcList: List<ProductInitSkcDto>?) {
        if (skcList.isNullOrEmpty()) {
            log.error { "上游新增SKC, 尺码为空" }
            return
        }
        val templateSpuList = productTemplateAeSpuRepository.listNoCompletedBySpuCode(product.spuCode!!)
        if (templateSpuList.isEmpty()) {
            log.error { "上游新增SKC, Lazada待上架无待处理SPU" }
            return
        }

        templateSpuList?.forEach { templateSpu ->

            // 处理SKC
            // 取出模板skc
            val skcAeList = productTemplateAeSkcRepository.listByAeSpuId(templateSpu.aeSpuId!!)
            val skcMap = skcAeList.associateBy { it.skc }
            // 取出商品skc
            val productSkcList = productSkcRepository.getByProductId(product.productId!!)
            // 以模板skc为基准, aeSpuId+color为key, 遍历productSkcList, 存在则更新, 不存在则新增
            productSkcList.forEach {
                val skc = skcMap[it.skc]
                if (skc != null) {
                    // 更新
                    skc.productSkcId = it.productSkcId
                    skc.skc = it.skc
                    skc.color = it.color
                    skc.platformColor = it.platformColor
                    skc.colorCode = it.colorCode
                    skc.colorAbbrCode = it.colorAbbrCode
                    skc.pictures = it.pictures
                    skc.state = it.state
                    skc.cbPrice = it.cbPrice
                    skc.localPrice = it.localPrice
                    skc.purchasePrice = it.purchasePrice
                    skc.costPrice = it.costPrice
                    productTemplateAeSkcRepository.updateById(skc)
                } else {
                    // 新增
                    val skc = ProductTemplateAeSkc().apply {
                        this.aeSkcId = IdHelper.getId()
                        this.aeSpuId = templateSpu.aeSpuId
                        this.productSkcId = it.productSkcId
                        this.skc = it.skc
                        this.color = it.color
                        this.platformColor = it.platformColor
                        this.colorCode = it.colorCode
                        this.colorAbbrCode = it.colorAbbrCode
                        this.pictures = it.pictures
                        this.state = it.state
                        this.cbPrice = it.cbPrice
                        this.localPrice = it.localPrice
                        this.purchasePrice = it.purchasePrice
                        this.costPrice = it.costPrice
                    }
                    productTemplateAeSkcRepository.save(skc)
                }
            }
            // 再获取一次SKC, 上面有新增和更新
            val lastSkcAeList = productTemplateAeSkcRepository.listByAeSpuId(templateSpu.aeSpuId!!).filter { it.state == Bool.YES.code }

            val skuList = productTemplateAeSkuRepository.listByAeSpuId(templateSpu.aeSpuId!!, Bool.YES.code)

            // 提取AE发货地
            val allShipsFrom = skuList.mapNotNull { it.getParsedShipsFromPropertyValueItem() }
            val foreachShipsFrom = mutableListOf<ProductAePropertyValueItemDTO?>()
            if (allShipsFrom.isEmpty()) {
                // 都是空, 则只有默认发货地
                foreachShipsFrom.add(ProductAePropertyValueItemDTO().apply {
                    this.attributeId = aliexpressProperties.aePlatform.shipsFromPropertyId
                    this.attributeName = aliexpressProperties.aePlatform.shipsFromPropertyName
                    this.attributeValueId = aliexpressProperties.aePlatform.shipsFromPropertyValueId
                    this.attributeValueName = aliexpressProperties.aePlatform.shipsFromPropertyValueName
                })
            } else {
                // 不为空, 则多发货地
                val tmpShipsFrom = allShipsFrom.associateBy { it.attributeValueId }
                if (tmpShipsFrom.values.isNotEmpty()) {
                    foreachShipsFrom.addAll(tmpShipsFrom.values)
                } else {
                    foreachShipsFrom.add(ProductAePropertyValueItemDTO().apply {
                        this.attributeId = aliexpressProperties.aePlatform.shipsFromPropertyId
                        this.attributeName = aliexpressProperties.aePlatform.shipsFromPropertyName
                        this.attributeValueId = aliexpressProperties.aePlatform.shipsFromPropertyValueId
                        this.attributeValueName = aliexpressProperties.aePlatform.shipsFromPropertyValueName
                    })
                }
            }

            // 查询店铺
            val shop = shopRepository.getById(templateSpu.shopId!!)

            // 初始化尺码
            // skcList map key=skcCode
            val skcMapByCode = skcList.groupBy { it.skcCode }
            lastSkcAeList.forEach { skc ->
                skcMapByCode[skc.skc]?.forEach { skcDto ->
                    skcDto.sizeList?.forEach { size ->
                        // 获取条码
                        val barCode = productBarCodeRepository.getBySpuCodeAndSkcAndSize(templateSpu.spuCode, skc.skc, size)

                        // 循环发货地
                        foreachShipsFrom.forEach { shipsFrom ->
                            // 判断sku是否存在
                            val skuAe = productTemplateAeSkuRepository.getByAeSkcIdAndSizeName(
                                skc.aeSkcId!!,
                                size,
                                shipsFrom?.attributeValueId,
                                shop.shopId!!
                            )
                            if (skuAe == null) {
                                // 新增
                                val skuAe = ProductTemplateAeSku().apply {
                                    this.aeSkuId = IdHelper.getId()
                                    this.aeSkcId = skc.aeSkcId
                                    this.aeSpuId = templateSpu.aeSpuId
                                    //                            this.sellerSku = importDto.skuCode
                                    barCode?.let { this.barcode = it.barcode }
                                    this.stockQuantity = lazadaDefaultProperties.defaultStock.toLong()
                                    this.sizeName = size
                                    this.shopId = shop.shopId
                                    this.flagFrontend = Bool.YES.code
                                    this.enableState = Bool.YES.code
                                    if (shipsFrom != null) {
                                        this.shipsFromAttributeId = shipsFrom.attributeId
                                        this.shipsFromAttributeName = shipsFrom.attributeName
                                        this.shipsFromAttributeValueId = shipsFrom.attributeValueId
                                        this.shipsFromAttributeValueName = shipsFrom.attributeValueName
                                    }
                                }
                                productTemplateAeSkuRepository.save(skuAe)
                            }
                        }
                    }
                }
            }
        }
    }

    private fun changeModifyGrossMargin(upsertProduct: Product, importGrossMargin: BigDecimal, shopIds: MutableList<Long>): Int {
        var modifyGrossMarginFlag = Bool.NO.code
        val defaultShopId = commonProperties.marketDefaultShop.firstOrNull { upsertProduct.marketCode.isNotNull() && it.marketCode.isNotNull() && it.marketCode == upsertProduct.marketCode }
            ?.shopId
        //没有指定店铺，则应用全部店铺
        val useToAllShop = if (shopIds.isEmpty()) {
            Bool.YES.code
        } else {
            Bool.NO.code
        }

        //毛利率调整：原来没有独立配置也没有统一配置，原来没有独立配置胆有统一配置，传入值与统一配置不一样，或者原来有独立配置，传入值与独立配置不一样
        //修改毛利率标识
        if (upsertProduct.grossMargin != null) {
            if (upsertProduct.grossMargin!!.compareTo(importGrossMargin) != 0) {
                modifyGrossMarginFlag = Bool.YES.code
                //如果没有指定了店铺，或者指定店铺刚好是选款店铺或者市场默认店铺，则毛利率就应用到选款店铺(或者市场默认店铺)
                if (useToAllShop == Bool.YES.code
                    || (upsertProduct.shopId != null && shopIds.contains(upsertProduct.shopId))
                    || (defaultShopId != null && shopIds.contains(defaultShopId))
                ) {
                    upsertProduct.grossMargin = importGrossMargin
                }
                productTagRepository.addTagByProductId(
                    upsertProduct.productId!!,
                    ProductTagEnum.TAG_POP_GROSS_MARGIN.code,
                    TagPopGrossMarginEnum.MODIFY_GROSS_MARGIN.code
                )
            }
            val configDto = upsertProduct.shopGrossMargin?.parseJson<ProductShopGrossMarginConfigDto>() ?: ProductShopGrossMarginConfigDto()
            fillProductShopGrossMarginConfigDto(importGrossMargin, useToAllShop, upsertProduct, configDto, shopIds)
            //导入时没有指定店铺，则应用全部店铺
            if (useToAllShop == Bool.YES.code) {
                upsertProduct.grossMarginUseAllShop = Bool.YES.code
                configDto.allShopGrossMargin = importGrossMargin
            }
            upsertProduct.shopGrossMargin = configDto.toJson()
        } else {
            //查询区域定价相关字段
            val productRegionPriceRuleDto = regionPriceRuleService.getProductRegionPriceRule(upsertProduct)

            modifyGrossMarginFlag = Bool.YES.code
            //如果没有指定了店铺，或者指定店铺刚好是选款店铺或者市场默认店铺，则毛利率就应用到选款店铺(或者市场默认店铺)
            if (useToAllShop == Bool.YES.code
                || (upsertProduct.shopId != null && shopIds.contains(upsertProduct.shopId))
                || (defaultShopId != null && shopIds.contains(defaultShopId))
            ) {
                upsertProduct.grossMargin = importGrossMargin
            }
            val configDto = upsertProduct.shopGrossMargin?.parseJson<ProductShopGrossMarginConfigDto>() ?: ProductShopGrossMarginConfigDto()
            fillProductShopGrossMarginConfigDto(importGrossMargin, useToAllShop, upsertProduct, configDto, shopIds)
            //导入时没有指定店铺，则应用全部店铺
            if (useToAllShop == Bool.YES.code) {
                upsertProduct.grossMarginUseAllShop = Bool.YES.code
                configDto.allShopGrossMargin = importGrossMargin
            }
            upsertProduct.shopGrossMargin = configDto.toJson()
            //原来没有独立配置，但导入数据与统一配置不一样
            if (productRegionPriceRuleDto.grossMargin == null
                || (upsertProduct.grossMargin != null && productRegionPriceRuleDto.grossMargin!!.compareTo(upsertProduct.grossMargin) != 0)
            ) {
                productTagRepository.addTagByProductId(
                    upsertProduct.productId!!,
                    ProductTagEnum.TAG_POP_GROSS_MARGIN.code,
                    TagPopGrossMarginEnum.MODIFY_GROSS_MARGIN.code
                )
            }
        }
        //是否为负数
        if (importGrossMargin.signum() < 0) {
            productTagRepository.addTagByProductId(
                upsertProduct.productId!!,
                ProductTagEnum.TAG_POP_GROSS_MARGIN.code,
                TagPopGrossMarginEnum.NEGATIVE.code
            )
        } else {
            productTagRepository.removeTag(
                upsertProduct.productId!!,
                ProductTagTargetTypeEnum.PRODUCT_ID,
                ProductTagEnum.TAG_POP_GROSS_MARGIN.code,
                TagPopGrossMarginEnum.SUBSTANDARD.code
            )
        }

        //毛利不达标
        val configGrossMargin = regionPriceRuleService.getGrossMarginByProduct(upsertProduct)
        if (configGrossMargin != null) {
            if (importGrossMargin < configGrossMargin) {
                productTagRepository.addTagByProductId(upsertProduct.productId!!, ProductTagEnum.TAG_POP_GROSS_MARGIN.code, TagPopGrossMarginEnum.SUBSTANDARD.code)
            } else {
                productTagRepository.removeTag(upsertProduct.productId!!, ProductTagTargetTypeEnum.PRODUCT_ID, ProductTagEnum.TAG_POP_GROSS_MARGIN.code, TagPopGrossMarginEnum.SUBSTANDARD.code)
            }
        }
        return modifyGrossMarginFlag
    }

    /**
     * 初始化模板表
     * 注意: 因为业务流程变动, 这里不再初始化待上架商品, 只处理新增SKC场景
     * @param initDto
     */
    override fun initTemplateByProduct(initDto: ProductInitDto) {
        if (initDto.spuCode.isNullOrBlank()) {
            log.error { "spuCode不能为空" }
            return
        }
        val product = productRepository.getBySpuCode(initDto.spuCode!!)
        if (product == null) {
            log.error { "商品不存在: ${initDto.spuCode}" }
            return
        }

        /*
        ODM 和 现货TRY ON, 以他们为准
        其他 都是以我们为准
         */
        val supplyModeEnum = SupplyModeEnum.getByCode(product.supplyMode)
        if (supplyModeEnum != null) {
            initDto.skcList?.forEach { skc ->
                skc.sizeList = ProductSizeUtils.getSizeBySupplyMode(supplyModeEnum, skc.sizeList)
            }
        }

        // 注意: 因为业务流程变动, 这里不再初始化待上架商品, 只处理新增SKC场景

        // upsert Lazada
        upsertLazadaTemplateNewSkc(product, initDto.skcList)

        // upsert AE
        upsertAeTemplateNewSkc(product, initDto.skcList)

        // upsert TEMU
        upsertTemuTemplateNewSkc(product, initDto.skcList)
    }

    /**
     * 补录商品的款式类型
     */
    override fun fillStyleType(req: FillProductStyleTypeReq) {
        if (req.productIds.isNullOrEmpty()) {
            log.warn { "补录商品的款式类型失败，商品ID为空" }
            return
        }
        val updateProducts: MutableList<Product> = mutableListOf()
        val dbProducts: List<Product> = productRepository.listByIds(req.productIds)
        if (dbProducts.isNotEmpty()) {
            val spuCodeToProductMap = dbProducts.filter { it.spuCode.isNotBlank() }.groupBy { it.spuCode!! }
            if (spuCodeToProductMap.isNotEmpty()) {
                val reslut: Map<String, Int> = designClientExternal.getStyleTypeBySpuCodes(spuCodeToProductMap.keys.toList())
                spuCodeToProductMap.forEach { (spuCode, dbProducts) ->
                    val styleType = reslut[spuCode]
                    if (styleType.isNotNull() && SdpStyleTypeEnum.UNKNOWN.code != styleType) {
                        updateProducts.addAll(dbProducts.map { v ->
                            val updateProduct = Product()
                            updateProduct.productId = v.productId
                            updateProduct.styleType = SdpStyleTypeEnum.getByCode(styleType)?.code ?: SdpStyleTypeEnum.UNKNOWN.code
                            updateProduct
                        }.toList())
                    }
                }
            }
        }

        //更新
        if (updateProducts.isNotEmpty()) {
            productRepository.fillProductStyleType(updateProducts)
        }
    }

    /**
     * 刷新商品的图包状态
     */
    override fun refreshImagePackageState(req: RefreshProductImagePackageStateReq) {
        if (req.productIds.isNullOrEmpty()) {
            log.warn { "刷新商品的图包状态失败，商品ID为空" }
            return
        }
        val dbProducts: List<Product> = productRepository.listByIds(req.productIds)
        if (dbProducts.isNotEmpty()) {
            val updateProducts: MutableList<Product> = mutableListOf()
            val spuCodeToProductMap = dbProducts.filter { it.spuCode.isNotBlank() }.groupBy { it.spuCode!! }
            if (spuCodeToProductMap.isNotEmpty()) {
                val reslut: Map<String, Int> = designClientExternal.getImagePackageStateBySpuCodes(spuCodeToProductMap.keys.toList())
                /**
                 * -未完成：无图包信息，无图包版本信息
                 *
                 * -更新中：款式管理的视觉任务进度状态="进行中"
                 *
                 * -已更新：有图包版本信息&提交状态=已提交&最新版本未被应用到上架
                 *
                 * -已完成：以下2种情况均为已完成
                 *
                 * -a.有图包版本信息&提交状态=未提交
                 *
                 * -b.有图包版本信息&提交状态=已提交&最新版本已被应用上架
                 */
                spuCodeToProductMap.forEach { (spuCode, dbProducts) ->
                    //POP端没有图包，则未完成
                    val imageRepository = imageRepositoryRepository.getBySpuCode(spuCode)
                    if (imageRepository == null) {
                        updateProducts.addAll(dbProducts.map { v ->
                            val updateProduct = Product()
                            updateProduct.productId = v.productId
                            updateProduct.imagePackageState = ProductImagePackageStateEnum.INCOMPLETE.code
                            updateProduct
                        }.toList())
                    }
                    //视觉那边有图包处理中
                    else if (reslut[spuCode] == ProductImagePackageStateEnum.UPDATING.code) {
                        updateProducts.addAll(dbProducts.map { v ->
                            val updateProduct = Product()
                            updateProduct.productId = v.productId
                            updateProduct.imagePackageState = ProductImagePackageStateEnum.UPDATING.code
                            updateProduct
                        }.toList())
                    } else {
                        updateProducts.addAll(dbProducts.map { v ->
                            //已上架,但上架时图包版本，跟最新的图包版本不一致
                            if (v.isSyncPlatform == Bool.YES.code) {
                                if (v.imageVersionNum.isNull() || imageRepository.versionNum != v.imageVersionNum) {
                                    val updateProduct = Product()
                                    updateProduct.productId = v.productId
                                    updateProduct.imagePackageState = ProductImagePackageStateEnum.UPDATED.code
                                    updateProduct
                                } else {
                                    val updateProduct = Product()
                                    updateProduct.productId = v.productId
                                    updateProduct.imagePackageState = ProductImagePackageStateEnum.COMPLETED.code
                                    updateProduct
                                }
                            }
                            //不是已上架状态，有图包，图包状态为已完成
                            else {
                                val updateProduct = Product()
                                updateProduct.productId = v.productId
                                updateProduct.imagePackageState = ProductImagePackageStateEnum.COMPLETED.code
                                updateProduct
                            }
                        }.toList())
                    }
                }
            }

            //更新
            if (updateProducts.isNotEmpty()) {
                productRepository.refreshImagePackageState(updateProducts)
            }
        }
    }

    /**
     * 更新Temu模板SKC和SKU
     */
    private fun upsertTemuTemplateNewSkc(product: Product, skcList: List<ProductInitSkcDto>?) {

        if (skcList.isNullOrEmpty()) {
            log.error { "上游新增SKC, 尺码为空" }
            return
        }
        val templateSpuList = productTemplateTemuSpuRepository.listNoCompletedBySpuCode(product.spuCode!!)
        if (templateSpuList.isEmpty()) {
            log.error { "上游新增SKC, Lazada待上架无待处理SPU" }
            return
        }

        templateSpuList?.forEach { templateSpu ->
            // 处理SKC
            // 取出模板skc
            val skcTemuList = productTemplateTemuSkcRepository.listByTemuSpuId(templateSpu.temuSpuId!!)

            // 取出商品skc
            val productSkcList = productSkcRepository.getByProductId(product.productId!!)
            val skcMap = skcTemuList.associateBy { it.skc }
            // 以模板skc为基准, aeSpuId+color为key, 遍历productSkcList, 存在则更新, 不存在则新增
            productSkcList.forEach {
                val skc = skcMap[it.skc]
                if (skc != null) {
                    // 更新
                    skc.productSkcId = it.productSkcId
                    skc.skc = it.skc
                    skc.color = it.color
                    skc.platformColor = it.color
                    skc.colorCode = it.colorCode
                    skc.colorAbbrCode = it.colorAbbrCode
                    skc.pictures = it.pictures
                    skc.state = it.state
                    skc.cbPrice = it.cbPrice
                    skc.localPrice = it.localPrice
                    skc.purchasePrice = it.purchasePrice
                    skc.costPrice = it.costPrice
                    productTemplateTemuSkcRepository.updateById(skc)
                } else {
                    // 新增
                    val skc = ProductTemplateTemuSkc().apply {
                        this.temuSkcId = IdHelper.getId()
                        this.temuSpuId = templateSpu.temuSpuId
                        this.productSkcId = it.productSkcId
                        this.skc = it.skc
                        this.color = it.color
                        this.platformColor = it.color
                        this.colorCode = it.colorCode
                        this.colorAbbrCode = it.colorAbbrCode
                        this.pictures = it.pictures
                        this.state = it.state
                        this.cbPrice = it.cbPrice
                        this.localPrice = it.localPrice
                        this.purchasePrice = it.purchasePrice
                        this.costPrice = it.costPrice
                    }
                    productTemplateTemuSkcRepository.save(skc)
                }
            }


            // 再获取一次SKC, 上面有新增和更新
            val lastSkcTemuList = productTemplateTemuSkcRepository.listByTemuSpuId(templateSpu.temuSpuId!!)

            // 初始化尺码
            // skcList map key=skcCode
            val skcMapByCode: Map<String?, List<ProductInitSkcDto>> = skcList.groupBy { it.skcCode }
            lastSkcTemuList.forEach { skc ->
                skcMapByCode[skc.skc]?.forEach { skcDto ->
                    skcDto.sizeList?.forEach { size ->
                        // 获取条码
                        val barCode = productBarCodeRepository.getBySpuCodeAndSkcAndSize(templateSpu.spuCode, skc.skc, size)
                        // 判断sku是否存在
                        val skuTemus = productTemplateTemuSkuRepository.getByTemuSkcIdAndSizeNameList(
                            skc.temuSkcId!!,
                            size
                        )
                        if (skuTemus.isEmpty()) {
                            // 新增
                            val skuTemu = ProductTemplateTemuSku().apply {
                                this.temuSkuId = IdHelper.getId()
                                this.temuSkcId = skc.temuSkcId
                                this.temuSpuId = skc.temuSpuId
                                //                            this.sellerSku = importDto.skuCode
                                barCode?.let { this.barcode = it.barcode }
                                this.sizeName = size
                                this.enableState = Bool.YES.code
                                //                            if (price != null) {
                                //                                this.salePrice = price.salePrice
                                //                                this.retailPrice = price.retailPrice
                                //                            }
                                //半托给个默认站点
                                if (templateSpu.businessType == ProductShopBusinessType.SEMI_MANAGED.value) {
                                    this.countryName = TemuSiteEnum.US_STATION.siteName
                                    this.country = TemuSiteEnum.US_STATION.siteId.toString()
                                }
                            }
                            val sellerSku = generateSellerSkuComponent.generateSellerSku(
                                product,
                                templateSpu,
                                skc,
                                skuTemu
                            )
                            skuTemu.sellerSku = sellerSku
                            productTemplateTemuSkuRepository.save(skuTemu)
                        } else {
                            //                        if (price != null) {
                            //                            skuAe.salePrice = price.salePrice
                            //                            skuAe.retailPrice = price.retailPrice
                            //                        }
                            productTemplateTemuSkuRepository.updateBatchById(skuTemus)
                        }
                    }
                }
            }
        }
    }

    /**
     * 收集给定产品ID的所有标签相关数据
     */
    private fun collectTagData(productIds: List<Long>): TagContext {
        // 1. 获取产品级别的标签
        val productIdTagMap = if (productIds.isNotEmpty()) {
            productTagRepository.getTagMapByProductIds(productIds)
        } else {
            emptyMap()
        }

        // 2.1 获取Lazada SPU数据
        val lazadaSpuList: List<ProductTemplateLazadaSpu> = productTemplateLazadaSpuRepository.ktQuery()
            .select(ProductTemplateLazadaSpu::lazadaSpuId, ProductTemplateLazadaSpu::productId)
            .`in`(ProductTemplateLazadaSpu::productId, productIds)
            .list()
        val lazadaSpuIds: List<Long> = lazadaSpuList
            .mapNotNull { it.lazadaSpuId }
            .distinct()
        val productIdToLazadaSpuIds: Map<Long, List<Long>> = lazadaSpuList
            .filter { it.productId.isNotNull() }
            .groupBy { it.productId!! }
            .mapValues { entry ->
                entry.value.mapNotNull { it.lazadaSpuId }
            }

        // 2.2 获取AE SPU数据
        val aeSpuList: List<ProductTemplateAeSpu> = productTemplateAeSpuRepository.ktQuery()
            .select(ProductTemplateAeSpu::aeSpuId, ProductTemplateAeSpu::productId)
            .`in`(ProductTemplateAeSpu::productId, productIds)
            .list()
        val aeSpuIds: List<Long> = aeSpuList
            .mapNotNull { it.aeSpuId }
            .distinct()
        val productIdToAeSpuIds: Map<Long, List<Long>> = aeSpuList
            .filter { it.productId.isNotNull() }
            .groupBy { it.productId!! }
            .mapValues { entry ->
                entry.value.mapNotNull { it.aeSpuId }
            }

        // 2.3 获取Temu SPU数据
        val temuSpuList: List<ProductTemplateTemuSpu> = productTemplateTemuSpuRepository.ktQuery()
            .select(ProductTemplateTemuSpu::temuSpuId, ProductTemplateTemuSpu::productId)
            .`in`(ProductTemplateTemuSpu::productId, productIds)
            .list()
        val temuSpuIds: List<Long> = temuSpuList
            .mapNotNull { it.temuSpuId }
            .distinct()
        val productIdToTemuSpuIds: Map<Long, List<Long>> = temuSpuList
            .filter { it.productId.isNotNull() }
            .groupBy { it.productId!! }
            .mapValues { entry ->
                entry.value.mapNotNull { it.temuSpuId }
            }

        // 4. 获取SPU的标签
        val lazadaSpuIdTagMap = if (lazadaSpuIds.isNotEmpty()) {
            productTagRepository.getTagMapByLazadaSpuIds(lazadaSpuIds)
        } else {
            emptyMap()
        }
        val aeSpuIdTagMap = if (aeSpuIds.isNotEmpty()) {
            productTagRepository.getTagMapByAeSpuIds(aeSpuIds)
        } else {
            emptyMap()
        }
        val temuSpuIdTagMap = if (temuSpuIds.isNotEmpty()) {
            productTagRepository.getTagMapByTemuSpuIds(temuSpuIds)
        } else {
            emptyMap()
        }

        return TagContext(
            productIdTagMap,
            productIdToLazadaSpuIds,
            productIdToAeSpuIds,
            productIdToTemuSpuIds,
            lazadaSpuIdTagMap,
            aeSpuIdTagMap,
            temuSpuIdTagMap,
        )
    }

    /**
     * 合并特定产品的所有标签
     */
    private fun getCombinedTags(productId: Long, tagContext: TagContext): List<String> {
        // 从产品级别的标签开始
        val combinedTagMap = (tagContext.productIdTagMap[productId] ?: emptyMap()).toMutableMap()

        // 添加Lazada SPU标签
        val lazadaIdsForThis = tagContext.productIdToLazadaSpuIds[productId].orEmpty()
        for (lazadaSpuId in lazadaIdsForThis) {
            val tagMapForLazada = tagContext.lazadaSpuIdTagMap[lazadaSpuId].orEmpty()
            combinedTagMap.putAll(tagMapForLazada)
        }

        // 添加AE SPU标签
        val aeIdsForThis = tagContext.productIdToAeSpuIds[productId].orEmpty()
        for (aeSpuId in aeIdsForThis) {
            val tagMapForAe = tagContext.aeSpuIdTagMap[aeSpuId].orEmpty()
            combinedTagMap.putAll(tagMapForAe)
        }

        // 添加Temu SPU标签
        val temuIdsForThis = tagContext.productIdToTemuSpuIds[productId].orEmpty()
        for (temuSpuId in temuIdsForThis) {
            val tagMapForTemu = tagContext.temuSpuIdTagMap[temuSpuId].orEmpty()
            combinedTagMap.putAll(tagMapForTemu)
        }

        // 生成最终的标签代码列表
        return ProductTagEnum
            .getDescListByPage(combinedTagMap)
            .distinct()
    }

    override fun initHisDataV2(file: MultipartFile) {
        val uploadDtoList: List<StyleHisDataUploadExcelDTO>
        try {
            uploadDtoList = EasyExcel.read(file.inputStream)
                .head(StyleHisDataUploadExcelDTO::class.java)
                .sheet()
                .doReadSync()
        } catch (e: IOException) {
            log.error { "导入失败" }
            throw BusinessException("导入失败", e)
        }

        if (uploadDtoList.isEmpty()) {
            throw BusinessException("导入数据为空")
        }

        val spuMap = uploadDtoList.associateBy({ it.spuCode }, { it })
        val spuCodeList = uploadDtoList.map { it.spuCode }.distinct().toList().filterNotNull()
        val productList = productRepository.listBySpuCodes(spuCodeList)
        Assert.isTrue(productList.isNotEmpty(), "未找到对应的商品")

        // 市场风格
        val marketMap = marketStyleComponent.getMarketNameMap(1)
        val marketSeriesMap = marketStyleComponent.getMarketNameMap(2)
        val marketStyleMap = marketStyleComponent.getMarketNameMap(3)

        val updateSpuList = mutableListOf<Product>()

        for (product in productList) {
            val excelDto = spuMap[product.spuCode] ?: continue

            if (excelDto.marketName.isNullOrBlank().not()) {
                product.marketCode = marketMap[excelDto.marketName]
            }

            if (!excelDto.marketName.isNullOrBlank() && !excelDto.marketSeriesName.isNullOrBlank()) {
                product.marketSeriesCode = marketSeriesMap[excelDto.marketName + "-" + excelDto.marketSeriesName]
            }

            if (excelDto.marketName.isNullOrBlank().not() &&
                excelDto.marketSeriesName.isNullOrBlank().not() &&
                excelDto.marketStyleName.isNullOrBlank().not()
            ) {
                product.clothingStyleCode = marketStyleMap[excelDto.marketName + "-" + excelDto.marketSeriesName + "-" + excelDto.marketStyleName]
                if (product.clothingStyleCode.isNotBlank()) {
                    product.clothingStyleName = excelDto.marketStyleName
                }
            }

            updateSpuList.add(product)
        }

        productRepository.updateBatchById(updateSpuList)
    }

    override fun planAudit(req: ProductPlanAuditReq) {
        val currentUser = CurrentUserHolder.get()
        val products = productRepository.listByIds(req.productIds)
        Assert.isTrue(products.isNotNull() && products.isNotEmpty(), "商品不存在")
        val nowTime = LocalDateTime.now()
        val updateProducts = products.map {
            Product().apply {
                this.productId = it.productId
                this.planAuditState = req.planAuditState
                this.planAuditTime = nowTime
                this.planAuditorId = currentUser.id
                this.planAuditorName = currentUser.name
            }
        }
        productRepository.updateBatchById(updateProducts)
    }

    @Transactional(rollbackFor = [Exception::class])
    override fun updateProductGrossMargin(req: UpdateProductGrossMarginReq): UpdateProductGrossMarginResp {
        val product = productRepository.getById(req.productId)
        Assert.isNotNull(product, "商品不存在")
        //更新毛利率之前先清一下缓存
        regionPriceRuleService.invalidateAllCache()

        val currentUser = CurrentUserHolder.get()

        val configDto = product.shopGrossMargin?.parseJson<ProductShopGrossMarginConfigDto>() ?: ProductShopGrossMarginConfigDto()
        val updateProduct = Product().apply {
            this.productId = product.productId
            this.grossMargin = req.grossMargin
            this.grossMarginUseAllShop = req.useToAllShop
            if (req.useToAllShop == 1) {
                configDto.allShopGrossMargin = req.grossMargin
            }
            //记录选款店铺和默认店铺的毛利率
            fillProductShopGrossMarginConfigDto(req.grossMargin, req.useToAllShop, product, configDto, null)
            this.shopGrossMargin = configDto.toJson()

            // 更新毛利率后, 默认审核通过
            this.planAuditState = ProductPlanAuditStateEnum.PASS.code
            this.planAuditTime = LocalDateTime.now()
            this.planAuditorId = currentUser.id
            this.planAuditorName = currentUser.name
        }
        productRepository.updateById(updateProduct)
        //增加标签
        //查询区域定价相关字段
        val productRegionPriceRuleDto = regionPriceRuleService.getProductRegionPriceRule(product)
        //毛利率调整：原来没有独立配置也没有统一配置，原来没有独立配置胆有统一配置，传入值与统一配置不一样，或者原来有独立配置，传入值与独立配置不一样
        if ((product.grossMargin == null && productRegionPriceRuleDto.grossMargin == null)
            || (product.grossMargin == null && productRegionPriceRuleDto.grossMargin!!.compareTo(updateProduct.grossMargin) != 0)
            || (product.grossMargin != null && product.grossMargin!!.compareTo(updateProduct.grossMargin) != 0)
        ) {
            productTagRepository.addTagByProductId(
                product.productId!!,
                ProductTagEnum.TAG_POP_GROSS_MARGIN.code,
                TagPopGrossMarginEnum.MODIFY_GROSS_MARGIN.code
            )
        }
        //毛利率为负数
        if (updateProduct.grossMargin!!.signum() < 0) {
            productTagRepository.addTagByProductId(product.productId!!, ProductTagEnum.TAG_POP_GROSS_MARGIN.code, TagPopGrossMarginEnum.NEGATIVE.code)
        } else {
            productTagRepository.removeTag(product.productId!!, ProductTagTargetTypeEnum.PRODUCT_ID, ProductTagEnum.TAG_POP_GROSS_MARGIN.code, TagPopGrossMarginEnum.NEGATIVE.code)
        }
        //毛利不达标
        val configGrossMargin = regionPriceRuleService.getGrossMarginByProduct(product)
        if (configGrossMargin != null) {
            if (updateProduct.grossMargin!! < configGrossMargin) {
                productTagRepository.addTagByProductId(product.productId!!, ProductTagEnum.TAG_POP_GROSS_MARGIN.code, TagPopGrossMarginEnum.SUBSTANDARD.code)
            } else {
                productTagRepository.removeTag(product.productId!!, ProductTagTargetTypeEnum.PRODUCT_ID, ProductTagEnum.TAG_POP_GROSS_MARGIN.code, TagPopGrossMarginEnum.SUBSTANDARD.code)
            }
        }
        //重新计算划线价
        val dbProduct = productRepository.getById(product.productId)
        val req = regionPriceRuleService.prepareCalculateReq(dbProduct)
        req.coverOriginalPrice = true
        req.coverDestinationCountries = true
        val aePricingCalResp = aeTemplatePricingService.calculateAndSave(req)
        log.info { "${product.productId}修改商品毛利率后重新计算划线价结果：${aePricingCalResp.toJson()}" }
        return UpdateProductGrossMarginResp(product.productId!!).apply {
            val salePrice = SalePriceDto()
            val retailPrice = RetailPriceDto()
            // 如果是美元，则需要将价格转换为美元，目前库中存的是CNY价格
            val exchangeRate = currencyExchangeRateService.getExchangeRate(CurrencyEnum.CNY.code, CountryEnum.US.code)
            //售价
            salePrice.apply {
                this.salePriceMax = aePricingCalResp.results.mapNotNull { it.salePrice }.maxOrNull() ?: BigDecimal.ZERO
                this.salePriceMin = aePricingCalResp.results.mapNotNull { it.salePrice }.minOrNull() ?: BigDecimal.ZERO
            }
            salePrice.salePriceUSDMax = salePrice.salePriceMax!!.multiply(exchangeRate).setScale(2, RoundingMode.HALF_UP)
            salePrice.salePriceUSDMin = salePrice.salePriceMin!!.multiply(exchangeRate).setScale(2, RoundingMode.HALF_UP)
            //划线价
            retailPrice.apply {
                this.retailPriceMax = aePricingCalResp.results.mapNotNull { it.retailPrice }.maxOrNull() ?: BigDecimal.ZERO
                this.retailPriceMin = aePricingCalResp.results.mapNotNull { it.retailPrice }.minOrNull() ?: BigDecimal.ZERO
            }
            retailPrice.retailPriceUSDMax = retailPrice.retailPriceMax!!.multiply(exchangeRate).setScale(2, RoundingMode.HALF_UP)
            retailPrice.retailPriceUSDMin = retailPrice.retailPriceMin!!.multiply(exchangeRate).setScale(2, RoundingMode.HALF_UP)

            this.retailPrice = retailPrice
            this.salePrice = salePrice
        }
    }

    /**
     * 待上架查询上架失败原因
     */
    override fun publishErrorLog(req: PendingErrorLogQueryReq): PageVo<PublishErrorLogResp> {
        val queryWrapper = KtQueryWrapper(ProductSyncLog::class.java)
            .eq(ProductSyncLog::logType, Bool.NO.code)
            .eq(ProductSyncLog::templateSpuId, req.templateSpuId)
            .orderByDesc(ProductSyncLog::createdTime)

        val page = Page<ProductSyncLog>(req.pageNum.toLong(), req.pageSize.toLong())
        val productSyncLogPage = productSyncLogRepository.page(page, queryWrapper)

        if (CollectionUtils.isEmpty(productSyncLogPage.records)) {
            return PageRespHelper.of(req.pageNum, productSyncLogPage.total, emptyList())
        }

        val result = productSyncLogPage.records.map { log ->
            PublishErrorLogResp().apply {
                this.errorMsg = log.errorMsg
                this.platformName = log.platformName
                this.shopName = log.shopName
                this.createdTime = log.createdTime
            }
        }

        return PageRespHelper.of(req.pageNum, productSyncLogPage.total, result)
    }

    @Transactional(rollbackFor = [Exception::class])
    override fun updateProductGrossMarginByShop(req: UpdateProductGrossMarginByShopReq) {
        val products = productRepository.listByIds(req.productIds)
        Assert.isTrue(products.isNotEmpty(), "商品不存在")
        //更新毛利率之前先清一下缓存
        regionPriceRuleService.invalidateAllCache()

        val currentUser = CurrentUserHolder.get()

        products.forEach { product ->

            // 更新毛利率
            changeModifyGrossMargin(product, req.grossMargin, req.shopIds ?: mutableListOf())

            // 更新毛利率后, 默认审核通过
            product.planAuditState = ProductPlanAuditStateEnum.PASS.code
            product.planAuditTime = LocalDateTime.now()
            product.planAuditorId = currentUser.id
            product.planAuditorName = currentUser.name
            productRepository.updateById(product)
        }
    }

    private fun fillProductShopGrossMarginConfigDto(
        grossMarginReq: BigDecimal,
        useToAllShop: Int,
        product: Product,
        configDto: ProductShopGrossMarginConfigDto,
        selectShopIds: MutableList<Long>?,
    ) {
        //如果有指定店铺也要记录对应的毛利率，默认要记录选款店铺或者商品所属市场默认店铺对应的毛利率
        val shopIds: MutableSet<Long> = selectShopIds?.toMutableSet() ?: mutableSetOf()
        //如果没有指定了店铺，则毛利率就应用到选款店铺(或者市场默认店铺)
        if (shopIds.isEmpty()) {
            product.shopId?.let {
                shopIds.add(it)
            }
            commonProperties.marketDefaultShop.firstOrNull { product.marketCode.isNotNull() && it.marketCode.isNotNull() && it.marketCode == product.marketCode }
                ?.shopId?.let {
                    shopIds.add(it)
                }
        }
        //补充或修改选款店铺和市场默认店铺的毛利率配置
        shopIds.forEach { shopId ->
            val shopGrossMarginConfigDto = configDto.shopGrossMarginConfigList.find { it.shopId == shopId }
            if (shopGrossMarginConfigDto != null) {
                shopGrossMarginConfigDto.grossMargin = grossMarginReq
            } else {
                configDto.shopGrossMarginConfigList.add(ShopGrossMarginConfigDto(grossMarginReq, shopId))
            }
        }
        //如果要应用全部，则再把每个店铺的毛利率再刷一次
        if (useToAllShop == Bool.YES.code) {
            configDto.shopGrossMarginConfigList.forEach {
                it.grossMargin = grossMarginReq
            }
        }
    }
}

/**
 * 内部类，用于保存所有标签相关数据
 */
private data class TagContext(
    val productIdTagMap: Map<Long, Map<String, List<String>>>,
    val productIdToLazadaSpuIds: Map<Long, List<Long>>,
    val productIdToAeSpuIds: Map<Long, List<Long>>,
    val productIdToTemuSpuIds: Map<Long, List<Long>>,
    val lazadaSpuIdTagMap: Map<Long, Map<String, List<String>>>,
    val aeSpuIdTagMap: Map<Long, Map<String, List<String>>>,
    val temuSpuIdTagMap: Map<Long, Map<String, List<String>>>,
)
