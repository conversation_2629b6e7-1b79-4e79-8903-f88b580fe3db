package tech.tiangong.pop.service.product.update

import tech.tiangong.pop.common.dto.ProductUpdateDto

/**
 * 商品更新接口
 * @see tech.tiangong.pop.common.enums.ProductUpdateTypeEnum
 */
interface ProductUpdateService {
    /**
     * 取消SKC
     */
    fun cancel(data: ProductUpdateDto.Data)

    /**
     * 供货价更新
     */
    fun updatePrice(data: ProductUpdateDto.Data)

    /**
     * 新增SKC
     */
    fun addSkc(data: ProductUpdateDto.Data)

    /**
     * 创建商品
     */
    fun create(data: ProductUpdateDto.Data)

    /**
     * 更新品类
     */
    fun updateCategory(data: ProductUpdateDto.Data)

    /**
     * 更新颜色
     */
    fun updateColor(data: ProductUpdateDto.Data)

    /**
     * 更新采购价
     */
    fun updatePurchasesPrice(data: ProductUpdateDto.Data)

    /**
     * 更新尺码信息
     */
    fun updateSize(data: ProductUpdateDto.Data)

    /**
     * 更新定价类型
     */
    fun updatePricingType(data: ProductUpdateDto.Data)

    /**
     * 更新款式风格
     */
    fun updateClothingStyle(data: ProductUpdateDto.Data)

    /**
     * 更新商品属性
     */
    fun updateAttributes(data: ProductUpdateDto.Data)
}