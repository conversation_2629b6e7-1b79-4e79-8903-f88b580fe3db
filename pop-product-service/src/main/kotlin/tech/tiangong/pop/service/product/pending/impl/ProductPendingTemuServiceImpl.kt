package tech.tiangong.pop.service.product.pending.impl

import com.alibaba.excel.EasyExcel
import com.baomidou.mybatisplus.extension.kotlin.KtQueryWrapper
import com.baomidou.mybatisplus.extension.plugins.pagination.Page
import org.apache.commons.collections4.CollectionUtils
import org.apache.commons.lang3.ObjectUtils
import org.springframework.stereotype.Service
import org.springframework.transaction.PlatformTransactionManager
import org.springframework.transaction.annotation.Transactional
import org.springframework.transaction.support.TransactionTemplate
import org.springframework.web.multipart.MultipartFile
import team.aikero.blade.core.enums.Bool
import team.aikero.blade.core.exception.BusinessException
import team.aikero.blade.core.protocol.PageVo
import team.aikero.blade.core.toolkit.isNotBlank
import team.aikero.blade.core.toolkit.isNotEmpty
import team.aikero.blade.core.toolkit.isNotNull
import team.aikero.blade.core.toolkit.isNull
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.sequence.id.IdHelper
import team.aikero.blade.util.json.parseJson
import team.aikero.blade.util.json.parseJsonList
import team.aikero.blade.util.json.toJson
import tech.tiangong.eis.temu.enums.TemuCountryEnum
import tech.tiangong.eis.temu.enums.TemuPlaceOfOrigionEnum
import tech.tiangong.eis.temu.enums.TemuSiteEnum
import tech.tiangong.pop.common.enums.PlatformEnum
import tech.tiangong.pop.common.enums.PlatformEnum.TEMU
import tech.tiangong.pop.common.enums.ProductShopBusinessType
import tech.tiangong.pop.common.enums.YesOrNoEnum
import tech.tiangong.pop.common.exception.BaseBizException
import tech.tiangong.pop.common.req.BatchCreateBarCodeReq
import tech.tiangong.pop.component.GenerateSellerSkuComponent
import tech.tiangong.pop.component.MarketStyleComponent
import tech.tiangong.pop.constant.ProductInnerConstant
import tech.tiangong.pop.dao.entity.*
import tech.tiangong.pop.dao.repository.*
import tech.tiangong.pop.dto.TagContextDto
import tech.tiangong.pop.dto.image.ImageCollectionDTO
import tech.tiangong.pop.dto.product.ProductTitleConfigGenerationDTO
import tech.tiangong.pop.dto.product.ProductTitleConfigGenerationItemDTO
import tech.tiangong.pop.enums.*
import tech.tiangong.pop.external.InspirationClientExternal
import tech.tiangong.pop.helper.ImageCollectionHelper
import tech.tiangong.pop.helper.PageRespHelper
import tech.tiangong.pop.listeners.ImportTemuProductExcelListener
import tech.tiangong.pop.req.product.ProductCreatePublishTaskReq
import tech.tiangong.pop.req.product.ProductPendingPublishLogPageReq
import tech.tiangong.pop.req.product.ProductTitleConfigMultiGenerateReq
import tech.tiangong.pop.req.product.ae.ProductPendingAePageReq
import tech.tiangong.pop.req.product.temu.*
import tech.tiangong.pop.resp.category.PublishAttributePairResp
import tech.tiangong.pop.resp.image.ImageAniVo
import tech.tiangong.pop.resp.product.ProductPendingPageShopResp
import tech.tiangong.pop.resp.product.ProductPendingPublishLogPageResp
import tech.tiangong.pop.resp.product.ProductPendingPublishLogStatisticsResp
import tech.tiangong.pop.resp.product.ae.ProductPendingAeStatisticsResp
import tech.tiangong.pop.resp.product.temu.ProductPendingTemuDetailDataResp
import tech.tiangong.pop.resp.product.temu.ProductPendingTemuDetailDataResp.*
import tech.tiangong.pop.resp.product.temu.ProductPendingTemuDetailResp
import tech.tiangong.pop.resp.product.temu.ProductPendingTemuPageResp
import tech.tiangong.pop.service.product.BarCodeService
import tech.tiangong.pop.service.product.ProductTitleGenerateService
import tech.tiangong.pop.service.product.pending.ProductPendingTemuService
import tech.tiangong.pop.service.settings.ProductTitleConfigService
import java.time.LocalDate
import java.util.*

/**
 * 待上架商品-Temu服务实现类
 */
@Service
@Slf4j
class ProductPendingTemuServiceImpl(
    private val productTemplateTemuSpuRepository: ProductTemplateTemuSpuRepository,
    private val productTemplateTemuSkcRepository: ProductTemplateTemuSkcRepository,
    private val productTemplateTemuSkuRepository: ProductTemplateTemuSkuRepository,
    private val shopRepository: ShopRepository,
    private val productRepository: ProductRepository,
    private val productSkcRepository: ProductSkcRepository,
    private val imageRepositoryRepository: ImageRepositoryRepository,
    private val productTagRepository: ProductTagRepository,
    private val marketStyleComponent: MarketStyleComponent,
    private val inspirationClientExternal: InspirationClientExternal,
    private val productAttributesRepository: ProductAttributesRepository,
    private val productPictureRepository: ProductPictureRepository,
    private val productBarCodeRepository: ProductBarCodeRepository,
    private val barCodeService: BarCodeService,
    private val temuSkcDetailedImageRepository: TemuSkcDetailedImageRepository,
    private val publishCategoryMappingRepository: PublishCategoryMappingRepository,
    private val imageCollectionHelper: ImageCollectionHelper,
    private val productTitleConfigService: ProductTitleConfigService,
    private val importProductRecordRepository: ImportProductRecordRepository,
    private val generateSellerSkuComponent: GenerateSellerSkuComponent,
    private val transactionManager: PlatformTransactionManager,
    private val productTitleGenerateService: ProductTitleGenerateService,
) : ProductPendingTemuService {

    private val platform = TEMU

    /**
     * 创建待上架商品
     */
    override fun createPendingTask(req: ProductCreatePublishTaskReq.PendingAeTaskChangeUserReq?, productList: List<Product>, shop: Shop) {
        productList.forEach { product ->

            // 手动事务
            TransactionTemplate(transactionManager).execute { status ->

                val multiTitleResp = try {
                    productTitleGenerateService.generateMultiTitlesByConfig(ProductTitleConfigMultiGenerateReq().apply {
                        this.productId = product.productId
                        this.product = product
                        this.shopId = product.shopId
                        this.platform = TEMU
                    })
                } catch (e: Exception) {
                    log.warn(e) { "生成多标题失败，回退到单标题生成: productId=${product.productId}" }
                    null
                }

                // 内联 title 赋值逻辑
                fun setGeneratedTitle(spu: ProductTemplateTemuSpu) {
                    if (multiTitleResp?.titleData != null) {
                        // 使用多标题数据
                        spu.setParsedGeneratedTitles(multiTitleResp.titleData)

                        // 使用第一个标题作为主标题
                        val firstTitle = multiTitleResp.titleData.titles.firstOrNull()?.title
                        if (!firstTitle.isNullOrBlank() && firstTitle.length < ProductInnerConstant.getTitleMaxLength(TEMU)) {
                            spu.productNameEn = firstTitle
                            spu.productName = firstTitle
                        }

                        // 设置多标题相关标记
                        val firstTitleData = multiTitleResp.titleData.titles.firstOrNull()
                        spu.generatedTitleOverLengthFlag = if (firstTitleData?.isOverLength == true) YesOrNoEnum.YES.code else YesOrNoEnum.NO.code
                        spu.generatedTitleMissingFieldsJson = firstTitleData?.missingFields?.toJson()
                    }
                }

                try {
                    val businessType = ProductShopBusinessType.fromValue(shop.businessType)

                    // 新增
                    val spuTemu: ProductTemplateTemuSpu = when (businessType) {
                        ProductShopBusinessType.SEMI_MANAGED -> {
                            // 半托
                            ProductTemplateTemuSpu().apply {
                                this.productId = product.productId
                                this.spuCode = product.spuCode
                                this.productName = product.productTitle
                                this.productNameEn = product.productTitle
                                this.sizeGroupName = product.sizeGroupName
                                this.sizeGroupCode = product.sizeGroupCode
                                this.temuSpuId = IdHelper.getId()
                                this.businessType = ProductShopBusinessType.SEMI_MANAGED.value
                                this.unitSetting = "1"
                                // 任务信息
                                this.taskExecutorId = req?.userId
                                this.taskExecutorName = req?.userName
                                this.taskStatus = ProductPendingTaskStatusEnum.PENDING.code
                                //半托 默认产地美国
                                countryOriginPlace = ProductTemuCountryOriginPlaceReq(
                                    TemuCountryEnum.UNITED_STATES.shortName,
                                    TemuCountryEnum.UNITED_STATES.chineseName
                                ).toJson()
                                //默认给个站点
                                val temuSiteVo = TemuSiteVo(TemuSiteEnum.US_STATION.siteId.toString(), TemuSiteEnum.US_STATION.siteName)
                                this.site = listOf(temuSiteVo).toJson()
                            }.also {
                                setGeneratedTitle(it)
                                productTemplateTemuSpuRepository.save(it)
                            }
                        }

                        ProductShopBusinessType.FULLY_MANAGED -> {
                            // 全托
                            ProductTemplateTemuSpu().apply {
                                this.productId = product.productId
                                this.spuCode = product.spuCode
                                this.productName = product.productTitle
                                this.productNameEn = product.productTitle
                                this.sizeGroupName = product.sizeGroupName
                                this.sizeGroupCode = product.sizeGroupCode
                                this.temuSpuId = IdHelper.getId()
                                this.businessType = ProductShopBusinessType.FULLY_MANAGED.value
                                this.unitSetting = "1"
                                // 任务信息
                                this.taskExecutorId = req?.userId
                                this.taskExecutorName = req?.userName
                                this.taskStatus = ProductPendingTaskStatusEnum.PENDING.code
                                //全托产地 默认中国大陆 广东
                                this.countryOriginPlace = ProductTemuCountryOriginPlaceReq(
                                    TemuCountryEnum.CHINA.shortName,
                                    TemuCountryEnum.CHINA.chineseName,
                                    TemuPlaceOfOrigionEnum.GUANGDONG.region2Id,
                                    TemuPlaceOfOrigionEnum.GUANGDONG.province
                                ).toJson()
                            }.also {
                                setGeneratedTitle(it)
                                productTemplateTemuSpuRepository.save(it)
                            }
                        }

                        else -> throw BusinessException("${shop.shopName} 未知Temu店铺业务类型 半托?全托?")
                    }

                    val productSkcList = productSkcRepository.getByProductId(product.productId!!)
                    val newSkcList = mutableListOf<ProductTemplateTemuSkc>()
                    val newSkuList = mutableListOf<ProductTemplateTemuSku>()
                    productSkcList.forEach { skc ->
                        val newSkc = ProductTemplateTemuSkc().apply {
                            this.temuSkcId = IdHelper.getId()
                            this.temuSpuId = spuTemu.temuSpuId
                            this.productSkcId = skc.productSkcId
                            this.skc = skc.skc
                            this.color = skc.color
                            this.platformColor = skc.color
                            this.colorCode = skc.colorCode
                            this.colorAbbrCode = skc.colorAbbrCode
                            this.pictures = skc.pictures
                            this.state = skc.state
                            this.cbPrice = skc.cbPrice
                            this.localPrice = skc.localPrice
                            this.purchasePrice = skc.purchasePrice
                            this.costPrice = skc.costPrice
                        }
                        newSkcList.add(newSkc)

                        val sizeList = listOf<String>() // TODO 需要product_skc获取尺码, 暂无该字段
                        sizeList.forEach { size ->
                            // 获取条码
                            val barCode = productBarCodeRepository.getBySpuCodeAndSkcAndSize(spuTemu.spuCode, skc.skc, size)

                            // 新增
                            val skuTemu = ProductTemplateTemuSku().apply {
                                this.temuSkuId = IdHelper.getId()
                                this.temuSkcId = newSkc.temuSkcId
                                this.temuSpuId = newSkc.temuSpuId
                                // this.sellerSku = importDto.skuCode
                                barCode?.let { this.barcode = it.barcode }
                                this.sizeName = size
                                this.enableState = Bool.YES.code
                                // if (price != null) {
                                //     this.salePrice = price.salePrice
                                //     this.retailPrice = price.retailPrice
                                // }
                                //半托给个默认站点
                                if (businessType == ProductShopBusinessType.SEMI_MANAGED) {
                                    this.countryName = TemuSiteEnum.US_STATION.siteName
                                    this.country = TemuSiteEnum.US_STATION.siteId.toString()
                                }
                            }
                            val sellerSku = generateSellerSkuComponent.generateSellerSku(
                                product,
                                spuTemu,
                                newSkc,
                                skuTemu
                            )
                            skuTemu.sellerSku = sellerSku
                            newSkuList.add(skuTemu)
                        }
                    }
                    if (newSkcList.isNotEmpty()) {
                        productTemplateTemuSkcRepository.saveBatch(newSkcList)
                    }
                    if (newSkcList.isNotEmpty()) {
                        productTemplateTemuSkuRepository.saveBatch(newSkuList)
                    }
                } catch (e: Exception) {
                    log.error(e) { "商品中心-创建Temu待上架-异常 productId: ${product.productId}, shopId: ${shop.shopId}" }
                    // 手动标记事务回滚
                    status.setRollbackOnly()
                }
            }
        }
    }

    /**
     * 商品列表-分页
     */
    override fun page(req: ProductPendingTemuPageReq): PageVo<ProductPendingTemuPageResp> {
        val page = productTemplateTemuSpuRepository.pendingPage(Page(req.pageNum.toLong(), req.pageSize.toLong()), req)
        val templateSpuList = page.records
        if (CollectionUtils.isEmpty(templateSpuList)) {
            return PageRespHelper.empty()
        }
        // 获取product
        val productIds = templateSpuList.mapNotNull { it.productId }
        if (productIds.isEmpty()) {
            return PageRespHelper.empty()
        }
        val products = productRepository.listByIds(productIds)
        if (productIds.isEmpty()) {
            return PageRespHelper.empty()
        }

        // 获取主图
        val spuMainImageMap = mutableMapOf<String, String?>()
        val spuCodes = products.mapNotNull { it.spuCode }.distinct()
        if (spuCodes.isNotEmpty()) {
            imageRepositoryRepository.listBySpuCodes(spuCodes)
                ?.filter { image -> image.spuCode.isNotBlank() }
                ?.forEach { image ->
                    spuMainImageMap[image.spuCode!!] = image.mainUrl
                }
        }

        val shopMap = shopRepository.listByIds(products.mapNotNull { it.shopId })
            ?.associateBy { it.shopId }

        // 获取标签
        val tagContext = collectTagData(templateSpuList)

        val productMap = products.associateBy { it.productId }
        val productsResp = templateSpuList.mapNotNull { templateSpu ->

            val product = productMap[templateSpu.productId] ?: return@mapNotNull null
            val tagCodes = TagContextDto.getCombinedTags(templateSpu.productId!!, templateSpu.temuSpuId!!, tagContext)

            ProductPendingTemuPageResp().apply {
                this.temuSpuId = templateSpu.temuSpuId
                this.productId = templateSpu.productId
                this.listingShopId = templateSpu.shopId
                this.listingShopName = shopMap?.get(templateSpu.shopId!!)?.shopName
                this.mainImgUrl = spuMainImageMap[product.spuCode] ?: product.mainImgUrl
                this.supplyMode = product.supplyMode
                this.waves = product.waves
                this.spuCode = product.spuCode
                this.tagCodes = tagCodes
                this.shopList = mutableListOf<ProductPendingPageShopResp>().apply {
                    this.add(ProductPendingPageShopResp().apply {
                        this.shopId = product.shopId
                        this.shopName = product.shopName
                    })
                }.toList()
                this.countryList = product.countrys?.let { c -> listOf<String>(c) }
                this.categoryId = product.categoryId
                this.categoryCode = product.categoryCode
                this.categoryName = product.categoryName
                this.creatorId = product.creatorId
                this.creatorName = product.creatorName
                this.createdTime = product.createdTime
                this.reviserId = product.reviserId
                this.reviserName = product.reviserName
                this.revisedTime = product.revisedTime
                this.styleType = product.styleType
                this.imagePackageState = product.imagePackageState
                this.planAuditState = product.planAuditState
                this.planAuditorId = product.planAuditorId
                this.planAuditorName = product.planAuditorName
                this.planAuditTime = product.planAuditTime
                this.listingUserId = templateSpu.taskExecutorId
                this.listingUserName = templateSpu.taskExecutorName
                this.taskStatus = templateSpu.taskStatus
            }
        }
        return PageRespHelper.of(page.current.toInt(), page.total, productsResp)
    }

    /**
     * 商品列表-统计
     */
    override fun statistics(req: ProductPendingAePageReq): ProductPendingAeStatisticsResp {
        // 状态统计
        val taskStatusStats = productTemplateTemuSpuRepository.statistics(req)
        return ProductPendingAeStatisticsResp().apply {
            this.pendingCount = taskStatusStats.pendingCount ?: 0
            this.startingCount = taskStatusStats.startingCount ?: 0
            this.failedCount = taskStatusStats.failedCount ?: 0
            this.missingInfoCount = taskStatusStats.missingInfoCount ?: 0
            this.completeInfoCount = taskStatusStats.completeInfoCount ?: 0
        }
    }

    /**
     * 商品详情
     */
    override fun detail(req: ProductPendingTemuDetailReq): ProductPendingTemuDetailResp {
        val templateSpu = productTemplateTemuSpuRepository.getById(req.temuSpuId) ?: throw IllegalArgumentException("商品不存在")
        val product = productRepository.getById(templateSpu.productId) ?: throw IllegalArgumentException("商品不存在")

        // 市场(一级节点)
        val marketMap = marketStyleComponent.getMarketNodeMap(1)
        // 系列(二级节点) 市场-系列
        val marketSeriesMap = marketStyleComponent.getMarketNodeMap(2)
        // 风格(三级节点) 市场-系列-风格
        val marketStyleMap = marketStyleComponent.getMarketNodeMap(3)
        // 获取图片
        val image = imageRepositoryRepository.getBySpuCode(product.spuCode!!)

        //设置商品属性
        val attributes = productAttributesRepository.listByProductId(product.productId!!, platform.platformId)

        val spuTemu = listOf(templateSpu)

        val businessTypeMap = spuTemu.groupBy { it.businessType }

        return ProductPendingTemuDetailResp().apply {
            this.temuSpuId = templateSpu.temuSpuId
            this.productId = templateSpu.productId
            this.shopId = product.shopId
            this.shopName = product.shopName
            this.mainImgUrl = image?.mainUrl ?: product.mainImgUrl
            this.spuCode = product.spuCode
            this.supplyMode = product.supplyMode
            this.spotTypeCode = product.spotTypeCode
            this.clothingStyleName = product.clothingStyleName ?: marketStyleMap?.get(product.clothingStyleCode)
            this.planningType = product.planningType
            this.marketCode = product.marketCode
            this.marketName = marketMap?.get(product.marketCode)
            this.marketSeriesCode = product.marketSeriesCode
            this.marketSeriesName = marketSeriesMap?.get(product.marketSeriesCode)
            this.planSourceName = product.planSourceName
            this.goodsRepType = product.goodsRepType
            this.categoryId = product.categoryId
            this.categoryCode = product.categoryCode
            this.categoryName = product.categoryName
            this.inspiraSource = product.inspiraSource
            this.inspiraImgUrl = product.inspiraImgUrl
            this.selectStyleName = product.selectStyleName
            this.selectStyleTime = product.selectStyleTime
            this.buyerRemark = product.buyerRemark
            this.creatorName = product.creatorName
            this.createdTime = product.createdTime
            this.prototypeNum = product.prototypeNum
            this.goodsType = product.goodsType
            this.pricingType = product.pricingType
            this.spotType = product.spotType
            this.waves = product.waves
            this.printType = product.printType
            if (product.inspiraSourceId != null) {
                // 获取灵感详情
                try {
                    val data = inspirationClientExternal.getByInspirationOrPickingId(product.inspiraSourceId!!)
                    if (data?.inspirationInfo != null) {
                        this.inspiraSource = data.inspirationInfo?.inspirationImageSource
                        this.inspiraCountry = data.inspirationInfo?.countrySiteName
                        this.planSourceName = data.inspirationInfo?.planningSourceName
                        this.inspiraImgUrl = data.inspirationInfo?.inspirationImage
                    }
                    if (data?.pickingInfo != null) {
                        this.countryList = data.pickingInfo?.countrySiteName?.let { listOf(it) }
                        this.shopId = data.pickingInfo?.shopId
                        this.shopName = data.pickingInfo?.shopName
                        this.selectStyleTime = data.pickingInfo?.pickingTime
                        this.buyerRemark = data.pickingInfo?.remark
                    }
                } catch (e: Exception) {
                    log.error { "getByInspirationOrPickingId 获取灵感详情报错或者数据为空，Exception=${e.message}," }
                }
            }
            this.styleType = product.styleType
            this.planAuditState = product.planAuditState
            this.imagePackageState = product.imagePackageState

            this.fullTemuDetail = ProductPendingTemuDetailDataResp().apply {

                this.categoryId = product.categoryId
                val platformCategory = getPlatformCategory(product, platform.platformId)
                this.categoryMappingId = platformCategory?.categoryMappingId
                this.platformCategoryName = platformCategory?.platformCategoryName
                this.platformName = platform.platformName
                this.platformCategoryId = platformCategory?.platformCategoryId
                this.attributes = attributes.map {
                    PublishAttributePairResp().apply {
                        this.attributeId = it.attributeId
                        this.attributeValueId = it.attributeValueId
                    }
                }
                val imageCollection = getImages(product.spuCode!!)
                this.detailImageList = imageCollection.sellingPointImages
                val detailedImageSetting = temuSkcDetailedImageRepository.getByProductId(product.productId!!)
                this.settingDetailImage = PendingSettingDetailImage(
                    detailedImageSetting?.productId,
                    detailedImageSetting?.skc,
                    detailedImageSetting?.colorCode,
                    detailedImageSetting?.colorAbbrCode,
                    detailedImageSetting?.color
                )

                val templateTemuSpus = businessTypeMap[ProductShopBusinessType.FULLY_MANAGED.value]?.firstOrNull()
                if (templateTemuSpus != null) {
                    this.temuSpuId = templateTemuSpus.temuSpuId
                    this.productId = templateTemuSpus.productId
                    this.spuCode = templateTemuSpus.spuCode
                    this.productName = templateTemuSpus.productName
                    this.productNameEn = templateTemuSpus.productNameEn
                    this.businessType = templateTemuSpus.businessType
                    this.sizeGroupName = templateTemuSpus.sizeGroupName
                    this.sizeGroupCode = templateTemuSpus.sizeGroupCode
                    this.isCustomized = templateTemuSpus.isCustomized
                    this.unitSetting = templateTemuSpus.unitSetting
                    this.documentFile = templateTemuSpus.documentFile
                    this.documentFileType = templateTemuSpus.documentFileType

                    val parseJsonList = templateTemuSpus.materialLanguages?.parseJsonList(MaterialLanguage::class.java)
                    this.materialLanguages = parseJsonList

                    val countryOriginPlaces =
                        templateTemuSpus.countryOriginPlace?.parseJson(ProductTemuCountryOriginPlaceReq::class.java)

                    this.countryOriginPlace = countryOriginPlaces
                    //站点
                    val temuSiteVos = templateTemuSpus.site?.parseJsonList(TemuSiteVo::class.java)
                    this.siteList = temuSiteVos

                    this.generatedTitleMissingFieldsJson = templateTemuSpus.getParsedGeneratedTitleMissingFields()
                    this.generatedTitleOverLengthFlag = templateTemuSpus.generatedTitleOverLengthFlag
                    this.generatedTitlesJson = buildCompatibleTitlesJson(
                        platform,
                        templateTemuSpus.getParsedGeneratedTitles(),
                        templateTemuSpus.productName,
                        templateTemuSpus.productNameEn
                    )

                    val skcTemuList = productTemplateTemuSkcRepository.listByTemuSpuId(templateTemuSpus.temuSpuId!!)
                        .filter { skcTemu ->
                            skcTemu.state == Bool.YES.code
                        }
                    //获取SKC的轮播图
                    val productDetailImages: List<ImageAniVo> = imageCollection.productDetailImages
                    val skcList = skcTemuList.map { it.skc!! }
                    val skcImages = imageCollectionHelper.fetchSkcImages(
                        productDetailImages,
                        skcList,
                        ImageTypeEnum.PRODUCT_DETAIL.code
                    )
                    if (CollectionUtils.isNotEmpty(skcTemuList)) {
                        val temuSkcIdList = skcTemuList.mapNotNull { it.temuSkcId }
                        val skuTemuList = productTemplateTemuSkuRepository.listByTemuSkcIdList(temuSkcIdList)

                        this.skcList = skcTemuList.map { skcTemu ->
                            ProductPendingTemuSkcResp().apply {
                                this.temuSkcId = skcTemu.temuSkcId
                                this.temuSpuId = skcTemu.temuSpuId
                                this.productSkcId = skcTemu.productSkcId
                                this.skc = skcTemu.skc
                                this.color = skcTemu.color
                                this.articleNumber = skcTemu.articleNumber
                                this.colorCode = skcTemu.colorCode
                                this.colorAbbrCode = skcTemu.colorAbbrCode
                                this.platformColor = skcTemu.platformColor
                                this.pictures = skcTemu.pictures
                                this.state = skcTemu.state
                                this.carouselImageUrls = skcImages[skcTemu.skc]
                                this.skuList = skuTemuList
                                    .filter { skuTemu -> skuTemu.temuSkcId == skcTemu.temuSkcId }
                                    .groupBy { it.sizeName }
                                    .map { (size, skuTemu) ->
                                        ProductPendingTemuSkuResp().apply {
                                            this.sizeName = size
                                            if (skuTemu.isNotEmpty()) {
                                                val first = skuTemu.first()
                                                this.sellerSku = first.sellerSku
                                                this.barcode = first.barcode
                                                this.enableState = first.enableState
                                                val classifiedAttr =
                                                    first.classifiedAttrs?.parseJson(ClassifiedAttr::class.java)
                                                this.classifiedAttrs = classifiedAttr

                                                val sensitiveConfigs =
                                                    first.sensitiveConfig?.parseJson(SensitiveConfig::class.java)
                                                this.sensitiveConfig = sensitiveConfigs

                                                this.longestEdge = first.longestEdge
                                                this.secondEdge = first.secondEdge
                                                this.shortestEdge = first.shortestEdge
                                                this.weight = first.weight
                                                this.referenceUrl = first.referenceUrl

                                                //                                                this.barcodes = skuTemu.first().barcodes?.parseJsonList(ComboBarcodeInfoDto::class.java)
                                                this.countryPriceList = skuTemu.map { cp ->
                                                    ProductPendingTemuDetailDataResp.ProductPendingTemuCountrySkuResp().apply {
                                                        this.temuSkuId = cp.temuSkuId
                                                        this.temuSkcId = cp.temuSkcId
                                                        this.temuSpuId = cp.temuSpuId
                                                        this.country = cp.country
                                                        this.stockQuantity = cp.stockQuantity
                                                        this.sizeName = cp.sizeName
                                                        this.enableState = cp.enableState
                                                        val manufacturerRecommendedPriceList =
                                                            cp.manufacturerRecommendedPrice?.parseJsonList(
                                                                FullyManagedPrice::class.java
                                                            )
                                                        this.manufacturerRecommendedPrices = manufacturerRecommendedPriceList

                                                        val recommendedPriceList =
                                                            cp.recommendedPrice?.parseJsonList(FullyManagedPrice::class.java)
                                                        this.recommendedPrices = recommendedPriceList

                                                        val declaredPriceList =
                                                            cp.declaredPrice?.parseJsonList(FullyManagedPrice::class.java)
                                                        this.declaredPrices = declaredPriceList
                                                    }
                                                }
                                            }
                                        }
                                    }
                            }
                        }
                    }
                }
            }

            this.semiTemuDetail = ProductPendingTemuDetailDataResp().apply {

                this.categoryId = product.categoryId
                val platformCategory = getPlatformCategory(product, platform.platformId)
                this.categoryMappingId = platformCategory?.categoryMappingId
                this.platformCategoryName = platformCategory?.platformCategoryName
                this.platformName = platform.platformName
                this.platformCategoryId = platformCategory?.platformCategoryId
                this.attributes = attributes.map {
                    PublishAttributePairResp().apply {
                        this.attributeId = it.attributeId
                        this.attributeValueId = it.attributeValueId
                    }
                }
                val imageCollection = getImages(product.spuCode!!)
//                    this.mainUrlList = imageCollection.limitedMainImages()
                this.detailImageList = imageCollection.sellingPointImages
                val detailedImageSetting = temuSkcDetailedImageRepository.getByProductId(product.productId!!)
                this.settingDetailImage = PendingSettingDetailImage(
                    detailedImageSetting?.productId,
                    detailedImageSetting?.skc,
                    detailedImageSetting?.colorCode,
                    detailedImageSetting?.colorAbbrCode,
                    detailedImageSetting?.color
                )

                val templateTemuSpus = businessTypeMap[ProductShopBusinessType.SEMI_MANAGED.value]?.firstOrNull()
                if (templateTemuSpus != null) {
                    this.temuSpuId = templateTemuSpus.temuSpuId
                    this.productId = templateTemuSpus.productId
                    this.spuCode = templateTemuSpus.spuCode
                    this.productName = templateTemuSpus.productName
                    this.productNameEn = templateTemuSpus.productNameEn
                    this.businessType = templateTemuSpus.businessType
                    this.sizeGroupName = templateTemuSpus.sizeGroupName
                    this.sizeGroupCode = templateTemuSpus.sizeGroupCode
                    this.isCustomized = templateTemuSpus.isCustomized
                    this.unitSetting = templateTemuSpus.unitSetting
                    this.documentFile = templateTemuSpus.documentFile
                    this.documentFileType = templateTemuSpus.documentFileType
                    val parseJsonList = templateTemuSpus.materialLanguages?.parseJsonList(MaterialLanguage::class.java)
                    this.materialLanguages = parseJsonList

                    val countryOriginPlaces =
                        templateTemuSpus.countryOriginPlace?.parseJson(ProductTemuCountryOriginPlaceReq::class.java)

                    this.countryOriginPlace = countryOriginPlaces

                    //站点
                    val temuSiteVos = templateTemuSpus.site?.parseJsonList(TemuSiteVo::class.java)
                    this.siteList = temuSiteVos

                    this.generatedTitleMissingFieldsJson = templateTemuSpus.getParsedGeneratedTitleMissingFields()
                    this.generatedTitleOverLengthFlag = templateTemuSpus.generatedTitleOverLengthFlag
                    this.generatedTitlesJson = buildCompatibleTitlesJson(
                        platform,
                        templateTemuSpus.getParsedGeneratedTitles(),
                        templateTemuSpus.productName,
                        templateTemuSpus.productNameEn
                    )

                    val skcTemuList = productTemplateTemuSkcRepository.listByTemuSpuId(templateTemuSpus.temuSpuId!!)
                        .filter { skcTemu ->
                            skcTemu.state == Bool.YES.code
                        }

                    //获取SKC的轮播图
                    val productDetailImages: List<ImageAniVo> = imageCollection.productDetailImages
                    val skcList = skcTemuList.map { it.skc!! }
                    val skcImages = imageCollectionHelper.fetchSkcImages(
                        productDetailImages,
                        skcList,
                        ImageTypeEnum.PRODUCT_DETAIL.code
                    )
                    if (CollectionUtils.isNotEmpty(skcTemuList)) {
                        val temuSkcIdList = skcTemuList.mapNotNull { it.temuSkcId }
                        val skuTemuList = productTemplateTemuSkuRepository.listByTemuSkcIdList(temuSkcIdList)

                        this.skcList = skcTemuList.map { skcTemu ->
                            ProductPendingTemuSkcResp().apply {
                                this.temuSkcId = skcTemu.temuSkcId
                                this.temuSpuId = skcTemu.temuSpuId
                                this.productSkcId = skcTemu.productSkcId
                                this.skc = skcTemu.skc
                                this.color = skcTemu.color
                                this.colorCode = skcTemu.colorCode
                                this.colorAbbrCode = skcTemu.colorAbbrCode
                                this.platformColor = skcTemu.platformColor
                                this.pictures = skcTemu.pictures
                                this.state = skcTemu.state
                                this.carouselImageUrls = skcImages[skcTemu.skc]
                                this.articleNumber = skcTemu.articleNumber
                                this.skuList = skuTemuList
                                    .filter { skuTemu -> skuTemu.temuSkcId == skcTemu.temuSkcId }
                                    .groupBy { it.sizeName }
                                    .map { (size, skuTemu) ->
                                        ProductPendingTemuSkuResp().apply {
                                            this.sizeName = size
                                            if (skuTemu.isNotEmpty()) {
                                                val first = skuTemu.first()
                                                this.sellerSku = first.sellerSku
                                                this.barcode = first.barcode
                                                this.enableState = first.enableState
                                                val classifiedAttr =
                                                    first.classifiedAttrs?.parseJson(ClassifiedAttr::class.java)
                                                this.classifiedAttrs = classifiedAttr

                                                val sensitiveConfigs =
                                                    first.sensitiveConfig?.parseJson(SensitiveConfig::class.java)
                                                this.sensitiveConfig = sensitiveConfigs

                                                this.longestEdge = first.longestEdge
                                                this.secondEdge = first.secondEdge
                                                this.shortestEdge = first.shortestEdge
                                                this.weight = first.weight
                                                this.referenceUrl = first.referenceUrl
                                                //                                                this.barcodes = skuTemu.first().barcodes?.parseJsonList(ComboBarcodeInfoDto::class.java)
                                                this.countryPriceList = skuTemu.map { cp ->
                                                    ProductPendingTemuCountrySkuResp().apply {
                                                        this.temuSkuId = cp.temuSkuId
                                                        this.temuSkcId = cp.temuSkcId
                                                        this.temuSpuId = cp.temuSpuId
                                                        this.country = cp.country
                                                        this.stockQuantity = cp.stockQuantity
                                                        this.sizeName = cp.sizeName
                                                        this.enableState = cp.enableState
                                                        val manufacturerRecommendedPriceList =
                                                            cp.manufacturerRecommendedPrice?.parseJsonList(
                                                                FullyManagedPrice::class.java
                                                            )
                                                        this.manufacturerRecommendedPrices = manufacturerRecommendedPriceList

                                                        val recommendedPriceList =
                                                            cp.recommendedPrice?.parseJsonList(FullyManagedPrice::class.java)
                                                        this.recommendedPrices = recommendedPriceList

                                                        val declaredPriceList =
                                                            cp.declaredPrice?.parseJsonList(FullyManagedPrice::class.java)
                                                        this.declaredPrices = declaredPriceList

                                                    }
                                                }
                                            }
                                        }
                                    }
                            }
                        }

                    }
                }
            }
        }
    }

    /**
     * 商品更新
     */
    override fun update(req: ProductPendingUpdateTemuReq) {
        req.validate()
        // sku(存在: 更新, 不存在: 新增), 新增时id为空, 也要判断尺码条件, 可能已经存在, 但可用状态是0
        val temuSpu = productTemplateTemuSpuRepository.getById(req.temuSpuId!!)
        requireNotNull(temuSpu) { "temu spu不存在 ${req.temuSpuId}" }

        val product = productRepository.getById(temuSpu.productId!!)

        //更新图库
        savePicture(product)

        //设置详情图
        settingDetailPicture(req.settingDetailImage)

        //设置属性
        if (CollectionUtils.isNotEmpty(req.attributes)) {
            val platformEnum = TEMU
            //先删除已有属性
            productAttributesRepository.removeByProductIdAndPlatformId(product.productId!!, platformEnum.platformId)
            productAttributesRepository.saveBatch(
                req.attributes?.map { k ->
                    ProductAttributes().apply {
                        this.attributeId = k.attributeId
                        this.attributeValueId = k.attributeValueId
                        this.productId = product.productId
                        this.categoryId = product.categoryId
                        this.platformId = platformEnum.platformId
                    }
                })
        }

        // 更新SPU (每个字段判断, 有不一致的才更新)
        updateTemuSpu(temuSpu, req)
        // 更新SKC 和 SKU
        updateTemuSkcAndSku(temuSpu.temuSpuId!!, req.skcList, temuSpu)
    }

    /**
     * 任务操作-开始任务
     */
    @Transactional(rollbackFor = [Exception::class])
    override fun start(temuSpuIds: List<Long>) {
        val templateSpuList = productTemplateTemuSpuRepository.listByIds(temuSpuIds)
            .filter { it.taskStatus == ProductPendingTaskStatusEnum.PENDING.code }
        if (templateSpuList.isEmpty()) {
            throw IllegalArgumentException("商品不存在或已被处理")
        }
        val updateSpuList = templateSpuList.map { templateSpu ->
            ProductTemplateTemuSpu().apply {
                this.temuSpuId = templateSpu.temuSpuId
                this.taskStatus = ProductPendingTaskStatusEnum.STARTING.code
            }
        }
        productTemplateTemuSpuRepository.updateBatchById(updateSpuList)
    }

    /**
     * 任务操作-更换人员
     */
    override fun change(req: ProductPendingTemuTaskChangeReq) {
        // 获取合法的商品SPU
        val templateSpuList = productTemplateTemuSpuRepository.listByIds(req.temuSpuIds)
            .filter {
                it.taskStatus == ProductPendingTaskStatusEnum.FAILED.code
                        || it.taskStatus == ProductPendingTaskStatusEnum.PENDING.code
                        || it.taskStatus == ProductPendingTaskStatusEnum.STARTING.code
            }
        if (templateSpuList.isEmpty()) {
            throw IllegalArgumentException("商品不存在或已被处理")
        }
        // 多人员则平均分配
        val userSpuMap = mutableMapOf<ProductPendingTemuTaskChangeReq.PendingTemuTaskChangeUserReq, MutableList<ProductTemplateTemuSpu>>()
        if (req.users.size > templateSpuList.size) {
            log.info { "人数 > 商品数" }
            // 前spuList.size个用户各分配一个商品，剩下的用户分配空列表
            for (i in req.users.indices) {
                val userId = req.users[i]
                val spuListForUser = if (i < templateSpuList.size) {
                    // 为前N个用户分配一个商品（N等于商品数量）
                    mutableListOf(templateSpuList[i])
                } else {
                    mutableListOf()
                }
                userSpuMap[userId] = spuListForUser
            }

        } else if (req.users.size < templateSpuList.size) {
            log.info { "人数 < 商品数" }
            // 计算基本分配数量和需要多分配一个商品的用户数
            val baseCount = templateSpuList.size / req.users.size
            val extraCount = templateSpuList.size % req.users.size

            var currentIndex = 0
            for (i in req.users.indices) {
                val userId = req.users[i]
                // 前extraCount个用户多分配一个商品
                val count = if (i < extraCount) baseCount + 1 else baseCount
                // 截取对应数量的商品
                val spuListForUser = templateSpuList.subList(currentIndex, currentIndex + count).toMutableList()
                userSpuMap[userId] = spuListForUser
                currentIndex += count
            }

        } else {
            log.info { "人数 = 商品数" }
            // 一对一分配
            for (i in req.users.indices) {
                userSpuMap[req.users[i]] = mutableListOf(templateSpuList[i])
            }
        }
        if (userSpuMap.isEmpty()) {
            throw BusinessException("平均分配失败")
        }
        userSpuMap.forEach { (user, spuList) ->
            val updateSpuList = spuList.map { templateSpu ->
                ProductTemplateTemuSpu().apply {
                    this.temuSpuId = templateSpu.temuSpuId
                    this.taskExecutorId = user.userId
                    this.taskExecutorName = user.userName
                }
            }
            productTemplateTemuSpuRepository.updateBatchById(updateSpuList)
        }
    }

    /**
     * 任务操作-取消任务
     */
    override fun cancel(req: ProductPendingTemuTaskCancelReq) {
        if (req.temuSpuIds.isEmpty()) {
            throw IllegalArgumentException("请选择商品")
        }
        val cancel = PendingCancelReasonEnum.getByCode(req.cancelReason) ?: throw IllegalArgumentException("取消原因不存在")
        val templateSpuList = productTemplateTemuSpuRepository.listByIds(req.temuSpuIds)
            .filter {
                it.taskStatus == ProductPendingTaskStatusEnum.FAILED.code
                        || it.taskStatus == ProductPendingTaskStatusEnum.PENDING.code
                        || it.taskStatus == ProductPendingTaskStatusEnum.STARTING.code
            }
        if (templateSpuList.isEmpty()) {
            throw IllegalArgumentException("商品不存在或已被处理")
        }
        val updateSpuList = templateSpuList.map { templateSpu ->
            ProductTemplateTemuSpu().apply {
                this.temuSpuId = templateSpu.temuSpuId
                this.taskStatus = ProductPendingTaskStatusEnum.CANCELED.code
                this.cancelReason = cancel.desc
            }
        }
        productTemplateTemuSpuRepository.updateBatchById(updateSpuList)
    }


    /**
     * 上架记录-统计
     */
    override fun publishLogStatistics(): ProductPendingPublishLogStatisticsResp {
        // 状态统计
        val taskStatusStats = productTemplateTemuSpuRepository.taskStatusStats()
        // 今天创建的数量
        val todayNew = productTemplateTemuSpuRepository.ktQuery()
            .between(ProductTemplateTemuSpu::createdTime, LocalDate.now().atStartOfDay(), LocalDate.now())
            .count()
        // 今天发布的数量
        val todayPublished = productTemplateTemuSpuRepository.ktQuery()
            .eq(ProductTemplateTemuSpu::taskStatus, ProductPendingTaskStatusEnum.COMPLETED.code)
            .between(ProductTemplateTemuSpu::taskCompleteTime, LocalDate.now().atStartOfDay(), LocalDate.now())
            .count()
        return ProductPendingPublishLogStatisticsResp().apply {
            this.pendingCount = taskStatusStats.find { it.taskStatus == ProductPendingTaskStatusEnum.PENDING.code }?.taskStatusCount?.toInt() ?: 0
            this.startingCount = taskStatusStats.find { it.taskStatus == ProductPendingTaskStatusEnum.STARTING.code }?.taskStatusCount?.toInt() ?: 0
            this.failedCount = taskStatusStats.find { it.taskStatus == ProductPendingTaskStatusEnum.FAILED.code }?.taskStatusCount?.toInt() ?: 0
            this.todayNewCount = todayNew?.toInt() ?: 0
            this.todayPublishedCount = todayPublished?.toInt() ?: 0
        }
    }

    /**
     * 上架记录-分页列表
     */
    override fun publishLogPage(req: ProductPendingPublishLogPageReq): PageVo<ProductPendingPublishLogPageResp> {
        val page = productTemplateTemuSpuRepository.page(
            Page(req.pageNum.toLong(), req.pageSize.toLong()),
            KtQueryWrapper(ProductTemplateTemuSpu::class.java)
                .eq(req.taskState != null, ProductTemplateTemuSpu::taskStatus, req.taskState)
                .orderByDesc(ProductTemplateTemuSpu::createdTime)
        )
        if (page.records.isEmpty()) {
            return PageRespHelper.of(page.current.toInt(), page.total, emptyList())
        }
        val shopMap = shopRepository.listByIds(page.records.mapNotNull { it.shopId })
            ?.associateBy { it.shopId }
        val result = page.records.map { templateSpu ->
            ProductPendingPublishLogPageResp().apply {
                this.shopName = shopMap?.get(templateSpu.shopId)?.shopName
                this.spuCode = templateSpu.spuCode
                this.creatorId = templateSpu.creatorId
                this.creatorName = templateSpu.creatorName
                this.createdTime = templateSpu.createdTime
                this.taskStatus = templateSpu.taskStatus
                this.listingUserName = templateSpu.taskExecutorName
                this.publishTime = templateSpu.taskCompleteTime
                this.operationCount = 0
                this.reason = templateSpu.cancelReason
            }
        }
        return PageRespHelper.of(page.current.toInt(), page.total, result)
    }

    /**
     * 导入-待上架-商品数据
     */
    override fun importExcel(file: MultipartFile) {
        // TEMU导入
        val listener = ImportTemuProductExcelListener()
        EasyExcel.read(file.inputStream, listener).doReadAll()

        val importList = listener.getProductList()
            .asSequence()
            .filter { it.spuCode.isNotBlank() }
            .filter { it.categoryName.isNotBlank() }
            .filter { it.skcCode.isNotBlank() }
            .filter { it.mainColor.isNotBlank() }
            .filter { it.size.isNotBlank() }
            .toList()
        if (CollectionUtils.isEmpty(importList)) {
            throw BaseBizException("导入TEMU数据为空")
        }

        // 检查店铺
        val shopNameList = importList.map { it.listingShopName }.distinct()
        if (shopNameList.isEmpty()) {
            throw BaseBizException("店铺不能为空")
        }
        val existsShopList = shopRepository.ktQuery()
            .`in`(Shop::shopName, shopNameList)
            .eq(Shop::platformId, platform.platformId)
            .list()
            .mapNotNull { it.shopName }
        shopNameList.forEach { shopName ->
            if (!existsShopList.contains(shopName)) {
                throw BaseBizException("店铺: $shopName 不存在")
            }
        }

        val list = importList.map {
            ImportProductRecord().apply {
                this.supplyMode = SupplyModeEnum.LOGO_NUM.desc
                this.spuCode = it.spuCode
                this.importDataType = ImportDataTypeEnum.ALL.code
                this.importSource = ImportSourceEnum.PENDING_UPLOAD.code
                this.platformId = platform.platformId
                this.importData = it.toJson()
                this.skc = it.skcCode
            }
        }
        importProductRecordRepository.saveBatch(list, 100)
    }

    /**
     * 收集给定产品ID的所有标签相关数据
     */
    private fun collectTagData(templateSpuList: List<ProductTemplateTemuSpu>): TagContextDto {
        val productIds = templateSpuList.mapNotNull { it.productId }.distinct()

        // 1. 获取产品级别的标签
        val productIdTagMap = productTagRepository.getTagMapByProductIds(productIds)

        // 获取SPU数据
        val spuList: List<ProductTemplateTemuSpu> = templateSpuList
        val spuIds: List<Long> = spuList.mapNotNull { it.temuSpuId }.distinct()
        val productIdToSpuIds: Map<Long, List<Long>> = spuList
            .filter { it.productId.isNotNull() }
            .groupBy { it.productId!! }
            .mapValues { entry ->
                entry.value.mapNotNull { it.temuSpuId }
            }

        val spuIdTagMap = if (spuIds.isNotEmpty()) {
            productTagRepository.getTagMapByTemuSpuIds(spuIds)
        } else {
            emptyMap()
        }

        return TagContextDto(
            productIdTagMap,
            productIdToSpuIds,
            spuIdTagMap,
        )
    }

    private fun settingDetailPicture(settingDetailImage: PendingSettingDetailImage?) {
        settingDetailImage?.let {
            val byProductId = temuSkcDetailedImageRepository.getByProductId(settingDetailImage.productId!!)
            if (byProductId.isNull()) {
                val detailedImage = TemuSkcDetailedImage().apply {
                    productId = settingDetailImage.productId
                    skc = settingDetailImage.skc
                    color = settingDetailImage.color
                    colorCode = settingDetailImage.colorCode
                    colorAbbrCode = settingDetailImage.colorAbbrCode
                }
                temuSkcDetailedImageRepository.save(detailedImage)
            } else {
                byProductId!!.skc = settingDetailImage.skc
                byProductId.color = settingDetailImage.color
                byProductId.colorCode = settingDetailImage.colorCode
                byProductId.colorAbbrCode = settingDetailImage.colorAbbrCode
                temuSkcDetailedImageRepository.updateById(byProductId)
            }
        }
    }

    /**
     * 获取映射的平台品类
     *
     * @param product
     * @param platformId
     * @return
     */
    private fun getPlatformCategory(product: Product, platformId: Long): PublishCategoryMapping? {
        if (product.categoryId != null) {
            val categoryMapping = publishCategoryMappingRepository.getByPublishCategoryIdAndPlatformId(product.categoryId!!, platformId)
            if (categoryMapping != null) {
                return categoryMapping
            }
        }
        return null
    }

    /**
     * 获取图片分类集合
     * @param spuCode
     */
    private fun getImages(spuCode: String): ImageCollectionDTO {
        val imageRepository = imageRepositoryRepository.getBySpuCode(spuCode)
        return if (imageRepository != null) {
            imageCollectionHelper.buildImageCollection(spuCode, imageRepository)
        } else {
            ImageCollectionDTO()
        }
    }

    /**
     * 创建兼容历史数据的标题生成数据
     * 如果generatedTitles为空，则构建一个兼容对象
     */
    private fun buildCompatibleTitlesJson(
        platform: PlatformEnum,
        existingTitles: ProductTitleConfigGenerationDTO?,
        fallbackTitle: String?,
        fallbackTitleEn: String? = null,
    ): ProductTitleConfigGenerationDTO {
        val config = productTitleConfigService.findByPlatformId(platform.platformId)
        val configId = config?.productTitleConfigId
        val count = config?.titleCount ?: 1

        return existingTitles?.apply {
            productTitleConfigId = configId
            titleCount = count
        } ?: ProductTitleConfigGenerationDTO(
            productTitleConfigId = configId,
            titleCount = count,
            titles = listOf(
                ProductTitleConfigGenerationItemDTO(
                    index = 0,
                    title = fallbackTitle,
                    titleEn = fallbackTitleEn,
                )
            ),
            lastGeneratedTime = null
        )
    }

    private fun savePicture(product: Product) {
        val imageRepository = imageRepositoryRepository.getBySpuCode(product.spuCode!!)
        if (Objects.isNull(imageRepository)) {
            return
        }
        val picture = productPictureRepository.getOneByProductId(product.productId!!)
        if (Objects.isNull(picture)) {
            val productPicture = ProductPicture().apply {
                this.productId = product.productId
                this.spuCode = product.spuCode
                this.sourceImageUrl = imageRepository!!.mainUrl
            }
            productPictureRepository.save(productPicture)
        }
    }

    /**
     * 更新Temu spu
     * @param req
     */
    private fun updateTemuSpu(temuSpu: ProductTemplateTemuSpu, req: ProductPendingUpdateTemuReq) {
        var update = false
        if (req.productName?.isNotBlank() == true && !Objects.equals(req.productName, temuSpu.productName)) {
            temuSpu.productName = req.productName
            update = true
        }

        if (req.productNameEn?.isNotBlank() == true && !Objects.equals(req.productNameEn, temuSpu.productNameEn)) {
            temuSpu.productNameEn = req.productNameEn
            update = true
        }

        val materialLanguages = req.materialLanguages
        if (req.materialLanguages?.isNotEmpty() == true && !Objects.equals(req.materialLanguages!!.toJson(), temuSpu.materialLanguages)) {
            temuSpu.materialLanguages = materialLanguages?.toJson()
            update = true
        }

        val countryOriginPlaces = req.countryOriginPlace
        if (req.countryOriginPlace?.isNotNull() == true && !Objects.equals(req.countryOriginPlace!!.toJson(), temuSpu.countryOriginPlace)) {
            temuSpu.countryOriginPlace = countryOriginPlaces?.toJson()
            update = true
        }

        val siteList = req.siteList
        if (req.siteList?.isNotEmpty() == true && !Objects.equals(req.siteList!!.toJson(), temuSpu.site)) {
            temuSpu.site = siteList?.toJson()
            update = true
        }

        if (req.sizeGroupCode?.isNotBlank() == true && req.sizeGroupName?.isNotBlank() == true
            && !Objects.equals(req.sizeGroupCode, temuSpu.sizeGroupCode) && !Objects.equals(req.sizeGroupName, temuSpu.sizeGroupName)
        ) {
            temuSpu.sizeGroupCode = req.sizeGroupCode
            temuSpu.sizeGroupName = req.sizeGroupName
            update = true
        }

        if (req.sizeSpecification?.isNotBlank() == true && !Objects.equals(req.sizeSpecification, temuSpu.sizeSpecification)) {
            temuSpu.sizeSpecification = req.sizeSpecification
            update = true
        }

        if (req.isCustomized?.isNotNull() == true && !Objects.equals(req.isCustomized, temuSpu.isCustomized)) {
            temuSpu.isCustomized = req.isCustomized
            update = true
        }

        if (req.documentFile?.isNotBlank() == true && !Objects.equals(req.documentFile, temuSpu.documentFile)) {
            temuSpu.documentFile = req.documentFile
            update = true
        }

        if (req.documentFileType?.isNotNull() == true && !Objects.equals(req.documentFileType, temuSpu.documentFileType)) {
            temuSpu.documentFileType = req.documentFileType
            update = true
        }

        if (req.unitSetting?.isNotBlank() == true && !Objects.equals(req.unitSetting, temuSpu.unitSetting)) {
            temuSpu.unitSetting = req.unitSetting
            update = true
        }

        if (req.businessType == 3) {
            //全托默认公制单位
            temuSpu.unitSetting = "1"
            update = true
        }

        if (req.generatedTitleMissingFieldsJson.isNotEmpty() && ObjectUtils.notEqual(req.generatedTitleMissingFieldsJson, temuSpu.getParsedGeneratedTitleMissingFields())) {
            temuSpu.generatedTitleMissingFieldsJson = req.generatedTitleMissingFieldsJson!!.toJson()
            update = true
        }

        if (req.generatedTitleOverLengthFlag != null && ObjectUtils.notEqual(req.generatedTitleOverLengthFlag, temuSpu.generatedTitleOverLengthFlag)) {
            temuSpu.generatedTitleOverLengthFlag = req.generatedTitleOverLengthFlag
            update = true
        }

        if (req.generatedTitlesJson != null && ObjectUtils.notEqual(req.generatedTitlesJson, temuSpu.getParsedGeneratedTitles())) {
            temuSpu.setParsedGeneratedTitles(req.generatedTitlesJson)
            update = true
        }

        if (update) {
            productTemplateTemuSpuRepository.updateById(temuSpu)
        }
    }

    /**
     * 更新temu skc和sku
     * @param temuSpuId
     * @param req
     */
    private fun updateTemuSkcAndSku(temuSpuId: Long, req: List<ProductPendingUpdateTemuSkcReq>?, temuSpu: ProductTemplateTemuSpu) {
        if (req.isNullOrEmpty()) {
            return
        }
        val spuTemu = productTemplateTemuSpuRepository.getById(temuSpuId)
        req.forEach { skcReq ->
            // 处理SKC
            val temuSkc: ProductTemplateTemuSkc? =
                productTemplateTemuSkcRepository.getByTemuSpuIdAndTemuSkcId(temuSpuId, skcReq.temuSkcId!!)

            requireNotNull(temuSkc) { "temu skc不存在 ${skcReq.temuSkcId}" }
            temuSkc.platformColor = skcReq.platformColor
            temuSkc.articleNumber = skcReq.articleNumber
            temuSkc.state = Bool.YES.code
            productTemplateTemuSkcRepository.updateById(temuSkc)

            // 处理SKU
            val saveSkuList = mutableListOf<ProductTemplateTemuSku>()
            val updateSkuList = mutableListOf<ProductTemplateTemuSku>()
            skcReq.skuList?.forEach { skuReq ->

                if (skuReq.classifiedAttrs == null) {
                    throw IllegalArgumentException("sku 分类不能为空")
                }
                if (skuReq.classifiedAttrs!!.typeCode == null) {
                    throw IllegalArgumentException("sku 分类类型不能为空")
                }
                if (skuReq.classifiedAttrs!!.unitCode == null) {
                    throw IllegalArgumentException("sku 分类单位不能为空")
                }

                // 处理SKU分类
                val classifiedAttrs = skuReq.classifiedAttrs
                if (classifiedAttrs != null) {
                    classifiedAttrs.typeCode?.let { cc -> classifiedAttrs.typeName = ClassifiedAttrTypeCodeEnum.getByCode(cc)?.desc }
                    classifiedAttrs.unitCode?.let { cc -> classifiedAttrs.unit = ClassifiedAttrUnitEnum.getByCode(cc)?.desc }
                }

                val temuSku: ProductTemplateTemuSku?
                if (skuReq.temuSkuId == null) {
                    // skuId为空, 可能新增/状态更新, 先用尺码和国家判断是否存在
                    temuSku = productTemplateTemuSkuRepository.getByTemuSpuIdAndTemuSkcIdAndSizeNameAndCountry(temuSpuId, temuSkc.temuSkcId!!, skuReq.sizeName!!, skuReq.country)
                    if (temuSku == null) {
                        // 不存在, 新增

                        // 判断尺码是否有条码
                        val skcTemu = productTemplateTemuSkcRepository.getByTemuSpuIdAndTemuSkcId(temuSpuId, temuSkc.temuSkcId!!)
                        val barCode = productBarCodeRepository.getBySpuCodeAndSkcAndSize(spuTemu.spuCode, skcTemu!!.skc, skuReq.sizeName!!)
                        var newBarcode: String? = null
                        if (barCode == null) {
                            // 添加条码
                            val product = productRepository.getById(spuTemu.productId)
                            val barcodeReq = BatchCreateBarCodeReq().apply {
                                this.categoryCode = product.categoryCode
                                this.categoryName = product.categoryName
                                this.skcCode = skcTemu.skc
                                this.color = skcTemu.color
                                this.spuCode = product.spuCode
                                this.groupName = product.sizeGroupName
                                this.sourceGroupCode = product.sizeGroupCode
                                this.sizeValues = listOf(skuReq.sizeName!!)
                                this.inspiraImgUrl = product.inspiraImgUrl
                                this.supplyMode = product.supplyMode
//                                this.localPrice = skcTemu.localPrice
                                this.designImgUrl = skcTemu.pictures
                                this.mainImgUrl = skcTemu.pictures
                            }
                            val barcodeResp = barCodeService.createBarcodeByForce(listOf(barcodeReq))
                            if (CollectionUtils.isNotEmpty(barcodeResp)) {
                                newBarcode = barcodeResp[0].barcode
                            }
                        } else {
                            newBarcode = barCode.barcode
                        }
                        saveSkuList.add(ProductTemplateTemuSku().apply {
                            this.temuSkuId = IdHelper.getId()
                            this.temuSkcId = temuSkc.temuSkcId
                            this.temuSpuId = spuTemu.temuSpuId
                            this.barcode = newBarcode
                            this.stockQuantity = skuReq.stockQuantity
                            this.sizeName = skuReq.sizeName
                            this.country = skuReq.country
                            this.countryName = skuReq.countryName
                            this.declaredPrice = skuReq.declaredPrices?.toJson()
                            this.recommendedPrice = skuReq.recommendedPrices?.toJson()
                            this.manufacturerRecommendedPrice = skuReq.manufacturerRecommendedPrices?.toJson()
                            this.classifiedAttrs = skuReq.classifiedAttrs?.toJson()
                            this.packingItems = skuReq.packingItems?.toJson()
                            this.referenceUrl = skuReq.referenceUrl
                            skuReq.sensitiveConfig?.isSensitiveCode = "0"
                            this.sensitiveConfig = skuReq.sensitiveConfig?.toJson()
                            this.longestEdge = skuReq.longestEdge
                            this.secondEdge = skuReq.secondEdge
                            this.shortestEdge = skuReq.shortestEdge
                            this.weight = skuReq.weight
                            this.enableState = Bool.YES.code
                            this.barcodes = skuReq.barcodeList?.toJson()
                        })
                    } // 存在, 状态更新(更新逻辑)
                } else {
                    // skuId不为空, 正常更新(更新逻辑)
                    temuSku = productTemplateTemuSkuRepository.getByTemuSpuIdAndTemuSkcIdAndTemuSkuId(temuSpuId, temuSkc.temuSkcId!!, skuReq.temuSkuId!!)
                }

                if (temuSku != null) {
                    temuSku.stockQuantity = skuReq.stockQuantity
                    temuSku.sizeName = skuReq.sizeName
                    temuSku.country = skuReq.country
                    temuSku.countryName = skuReq.countryName
                    temuSku.declaredPrice = skuReq.declaredPrices?.toJson()
                    temuSku.recommendedPrice = skuReq.recommendedPrices?.toJson()
                    temuSku.manufacturerRecommendedPrice = skuReq.manufacturerRecommendedPrices?.toJson()
                    temuSku.classifiedAttrs = skuReq.classifiedAttrs?.toJson()
                    temuSku.packingItems = skuReq.packingItems?.toJson()
                    temuSku.referenceUrl = skuReq.referenceUrl
                    skuReq.sensitiveConfig?.isSensitiveCode = "0"
                    temuSku.sensitiveConfig = skuReq.sensitiveConfig?.toJson()
                    temuSku.longestEdge = skuReq.longestEdge
                    temuSku.secondEdge = skuReq.secondEdge
                    temuSku.shortestEdge = skuReq.shortestEdge
                    temuSku.weight = skuReq.weight
                    temuSku.enableState = Bool.YES.code
                    updateSkuList.add(temuSku)
                }
            }

            if (saveSkuList.isNotEmpty()) {
                productTemplateTemuSkuRepository.saveBatch(saveSkuList)
            }
            if (updateSkuList.isNotEmpty()) {
                productTemplateTemuSkuRepository.updateBatchById(updateSkuList)
            }

            // 不在req的sku, 标记为enable=0
            val existSkuList = productTemplateTemuSkuRepository.list(
                KtQueryWrapper(ProductTemplateTemuSku::class.java)
                    .eq(ProductTemplateTemuSku::temuSpuId, temuSpuId)
                    .eq(ProductTemplateTemuSku::temuSkcId, temuSkc.temuSkcId)
                    .eq(ProductTemplateTemuSku::enableState, Bool.YES.code)
            )
            val savetemuSkuId = (saveSkuList + updateSkuList).map { it.temuSkuId }
            val noEnableSkuList = mutableListOf<ProductTemplateTemuSku>()
            val oldTemplateTemuSkus = existSkuList.filter { existSku -> !savetemuSkuId.contains(existSku.temuSkuId) }
            oldTemplateTemuSkus.forEach { existSku ->
                // 是否存在于req
                var exist = false
                val newSkus = skcReq.skuList?.filter { skuReq -> skuReq.temuSkuId != null }
                newSkus?.forEach innerForEach@{ skuReq ->
                    if (Objects.equals(skuReq.temuSkuId, existSku.temuSkuId)
                        && existSku.enableState == Bool.YES.code
                    ) {
                        exist = true
                        return@innerForEach
                    }
                }
                if (!exist) {
                    // 不存在的标记enable=0
                    existSku.enableState = Bool.NO.code
                    noEnableSkuList.add(existSku)
                }
            }
            if (noEnableSkuList.isNotEmpty()) {
                productTemplateTemuSkuRepository.updateBatchById(noEnableSkuList)
            }
        }

        //停用未勾选的颜色
        val skcColorMap = req.map { it.color }
        val templateTemuSkcs = productTemplateTemuSkcRepository.getByTemuSpuId(temuSpuId)

        val filteredList = templateTemuSkcs.filter { !skcColorMap.contains(it.color) }.onEach { it.state = Bool.NO.code }
        filteredList.let { productTemplateTemuSkcRepository.updateBatchById(it) }
    }


}