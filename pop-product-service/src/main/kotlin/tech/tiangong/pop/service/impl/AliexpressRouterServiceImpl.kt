package tech.tiangong.pop.service.impl

import com.aliexpress.open.domain.AliexpressPostproductRedefiningFindproductinfolistqueryAeopAEProductListQuery
import com.aliexpress.open.request.AliexpressPostproductRedefiningEditsimpleproductfiledRequest
import com.aliexpress.open.response.AliexpressPostproductRedefiningEditsimpleproductfiledResponse
import com.aliexpress.open.response.AliexpressPostproductRedefiningOnlineaeproductResponse
import tech.tiangong.pop.aspect.annotation.ExternalLogApiCall
import tech.tiangong.pop.req.sdk.ae.AliexpressProductInfoRequest
import tech.tiangong.pop.req.sdk.ae.AliexpressQueryRegulatoryAttributesInfoRequest
import tech.tiangong.pop.req.sdk.ae.AliexpressSelectRegulatoryAttributesOptionsRequest
import tech.tiangong.pop.req.sdk.ae.AliexpressSetProductGroupsRequest
import tech.tiangong.pop.resp.sdk.aliexpress.*
import tech.tiangong.pop.service.AliexpressService
import java.io.File

/**
 * Aliexpress 路由
 *
 * <AUTHOR>
 */
//@Service
class AliexpressRouterServiceImpl(

) : AliexpressService {

    /**
     * 上架商品信息
     * @param productId 商品ID（必需） 日志记录关联业务ID
     * @param accessToken 访问令牌（必需）
     * @param productInfo 商品信息
     * @return AliexpressProductEditResponse 商品编辑响应
     */
    @ExternalLogApiCall(businessIdParamName = "productId")
    override fun postProduct(
        productId: Long,
        accessToken: String,
        productInfo: AliexpressProductInfoRequest
    ): AliexpressProductEditResponse {
        TODO()
    }

    /**
     * 编辑商品信息
     * @param productId 商品ID（必需） 日志记录关联业务ID
     * @param accessToken 访问令牌（必需）
     * @param productInfo 商品信息
     * @return AliexpressProductEditResponse 商品编辑响应
     */
    @ExternalLogApiCall(businessIdParamName = "productId")
    override fun editProduct(
        productId: Long,
        accessToken: String,
        productInfo: AliexpressProductInfoRequest
    ): AliexpressProductEditResponse {
        TODO()
    }

    /**
     * 查询商品详情
     *
     * @param platformProductId AE 商品ID
     * @param accessToken 访问令牌
     * @return 商品详情响应
     */
    override fun queryProduct(
        platformProductId: Long,
        accessToken: String
    ): AliexpressProductQueryResponse {
        TODO("Not yet implemented")
    }

    /**
     * 分页查询商品列表
     *
     * @param req 分页参数
     * @param accessToken 访问令牌
     * @return 商品列表响应
     */
    override fun pageProduct(
        req: AliexpressPostproductRedefiningFindproductinfolistqueryAeopAEProductListQuery,
        accessToken: String
    ): AliexpressProductPageResponse {
        TODO("Not yet implemented")
    }

    /**
     * 查询类目树列表
     *
     * @param accessToken 访问令牌
     * @param channelSellerId 渠道卖家ID
     * @param onlyWithPermission 是否只返回有权限的类目
     * @param channel 渠道
     * @param categoryId 类目ID
     * @return 类目树列表响应
     */
    override fun queryCategoryTreeList(
        accessToken: String?,
        channelSellerId: Long?,
        onlyWithPermission: Boolean,
        channel: String?,
        categoryId: Long?
    ): AliexpressCategoryTreeListResponse {
        TODO("Not yet implemented")
    }

    /**
     * 查询速卖通类目属性
     *
     * @param accessToken 访问令牌
     * @param categoryId 类目ID (必填)
     * @param locale 属性值文本对应的多语言信息，例如"en_US" (可选)
     * @param channel 渠道，例如"AE_GLOBAL" (可选)
     * @param productType 全托管商品备仓类型，0(国内备仓)，1(JIT类型)，2(海外备仓) (可选)
     * @param param2 示例：219=9441741844 类目子属性路径,由该子属性上层的类目属性id和类目属性值id组成,格式参考示例，多个用逗号隔开
     * @return CategoryAttributeResponseDTO 类目属性响应
     */
    override fun queryCategoryAttributes(
        accessToken: String?,
        categoryId: Long,
        param2: String?,
        locale: String?,
        channel: String?,
        productType: String?
    ): AliexpressCategoryAttributeResponse {
        TODO("Not yet implemented")
    }

    /**
     * 刷新授权令牌
     *
     * @param refreshToken 刷新令牌
     * @return 刷新令牌响应
     */
    override fun refreshToken(refreshToken: String): AliexpressAuthTokenRefreshResponse {
        TODO("Not yet implemented")
    }

    /**
     * 创建授权令牌
     *
     * @param code 授权码
     * @return 创建令牌响应
     */
    override fun createToken(code: String): AliexpressAuthTokenResponse {
        TODO("Not yet implemented")
    }

    /**
     * 从本地文件上传图片到速卖通临时目录
     *
     * @param accessToken 访问令牌，如为空则使用配置的默认令牌
     * @param file 本地图片文件
     * @return AliexpressImageUploadResponse 图片上传响应
     */
    override fun uploadImageFileToTempDirectory(
        accessToken: String?,
        file: File
    ): AliexpressImageUploadResponse {
        TODO("Not yet implemented")
    }

    /**
     * 查询速卖通用户运费模板列表
     *
     * @param accessToken 访问令牌（必需）
     * @param channelSellerId 渠道卖家ID（可选）
     * @return AliexpressFreightTemplateResponse 运费模板列表响应
     */
    override fun queryFreightTemplateList(
        accessToken: String,
        channelSellerId: String?
    ): AliexpressFreightTemplateResponse {
        TODO("Not yet implemented")
    }

    /**
     * 查询速卖通服务模板
     *
     * @param accessToken 访问令牌（必需）
     * @param templateId 服务模板ID（可选，默认为-1，查询所有模板）
     * @return AliexpressPromiseTemplateResponse 服务模板查询响应
     */
    override fun queryPromiseTemplates(
        accessToken: String,
        templateId: Long
    ): AliexpressPromiseTemplateResponse {
        TODO("Not yet implemented")
    }

    /**
     * 通过模板ID获取单个运费模板内容
     *
     * @param accessToken 访问令牌（必需）
     * @param templateId 服务模板ID（必须）
     * @return 服务模板查询响应
     */
    override fun getFreightSettingByTemplateQuery(
        accessToken: String,
        templateId: Long
    ): AliexpressFreightTemplateDetailResponse {
        TODO("Not yet implemented")
    }

    /**
     * 设置商品尺码表模板
     *
     * @param accessToken 访问令牌（必需）
     * @param productId 商品ID（必需）
     * @param sizeChartId 尺码表模板ID（必需）
     * @param channel 渠道（可选）
     * @param channelSellerId 渠道卖家ID（可选）
     * @return AliexpressSetSizechartResponse 设置尺码表响应
     */
    override fun setSizeChart(
        accessToken: String,
        productId: Long,
        sizeChartId: Long,
        channel: String?,
        channelSellerId: String?
    ): AliexpressSetSizeChartResponse {
        TODO("Not yet implemented")
    }

    /**
     * 查询类目下的尺码模板列表
     *
     * @param accessToken 访问令牌（必需）
     * @param leafCategoryId 叶子类目ID（必需）
     * @param currentPage 当前页码，从1开始（默认为1）
     * @param channel 渠道（可选）
     * @param channelSellerId 渠道卖家ID（可选）
     * @return AliexpressSizeTemplatesResponse 尺码模板列表响应
     */
    override fun querySizeTemplatesByCategory(
        accessToken: String,
        leafCategoryId: Long,
        currentPage: Long,
        channel: String?,
        channelSellerId: String?
    ): AliexpressSizeTemplatesResponse {
        TODO("Not yet implemented")
    }

    /**
     * 商品上架, 最多一次只能上架50个商品
     * @param accessToken token
     * @param platformProductIds 平台商品ID列表
     */
    override fun onlineAeProduct(
        accessToken: String,
        platformProductIds: List<String>
    ): AliexpressPostproductRedefiningOnlineaeproductResponse {
        TODO("Not yet implemented")
    }

    /**
     * 查询欧盟责任人列表
     *
     * @param accessToken 访问令牌（必需）
     * @param channelSellerId 渠道seller id（可选）
     * @param channel 渠道（可选）
     * @return AliexpressMsrListResponse 欧盟责任人列表响应
     */
    override fun queryMsrList(
        accessToken: String,
        channelSellerId: Long?,
        channel: String?
    ): AliexpressMsrListResponse {
        TODO("Not yet implemented")
    }

    /**
     * 商品下架
     * @param accessToken token
     * @param platformProductIds 平台商品ID列表
     */
    override fun offlineAeProduct(
        accessToken: String,
        platformProductIds: List<String>
    ): AeOfflineProductResp {
        TODO("Not yet implemented")
    }

    /**
     * 查询制造商信息详情
     *
     * @param accessToken 访问令牌（必需）
     * @param manufactureId 制造商ID（必需）
     * @param channelSellerId 渠道seller id（可选）
     * @param channel 渠道（可选）
     * @return AliexpressManufactureDetailResponse 制造商信息详情响应
     */
    override fun queryManufactureDetail(
        accessToken: String,
        manufactureId: Long,
        channelSellerId: Long?,
        channel: String?
    ): AliexpressManufactureDetailResponse {
        TODO("Not yet implemented")
    }

    /**
     * 查询商品状态
     *
     * @param accessToken 访问令牌
     * @param productId 商品ID
     * @return AliexpressProductStatusResponse 商品状态响应
     */
    override fun queryProductStatus(
        accessToken: String,
        productId: Long
    ): AliexpressProductStatusResponse {
        TODO("Not yet implemented")
    }

    /**
     * 获取制造商信息列表
     *
     * @param accessToken 访问令牌（必需）
     * @param channelSellerId 渠道seller id（可选）
     * @param channel 渠道（可选）
     * @return AliexpressManufactureListResponse 制造商信息列表响应
     */
    override fun queryManufactureList(
        accessToken: String,
        channelSellerId: Long?,
        channel: String?
    ): AliexpressManufactureListResponse {
        TODO("Not yet implemented")
    }

    /**
     * 查询商家账号关系列表
     *
     * @param token 访问令牌（必需）
     * @return AliexpressSellerRelationResponse 商家账号关系列表响应
     */
    override fun querySellerRelations(token: String): AliexpressSellerRelationResponse {
        TODO("Not yet implemented")
    }

    /**
     * 获取当前会员的产品分组
     *
     * @param accessToken 访问令牌（必需）
     * @return AliexpressProductGroupsResponse 产品分组响应
     */
    override fun queryProductGroups(accessToken: String): AliexpressProductGroupsResponse {
        TODO("Not yet implemented")
    }

    /**
     * 编辑单商品多sku价格
     *
     * @param accessToken 访问令牌（必需）
     * @param productId 商品ID（必需）
     * @param skuPrices SKU价格映射（必需）{"skuId 1": "价格", "skuId 2": "价格"}
     */
    override fun updateProductSkuPrices(
        accessToken: String,
        productId: Long,
        skuPrices: Map<String, String>
    ): AeUpdateSkuPricesResp {
        TODO("Not yet implemented")
    }

    /**

     * 编辑单商品多sku库存
     *
     * @param accessToken 访问令牌（必需）
     * @param productId 商品ID（必需）
     * @param skuStocks SKU库存映射（必需）{"skuId 1": "库存", "skuId 2": "库存"}
     */
    override fun updateProductSkuStocks(
        accessToken: String,
        productId: Long,
        skuStocks: Map<String, Long>
    ): AeUpdateSkuStocksResp {
        TODO("Not yet implemented")
    }

    /**
     * 结算税务-监管属性渲染服务
     *
     * [Pop&半托管&全托管-HScode-ISV技术手册](https://www.yuque.com/xinghui-acymr/zug5gc/rg4vg2as37c3f53b#o2PUP)
     * [结算税务-监管属性渲染服务](https://open.aliexpress.com/doc/api.htm?_mtopPrev_=use-pre-acs&_pluginSafe_=yes#/api?cid=21451&path=aliexpress.trade.tax.hscode.queryRegulatoryAttributesInfo&methodType=GET/POST)
     * @param accessToken 访问令牌（必需）
     * @param request 监管属性查询请求
     * @return AliexpressQueryRegulatoryAttributesInfoResponse 监管属性查询响应
     */
    override fun queryRegulatoryAttributesInfo(
        accessToken: String,
        request: AliexpressQueryRegulatoryAttributesInfoRequest
    ): AliexpressQueryRegulatoryAttributesInfoResponse {
        TODO("Not yet implemented")
    }

    /**
     * 批量 结算税务-监管属性渲染服务
     *
     * @param accessToken 访问令牌（必需）
     * @param requests 监管属性批量查询请求列表（最多10条）
     * @return AliexpressQueryRegulatoryAttributesInfoResponse 监管属性查询响应
     */
    override fun batchQueryRegulatoryAttributesInfo(
        accessToken: String,
        requests: List<AliexpressQueryRegulatoryAttributesInfoRequest>
    ): AliexpressQueryRegulatoryAttributesInfoResponse {
        TODO("Not yet implemented")
    }

    /**
     * 结算税务-监管属性-属性选择服务
     *
     * [链接](https://open.aliexpress.com/doc/api.htm?_mtopPrev_=use-pre-acs&_pluginSafe_=yes#/api?cid=21451&path=aliexpress.trade.tax.hscode.selectRegulatoryAttributesOptions&methodType=GET/POST)
     *
     * @param accessToken 访问令牌（必需）
     * @param request 监管属性选择请求
     * @return AliexpressSelectRegulatoryAttributesOptionsResponse 监管属性选择响应
     */
    override fun selectRegulatoryAttributesOptions(
        accessToken: String,
        request: AliexpressSelectRegulatoryAttributesOptionsRequest
    ): AliexpressSelectRegulatoryAttributesOptionsResponse {
        TODO("Not yet implemented")
    }

    /**
     * 设置商品分组
     *
     * @param productId 商品ID（必需） 日志记录关联业务ID
     * @param request 设置商品分组请求参数（必需）
     * @param accessToken 访问令牌（必需）
     * @return SetProductGroupsResponse 设置分组响应
     */
    override fun setProductGroups(
        productId: Long,
        request: AliexpressSetProductGroupsRequest,
        accessToken: String
    ): AliexpressSetProductGroupsResponse {
        TODO("Not yet implemented")
    }

    /**
     * 编辑商品的单个字段
     * @see tech.tiangong.pop.enums.ae.AeEditFiedEnum
     *
     * @param req.productId 商品ID（必需） 日志记录关联业务ID
     * @param req.fiedName （必需） AeEditFiedEnum
     * @param req.fiedValue （必需） AeEditFiedEnum不同类型, 不同value
     * @param accessToken 访问令牌（必需）
     * @return
     */
    override fun editSimpleProductFiled(
        req: AliexpressPostproductRedefiningEditsimpleproductfiledRequest,
        accessToken: String
    ): AliexpressPostproductRedefiningEditsimpleproductfiledResponse {
        TODO("Not yet implemented")
    }
}
