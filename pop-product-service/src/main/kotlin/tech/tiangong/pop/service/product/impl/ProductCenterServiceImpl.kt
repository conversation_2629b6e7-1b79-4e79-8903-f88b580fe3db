package tech.tiangong.pop.service.product.impl

import com.baomidou.mybatisplus.extension.plugins.pagination.Page
import org.apache.commons.collections4.CollectionUtils
import org.springframework.beans.BeanUtils
import org.springframework.stereotype.Service
import org.springframework.util.Assert
import team.aikero.blade.core.enums.Bool
import team.aikero.blade.core.exception.BusinessException
import team.aikero.blade.core.protocol.PageVo
import team.aikero.blade.core.toolkit.isNotBlank
import team.aikero.blade.core.toolkit.isNotEmpty
import team.aikero.blade.core.toolkit.isNotNull
import team.aikero.blade.core.toolkit.isNull
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.sequence.id.IdHelper
import team.aikero.blade.util.json.parseJson
import team.aikero.blade.util.json.parseJsonList
import team.aikero.blade.util.json.toJson
import tech.tiangong.pop.common.enums.PlatformEnum
import tech.tiangong.pop.component.MarketStyleComponent
import tech.tiangong.pop.dao.entity.*
import tech.tiangong.pop.dao.entity.dto.ProductSizeJsonDto
import tech.tiangong.pop.dao.repository.*
import tech.tiangong.pop.enums.ProductTagEnum
import tech.tiangong.pop.external.InspirationClientExternal
import tech.tiangong.pop.helper.PageRespHelper
import tech.tiangong.pop.req.product.ProductCreatePublishTaskReq
import tech.tiangong.pop.req.product.center.ProductCenterDetailReq
import tech.tiangong.pop.req.product.center.ProductCenterPageReq
import tech.tiangong.pop.req.product.center.SaveProductAttributesV2Req
import tech.tiangong.pop.req.product.center.SaveProductCenterDetailReq
import tech.tiangong.pop.resp.product.center.*
import tech.tiangong.pop.service.category.PublishGroupAttrService
import tech.tiangong.pop.service.product.ProductCenterService
import tech.tiangong.pop.service.product.pending.ProductPendingAeService
import tech.tiangong.pop.service.product.pending.ProductPendingLazadaService
import tech.tiangong.pop.service.product.pending.ProductPendingTemuService
import tech.tiangong.pop.utils.AverageGroupUtil
import java.time.LocalDateTime
import java.time.LocalTime
import java.util.*

/**
 * 商品中心 逻辑服务
 */
@Service
@Slf4j
class ProductCenterServiceImpl(
    private val productRepository: ProductRepository,
    private val productSkcRepository: ProductSkcRepository,
    private val productSizeDetailRepository:ProductSizeDetailRepository,
    private val productAttributesV2Repository: ProductAttributesV2Repository,
    private val publishAttributeRepository: PublishAttributeRepository,
    private val publishCategoryAttrRepository: PublishCategoryAttrRepository,
    private val publishGroupAttrService:PublishGroupAttrService,
    private val marketStyleComponent: MarketStyleComponent,
    private val imageRepositoryRepository:ImageRepositoryRepository,
    private val inspirationClientExternal: InspirationClientExternal,
    private val productTagRepository:ProductTagRepository,
    private val productPublishStoreRepository:ProductPublishStoreRepository,
    private val shopRepository:ShopRepository,
    private val productTemplateLazadaSpuRepository:ProductTemplateLazadaSpuRepository,
    private val productTemplateAeSpuRepository:ProductTemplateAeSpuRepository,
    private val productTemplateTemuSpuRepository:ProductTemplateTemuSpuRepository,
    private val productPendingAeService: ProductPendingAeService,
    private val productPendingLazadaService: ProductPendingLazadaService,
    private val productPendingTemuService: ProductPendingTemuService,
): ProductCenterService{


    /**
     * 商品列表-分页
     * @param req
     * @return
     */
    override fun page(req: ProductCenterPageReq): PageVo<ProductCenterPageResp> {
        if (Objects.equals(Bool.YES.code, req.today)) {
            req.createdTimeStart = LocalDateTime.now().with(LocalTime.MIN)
            req.createdTimeEnd = LocalDateTime.now().with(LocalTime.MAX)
        }
        val page = productRepository.getCenterPage(Page(req.pageNum.toLong(), req.pageSize.toLong()), req)
        val products = page.records
        if (CollectionUtils.isEmpty(products)) {
            return PageRespHelper.empty()
        }
        // 市场(一级节点)
        val marketMap = marketStyleComponent.getMarketNodeMap(1)

        val spuMainImageMap = mutableMapOf<String, String?>()
        val spuCodes = products.mapNotNull { it.spuCode }.distinct()
        if (spuCodes.isNotEmpty()) {
            val images = imageRepositoryRepository.listBySpuCodes(spuCodes)
            images
                ?.filter { image -> image.spuCode.isNotBlank() }
                ?.forEach { image ->
                    spuMainImageMap[image.spuCode!!] = image.mainUrl
                }
        }

        val productIds = products.mapNotNull { it.productId }.distinct()
        //已上架店铺
        val productIdToPublishStoreMap = getPublishStoreByProductIds(productIds);
        // 提取标签处理逻辑到单独的方法
        val tagContext = collectTagData(productIds)
        val result = products.map { product ->
            val productId = product.productId!!
            val tagCodes = getCombinedTags(productId, tagContext)
            val resp = ProductCenterPageResp().apply {
                this.productId = product.productId
                // 先找图库301, 空则用product的主图
                this.mainImgUrl = spuMainImageMap[product.spuCode] ?: product.mainImgUrl
                this.productType = product.productType
                this.supplyMode = product.supplyMode
                this.goodsType = product.goodsType
                this.waves = product.waves
                this.tagCodes = tagCodes
                this.marketCode = product.marketCode
                this.marketName = marketMap?.get(product.marketCode)
                this.spuCode = product.spuCode
                this.shopList = mutableListOf<ProductCenterPageShopResp>().apply {
                    this.add(ProductCenterPageShopResp().apply {
                        this.shopId = product.shopId
                        this.shopName = product.shopName
                    })
                }.toList()
                this.countryList = product.countrys?.split("-")?.toList()
                this.categoryId = product.categoryId
                this.categoryCode = product.categoryCode
                this.categoryName = product.categoryName
                this.creatorId = product.creatorId
                this.creatorName = product.creatorName
                this.createdTime = product.createdTime
                this.reviserId = product.reviserId
                this.reviserName = product.reviserName
                this.revisedTime = product.revisedTime
                this.submitPlatformNameList = if(productIdToPublishStoreMap.isEmpty()
                    || productIdToPublishStoreMap[productId].isNullOrEmpty()){
                        product.submitPlatform?.parseJsonList(Long::class.java)?.mapNotNull { platformId ->
                        PlatformEnum.getByPlatformId(platformId)?.platformName
                    } ?: emptyList()
                }else{
                    productIdToPublishStoreMap[productId]!!.mapNotNull {  it ->
                        PlatformEnum.getByPlatformId(it.platformId!!)?.platformName
                    }
                }
                this.imagePackageState = product.imagePackageState
                this.sourceType = product.sourceType
                this.submitShopList = mutableListOf<ProductCenterPageShopResp>().apply {
                    productIdToPublishStoreMap[productId]?.let {
                        this.addAll(it.map { ProductCenterPageShopResp().apply {
                            this.shopId = product.shopId
                            this.shopName = product.shopName
                        } })
                    }
                }.toList()
            }

            resp
        }
        return PageRespHelper.of(page.current.toInt(), page.total, result)
    }

    private fun getPublishStoreByProductIds(productIds:List<Long>):MutableMap<Long,List<Shop>>{
        val productIdToPublishShopMap:MutableMap<Long,List<Shop>> = mutableMapOf()
        val publishStoreList = productPublishStoreRepository.listByProductIds(productIds)
        if(publishStoreList.isNotEmpty()) {
            val shopInfoList = shopRepository.listByIds(publishStoreList.map { it.shopId })
            val shopIdToMap = shopInfoList.associateBy { it.shopId }

            val productIdToPublishStoreListMap = publishStoreList.groupBy { it.productId }
            productIdToPublishStoreListMap.forEach {(productId,publishStoreList) ->
                productIdToPublishShopMap.put(productId!!,publishStoreList.mapNotNull {
                    shopIdToMap[it.shopId]
                })
            }
        }
        return productIdToPublishShopMap
    }

    override fun detail(req: ProductCenterDetailReq): ProductCenterDetailResp {
        val product = productRepository.getById(req.productId)
        Assert.isTrue(product!=null,"商品不存在")
        // 市场(一级节点)
        val marketMap = marketStyleComponent.getMarketNodeMap(1)
        // 系列(二级节点) 市场-系列
        val marketSeriesMap = marketStyleComponent.getMarketNodeMap(2)
        // 风格(三级节点) 市场-系列-风格
        val marketStyleMap = marketStyleComponent.getMarketNodeMap(3)
        // 获取图片
        val image = imageRepositoryRepository.getBySpuCode(product.spuCode!!)

        var resp = ProductCenterDetailResp().apply {
            //原字段直接复制
            BeanUtils.copyProperties(product, this)
            //以下为需要特殊处理的字段
            this.mainImgUrl = image?.mainUrl ?: product.mainImgUrl
            this.marketName = marketMap?.get(product.marketCode)
            this.marketSeriesName = marketSeriesMap?.get(product.marketSeriesCode)
            this.clothingStyleName = product.clothingStyleName ?: marketStyleMap?.get(product.clothingStyleCode)
            this.shopList = mutableListOf<ProductCenterDetailShopResp>().apply {
                this.add(ProductCenterDetailShopResp().apply {
                    this.shopId = product?.shopId
                    this.shopName = product?.shopName
                })
            }.toList()
            if (product.inspiraSourceId != null) {
                // 获取灵感详情
                try {
                    val data = inspirationClientExternal.getByInspirationOrPickingId(product.inspiraSourceId!!)
                    if (data?.inspirationInfo != null) {
                        this.inspiraSource = data.inspirationInfo?.inspirationImageSource
                        this.inspiraCountry = data.inspirationInfo?.countrySiteName
                        this.planSourceName = data.inspirationInfo?.planningSourceName
                        this.inspiraImgUrl = data.inspirationInfo?.inspirationImage
                    }
                    if (data?.pickingInfo != null) {
                        this.countryList = data.pickingInfo?.countrySiteName?.let { listOf(it) }
                        this.shopList = mutableListOf<ProductCenterDetailShopResp>().apply {
                            this.add(ProductCenterDetailShopResp().apply {
                                this.shopId = data.pickingInfo?.shopId
                                this.shopName = data.pickingInfo?.shopName
                            })
                        }.toList()
                        this.selectStyleTime = data.pickingInfo?.pickingTime
                    }
                } catch (e: Exception) {
                    log.error { "getByInspirationOrPickingId 获取灵感详情报错或者数据为空，Exception=${e.message}," }
                }
            }
            //商品SKC
            val skcList = productSkcRepository.listActiveByProductId(product.productId!!)
            this.skcList = skcList.map {productSkc->
                ProductCenterDetailSkcResp().apply {
                    BeanUtils.copyProperties(productSkc, this)
                    //TODO 尺寸范围
                }
            }
            //尺码表
            val productSizeDetailList = productSizeDetailRepository.listByProductIds(mutableListOf(product.productId!!))
            this.sizeList = productSizeDetailList.map {
                ProductSizeDetailResp().apply {
                    this.sizeDetailId = it.sizeDetailId
                    this.productId = it.productId
                    this.partName = it.partName
                    this.sizeJson = it.sizeJson?.parseJson<ProductSizeJsonDto>()
                }
            }
            //商品属性值
            val attrs = productAttributesV2Repository.listByProductId(product.productId!!)
            this.productAttributeValueList = attrs?.map { attr ->
                ProductAttributesV2Vo().apply {
                    BeanUtils.copyProperties(attr, this)
                }
            }
            //商品图片及视频 TODO
            this.productImagesAndVideo = ProductImagePackageAndVideoResp()
            //品类属性选项
            this.categoryAttributeList = publishCategoryAttrRepository.listEnableAttributesByCategoryId(product.categoryId!!)
        }
        return resp
    }

    override fun saveProductDetail(req: SaveProductCenterDetailReq) {
        val product = productRepository.getById(req.productId)
        Assert.isTrue(product!=null,"商品不存在")
        //更新物流包装信息
        req.packageInfo?.let {
            product.packageInfo = it.toJson()
        }
        //更新电子说明书
        req.instructions?.let{
            product.instructions = it.toJson()
        }
        //更新商品属性
        if(req.productAttributeList.isNotEmpty()){
            updateProductAttributes(product,req.productAttributeList)
        }
    }

    /**
     * 创建上架任务
     */
    override fun createPublishTask(req: ProductCreatePublishTaskReq) {
        // 校验店铺
        val shop = shopRepository.getById(req.shopId) ?: throw Exception("店铺不存在")
        // 校验商品
        val productList = productRepository.listByIds(req.productIds)
        if (productList.isEmpty()) {
            throw Exception("商品不存在")
        }
        // 人员转map
        val userMap = req.users.associateBy { it.userId }
        when (val platformEnum = PlatformEnum.getByPlatformId(shop.platformId!!)) {
            PlatformEnum.LAZADA -> {
                // 校验商品+店铺是否存在
                val lazadaSpuCount = productTemplateLazadaSpuRepository.countByProductIdsAndShopId(req.productIds, req.shopId)
                if (lazadaSpuCount > 0) {
                    throw BusinessException("店铺:${shop.shopName}(${platformEnum.platformName}) 商品已存在待上架")
                }
                // 创建待上架
                if (userMap.isNotEmpty()) {
                    // 有人员: 平均分配
                    AverageGroupUtil.group(req.productIds, req.users.map { it.userId }).forEach { (userId, productIds) ->
                        val products = productList.filter { p -> productIds.contains(p.productId)}
                        if (products.isEmpty()) {
                            return@forEach
                        }
                        productPendingLazadaService.createPendingTask(userMap[userId], products, shop)
                    }
                } else {
                    // 无人员: 直接创建
                    productPendingLazadaService.createPendingTask(null, productList, shop)
                }
            }

            PlatformEnum.AE -> {
                // 校验商品+店铺是否存在
                val aeSpuCount = productTemplateAeSpuRepository.countByProductIdsAndShopId(req.productIds, req.shopId)
                if (aeSpuCount > 0) {
                    throw BusinessException("店铺:${shop.shopName}(${platformEnum.platformName}) 商品已存在待上架")
                }
                // 创建待上架
                if (userMap.isNotEmpty()) {
                    // 有人员: 平均分配
                    AverageGroupUtil.group(req.productIds, req.users.map { it.userId }).forEach { (userId, productIds) ->
                        val products = productList.filter { p -> productIds.contains(p.productId)}
                        if (products.isEmpty()) {
                            return@forEach
                        }
                        productPendingAeService.createPendingTask(userMap[userId], products, shop)
                    }
                } else {
                    // 无人员: 直接创建
                    productPendingAeService.createPendingTask(null, productList, shop)
                }

            }

            PlatformEnum.TEMU -> {
                // 校验商品+店铺是否存在
                val temuSpuCount = productTemplateTemuSpuRepository.countByProductIdsAndShopId(req.productIds, req.shopId)
                if (temuSpuCount > 0) {
                    throw BusinessException("店铺:${shop.shopName}(${platformEnum.platformName}) 商品已存在待上架")
                }
                // 创建待上架
                if (userMap.isNotEmpty()) {
                    // 有人员: 平均分配
                    AverageGroupUtil.group(req.productIds, req.users.map { it.userId }).forEach { (userId, productIds) ->
                        val products = productList.filter { p -> productIds.contains(p.productId)}
                        if (products.isEmpty()) {
                            return@forEach
                        }
                        productPendingTemuService.createPendingTask(userMap[userId], products, shop)
                    }
                } else {
                    // 无人员: 直接创建
                    productPendingTemuService.createPendingTask(null, productList, shop)
                }
            }

            else -> throw Exception("暂不支持该平台")
        }
    }

    private fun updateProductAttributes(product:Product,productAttributeList: List<SaveProductAttributesV2Req>) {
        if(productAttributeList.any{it.attributeId.isNull()}){
            throw BusinessException("属性ID不能为空")
        }
        val attributeIds = productAttributeList.map { it.attributeId }
        val attributeList = publishAttributeRepository.listByIds(attributeIds)
        Assert.isTrue(attributeIds.size==attributeList.size,"存在无效商品属性")
        val attributeWithValueList = publishGroupAttrService.listAttributeWithValueByIds(attributeIds)
        val attributeIdToMap = attributeWithValueList.associateBy { it.attributeId }
        val dbProductAttributes = productAttributesV2Repository.listByProductId(product.productId!!)

        var saveProductAttributesV2List: MutableList<ProductAttributesV2> = mutableListOf()
        var deleteProductAttributesV2IdList: MutableList<Long> = mutableListOf()
        //以页面提交为准创建或者更新商品属性
        if(dbProductAttributes.isNullOrEmpty()){
            saveProductAttributesV2List.addAll(productAttributeList.map {saveAttributeReq->
                var attr = attributeIdToMap[saveAttributeReq.attributeId]
                Assert.isTrue(attr!=null,"属性ID${saveAttributeReq.attributeId}无效")
                Assert.isTrue(!attr!!.attrValues.isNullOrEmpty(),"属性${attr.attributeName}没有配置值选项")
                val valueOption = attr.attrValues!!.firstOrNull() { it.attributeValueId == saveAttributeReq.attributeValueId }
                Assert.isTrue(valueOption!=null,"值ID${saveAttributeReq.attributeValueId}无效")

                ProductAttributesV2().apply {
                    this.productAttributesId = IdHelper.getId()
                    this.productId = product.productId
                    this.attributeId = attr.attributeId
                    this.attributeValueId = valueOption!!.attributeValueId
                    this.attributeValue = saveAttributeReq.attributeValue
                    this.unit = valueOption.unit
                }
            })
        }else{
            val dbAttributeIdToProductAttributeMap = dbProductAttributes.associateBy { it.attributeId }
            productAttributeList.forEach {saveAttributeReq->
                var attr = attributeIdToMap[saveAttributeReq.attributeId]
                Assert.isTrue(attr!=null,"属性ID${saveAttributeReq.attributeId}无效")
                Assert.isTrue(!attr!!.attrValues.isNullOrEmpty(),"属性${attr.attributeName}没有配置值选项")
                val valueOption = attr.attrValues!!.firstOrNull() { it.attributeValueId == saveAttributeReq.attributeValueId }
                Assert.isTrue(valueOption!=null,"值ID${saveAttributeReq.attributeValueId}无效")
                var dbProductAttributeV2 = dbAttributeIdToProductAttributeMap[attr.attributeId]
                if(dbProductAttributeV2==null){
                    dbProductAttributeV2 = ProductAttributesV2().apply {
                        this.productAttributesId = IdHelper.getId()
                        this.productId = product.productId
                        this.attributeId = attr.attributeId
                    }
                }
                dbProductAttributeV2.attributeValueId = valueOption!!.attributeValueId
                dbProductAttributeV2.attributeValue = saveAttributeReq.attributeValue
                dbProductAttributeV2.unit = valueOption.unit
                saveProductAttributesV2List.add(dbProductAttributeV2)
            }
            //将前端没传过来的属性删掉
            val keepAttributeIds = saveProductAttributesV2List.map { it.attributeId }
            val deleteIds = dbProductAttributes.filter { !keepAttributeIds.contains(it.attributeId) }.mapNotNull { it.productAttributesId }
            deleteProductAttributesV2IdList.addAll(deleteIds)
        }
        if(saveProductAttributesV2List.isNotEmpty()){
            productAttributesV2Repository.saveOrUpdateBatch(saveProductAttributesV2List)
        }
        if(deleteProductAttributesV2IdList.isNotEmpty()){
            productAttributesV2Repository.removeBatchByIds(deleteProductAttributesV2IdList)
        }
    }


    /**
     * 收集给定产品ID的所有标签相关数据
     */
    private fun collectTagData(productIds: List<Long>): TagContext {
        // 1. 获取产品级别的标签
        val productIdTagMap = if (productIds.isNotEmpty()) {
            productTagRepository.getTagMapByProductIds(productIds)
        } else {
            emptyMap()
        }

        // 2.1 获取Lazada SPU数据
        val lazadaSpuList: List<ProductTemplateLazadaSpu> = productTemplateLazadaSpuRepository.ktQuery()
            .select(ProductTemplateLazadaSpu::lazadaSpuId, ProductTemplateLazadaSpu::productId)
            .`in`(ProductTemplateLazadaSpu::productId, productIds)
            .list()
        val lazadaSpuIds: List<Long> = lazadaSpuList
            .mapNotNull { it.lazadaSpuId }
            .distinct()
        val productIdToLazadaSpuIds: Map<Long, List<Long>> = lazadaSpuList
            .filter { it.productId.isNotNull() }
            .groupBy { it.productId!! }
            .mapValues { entry ->
                entry.value.mapNotNull { it.lazadaSpuId }
            }

        // 2.2 获取AE SPU数据
        val aeSpuList: List<ProductTemplateAeSpu> = productTemplateAeSpuRepository.ktQuery()
            .select(ProductTemplateAeSpu::aeSpuId, ProductTemplateAeSpu::productId)
            .`in`(ProductTemplateAeSpu::productId, productIds)
            .list()
        val aeSpuIds: List<Long> = aeSpuList
            .mapNotNull { it.aeSpuId }
            .distinct()
        val productIdToAeSpuIds: Map<Long, List<Long>> = aeSpuList
            .filter { it.productId.isNotNull() }
            .groupBy { it.productId!! }
            .mapValues { entry ->
                entry.value.mapNotNull { it.aeSpuId }
            }

        // 2.3 获取Temu SPU数据
        val temuSpuList: List<ProductTemplateTemuSpu> = productTemplateTemuSpuRepository.ktQuery()
            .select(ProductTemplateTemuSpu::temuSpuId, ProductTemplateTemuSpu::productId)
            .`in`(ProductTemplateTemuSpu::productId, productIds)
            .list()
        val temuSpuIds: List<Long> = temuSpuList
            .mapNotNull { it.temuSpuId }
            .distinct()
        val productIdToTemuSpuIds: Map<Long, List<Long>> = temuSpuList
            .filter { it.productId.isNotNull() }
            .groupBy { it.productId!! }
            .mapValues { entry ->
                entry.value.mapNotNull { it.temuSpuId }
            }

        // 4. 获取SPU的标签
        val lazadaSpuIdTagMap = if (lazadaSpuIds.isNotEmpty()) {
            productTagRepository.getTagMapByLazadaSpuIds(lazadaSpuIds)
        } else {
            emptyMap()
        }
        val aeSpuIdTagMap = if (aeSpuIds.isNotEmpty()) {
            productTagRepository.getTagMapByAeSpuIds(aeSpuIds)
        } else {
            emptyMap()
        }
        val temuSpuIdTagMap = if (temuSpuIds.isNotEmpty()) {
            productTagRepository.getTagMapByTemuSpuIds(temuSpuIds)
        } else {
            emptyMap()
        }

        return TagContext(
            productIdTagMap,
            productIdToLazadaSpuIds,
            productIdToAeSpuIds,
            productIdToTemuSpuIds,
            lazadaSpuIdTagMap,
            aeSpuIdTagMap,
            temuSpuIdTagMap,
        )
    }
    /**
     * 合并特定产品的所有标签
     */
    private fun getCombinedTags(productId: Long, tagContext: TagContext): List<String> {
        // 从产品级别的标签开始
        val combinedTagMap = (tagContext.productIdTagMap[productId] ?: emptyMap()).toMutableMap()

        // 添加Lazada SPU标签
        val lazadaIdsForThis = tagContext.productIdToLazadaSpuIds[productId].orEmpty()
        for (lazadaSpuId in lazadaIdsForThis) {
            val tagMapForLazada = tagContext.lazadaSpuIdTagMap[lazadaSpuId].orEmpty()
            combinedTagMap.putAll(tagMapForLazada)
        }

        // 添加AE SPU标签
        val aeIdsForThis = tagContext.productIdToAeSpuIds[productId].orEmpty()
        for (aeSpuId in aeIdsForThis) {
            val tagMapForAe = tagContext.aeSpuIdTagMap[aeSpuId].orEmpty()
            combinedTagMap.putAll(tagMapForAe)
        }

        // 添加Temu SPU标签
        val temuIdsForThis = tagContext.productIdToTemuSpuIds[productId].orEmpty()
        for (temuSpuId in temuIdsForThis) {
            val tagMapForTemu = tagContext.temuSpuIdTagMap[temuSpuId].orEmpty()
            combinedTagMap.putAll(tagMapForTemu)
        }

        // 生成最终的标签代码列表
        return ProductTagEnum
            .getDescListByPage(combinedTagMap)
            .distinct()
    }

    /**
     * 内部类，用于保存所有标签相关数据
     */
    private data class TagContext(
        val productIdTagMap: Map<Long, Map<String, List<String>>>,
        val productIdToLazadaSpuIds: Map<Long, List<Long>>,
        val productIdToAeSpuIds: Map<Long, List<Long>>,
        val productIdToTemuSpuIds: Map<Long, List<Long>>,
        val lazadaSpuIdTagMap: Map<Long, Map<String, List<String>>>,
        val aeSpuIdTagMap: Map<Long, Map<String, List<String>>>,
        val temuSpuIdTagMap: Map<Long, Map<String, List<String>>>,
    )
}