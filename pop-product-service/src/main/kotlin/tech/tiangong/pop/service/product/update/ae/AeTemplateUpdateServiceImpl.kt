package tech.tiangong.pop.service.product.update.ae

import org.apache.commons.collections4.CollectionUtils
import org.springframework.stereotype.Service
import team.aikero.blade.core.enums.Bool
import team.aikero.blade.core.toolkit.isEmpty
import team.aikero.blade.core.toolkit.isNotEmpty
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.sequence.id.IdHelper
import team.aikero.blade.util.json.toJson
import tech.tiangong.pop.common.dto.ProductUpdateDto
import tech.tiangong.pop.common.enums.PlatformEnum
import tech.tiangong.pop.common.req.BatchCreateBarCodeReq
import tech.tiangong.pop.config.AliexpressProperties
import tech.tiangong.pop.dao.entity.ProductSkc
import tech.tiangong.pop.dao.entity.ProductTemplateAeSkc
import tech.tiangong.pop.dao.entity.ProductTemplateAeSku
import tech.tiangong.pop.dao.repository.*
import tech.tiangong.pop.dto.product.ProductAePropertyValueItemDTO
import tech.tiangong.pop.helper.ProductPublishAeHelper
import tech.tiangong.pop.service.product.BarCodeService
import tech.tiangong.pop.service.product.price.sale.AeSalePricingService
import tech.tiangong.pop.service.product.update.PlatformTemplateProductUpdateService

/**
 * 模板相关表更新-AE
 */
@Service
@Slf4j
class AeTemplateUpdateServiceImpl(
    private val productTemplateAeSkcRepository: ProductTemplateAeSkcRepository,
    private val productTemplateAeSkuRepository: ProductTemplateAeSkuRepository,
    private val productTemplateAeSpuRepository: ProductTemplateAeSpuRepository,
    private val productBarCodeRepository: ProductBarCodeRepository,
    private val productRepository: ProductRepository,
    private val barCodeService: BarCodeService,
    private val aeDefaultProperties: AliexpressProperties,
    private val productSkcRepository: ProductSkcRepository,
    private val aeSalePricingService: AeSalePricingService,
    private val productPublishAeHelper: ProductPublishAeHelper,
) : PlatformTemplateProductUpdateService {

    /**
     * 获取平台
     */
    override fun getPlatform(): PlatformEnum {
        return PlatformEnum.AE
    }

    /**
     * 取消SKC
     */
    override fun cancel(data: ProductUpdateDto.Data) {
        val spuList = productTemplateAeSpuRepository.listNoCompletedBySpuCode(data.spuCode!!)
        if (spuList.isEmpty()) {
            log.warn { "待上架-待上架取消SKC-错误，找不到spuCode, dto=${data.toJson()}" }
            return
        }
        spuList?.forEach { spu ->
            val productSkcList = productTemplateAeSkcRepository.getByAeSpuId(spu.aeSpuId!!)
            if (CollectionUtils.isEmpty(productSkcList)) {
                return@forEach
            }
            val updateSkcList = productSkcList
                .filter { it.skc == data.skc }
                .map {
                    ProductTemplateAeSkc().apply {
                        this.aeSkcId = it.aeSkcId
                        this.state = Bool.NO.code
                    }
                }
            // 批量更新SKC
            if (updateSkcList.isNotEmpty()) {
                productTemplateAeSkcRepository.updateBatchById(updateSkcList)

                // 批量更新SKC下的SKU
                val skcIdList = updateSkcList.map { it.aeSkcId!! }
                val skuList = productTemplateAeSkuRepository.listByAeSkcIdList(skcIdList)
                if (skuList.isNotEmpty()) {
                    val updateSkuList = skuList.map {
                        ProductTemplateAeSku().apply {
                            this.aeSkuId = it.aeSkuId
                            this.enableState = Bool.NO.code
                        }
                    }
                    // 批量更新
                    if (updateSkuList.isNotEmpty()) {
                        productTemplateAeSkuRepository.updateBatchById(updateSkuList)
                    }
                }
            }
        }
    }

    /**
     * 供货价更新
     */
    override fun updatePrice(data: ProductUpdateDto.Data) {
        val spuList = productTemplateAeSpuRepository.listNoCompletedBySpuCode(data.spuCode!!)
        if (spuList.isEmpty()) {
            log.warn { "待上架-供货价更新-错误，找不到spuCode, dto=${data.toJson()}" }
            return
        }
        spuList?.forEach { spu ->
            val productSkcList = productTemplateAeSkcRepository.getByAeSpuId(spu.aeSpuId!!)
            if (CollectionUtils.isEmpty(productSkcList)) {
                return@forEach
            }

            val productSkcIdMap = mutableMapOf<Long, ProductSkc>()
            val productSkcIds = productSkcList.mapNotNull { it.productSkcId }.distinct()
            if (productSkcIds.isNotEmpty()) {
                val productSkcList = productSkcRepository.listByIds(productSkcIds)
                if (productSkcList.isNotEmpty()) {
                    productSkcList.forEach {
                        productSkcIdMap[it.productSkcId!!] = it
                    }
                }
            }

            // 更新供货价和定价成本
            productSkcList
                .filter { it.skc == data.skc }
                .forEach {
                    val productSkc = productSkcIdMap[it.productSkcId]
                    if (productSkc != null) {
                        val updateSkc = ProductTemplateAeSkc().apply {
                            this.aeSkcId = it.aeSkcId
                            this.localPrice = data.price
                            this.costPrice = productSkc.costPrice
                        }
                        productTemplateAeSkcRepository.updateById(updateSkc)
                    }
                }

            // 所有skc更新定价成本
            productSkcList.forEach {
                val productSkc = productSkcIdMap[it.productSkcId]
                if (productSkc != null) {
                    val updateSkc = ProductTemplateAeSkc().apply {
                        this.aeSkcId = it.aeSkcId
                        this.costPrice = productSkc.costPrice
                    }
                    productTemplateAeSkcRepository.updateById(updateSkc)
                }
            }
            // 重新查一次skc, 所有skc重算售价
            val updateSkuList = mutableListOf<ProductTemplateAeSku>()

            // 重新计算售价
            val priceResp = aeSalePricingService.autoCalPriceByTemplate(spu.aeSpuId!!)
            if (!priceResp.success) {
                log.error { "[SPU:${spu.spuCode} 计算售价失败:${priceResp.errorMessage}" }
            } else {
                // 创建价格查找器，避免重复filter操作
                val priceLookup = priceResp.createPriceLookup()

                productTemplateAeSkcRepository.listByAeSpuId(spu.aeSpuId!!)
                    .forEach { skc ->
                        val skuList = productTemplateAeSkuRepository.listByAeSkcIdList(listOf(skc.aeSkcId!!))
                        if (skuList.isNotEmpty()) {
                            skuList.forEach { sku ->
                                val skcCode = skc.skc ?: ""
                                if (skcCode.isNotBlank()) {
                                    productPublishAeHelper.applyUnifiedPricingToTemplateSku(
                                        templateSku = sku,
                                        skcCode = skcCode,
                                        priceLookup = priceLookup,
                                        targetShopId = sku.shopId!!,
                                        coverOriginalPrice = true
                                    )
                                }
                                val updateSku = ProductTemplateAeSku().apply {
                                    this.aeSkuId = sku.aeSkuId
                                    this.salePrice = sku.salePrice
                                    this.retailPrice = sku.retailPrice
                                    this.purchaseSalePrice = sku.purchaseSalePrice
                                    this.purchaseRetailPrice = sku.purchaseRetailPrice
                                    this.lastSalePrice = sku.lastSalePrice
                                    this.lastRetailPrice = sku.lastRetailPrice
                                    this.nationalQuoteConfig = sku.nationalQuoteConfig
                                }
                                updateSkuList.add(updateSku)
                            }
                        }
                    }
            }

            if (updateSkuList.isNotEmpty()) {
                productTemplateAeSkuRepository.updateBatchById(updateSkuList)
            }
        }
    }

    /**
     * 新增SKC
     */
    override fun addSkc(data: ProductUpdateDto.Data) {
        // 新增SKC使用create来源, 那里有模板init操作
    }

    /**
     * 创建商品
     */
    override fun create(data: ProductUpdateDto.Data) {

    }

    /**
     * 更新品类
     */
    override fun updateCategory(data: ProductUpdateDto.Data) {
        // 模板表没有品类, product表记录
    }

    /**
     * 更新颜色
     */
    override fun updateColor(data: ProductUpdateDto.Data) {
        val spuList = productTemplateAeSpuRepository.listNoCompletedBySpuCode(data.spuCode!!)
        if (spuList.isEmpty()) {
            log.warn { "待上架-更新颜色-错误，找不到spuCode, dto=${data.toJson()}" }
            return
        }
        spuList?.forEach { spu ->
            val productSkcList = productTemplateAeSkcRepository.getByAeSpuId(spu.aeSpuId!!)
            if (CollectionUtils.isEmpty(productSkcList)) {
                return@forEach
            }
            productSkcList
                .filter { it.skc == data.skc }
                .forEach {
                    val updateSkc = ProductTemplateAeSkc().apply {
                        this.aeSkcId = it.aeSkcId
                        this.color = data.color
                        this.platformColor = data.colorCode
                        this.colorCode = data.colorCode
                        this.colorAbbrCode = data.colorAbbrCode
                    }
                    productTemplateAeSkcRepository.updateById(updateSkc)
                }
        }
    }

    /**
     * 更新采购价
     */
    override fun updatePurchasesPrice(data: ProductUpdateDto.Data) {
        val spuList = productTemplateAeSpuRepository.listNoCompletedBySpuCode(data.spuCode!!)
        if (spuList.isEmpty()) {
            log.warn { "待上架-更新采购价-错误，找不到spuCode, dto=${data.toJson()}" }
            return
        }
        spuList?.forEach { spu ->
            val productSkcList = productTemplateAeSkcRepository.listByAeSpuId(spu.aeSpuId!!)
            if (CollectionUtils.isEmpty(productSkcList)) {
                return@forEach
            }

            val productSkcIdMap = mutableMapOf<Long, ProductSkc>()
            val productSkcIds = productSkcList.mapNotNull { it.productSkcId }.distinct()
            if (productSkcIds.isNotEmpty()) {
                val productSkcList = productSkcRepository.listByIds(productSkcIds)
                if (productSkcList.isNotEmpty()) {
                    productSkcList.forEach {
                        productSkcIdMap[it.productSkcId!!] = it
                    }
                }
            }

            productSkcList
                .filter { it.skc == data.skc }
                .forEach {
                    val productSkc = productSkcIdMap[it.productSkcId]
                    if (productSkc != null) {
                        val updateSkc = ProductTemplateAeSkc().apply {
                            this.aeSkcId = it.aeSkcId
                            this.purchasePrice = data.purchasePrice
                            this.costPrice = productSkc.costPrice
                        }
                        productTemplateAeSkcRepository.updateById(updateSkc)
                    }
                }

            // 所有skc更新定价成本
            productSkcList.forEach {
                val productSkc = productSkcIdMap[it.productSkcId]
                if (productSkc != null) {
                    val updateSkc = ProductTemplateAeSkc().apply {
                        this.aeSkcId = it.aeSkcId
                        this.costPrice = productSkc.costPrice
                    }
                    productTemplateAeSkcRepository.updateById(updateSkc)
                }
            }

            // 重新查一次skc, 所有skc重算售价
            val updateSkuList = mutableListOf<ProductTemplateAeSku>()

            // 计算售价
            val priceResp = aeSalePricingService.autoCalPriceByTemplate(spu.aeSpuId!!)
            if (!priceResp.success) {
                log.error { "[SPU:${spu.spuCode} 计算售价失败:${priceResp.errorMessage}" }
            } else {
                // 创建价格查找器，避免重复filter操作
                val priceLookup = priceResp.createPriceLookup()

                productTemplateAeSkcRepository.listByAeSpuId(spu.aeSpuId!!)
                    .forEach { skc ->
                        val skuList = productTemplateAeSkuRepository.listByAeSkcIdList(listOf(skc.aeSkcId!!))
                        if (skuList.isNotEmpty()) {
                            skuList.forEach { sku ->
                                val skcCode = skc.skc ?: ""
                                if (skcCode.isNotBlank()) {
                                    productPublishAeHelper.applyUnifiedPricingToTemplateSku(
                                        templateSku = sku,
                                        skcCode = skcCode,
                                        priceLookup = priceLookup,
                                        targetShopId = sku.shopId!!,
                                        coverOriginalPrice = true
                                    )
                                }
                                val updateSku = ProductTemplateAeSku().apply {
                                    this.aeSkuId = sku.aeSkuId
                                    this.salePrice = sku.salePrice
                                    this.retailPrice = sku.retailPrice
                                    this.purchaseSalePrice = sku.purchaseSalePrice
                                    this.purchaseRetailPrice = sku.purchaseRetailPrice
                                    this.lastSalePrice = sku.lastSalePrice
                                    this.lastRetailPrice = sku.lastRetailPrice
                                    this.nationalQuoteConfig = sku.nationalQuoteConfig
                                }
                                updateSkuList.add(updateSku)
                            }
                        }
                    }
            }
            if (updateSkuList.isNotEmpty()) {
                productTemplateAeSkuRepository.updateBatchById(updateSkuList)
            }
        }
    }

    /**
     * 更新尺码信息
     */
    override fun updateSize(data: ProductUpdateDto.Data) {
        if (CollectionUtils.isEmpty(data.sizeNameList)) {
            return
        }
        val spuList = productTemplateAeSpuRepository.listNoCompletedBySpuCode(data.spuCode!!)
        if (spuList.isEmpty()) {
            log.warn { "待上架-更新尺码信息-错误，找不到spuCode, dto=${data.toJson()}" }
            return
        }
        spuList?.forEach { spu ->
            val productSkcList = productTemplateAeSkcRepository.getByAeSpuId(spu.aeSpuId!!)
            if (CollectionUtils.isEmpty(productSkcList)) {
                return@forEach
            }

            val skuList = productTemplateAeSkuRepository.listByAeSpuId(spu.aeSpuId!!, Bool.YES.code)

            // 提取AE发货地
            val allShipsFrom = skuList.mapNotNull { it.getParsedShipsFromPropertyValueItem() }
            val foreachShipsFrom = mutableListOf<ProductAePropertyValueItemDTO?>()
            if (allShipsFrom.isEmpty()) {
                // 都是空, 则只有默认发货地
                foreachShipsFrom.add(ProductAePropertyValueItemDTO())
            } else {
                // 不为空, 则多发货地
                val tmpShipsFrom = allShipsFrom.associateBy { it.attributeValueId }
                if (tmpShipsFrom.values.isNotEmpty()) {
                    foreachShipsFrom.addAll(tmpShipsFrom.values)
                } else {
                    foreachShipsFrom.add(ProductAePropertyValueItemDTO())
                }
            }

            // 提取店铺
            val allShopIds = skuList.mapNotNull { it.shopId }.distinct()

            // 循环尺码
            productSkcList
                .filter { it.skc == data.skc }
                .forEach { skc ->
                    val skuList = productTemplateAeSkuRepository.listByAeSkcIdList(listOf(skc.aeSkcId!!), Bool.YES.code)
                    if (skuList.isEmpty()) {
                        return@forEach
                    }
                    /*
                以模板sku为基准, 匹配data尺码, 模板不存在data内, 删除
                 */
                    skuList
                        .filter { !data.sizeNameList!!.contains(it.sizeName) }
                        .forEach { sku ->
                            val updateSku = ProductTemplateAeSku().apply {
                                this.aeSkuId = sku.aeSkuId
                                this.enableState = Bool.NO.code
                            }
                            productTemplateAeSkuRepository.updateById(updateSku)
                        }
                    /*
                以data尺码为基准, 匹配模板sku
                1. 模板不存在data内, 新增
                2. 模板存在data内, enable 改为 1
                 */
                    // 循环发货地
                    allShopIds.forEach { shopId ->
                        foreachShipsFrom.forEach { shipsFrom ->
                            val shipsFromSkuList = skuList.filter { sku -> sku.shipsFromAttributeValueId == shipsFrom?.attributeValueId && sku.shopId == shopId }
                            data.sizeNameList!!.forEach { sizeName ->
                                if (!shipsFromSkuList.map { it.sizeName }.contains(sizeName)) {
                                    // 模板不存在data内
                                    var newBarcode: String? = null
                                    val barCode = productBarCodeRepository.getBySpuCodeAndSkcAndSize(spu.spuCode, skc.skc, sizeName)
                                    if (barCode == null) {
                                        // 添加条码
                                        val product = productRepository.getById(spu.productId)
                                        val barcodeReq = BatchCreateBarCodeReq().apply {
                                            this.categoryCode = product.categoryCode
                                            this.categoryName = product.categoryName
                                            this.skcCode = skc.skc
                                            this.color = skc.color
                                            this.spuCode = product.spuCode
                                            this.groupName = product.sizeGroupName
                                            this.sourceGroupCode = product.sizeGroupCode
                                            this.sizeValues = listOf(sizeName)
                                            this.inspiraImgUrl = product.inspiraImgUrl
                                            this.supplyMode = product.supplyMode
                                            this.localPrice = skc.localPrice
                                            this.designImgUrl = skc.pictures
                                            this.mainImgUrl = skc.pictures
                                        }
                                        val barcodeResp = barCodeService.createBarcodeByForce(listOf(barcodeReq))
                                        if (CollectionUtils.isNotEmpty(barcodeResp)) {
                                            newBarcode = barcodeResp[0].barcode
                                        }
                                    } else {
                                        newBarcode = barCode.barcode
                                    }

                                    val sku = ProductTemplateAeSku().apply {
                                        this.aeSkuId = IdHelper.getId()
                                        this.aeSkcId = skc.aeSkcId
                                        this.aeSpuId = spu.aeSpuId
                                        this.shopId = shopId
                                        this.barcode = newBarcode
                                        this.stockQuantity = aeDefaultProperties.aePlatform.defaultStockQuantity
                                        this.sizeName = sizeName
                                        this.flagFrontend = Bool.YES.code
                                        this.enableState = Bool.YES.code

                                        if (shipsFrom != null) {
                                            this.shipsFromAttributeId = shipsFrom.attributeId
                                            this.shipsFromAttributeName = shipsFrom.attributeName
                                            this.shipsFromAttributeValueId = shipsFrom.attributeValueId
                                            this.shipsFromAttributeValueName = shipsFrom.attributeValueName
                                        }
                                    }
                                    productTemplateAeSkuRepository.save(sku)
                                } else {
                                    val updateSkuList = shipsFromSkuList
                                        .filter { it.sizeName == sizeName }
                                        .map { sku ->
                                            val updateSku = ProductTemplateAeSku().apply {
                                                this.aeSkuId = sku.aeSkuId
                                                this.enableState = Bool.YES.code
                                            }
                                            updateSku
                                        }
                                    if (CollectionUtils.isNotEmpty(updateSkuList)) {
                                        productTemplateAeSkuRepository.updateBatchById(updateSkuList)
                                    }
                                }
                            }
                        }
                    }
                }
        }
    }

    /**
     * 更新定价类型
     */
    override fun updatePricingType(data: ProductUpdateDto.Data) {
        // 模板表没有定价类型, product表记录
    }

    /**
     * 更新款式风格
     */
    override fun updateClothingStyle(data: ProductUpdateDto.Data) {
        // 模板表没有款式风格, product表记录
    }

    /**
     * 更新商品属性
     */
    override fun updateAttributes(data: ProductUpdateDto.Data){

    }
}