package tech.tiangong.pop.service.product.update

import com.alibaba.fastjson2.parseArray
import com.baomidou.mybatisplus.extension.kotlin.KtUpdateWrapper
import org.apache.commons.collections4.CollectionUtils
import org.apache.commons.lang3.StringUtils
import org.springframework.stereotype.Service
import team.aikero.blade.core.enums.Bool
import team.aikero.blade.core.toolkit.isNotEmpty
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.util.json.toJson
import tech.tiangong.pop.common.dto.ProductUpdateDto
import tech.tiangong.pop.common.exception.BaseBizException
import tech.tiangong.pop.dao.entity.Product
import tech.tiangong.pop.dao.entity.ProductAttributes
import tech.tiangong.pop.dao.entity.ProductAttributesV2
import tech.tiangong.pop.dao.entity.ProductSkc
import tech.tiangong.pop.dao.repository.*
import tech.tiangong.pop.enums.ProductTagEnum
import tech.tiangong.pop.enums.TagPopSpuCanceledEnum
import tech.tiangong.pop.external.DesignClientExternal
import tech.tiangong.pop.service.product.price.cost.ProductCostPricingServiceV2
import tech.tiangong.pop.service.product.price.overrange.PriceOverrangeService
import tech.tiangong.pop.utils.CategoryUtils
import java.math.BigDecimal
import java.util.*

/**
 * product相关表更新
 */
@Service
@Slf4j
class ProductUpdateServiceImpl(
    private val productSkcRepository: ProductSkcRepository,
    private val productRepository: ProductRepository,
    private val publishCategoryRepository: PublishCategoryRepository,
    private val productAttributesRepository: ProductAttributesRepository,
    private val productCostPricingServiceV2: ProductCostPricingServiceV2,
    private val priceOverrangeService: PriceOverrangeService,
    private val designClientExternal:DesignClientExternal,
    private val productTagRepository:ProductTagRepository,
    private val productAttributesV2Repository: ProductAttributesV2Repository,
) : ProductUpdateService {
    /**
     * 取消SKC
     */
    override fun cancel(data: ProductUpdateDto.Data) {
        val product = productRepository.getBySpuCode(data.spuCode!!)
        if (product == null) {
            log.warn { "待上架取消SKC错误，找不到spuCode, dto=${data.toJson()}" }
            return
        }

        val productSkc = productSkcRepository.getActiveByProductIdAndSkc(product.productId!!, Objects.requireNonNull(data.skc!!))
        if (Objects.isNull(productSkc)) {
            log.info { "待上架取消SKC错误，找不到skc, dto=${data.toJson()}" }
            return
        }

        // 保存SKC取消前的关键信息
        val updateSkc = ProductSkc().apply {
            this.productSkcId = productSkc!!.productSkcId
            this.state = Bool.NO.code
        }
        productSkcRepository.updateById(updateSkc)

        //判断上游款式是否已被取消，如果是则要标记商品款式已取消
        //整个SPU都被取消了
        val isCancelSpu: Boolean = designClientExternal.isCancelSpu(data.spuCode!!) == true
        if(isCancelSpu){
            val updateProduct = Product().apply {
                this.productId = product.productId
                this.spuCanceled = Bool.YES.code
            }
            productRepository.updateById(updateProduct)
            //添加款式取消标签
            productTagRepository.addTagByProductId(
                updateProduct.productId!!,
                ProductTagEnum.SPU_CANCELED.code,
                TagPopSpuCanceledEnum.SPU_CANCELED.code
            )
        }
    }

    /**
     * 供货价更新
     */
    override fun updatePrice(data: ProductUpdateDto.Data) {
        val spuCode: String = data.spuCode!!
        val skc: String = data.skc!!
        val price: BigDecimal = data.price!!

        val product = productRepository.getBySpuCode(spuCode)
        if (product == null) {
            return
        }
        val productSkc = productSkcRepository.getActiveByProductIdAndSkc(product.productId!!, skc)
        if (productSkc == null) {
            log.info { "待上架更新供货价错误，找不到skc, spuCode:${spuCode}, skc:${skc}, price:${price}" }
            return
        }

        val updateSkc = ProductSkc().apply {
            this.productSkcId = productSkc.productSkcId
            this.localPrice = price
        }
        productSkcRepository.updateById(updateSkc)
        // 重算定价成本
        val costPriceResults = productCostPricingServiceV2.calculateCostPrice(product.productId!!)
        if (CollectionUtils.isNotEmpty(costPriceResults)) {
            // 更新当前skc的成本价和定价成本
            val maxCostPrice = costPriceResults.mapNotNull { it.costPrice }.maxOrNull()

            // 更新当前SPU下的其他定价成本(成本价不更新)
            if (maxCostPrice != null && maxCostPrice != BigDecimal.ZERO) {
                val spuProduct = productRepository.listBySpuCodes(listOf(spuCode))
                if (spuProduct.isNotEmpty()) {
                    // 获取所有的skc
                    val skcList = productSkcRepository.getByProductIds(spuProduct.mapNotNull { it.productId }.toSet())
                    if (skcList.isNotEmpty()) {
                        // 更新所有的定价成本
                        val updateSkcList = skcList.map {
                            ProductSkc().apply {
                                this.productSkcId = it.productSkcId
                                this.costPrice = maxCostPrice
                                this.checkPriceType = data.checkPriceType
                                this.pricerId = data.pricerId
                                this.pricerName = data.pricerName
                            }
                        }
                        if (updateSkcList.isNotEmpty()) {
                            productSkcRepository.updateBatchById(updateSkcList)
                        }
                    }
                }
            }

            // 超价
            if (productSkc.localPrice != null && productSkc.costPrice != null) {
                priceOverrangeService.compareCost(
                    product.spuCode!!,
                    productSkc.skc!!,
                    productSkc.color,
                    price,
                    productSkc.localPrice!!,
                    null,
                    null,
                    maxCostPrice!!,
                    productSkc.costPrice!!,
                    data.checkPriceType,
                    data.pricerId,
                    data.pricerName,
                    productSkc.pictures?.split(",")?.firstOrNull(),
                )
            }
        }
    }

    /**
     * 新增SKC
     */
    override fun addSkc(data: ProductUpdateDto.Data) {
    }

    /**
     * 创建商品
     */
    override fun create(data: ProductUpdateDto.Data) {
    }

    /**
     * 更新品类
     */
    override fun updateCategory(data: ProductUpdateDto.Data) {
        val product = productRepository.getBySpuCode(data.spuCode!!)
        if (product == null) {
            log.warn { "待上架更新品类错误，找不到spuCode, dto=${data.toJson()}" }
            return
        }
        val publishCategories = publishCategoryRepository.listWithCache()
        if (CollectionUtils.isEmpty(publishCategories)) {
            throw BaseBizException("请维护品类管理!")
        }
        if (StringUtils.isBlank(data.categoryName)) {
            throw BaseBizException("上游无传入品类!")
        }
        // 保存更新前的品类信息
        val newCategoryName = data.categoryName?.replace("-", ">")

        val publishCategoryIdByPath = CategoryUtils.findPublishCategoryIdByPath(publishCategories, newCategoryName!!)
        if (Objects.isNull(publishCategoryIdByPath)) {
            throw BaseBizException("品类:" + product.categoryName + " 未设置")
        }

        val categoryCode = CategoryUtils.findCategoryCodeByPath(publishCategories, newCategoryName)

        productRepository.updateById(Product().apply {
            this.productId = product.productId
            this.categoryId = publishCategoryIdByPath
            this.categoryName = newCategoryName
            this.categoryCode = categoryCode
        })

        productAttributesRepository.update(
            KtUpdateWrapper(ProductAttributes::class.java)
                .eq(ProductAttributes::productId, product.productId)
                .set(ProductAttributes::categoryId, publishCategoryIdByPath)
        )

        log.info { "待上架更新品类成功,dto=${data.toJson()}" }
    }

    /**
     * 更新颜色
     */
    override fun updateColor(data: ProductUpdateDto.Data) {
        val product = productRepository.getBySpuCode(data.spuCode!!)
        if (product == null) {
            return
        }
        val productSkc = productSkcRepository.getActiveByProductIdAndSkc(product.productId!!, data.skc!!)
        if (Objects.isNull(productSkc)) {
            log.error { "待上架更新颜色错误，找不到skc,dto=${data.toJson()}" }
            return
        }

        // 准备更新的数据
        val updateSkc = ProductSkc()
        updateSkc.productSkcId = productSkc!!.productSkcId
        updateSkc.color = data.color
        updateSkc.platformColor = data.colorCode
        updateSkc.colorCode = data.colorCode
        updateSkc.colorAbbrCode = data.colorAbbrCode
        productSkcRepository.updateById(updateSkc)
    }

    /**
     * 更新采购价
     */
    override fun updatePurchasesPrice(data: ProductUpdateDto.Data) {
        val spuCode: String = data.spuCode!!
        val skc: String = data.skc!!
        val purchasePrice: BigDecimal = data.purchasePrice!!
        val product = productRepository.getBySpuCode(spuCode)
        if (product == null) {
            return
        }
        val productSkc = productSkcRepository.getActiveByProductIdAndSkc(product.productId!!, skc)
        if (productSkc == null) {
            log.info { "待上架更新采购价错误，找不到skc, spuCode:${spuCode}, skc:${skc}, price:${purchasePrice}" }
            return
        }

        val updateSkc = ProductSkc().apply {
            this.productSkcId = productSkc.productSkcId
            this.purchasePrice = purchasePrice
        }
        productSkcRepository.updateById(updateSkc)
        // 重算定价成本
        val costPriceResults = productCostPricingServiceV2.calculateCostPrice(product.productId!!)
        if (CollectionUtils.isNotEmpty(costPriceResults)) {
            // 更新当前skc的成本价和定价成本
            val maxCostPrice = costPriceResults.mapNotNull { it.costPrice }.maxOrNull()

            // 更新当前SPU下的其他定价成本(成本价不更新)
            if (maxCostPrice != null && maxCostPrice != BigDecimal.ZERO) {
                val spuProduct = productRepository.listBySpuCodes(listOf(spuCode))
                if (spuProduct.isNotEmpty()) {
                    // 获取所有的skc
                    val skcList = productSkcRepository.getByProductIds(spuProduct.mapNotNull { it.productId }.toSet())
                    if (skcList.isNotEmpty()) {
                        // 更新所有的定价成本
                        val updateSkcList = skcList.map {
                            ProductSkc().apply {
                                this.productSkcId = it.productSkcId
                                this.costPrice = maxCostPrice
                                this.checkPriceType = data.checkPriceType
                                this.pricerId = data.pricerId
                                this.pricerName = data.pricerName
                            }
                        }
                        if (updateSkcList.isNotEmpty()) {
                            productSkcRepository.updateBatchById(updateSkcList)
                        }
                    }
                }
            }

            // 超价
            if (productSkc.purchasePrice != null && productSkc.costPrice != null) {
                priceOverrangeService.compareCost(
                    product.spuCode!!,
                    productSkc.skc!!,
                    productSkc.color,
                    null,
                    null,
                    purchasePrice,
                    productSkc.purchasePrice!!,
                    maxCostPrice!!,
                    productSkc.costPrice!!,
                    data.checkPriceType,
                    data.pricerId,
                    data.pricerName,
                    productSkc.pictures?.split(",")?.firstOrNull(),
                )
            }
        }
    }

    /**
     * 更新尺码信息
     */
    override fun updateSize(data: ProductUpdateDto.Data) {
        val spuCode: String = data.spuCode!!
        val product = productRepository.getBySpuCode(spuCode)
        if (product == null) {
            return
        }
        val sizeNameList = data.sizeNameList
        val specialSizeNameList = data.specialSizeNameList
        val skc: String = data.skc!!
        val productSkc = productSkcRepository.getActiveByProductIdAndSkc(product.productId!!, skc)
        if (productSkc == null) {
            log.info { "待上架更新尺寸错误，找不到skc, spuCode:${spuCode}, skc:${skc}, sizeNameList:${sizeNameList?.toJson()}" }
            return
        }
        productSkc.sizeNames = sizeNameList?.toJson()
        productSkc.specialSizeNames = specialSizeNameList?.toJson()
        productSkcRepository.updateById(productSkc)
    }

    /**
     * 更新定价类型
     */
    override fun updatePricingType(data: ProductUpdateDto.Data) {
        val product = productRepository.getBySpuCode(data.spuCode!!)
        if (product == null) {
            return
        }
        product.pricingType = data.pricingType
        productRepository.updateById(product)
    }

    /**
     * 更新款式风格
     */
    override fun updateClothingStyle(data: ProductUpdateDto.Data) {
        val product = productRepository.getBySpuCode(data.spuCode!!)
        if (product == null) {
            return
        }
        // 保存更新前的服装风格信息
        val updateProduct = Product()
        updateProduct.productId = product.productId
        updateProduct.clothingStyleCode = data.clothingStyleCode
        updateProduct.clothingStyleName = data.clothingStyleName
        updateProduct.planningType = data.planningType
        updateProduct.marketCode = data.marketCode
        updateProduct.marketSeriesCode = data.marketSeriesCode
        productRepository.updateById(updateProduct)
    }

    /**
     * 商品属性更新
     */
    override fun updateAttributes(data: ProductUpdateDto.Data) {
        //TODO
        val product = productRepository.getBySpuCode(data.spuCode!!)
        if (product == null) {
            return
        }
        val attributeList = data.attributeList
        //以更新数据为准，删除不存在的属性配置

        val dbProductAttributeList = productAttributesV2Repository.listByProductId(product.productId!!)
        dbProductAttributeList?.forEach {

        }
    }
}