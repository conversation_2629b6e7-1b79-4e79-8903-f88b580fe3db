package tech.tiangong.pop.service.category.impl

import com.alibaba.excel.EasyExcel
import com.alibaba.excel.context.AnalysisContext
import com.alibaba.excel.read.listener.ReadListener
import org.springframework.beans.BeanUtils
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import org.springframework.util.Assert
import org.springframework.web.multipart.MultipartFile
import team.aikero.blade.core.enums.Bool
import team.aikero.blade.core.exception.BusinessException
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.sequence.id.IdHelper
import tech.tiangong.pop.convert.PublishCategoryConvert
import tech.tiangong.pop.dao.entity.PublishCategory
import tech.tiangong.pop.dao.repository.*
import tech.tiangong.pop.dto.ImportPublishCategoryDTO
import tech.tiangong.pop.req.category.PublishCategoryTreeQueryReq
import tech.tiangong.pop.req.category.SavePublishCategoryReq
import tech.tiangong.pop.resp.category.PublishCategoryMappingTreeNodeVo
import tech.tiangong.pop.resp.category.PublishCategoryTreeNodeVo
import tech.tiangong.pop.service.category.PublishCategoryService
import tech.tiangong.pop.utils.TransactionUtils

/**
 * 上架品类服务
 */
@Slf4j
@Service
class PublishCategoryServiceImpl(
    private val publishCategoryRepository: PublishCategoryRepository,
    private val publishCategoryAttrRepository: PublishCategoryAttrRepository,
    private val publishCategoryMappingRepository: PublishCategoryMappingRepository,
    private val publishPlatformAttrRepository: PublishPlatformAttrRepository,
    private val publishPlatformAttrValueRepository: PublishPlatformAttrValueRepository,
    private val categoryConvert: PublishCategoryConvert
) : PublishCategoryService {

    override fun save(req: SavePublishCategoryReq) {
        val existName = publishCategoryRepository.existCategoryName(
            if (req.parentCategoryId == null) 0L else req.parentCategoryId,
            req.publishCategoryId,
            req.categoryName
        )

        Assert.isTrue(!existName, "品类名称已存在")

        var isUpdate = false
        val publishCategory = if (req.publishCategoryId != null) {
            publishCategoryRepository.getByCategoryId(req.publishCategoryId)?.also {
                isUpdate = true
            } ?: throw IllegalArgumentException("品类不存在")
        } else {
            PublishCategory().apply {
                publishCategoryId = IdHelper.getId()
            }
        }

        if (req.parentCategoryId != null) {
            val oldParentId = publishCategory.parentCategoryId

            if (req.parentCategoryId != 0L) {
                val newParent = publishCategoryRepository.getByCategoryId(req.parentCategoryId)
                    ?: throw IllegalArgumentException("上级品类不存在")

                // 变更父路径
                publishCategory.parentCategoryId = newParent.publishCategoryId
                publishCategory.parentPaths = "${newParent.parentPaths},${newParent.publishCategoryId}"
                publishCategory.level = newParent.level?.plus(1)
            } else {
                publishCategory.parentCategoryId = 0L
                publishCategory.parentPaths = "0"
                publishCategory.level = 1
            }

            // 变更父节点
            if (isUpdate && oldParentId != null && oldParentId != req.parentCategoryId) {
                // 变更子节点路径
                val children = publishCategoryRepository.listChildren(publishCategory.publishCategoryId)
                children?.forEach { child ->
                    val lastIndex = child.parentPaths!!.lastIndexOf(",${publishCategory.publishCategoryId}")
                    child.parentPaths = publishCategory.parentPaths + child.parentPaths!!.substring(lastIndex)
                    child.level = child.parentPaths!!.split(",").size // 不用+1，因为父节点路径包含了0
                }
                publishCategoryRepository.updateBatchById(children)
            }
        } else {
            publishCategory.parentCategoryId = 0L
            publishCategory.parentPaths = "0"
            publishCategory.level = 1
        }

        publishCategory.categoryName = req.categoryName
        publishCategoryRepository.saveOrUpdate(publishCategory)
        TransactionUtils.afterCommit {
            publishCategoryRepository.invalidateCache()
        }
    }

    override fun tree(req: PublishCategoryTreeQueryReq): List<PublishCategoryTreeNodeVo> {
        val list = publishCategoryRepository.query(req).toMutableList()

        if (list.isEmpty()) {
            return emptyList()
        }

        // 如果有查询条件，需要重新组装树，将父节点和子节点都找出来
        if (!req.categoryName.isNullOrBlank()
            || req.categoryIds.isNotEmpty()
            || req.state!=null) {
            val categoryIds = mutableListOf<Long>()
            val allParentIds = mutableSetOf<Long>()

            list.forEach { category ->
                categoryIds.add(category.publishCategoryId!!)
                category.parentPaths!!.split(",").forEach { id ->
                    allParentIds.add(id.toLong())
                }
            }

            list.addAll(publishCategoryRepository.listByIds(allParentIds).toList())
            categoryIds.forEach { id ->
                publishCategoryRepository.listChildren(id).let { list.addAll(it.toList()) }
            }
        }
        return categoryConvert.trans2Tree(list)
    }

    override fun getMappingCategoryTree(categoryMappingId:Long):List<PublishCategoryMappingTreeNodeVo>{
        val categoryMapping = publishCategoryMappingRepository.getById(categoryMappingId)
        //先找出已映射的品类
        val mappingList = publishCategoryMappingRepository.listByPlatformCategoryIdAndPlatformIdAndCountry(
            categoryMapping.platformCategoryId!!,
            categoryMapping.platformId!!,
            categoryMapping.country!!)
        if(mappingList.isNullOrEmpty()){
            return emptyList()
        }
        val categoryMappingIds = mappingList.mapNotNull { it.categoryMappingId }
        val categoryIds = mappingList.mapNotNull { it.publishCategoryId }
        //查询同样映射了同平台同站点同品类的内部品类是否已关联第三方平台属性
        val countAttributeMapping = publishPlatformAttrRepository.countAttributeMappingByCategoryId(categoryIds,categoryMappingIds)
            .associate { it.categoryId to it.amount }
        //再从本地品类树中过滤出来
        val req = PublishCategoryTreeQueryReq().apply {
            this.categoryIds.addAll(categoryIds)
        }
        val categoryTree = tree(req)

        return buildCategoryMappingTree(categoryTree,countAttributeMapping)
    }

    private fun buildCategoryMappingTree(categoryTree:List<PublishCategoryTreeNodeVo>?,
                                         countAttributeMapping:Map<Long?,Integer?>?):List<PublishCategoryMappingTreeNodeVo>{
        if(categoryTree.isNullOrEmpty()){
            return emptyList()
        }
        return categoryTree.map {categoryTreeNodeVo->
            PublishCategoryMappingTreeNodeVo().apply {
                BeanUtils.copyProperties(categoryTreeNodeVo,this)
                this.mappingAttributeFlag = if(!countAttributeMapping.isNullOrEmpty()
                    && countAttributeMapping.contains(categoryTreeNodeVo.publishCategoryId)){
                    Bool.YES.code
                }else{
                    Bool.NO.code
                }
                this.children = buildCategoryMappingTree(categoryTreeNodeVo.children,countAttributeMapping)
            }
        }
    }

    override fun importCategory(file: MultipartFile): List<String> {
        val errorInfos = mutableListOf<String>()
        val categoryName1 = mutableMapOf<String, MutableSet<String>>()
        val categoryName2 = mutableMapOf<String, MutableSet<String>>()
        val categoryName3 = mutableMapOf<String, MutableSet<String>>()

        try {
            EasyExcel.read(file.inputStream, ImportPublishCategoryDTO::class.java, object : ReadListener<ImportPublishCategoryDTO> {
                private val cachedDataList = mutableListOf<ImportPublishCategoryDTO>()
                private var successCount = 0
                private val tempErrorList = mutableListOf<String>()

                override fun invoke(importPublishCategoryDTO: ImportPublishCategoryDTO, analysisContext: AnalysisContext) {
                    val readRowHolder = analysisContext.readRowHolder()
                    // 一级品类 不能为空
                    if (importPublishCategoryDTO.categoryName1.isNullOrBlank()) {
                        tempErrorList.add("第 ${readRowHolder.rowIndex} 行的一级品类不能为空！")
                        return
                    } else if (importPublishCategoryDTO.categoryName1!!.length > 60) {
                        tempErrorList.add("第 ${readRowHolder.rowIndex} 行的一级品类长度不能超过60个字符！")
                        return
                    }

                    val key1 = "0"
                    categoryName1.getOrPut(key1) { mutableSetOf() }.add(importPublishCategoryDTO.categoryName1!!)

                    // 二级品类 不能为空
                    if (importPublishCategoryDTO.categoryName2.isNullOrBlank()) {
                        tempErrorList.add("第 ${readRowHolder.rowIndex} 行的二级品类不能为空！")
                        return
                    } else if (importPublishCategoryDTO.categoryName2!!.length > 60) {
                        tempErrorList.add("第 ${readRowHolder.rowIndex} 行的二级品类长度不能超过60个字符！")
                        return
                    }

                    val key2 = "$key1,${importPublishCategoryDTO.categoryName1}"
                    categoryName2.getOrPut(key2) { mutableSetOf() }.add(importPublishCategoryDTO.categoryName2!!)

                    // 三级品类 不能为空
                    if (importPublishCategoryDTO.categoryName3.isNullOrBlank()) {
                        tempErrorList.add("第 ${readRowHolder.rowIndex} 行的三级品类不能为空！")
                        return
                    } else if (importPublishCategoryDTO.categoryName3!!.length > 60) {
                        tempErrorList.add("第 ${readRowHolder.rowIndex} 行的三级品类长度不能超过60个字符！")
                        return
                    }

                    val key3 = "$key2,${importPublishCategoryDTO.categoryName2}"
                    categoryName3.getOrPut(key3) { mutableSetOf() }.add(importPublishCategoryDTO.categoryName3!!)

                    cachedDataList.add(importPublishCategoryDTO)
                    successCount++
                }

                override fun doAfterAllAnalysed(analysisContext: AnalysisContext) {
                    // 保存数据
                    saveData()

                    // 返回信息的第一条，是统计信息
                    errorInfos.add("品类导入成功 $successCount 条，失败 ${tempErrorList.size} 条 数据！")
                    errorInfos.addAll(tempErrorList)
                }

                @Transactional(rollbackFor = [Exception::class])
                private fun saveData() {
                    log.info { "开始存储品类数据库！" }

                    val saveList = mutableListOf<PublishCategory>()

                    // 处理一级品类
                    val categoryName1s = categoryName1["0"] ?: emptySet()
                    categoryName1s.forEach { name ->
                        val publishCategory = publishCategoryRepository.getByParentIdAndCategoryName(0L, name)
                            ?: PublishCategory().apply {
                                publishCategoryId = IdHelper.getId()
                                parentCategoryId = 0L
                                parentPaths = "0"
                                level = 1
                                sortNum = 0
                                categoryName = name
                            }
                        saveList.add(publishCategory)
                    }

                    // 处理二级品类
                    categoryName2.forEach { (path, names) ->
                        val parent = findParentByNamePath(path, saveList)
                        names.forEach { name ->
                            val publishCategory = publishCategoryRepository.getByParentIdAndCategoryName(parent.publishCategoryId, name)
                                ?: PublishCategory().apply {
                                    publishCategoryId = IdHelper.getId()
                                    parentCategoryId = parent.publishCategoryId
                                    parentPaths = "${parent.parentPaths},${parent.publishCategoryId}"
                                    level = 2
                                    sortNum = 0
                                    categoryName = name
                                }
                            saveList.add(publishCategory)
                        }
                    }

                    // 处理三级品类
                    categoryName3.forEach { (path, names) ->
                        val parent = findParentByNamePath(path, saveList)
                        names.forEach { name ->
                            val publishCategory = publishCategoryRepository.getByParentIdAndCategoryName(parent.publishCategoryId, name)
                                ?: PublishCategory().apply {
                                    publishCategoryId = IdHelper.getId()
                                    parentCategoryId = parent.publishCategoryId
                                    parentPaths = "${parent.parentPaths},${parent.publishCategoryId}"
                                    level = 3
                                    sortNum = 0
                                    categoryName = name
                                }
                            saveList.add(publishCategory)
                        }
                    }

                    publishCategoryRepository.saveOrUpdateBatch(saveList)
                    log.info { "导入属性,存储品类数据库成功！" }
                }
            }).sheet().doRead()
        } catch (e: Exception) {
            log.error { "导入品类失败: ${e.message}" }
            throw BusinessException("导入品类失败")
        }

        return errorInfos
    }

    private fun findParentByNamePath(namePath: String, list: List<PublishCategory>): PublishCategory {
        // 查询父节点从第二位开始
        val names = namePath.split(",")
        val rootName = names[1]

        var parent = list.find {
            it.parentCategoryId == 0L && it.categoryName == rootName
        } ?: throw BusinessException("数据错误")

        if (names.size > 2) {
            for (i in 2 until names.size) {
                parent = findParentByName(names[i], parent, list)
            }
        }

        return parent
    }

    private fun findParentByName(name: String, parent: PublishCategory, list: List<PublishCategory>): PublishCategory {
        return list.find {
            it.parentCategoryId == parent.publishCategoryId && it.categoryName == name
        } ?: throw NoSuchElementException("找不到父节点")
    }

    override fun deleteCategory(publishCategoryIds: List<Long>?) {
        if (publishCategoryIds.isNullOrEmpty()) {
            return
        }

        // 查找所有要删除的品类（包括子品类）
        val allCategory = publishCategoryRepository.listCategoryWithChildByIds(publishCategoryIds)
        if (allCategory.isEmpty()) {
            return
        }

        val allCategoryIds = allCategory.map { it.publishCategoryId!! }

        // 根据品类找出已关联的平台品类属性和值
        val publishPlatformAttrs = publishPlatformAttrRepository.listByCategoryIds(allCategoryIds)
        if (publishPlatformAttrs.isNotEmpty()) {
            val platformAttrIds = publishPlatformAttrs.map { it.platformAttrId!! }

            // 删除已关联的平台属性值
            publishPlatformAttrValueRepository.deleteByPublicPlatformAttrIds(platformAttrIds)

            // 再删除已关联的平台属性
            publishPlatformAttrRepository.deleteByCategoryIds(allCategoryIds)
        }

        // 删除已关联的平台品类
        publishCategoryMappingRepository.deleteByCategoryIds(allCategoryIds)

        // 删除已关联的属性
        publishCategoryAttrRepository.deleteByCategoryIds(allCategoryIds)

        // 删除已品类及子品类
        publishCategoryRepository.deleteCategoryByIds(allCategoryIds, false)
    }
}