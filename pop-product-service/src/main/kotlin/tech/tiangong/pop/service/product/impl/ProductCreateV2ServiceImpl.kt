package tech.tiangong.pop.service.product.impl

import com.baomidou.mybatisplus.extension.kotlin.KtQueryWrapper
import com.baomidou.mybatisplus.extension.kotlin.KtUpdateWrapper
import org.apache.commons.collections4.CollectionUtils
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import team.aikero.blade.core.enums.Bool.NO
import team.aikero.blade.core.enums.Bool.YES
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.user.holder.CurrentUserHolder
import team.aikero.blade.util.json.toJson
import tech.tiangong.pop.common.dto.ProductUpdateDto
import tech.tiangong.pop.common.enums.PlatformEnum
import tech.tiangong.pop.common.enums.ProductPublishStateEnum
import tech.tiangong.pop.common.enums.ProductUpdateTypeEnum
import tech.tiangong.pop.common.enums.ProductUpdateTypeEnum.*
import tech.tiangong.pop.common.exception.BaseBizException
import tech.tiangong.pop.common.exception.PublishAscBizException
import tech.tiangong.pop.component.ProductSourceDataComponent
import tech.tiangong.pop.convert.TypeConverter
import tech.tiangong.pop.dao.entity.*
import tech.tiangong.pop.dao.repository.*
import tech.tiangong.pop.dto.product.UpdateProductStockDto
import tech.tiangong.pop.enums.PlatformOperatorTypeEnum
import tech.tiangong.pop.enums.PlatformSyncStateEnum
import tech.tiangong.pop.enums.ProductPricingTypeEnum
import tech.tiangong.pop.enums.SupplyModeEnum
import tech.tiangong.pop.resp.sdk.lazada.LazadaUpdatePriceQuantityResponse
import tech.tiangong.pop.resp.sdk.lazada.UpdatePriceQuantityReq
import tech.tiangong.pop.service.lazada.LazadaApiService
import tech.tiangong.pop.service.product.ProductCreateV2Service
import tech.tiangong.pop.service.product.update.ProductUpdateServiceImpl
import tech.tiangong.pop.service.product.update.ProductUpdateServiceSelector
import tech.tiangong.pop.utils.ProductSizeUtils
import java.math.BigDecimal
import java.util.*

@Service
@Slf4j
class ProductCreateV2ServiceImpl(
    private val productRepository: ProductRepository,
    private val productSkcRepository: ProductSkcRepository,
    private val productSourceDataComponent: ProductSourceDataComponent,
    private val saleSkuRepository: SaleSkuRepository,
    private val productOperateLogRepository: ProductOperateLogRepository,
    private val productUpdateServiceImpl: ProductUpdateServiceImpl,
    private val productUpdateServiceSelector: ProductUpdateServiceSelector,
    private val productSourceDataRepository: ProductSourceDataRepository,
    private val saleGoodsRepository: SaleGoodsRepository,
    private val shopRepository: ShopRepository,
    private val lazadaApiService: LazadaApiService,
    private val productSyncLogRepository: ProductSyncLogRepository,
    ) : ProductCreateV2Service {

    override fun updateProduct(productUpdateDto: ProductUpdateDto) {
        val dataList = productUpdateDto.dataList.orEmpty()
        if (dataList.isEmpty()) {
            log.error { "更新商品数据为空" }
            return
        }

        val spuList = dataList.mapNotNull { it.spuCode }
        if (spuList.isEmpty()) {
            log.error { "更新商品数据中 spuCode 全为空，共 ${dataList.size} 条记录" }
            return
        }

        try {
            // 因为更新供货价的时候, 上游会发送2个MQ, 一个是SKC更新MQ, 一个是创建商品MQ, 可能存在并发问题, 导致旧的数据覆盖新的数据, 所以这里休眠2s
            Thread.sleep(2000)
        } catch (e: InterruptedException) {
            throw RuntimeException(e)
        }

        val products = productRepository.listBySpuCodes(spuList)
            .ifEmpty {
                log.error { "根据 spuList 找不到任何商品（spuList 大小：${spuList.size}）, ${spuList.toJson()}" }
                return
            }

        // 循环data
        dataList.forEach { data ->
            // 匹配操作类型
            val updateTypeEnum = ProductUpdateTypeEnum.findByCode(data.opType)

            // 循环product
            products.forEach innerForEach@{ product ->
                if (Objects.isNull(product)) {
                    log.error { "找不到对应商品 data:${data.toJson()}" }
                    return@innerForEach
                }

                // 校验供给方式类型, 使用对应尺码
                if (updateTypeEnum == UPDATE_SIZE) {
                    val supplyModeEnum = SupplyModeEnum.getByCode(product.supplyMode)
                    if (supplyModeEnum != null) {
                        data.sizeNameList = ProductSizeUtils.getSizeBySupplyMode(supplyModeEnum, data.sizeNameList)
                    }
                }

                // 如果是未更新状态, 标记为更新
                if (product.isUpdate != YES.code) {
                    val updateProduct = Product().apply {
                        this.productId = product.productId
                        this.isUpdate = YES.code
                    }
                    productRepository.updateById(updateProduct)
                }

                // 待上架
                updateProductIfNoPublish(data, product)

                if (product.getIsSyncPlatform() == YES.code) {
                    // 处理已上架
                    //已上架则缓存到ProductSourceData
                    if (Objects.equals(data.opType, CANCLE.code)) {
                        log.error { "已经上架商品忽略SKC取消动作 - productId: ${product.productId}, data: ${data.toJson()}" }
                        return@innerForEach
                    }

                    // 已上架: 找出所有有pid的saleGoods, 按照product+shop维度创建记录 (已上架需要点应用更新按钮)
                    productSourceDataComponent.saveProductSourceData(updateTypeEnum, data.toJson(), product.productId!!)

                    // 构建详细的日志内容
                    val logContent = String.format("缓存%s操作: SKC[%s]", updateTypeEnum.desc, data.skc)
                    saveProductOperateLog(logContent, TypeConverter.updateToOperatorType(data.opType), product.productId)
                } else {
                    // 处理待上架特殊情况
                    // 待上架: 也要记录一个源数据-查看更新用
                    val productSourceData = ProductSourceData().apply {
                        this.productId = product.productId
                        this.shopId = product.shopId
                        this.spu = product.spuCode
                        this.dataType = updateTypeEnum.code
                        this.sourceData = data.toJson()
                    }
                    productSourceDataRepository.save(productSourceData)
                }
            }
        }
    }

    override fun updateSize(newSizeList: List<String>, skcs: List<ProductSkc>, productId: Long, updateSkc: ProductSkc) {
        if (CollectionUtils.isEmpty(skcs)) {
            log.info { "${"spot类型，skc为空,skcs={}"} $skcs" }
            return
        }
        val skuList = saleSkuRepository.list(
            KtQueryWrapper(SaleSku::class.java)
                .eq(SaleSku::productId, productId)
                .eq(SaleSku::enable, YES.code)
        )

        // 生成全局尺码并集（所有SKC的sizeName去重集合）
        val currentSkcSizes = newSizeList.toMutableSet()
        val globalSizeUnion = currentSkcSizes.toMutableSet()
        if (CollectionUtils.isNotEmpty(skuList)) {
            globalSizeUnion.addAll(skuList.mapNotNull { it.sizeName })
        }
        val saleSkuList = globalSizeUnion.map { size ->
            val saleSku = SaleSku()
            saleSku.productSkcId = updateSkc.productSkcId
            // enable=1条件：尺码存在于全局并集中（存在即有效）
            saleSku.enable = if (globalSizeUnion.contains(size)) YES.code else NO.code
            saleSku.sizeName = size
            // publishState=1条件：当前SKC包含该尺码
            saleSku.publishState = if (currentSkcSizes.contains(size)) ProductPublishStateEnum.ACTIVE.code else ProductPublishStateEnum.IN_ACTIVE.code

            // 其他字段映射（如skuId、price等）
            saleSku
        }

        if (CollectionUtils.isNotEmpty(saleSkuList)) {
            for (saleSku in saleSkuList) {
                saleSkuRepository.update(
                    KtUpdateWrapper(SaleSku::class.java)
                        .eq(SaleSku::sizeName, saleSku.sizeName)
                        .eq(SaleSku::productSkcId, saleSku.productSkcId)
                        .eq(SaleSku::productId, productId)
                        .set(SaleSku::enable, saleSku.enable)
                        .set(SaleSku::publishState, saleSku.publishState)
                )
                saleSkuRepository.update(
                    KtUpdateWrapper(SaleSku::class.java)
                        .ne(SaleSku::productSkcId, saleSku.productSkcId)
                        .eq(SaleSku::productId, productId)
                        .eq(SaleSku::sizeName, saleSku.sizeName)
                        .set(SaleSku::enable, saleSku.enable)
                )
            }
        }

    }

    override fun updateProductStateByCreateError(product: Product) {
        val updateProduct = Product()
        updateProduct.productId = product.productId
        updateProduct.publishAuthor = CurrentUserHolder.get().name
        val judeGoodsList = saleGoodsRepository.getByProduct(product.productId!!)
        val platformSyncFailCount = judeGoodsList.count { k -> Objects.equals(k.platformSyncState, PlatformSyncStateEnum.FAILURE.code) }
        if (platformSyncFailCount > 0) {
            updateProduct.platformSyncState = PlatformSyncStateEnum.FAILURE.code
        } else {
            updateProduct.platformSyncState = PlatformSyncStateEnum.SUCCESS.code
        }
        val count = judeGoodsList.count { k -> Objects.equals(k.publishState, ProductPublishStateEnum.ACTIVE.code) }
        if (count > 0) {
            if (product.getIsSyncPlatform() == null || Objects.equals(product.isSyncPlatform, NO.code)) {
                updateProduct.setIsSyncPlatform(YES.code)
            }
            updateProduct.publishState = ProductPublishStateEnum.ACTIVE.code
        }
        productRepository.updateById(updateProduct)
    }

    @Transactional(rollbackFor = [Exception::class])
    override fun updateLazadaStock(dto: UpdateProductStockDto) {
        var product: Product? = null
        val platformName = PlatformEnum.LAZADA.platformName
        val saleGoodsList = mutableListOf<SaleGoods>()

        // 收集库存变更信息，用于日志记录
        val platformProductIdWithStock = HashMap<String, String>()

        try {
            product = productRepository.getById(dto.productId)
            if (product == null) {
                log.info { "商品不存在 - productId: ${dto.productId}, req=${dto.toJson()}" }
            }

            saleGoodsList.addAll(saleGoodsRepository.ktQuery()
                .eq(SaleGoods::productId, dto.productId)
                .`in`(SaleGoods::country, dto.countryList)
                .isNotNull(SaleGoods::platformProductId)
                .list())

            if (CollectionUtils.isEmpty(saleGoodsList)) {
                addErrorLogAndUpdateSaleGoodsState(null, null, saleGoodsList, "销售商品不存在")
                throw BaseBizException("不存在销售商品！")
            }

            val saleSkuList = saleSkuRepository.ktQuery()
                .`in`(SaleSku::saleGoodsId, saleGoodsList.map { it.saleGoodsId })
                .isNotNull(SaleSku::platformSkuId)
                .list()

            if (CollectionUtils.isEmpty(saleSkuList)) {
                addErrorLogAndUpdateSaleGoodsState(null, null, saleGoodsList, "sku不存在")
                throw BaseBizException("sku不存在！")
            }

            for (saleGoods in saleGoodsList) {
                val shop = shopRepository.getById(saleGoods.shopId)
                if (shop == null) {
                    addErrorLogAndUpdateSaleGoodsState(platformName, null, saleGoodsList, "店铺不存在")
                    throw BaseBizException("店铺不存在！")
                }
                if (shop.token.isNullOrBlank()) {
                    addErrorLogAndUpdateSaleGoodsState(platformName, null, saleGoodsList, "店铺Token不存在")
                    throw BaseBizException("店铺Token不存在")
                }

                val updatePriceQuantityReq = UpdatePriceQuantityReq()
                var updateProductResponse: LazadaUpdatePriceQuantityResponse? = null

                try {
                    val updatePriceQuantityProduct = UpdatePriceQuantityReq.Product()
                    val skus = UpdatePriceQuantityReq.Product.Skus()
                    val skuList = mutableListOf<UpdatePriceQuantityReq.Product.Skus.Sku>()

                    val waitUpdateSkuList = saleSkuList.filter { it.saleGoodsId == saleGoods.saleGoodsId }

                    for (saleSku in waitUpdateSkuList) {
                        // 保存platformProductId和库存信息，用于后续记录日志
                        platformProductIdWithStock[saleSku.platformSkuId!!] = saleSku.stockQuantity.toString()

                        skuList.add(UpdatePriceQuantityReq.Product.Skus.Sku().apply {
                            skuId = saleSku.platformSkuId
                            itemId = saleGoods.platformProductId
                            sellerSku = saleSku.sellerSku
                            quantity = saleSku.stockQuantity.toString()
                        })
                    }

                    skus.skuList = skuList
                    updatePriceQuantityProduct.skus = skus
                    updatePriceQuantityReq.product = updatePriceQuantityProduct

                    updateProductResponse = lazadaApiService.updatePriceQuantity(
                        saleGoods.country!!, shop.token!!, updatePriceQuantityReq, saleGoods.productId!!
                    )

                    if (!updateProductResponse.isSuccess) {
                        throw PublishAscBizException("更新价格库存异常:${updateProductResponse.toJson()}")
                    }

                    if (updateProductResponse.isSuccess) {
                        saleGoodsRepository.updateById(SaleGoods().apply {
                            saleGoodsId = saleGoods.saleGoodsId
                            platformSyncState = PlatformSyncStateEnum.SUCCESS.code
                        })

                        // 删除错误日志

                        productSyncLogRepository.ktUpdate()
                            .eq(ProductSyncLog::saleGoodId, saleGoods.saleGoodsId)
                            .eq(ProductSyncLog::logType, NO.code)
                            .eq(ProductSyncLog::opType, PlatformOperatorTypeEnum.MODIFY_STOCK.code).remove()

                        addSuccessSyncLog(
                            platformName,
                            shop.shopName,
                            saleGoods,
                            updatePriceQuantityReq.toJson(),
                            updateProductResponse.toJson(),
                            PlatformOperatorTypeEnum.MODIFY_STOCK.code
                        )
                    }
                } catch (e: Exception) {
                    addErrorSyncLog(
                        null,
                        null,
                        saleGoods,
                        e.message ?: "",
                        updatePriceQuantityReq.toJson(),
                        updateProductResponse?.toJson() ?: "",
                        PlatformOperatorTypeEnum.MODIFY_STOCK.code
                    )

                    if (saleGoods.platformSyncState != PlatformSyncStateEnum.SUCCESS.code) {
                        saleGoodsRepository.updateById(SaleGoods().apply {
                            saleGoodsId = saleGoods.saleGoodsId
                            platformSyncState = PlatformSyncStateEnum.FAILURE.code
                        })
                    }
                    log.info { "修改库存失败，e=${e.message}" }
                }
            }

            // 构建详细的日志内容
            val logContent = StringBuilder("修改库存: ")
            if (platformProductIdWithStock.isNotEmpty()) {
                var count = 0
                for ((key, value) in platformProductIdWithStock) {
                    if (count > 0) {
                        logContent.append(", ")
                    }
                    logContent.append("SKU[$key]库存为[$value]")
                    count++
                }
            } else {
                logContent.append("无库存变更")
            }

            saveProductOperateLog(logContent.toString(), PlatformOperatorTypeEnum.MODIFY_STOCK, product?.productId)
        } catch (e: Exception) {
            // 非单站店异常需要记录日志（单站点异常在上面for循环记录）
            val isAscException = e is PublishAscBizException
            if (!isAscException) {
                addErrorLogAndUpdateSaleGoodsState(platformName, null, saleGoodsList, e.message ?: "")
            }
            product?.let { updateProductStateByCreateError(it) }
            log.info { "创建商品异常,error:${e.message}" }
        } finally {
            product?.let { updateProductStateByCreateError(it) }
        }
    }

    private fun addErrorLogAndUpdateSaleGoodsState(platformName: String?, shop: String?, saleGoodsList: List<SaleGoods>, error: String) {
        if (CollectionUtils.isEmpty(saleGoodsList)) return

        // 批量修改状态
        saleGoodsList.forEach { it.platformSyncState = PlatformSyncStateEnum.FAILURE.code }
        saleGoodsRepository.updateBatchById(saleGoodsList)

        // 批量添加日志
        batchAddErrorSyncLog(
            platformName,
            shop,
            saleGoodsList,
            error,
            null,
            null,
            PlatformOperatorTypeEnum.ACTIVE.code
        )
    }

    private fun batchAddErrorSyncLog(
        platformName: String?,
        shop: String?,
        saleGoodsList: List<SaleGoods>,
        error: String,
        platformRequestParams: String?,
        resp: String?,
        opType: Int
    ) {
        val saveList = mutableListOf<ProductSyncLog>()
        for (saleGoods in saleGoodsList) {
            saveList.add(ProductSyncLog().apply {
                productId = saleGoods.productId
                saleGoodId = saleGoods.saleGoodsId
                this.opType = opType
                errorMsg = "站点:${saleGoods.country},$error"
                this.platformName = platformName
                shopName = shop
                this.platformRequestParams = platformRequestParams
                platformHttpResp = resp
            })
        }
        productSyncLogRepository.saveBatch(saveList)
    }

    private fun addErrorSyncLog(
        platformName: String?,
        shop: String?,
        saleGoods: SaleGoods,
        error: String,
        platformRequestParams: String?,
        resp: String?,
        opType: Int
    ) {
        productSyncLogRepository.save(ProductSyncLog().apply {
            productId = saleGoods.productId
            saleGoodId = saleGoods.saleGoodsId
            this.opType = opType
            errorMsg = "站点:${saleGoods.country},$error"
            this.platformName = platformName
            shopName = shop
            this.platformRequestParams = platformRequestParams
            platformHttpResp = resp
        })
    }

    private fun addSuccessSyncLog(
        platformName: String?,
        shop: String?,
        saleGoods: SaleGoods,
        platformRequestParams: String?,
        resp: String?,
        opType: Int
    ) {
        productSyncLogRepository.save(ProductSyncLog().apply {
            productId = saleGoods.productId
            saleGoodId = saleGoods.saleGoodsId
            this.opType = opType
            logType = YES.code
            this.platformName = platformName
            shopName = shop
            this.platformRequestParams = platformRequestParams
            platformHttpResp = resp
        })
    }

    /**
     * 保存产品操作日志
     *
     * @param content 日志内容
     * @param operationType 操作类型
     * @param productId 产品ID
     */
    private fun saveProductOperateLog(content: String, operationType: PlatformOperatorTypeEnum?, productId: Long?) {
        val productOperateLog = ProductOperateLog().apply {
            this.content = content
            this.logType = operationType?.code
            this.productId = productId
        }
        productOperateLogRepository.save(productOperateLog)
    }

    /**
     * 获取值，如果为空则返回 "未设置"
     */
    private fun getValueOrDefault(value: String?): String {
        return value ?: "未设置"
    }

    private fun updateProductIfNoPublish(data: ProductUpdateDto.Data, product: Product) {

        if (product.productId == null) {
            log.error { "找不到对应商品:${data.toJson()}" }
            return
        }

        // 需要执行的平台
        val platformEnumList = PlatformEnum.entries

        val opTypeEnum = ProductUpdateTypeEnum.findByCode(data.opType)
        when (opTypeEnum) {
            UPDATE_PRICE -> {
                productUpdateServiceImpl.updatePrice(data)
                platformEnumList.forEach {
                    productUpdateServiceSelector.getTemplateUpdateHandler(it)?.updatePrice(data)
                }

                saveProductOperateLog("修改价格", TypeConverter.updateToOperatorType(opTypeEnum), product.productId)
                log.info { "${"待上架修改价格成功,dto={}"} ${data.toJson()}" }
            }

            UPDATE_PRICING_TYPE -> {
                productUpdateServiceImpl.updatePricingType(data)
                platformEnumList.forEach {
                    productUpdateServiceSelector.getTemplateUpdateHandler(it)?.updatePricingType(data)
                }

                //新增操作日志
                val oldPricingTypeDesc = ProductPricingTypeEnum.getByCode(product.pricingType)?.desc ?: "未设置"
                val newPricingTypeDesc = ProductPricingTypeEnum.getByCode(product.pricingType)?.desc ?: "未设置"
                val logContent = if (Objects.equals(product.pricingType, product.pricingType)) {
                    String.format("定价类型无变化: [%s]", newPricingTypeDesc)
                } else {
                    String.format("更新定价类型: 由[%s]变更为[%s]", oldPricingTypeDesc, newPricingTypeDesc)
                }
                saveProductOperateLog(logContent, TypeConverter.updateToOperatorType(opTypeEnum), product.productId)
                log.info { "${"待上架更新定价类型情况，productId: {}，dto={}"} ${product.productId} ${data.toJson()}" }
            }

            UPDATE_PURCHASES_PRICE -> {
                val productSkc = productSkcRepository.getActiveByProductIdAndSkc(product.productId!!, data.skc!!)
                if (Objects.isNull(productSkc)) {
                    log.info { "${"待上架更新采购价错误，找不到skc,dto={}"} ${data.toJson()}" }
                    return
                }
                // 获取原采购价和SKC编码
                val oldPurchasePrice = productSkc!!.purchasePrice
                val skcCode = data.skc
                val newPurchasePrice = data.purchasePrice

                // 更新
                productUpdateServiceImpl.updatePurchasesPrice(data)
                platformEnumList.forEach {
                    productUpdateServiceSelector.getTemplateUpdateHandler(it)?.updatePurchasesPrice(data)
                }

                val logContent = String.format(
                    "更新SKC[%s]采购价: 由[%.2f]变更为[%.2f]", skcCode,
                    Optional.ofNullable(oldPurchasePrice).orElse(BigDecimal.ZERO),
                    Optional.ofNullable(newPurchasePrice).orElse(BigDecimal.ZERO)
                )
                saveProductOperateLog(logContent, TypeConverter.updateToOperatorType(opTypeEnum), product.productId)
                log.info { "${"待上架更新采购价成功 - productId: {}，dto={}"} ${product.productId} ${data.toJson()}" }
            }

            UPDATE_SIZE -> {
                productUpdateServiceImpl.updateSize(data)
                platformEnumList.forEach {
                    productUpdateServiceSelector.getTemplateUpdateHandler(it)?.updateSize(data)
                }

                //新增操作日志
                val newSizes = data.sizeNameList
                val logContent = StringBuilder("更新尺码信息: ")
                if (newSizes?.isNotEmpty() == true) {
                    logContent.append("新增尺码[").append(newSizes.joinToString(",")).append("] ")
                }
                saveProductOperateLog(logContent.toString(), TypeConverter.updateToOperatorType(opTypeEnum), product.productId)
                log.info { "${"待上架更新尺码信息成功 - productId: {}，dto={}"} ${product.productId} ${data.toJson()}" }
            }

            CANCLE -> {
                productUpdateServiceImpl.cancel(data)
                platformEnumList.forEach {
                    productUpdateServiceSelector.getTemplateUpdateHandler(it)?.cancel(data)
                }

                val logContent = String.format("取消SKC[%s]", data.skc)
                saveProductOperateLog(logContent, TypeConverter.updateToOperatorType(opTypeEnum), product.productId)
                log.info { "${"待上架取消SKC成功 - productId: {},dto={}"} ${product.productId} ${data.toJson()}" }
            }

            UPDATE_CATEGORY -> {
                productUpdateServiceImpl.updateCategory(data)
                platformEnumList.forEach {
                    productUpdateServiceSelector.getTemplateUpdateHandler(it)?.updateCategory(data)
                }

                val oldCategoryName = product.categoryName
                val newCategoryName = data.categoryName?.replace("-", ">")
                val logContent = if (Objects.equals(oldCategoryName, newCategoryName)) {
                    String.format("品类无变化: [%s]", newCategoryName)
                } else {
                    String.format("更新品类: 由[%s]变更为[%s]", oldCategoryName ?: "未设置", newCategoryName)
                }
                saveProductOperateLog(logContent, TypeConverter.updateToOperatorType(opTypeEnum), product.productId)

                log.info { "${"待上架更新品类信息成功 - productId: {}, dto={}"} ${product.productId} ${data.toJson()}" }
            }

            UPDATE_COLOR -> {

                val productSkc = productSkcRepository.getActiveByProductIdAndSkc(product.productId!!, data.skc!!)
                if (Objects.isNull(productSkc)) {
                    log.error { "${"待上架更新颜色错误，找不到skc,dto={}"} ${data.toJson()}" }
                    return
                }

                // 保存更新前的颜色信息
                val oldColor = productSkc!!.color
                val oldColorCode = productSkc.colorCode
                val skcCode = data.skc

                productUpdateServiceImpl.updateColor(data)
                platformEnumList.forEach {
                    productUpdateServiceSelector.getTemplateUpdateHandler(it)?.updateColor(data)
                }

                val logContent = StringBuilder()
                if (Objects.equals(oldColor, data.color) && Objects.equals(oldColorCode, data.colorCode)) {
                    logContent.append("SKC[$skcCode]颜色无变化: [${getValueOrDefault(data.color)}]")
                } else {
                    logContent.append("更新SKC[$skcCode]颜色: 由[${getValueOrDefault(oldColor)}]变更为[${getValueOrDefault(data.color)}]")

                    // 如果颜色代码也变更了，一并记录
                    if (!Objects.equals(oldColorCode, data.colorCode)) {
                        logContent.append(", 颜色代码由[${getValueOrDefault(oldColorCode)}]变更为[${getValueOrDefault(data.colorCode)}]")
                    }
                }
                saveProductOperateLog(logContent.toString(), TypeConverter.updateToOperatorType(opTypeEnum), product.productId)

                log.info { "${"待上架更新颜色信息成功 - productId: {}, dto={}"} ${product.productId} ${data.toJson()}" }
            }

            UPDATE_CLOTHING_STYLE -> {
                productUpdateServiceImpl.updateClothingStyle(data)
                platformEnumList.forEach {
                    productUpdateServiceSelector.getTemplateUpdateHandler(it)?.updateClothingStyle(data)
                }

                val oldClothingStyleCode = product.clothingStyleCode
                val oldClothingStyleName = product.clothingStyleName
                val logContent = StringBuilder()
                if (Objects.equals(oldClothingStyleCode, data.clothingStyleCode) &&
                    Objects.equals(oldClothingStyleName, data.clothingStyleName)
                ) {
                    logContent.append(String.format("服装风格无变化: [%s]", if (data.clothingStyleName != null) data.clothingStyleName else "未设置"))
                } else {
                    logContent.append(
                        String.format(
                            "更新服装风格: 由[%s]变更为[%s]",
                            oldClothingStyleName ?: "未设置", if (data.clothingStyleName != null) data.clothingStyleName else "未设置"
                        )
                    )

                    // 如果风格代码也变更了，一并记录
                    if (!Objects.equals(oldClothingStyleCode, data.clothingStyleCode)) {
                        logContent.append(
                            ", 风格代码由[${oldClothingStyleCode ?: "未设置"}]变更为[${data.clothingStyleCode ?: "未设置"}]"
                        )
                    }
                }

                // 保存操作日志
                saveProductOperateLog(logContent.toString(), TypeConverter.updateToOperatorType(opTypeEnum), product.productId)
                log.info { "${"待上架更新服装风格信息成功 - productId: {}, dto={}"} ${product.productId} ${data.toJson()}" }
            }

            ADD_SKC -> {}
            CREATE -> {}
            UPDATE_PRODUCT_SPU -> TODO()
            UPDATE_PRODUCT_ATTRIBUTE ->TODO()
            UNKNOWN -> {}
        }
    }
}