package tech.tiangong.pop.service.product.update.lazada

import org.apache.commons.collections4.CollectionUtils
import org.springframework.stereotype.Service
import team.aikero.blade.core.enums.Bool
import team.aikero.blade.core.toolkit.isEmpty
import team.aikero.blade.core.toolkit.isNotEmpty
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.sequence.id.IdHelper
import team.aikero.blade.util.json.toJson
import tech.tiangong.pop.common.constant.LazadaConstants
import tech.tiangong.pop.common.dto.ProductUpdateDto
import tech.tiangong.pop.common.enums.PlatformEnum
import tech.tiangong.pop.common.req.BatchCreateBarCodeReq
import tech.tiangong.pop.config.LazadaDefaultProperties
import tech.tiangong.pop.dao.entity.ProductSkc
import tech.tiangong.pop.dao.entity.ProductTemplateLazadaSkc
import tech.tiangong.pop.dao.entity.ProductTemplateLazadaSku
import tech.tiangong.pop.dao.repository.*
import tech.tiangong.pop.service.product.BarCodeService
import tech.tiangong.pop.service.product.price.ProductPricingServiceSelector
import tech.tiangong.pop.service.product.update.PlatformTemplateProductUpdateService

/**
 * 模板相关表更新-Lazada
 */
@Service
@Slf4j
class LazadaTemplateUpdateServiceImpl(
    private val productTemplateLazadaSkcRepository: ProductTemplateLazadaSkcRepository,
    private val productTemplateLazadaSkuRepository: ProductTemplateLazadaSkuRepository,
    private val productTemplateLazadaSpuRepository: ProductTemplateLazadaSpuRepository,
    private val productBarCodeRepository: ProductBarCodeRepository,
    private val productRepository: ProductRepository,
    private val barCodeService: BarCodeService,
    private val lazadaDefaultProperties: LazadaDefaultProperties,
    private val productSkcRepository: ProductSkcRepository,
    private val productPricingServiceSelector: ProductPricingServiceSelector,
) : PlatformTemplateProductUpdateService {

    /**
     * 获取平台
     */
    override fun getPlatform(): PlatformEnum {
        return PlatformEnum.LAZADA
    }

    /**
     * 取消SKC
     */
    override fun cancel(data: ProductUpdateDto.Data) {
        val spuList = productTemplateLazadaSpuRepository.listNoCompletedBySpuCode(data.spuCode!!)
        if (spuList.isEmpty()) {
            log.warn { "待上架-取消SKC-错误，找不到spuCode, dto=${data.toJson()}" }
            return
        }
        spuList?.forEach { spu ->

            val productSkcList = productTemplateLazadaSkcRepository.listByLazadaSpuId(spu.lazadaSpuId!!)
            if (CollectionUtils.isEmpty(productSkcList)) {
                return@forEach
            }
            val updateSkcList = productSkcList
                ?.filter { it.skc == data.skc }
                ?.map {
                    ProductTemplateLazadaSkc().apply {
                        this.lazadaSkcId = it.lazadaSkcId
                        this.state = Bool.NO.code
                    }
                } ?: emptyList()
            // 批量更新SKU
            if (updateSkcList.isNotEmpty()) {
                productTemplateLazadaSkcRepository.updateBatchById(updateSkcList)

                // 批量更新SKC下的SKU
                val skcIdList = updateSkcList.map { it.lazadaSkcId!! }
                val skuList = productTemplateLazadaSkuRepository.getByLazadaSkcId(skcIdList)
                if (skuList.isNotEmpty()) {
                    val updateSkuList = skuList.map {
                        ProductTemplateLazadaSku().apply {
                            this.lazadaSkuId = it.lazadaSkuId
                            this.enableState = Bool.NO.code
                        }
                    }
                    // 批量更新
                    if (updateSkuList.isNotEmpty()) {
                        productTemplateLazadaSkuRepository.updateBatchById(updateSkuList)
                    }
                }
            }
        }
    }

    /**
     * 供货价更新
     */
    override fun updatePrice(data: ProductUpdateDto.Data) {
        val spuList = productTemplateLazadaSpuRepository.listNoCompletedBySpuCode(data.spuCode!!)
        if (spuList.isEmpty()) {
            log.warn { "待上架-供货价更新-错误，找不到spuCode, dto=${data.toJson()}" }
            return
        }
        spuList?.forEach { spu ->
            val productSkcList = productTemplateLazadaSkcRepository.listByLazadaSpuId(spu.lazadaSpuId!!)
            if (CollectionUtils.isEmpty(productSkcList)) {
                return@forEach
            }

            val productSkcIdMap = mutableMapOf<Long, ProductSkc>()
            val productSkcIds = productSkcList?.mapNotNull { it.productSkcId }?.distinct()
            if (productSkcIds.isNotEmpty()) {
                val productSkcList = productSkcRepository.listByIds(productSkcIds)
                if (productSkcList.isNotEmpty()) {
                    productSkcList.forEach {
                        productSkcIdMap[it.productSkcId!!] = it
                    }
                }
            }

            // 当前skc更新供货价
            productSkcList
                ?.filter { it.skc == data.skc }
                ?.forEach {
                    val productSkc = productSkcIdMap[it.productSkcId]
                    if (productSkc != null) {
                        val updateSkc = ProductTemplateLazadaSkc().apply {
                            this.lazadaSkcId = it.lazadaSkcId
                            this.localPrice = data.price
                        }
                        productTemplateLazadaSkcRepository.updateById(updateSkc)
                    }
                }

            // 所有skc更新定价成本
            productSkcList?.forEach {
                val productSkc = productSkcIdMap[it.productSkcId]
                if (productSkc != null) {
                    val updateSkc = ProductTemplateLazadaSkc().apply {
                        this.lazadaSkcId = it.lazadaSkcId
                        this.costPrice = productSkc.costPrice
                    }
                    productTemplateLazadaSkcRepository.updateById(updateSkc)
                }
            }

            // 重算价格(里面使用product_skc来计算)
            val autoCulPriceList = productPricingServiceSelector
                .getSalePricingHandler(getPlatform())
                .autoCalPriceByTemplate(spu.lazadaSpuId!!, LazadaConstants.LAZADA_DEFAULT_COUNTRY.map { it.code })
            // 重新查一次skc, 所有skc重算售价
            val updateSkuList = mutableListOf<ProductTemplateLazadaSku>()
            productTemplateLazadaSkcRepository.listByLazadaSpuId(spu.lazadaSpuId!!)
                ?.forEach {
                    val productSkcIdMap = autoCulPriceList.associateBy({ it.skc }, { it })
                    val skuList = productTemplateLazadaSkuRepository.getByLazadaSkcId(listOf(it.lazadaSkcId!!))
                    if (skuList.isNotEmpty()) {
                        skuList.forEach { sku ->
                            val price = productSkcIdMap[it.skc]?.countryPriceList?.find { it.country == sku.country }
                            if (price != null) {
                                val updateSku = ProductTemplateLazadaSku().apply {
                                    this.lazadaSkuId = sku.lazadaSkuId
                                    this.salePrice = price.salePrice
                                    this.retailPrice = price.retailPrice
                                }
                                updateSkuList.add(updateSku)
                            }
                        }
                    }
                }

            if (updateSkuList.isNotEmpty()) {
                productTemplateLazadaSkuRepository.updateBatchById(updateSkuList)
            }
        }
    }

    /**
     * 新增SKC
     */
    override fun addSkc(data: ProductUpdateDto.Data) {
        // 新增SKC使用create来源, 那里有模板init操作
    }

    /**
     * 创建商品
     */
    override fun create(data: ProductUpdateDto.Data) {
    }

    /**
     * 更新品类
     */
    override fun updateCategory(data: ProductUpdateDto.Data) {
        // 模板表没有品类, product表记录
    }

    /**
     * 更新颜色
     */
    override fun updateColor(data: ProductUpdateDto.Data) {
        val spuList = productTemplateLazadaSpuRepository.listNoCompletedBySpuCode(data.spuCode!!)
        if (spuList.isEmpty()) {
            log.warn { "待上架-更新颜色-错误，找不到spuCode, dto=${data.toJson()}" }
            return
        }
        spuList?.forEach { spu ->
            val productSkcList = productTemplateLazadaSkcRepository.listByLazadaSpuId(spu.lazadaSpuId!!)
            if (CollectionUtils.isEmpty(productSkcList)) {
                return@forEach
            }
            val updateSkuList = mutableListOf<ProductTemplateLazadaSkc>()
            productSkcList
                ?.filter { it.skc == data.skc }
                ?.forEach {
                    val updateSkc = ProductTemplateLazadaSkc().apply {
                        this.lazadaSkcId = it.lazadaSkcId
                        this.color = data.color
                        this.platformColor = data.colorCode
                        this.colorCode = data.colorCode
                        this.colorAbbrCode = data.colorAbbrCode
                    }
                    updateSkuList.add(updateSkc)
                }
            if (updateSkuList.isNotEmpty()) {
                productTemplateLazadaSkcRepository.updateBatchById(updateSkuList)
            }
        }
    }

    /**
     * 更新采购价
     */
    override fun updatePurchasesPrice(data: ProductUpdateDto.Data) {
        val spuList = productTemplateLazadaSpuRepository.listNoCompletedBySpuCode(data.spuCode!!)
        if (spuList.isEmpty()) {
            log.warn { "待上架-更新采购价-错误，找不到spuCode, dto=${data.toJson()}" }
            return
        }
        spuList?.forEach { spu ->
            val productSkcList = productTemplateLazadaSkcRepository.listByLazadaSpuId(spu.lazadaSpuId!!)
            if (CollectionUtils.isEmpty(productSkcList)) {
                return@forEach
            }

            val productSkcIdMap = mutableMapOf<Long, ProductSkc>()
            val productSkcIds = productSkcList?.mapNotNull { it.productSkcId }?.distinct()
            if (productSkcIds.isNotEmpty()) {
                val productSkcList = productSkcRepository.listByIds(productSkcIds)
                if (productSkcList.isNotEmpty()) {
                    productSkcList.forEach {
                        productSkcIdMap[it.productSkcId!!] = it
                    }
                }
            }

            productSkcList
                ?.filter { it.skc == data.skc }
                ?.forEach {
                    val productSkc = productSkcIdMap[it.productSkcId]
                    if (productSkc != null) {
                        val updateSkc = ProductTemplateLazadaSkc().apply {
                            this.lazadaSkcId = it.lazadaSkcId
                            this.purchasePrice = data.purchasePrice
                            this.costPrice = productSkc.costPrice
                        }
                        productTemplateLazadaSkcRepository.updateById(updateSkc)
                    }
                }

            // 所有skc更新定价成本
            productSkcList?.forEach {
                val productSkc = productSkcIdMap[it.productSkcId]
                if (productSkc != null) {
                    val updateSkc = ProductTemplateLazadaSkc().apply {
                        this.lazadaSkcId = it.lazadaSkcId
                        this.costPrice = productSkc.costPrice
                    }
                    productTemplateLazadaSkcRepository.updateById(updateSkc)
                }
            }

            // 重算价格(里面使用product_skc来计算)
            val autoCulPriceList = productPricingServiceSelector
                .getSalePricingHandler(getPlatform())
                .autoCalPriceByTemplate(spu.lazadaSpuId!!, LazadaConstants.LAZADA_DEFAULT_COUNTRY.map { it.code })
            // 重新查一次skc, 所有skc重算售价
            val updateSkuList = mutableListOf<ProductTemplateLazadaSku>()
            productTemplateLazadaSkcRepository.listByLazadaSpuId(spu.lazadaSpuId!!)
                ?.forEach {
                    val productSkcIdMap = autoCulPriceList.associateBy({ it.skc }, { it })
                    val skuList = productTemplateLazadaSkuRepository.getByLazadaSkcId(listOf(it.lazadaSkcId!!))
                    if (skuList.isNotEmpty()) {
                        skuList.forEach { sku ->
                            val price = productSkcIdMap[it.skc]?.countryPriceList?.find { it.country == sku.country }
                            if (price != null) {
                                val updateSku = ProductTemplateLazadaSku().apply {
                                    this.lazadaSkuId = sku.lazadaSkuId
                                    this.salePrice = price.salePrice
                                    this.retailPrice = price.retailPrice
                                }
                                updateSkuList.add(updateSku)
                            }
                        }
                    }
                }

            if (updateSkuList.isNotEmpty()) {
                productTemplateLazadaSkuRepository.updateBatchById(updateSkuList)
            }
        }
    }

    /**
     * 更新尺码信息
     */
    override fun updateSize(data: ProductUpdateDto.Data) {
        if (CollectionUtils.isEmpty(data.sizeNameList)) {
            return
        }
        val spuList = productTemplateLazadaSpuRepository.listNoCompletedBySpuCode(data.spuCode!!)
        if (spuList.isEmpty()) {
            log.warn { "待上架-更新尺码信息-错误，找不到spuCode, dto=${data.toJson()}" }
            return
        }
        spuList?.forEach { spu ->
            val productSkcList = productTemplateLazadaSkcRepository.listByLazadaSpuId(spu.lazadaSpuId!!)
            if (CollectionUtils.isEmpty(productSkcList)) {
                return@forEach
            }
            productSkcList
                ?.filter { it.skc == data.skc }
                ?.forEach { skc ->
                    val skuList = productTemplateLazadaSkuRepository.getByLazadaSkcId(listOf(skc.lazadaSkcId!!))
                    if (skuList.isNotEmpty()) {
                        /*
                        以模板sku为基准, 匹配data尺码, 模板不存在data内, 删除
                         */
                        skuList
                            .filter { !data.sizeNameList!!.contains(it.sizeName) }
                            .forEach { sku ->
                                val updateSku = ProductTemplateLazadaSku().apply {
                                    this.lazadaSkuId = sku.lazadaSkuId
                                    this.enableState = Bool.NO.code
                                }
                                productTemplateLazadaSkuRepository.updateById(updateSku)
                            }
                        /*
                        以data尺码为基准, 匹配模板sku
                        1. 模板不存在data内, 新增
                        2. 模板存在data内, enable 改为 1
                         */
                        skuList
                            .groupBy { it.country }
                            .forEach { (country, skuList) ->
                                data.sizeNameList!!.forEach { sizeName ->

                                    if (!skuList.map { it.sizeName }.contains(sizeName)) {
                                        // 模板不存在data内
                                        var newBarcode: String? = null
                                        val barCode = productBarCodeRepository.getBySpuCodeAndSkcAndSize(spu.spuCode, skc.skc, sizeName)
                                        if (barCode == null) {
                                            // 添加条码
                                            val product = productRepository.getById(spu.productId)
                                            val barcodeReq = BatchCreateBarCodeReq().apply {
                                                this.categoryCode = product.categoryCode
                                                this.categoryName = product.categoryName
                                                this.skcCode = skc.skc
                                                this.color = skc.color
                                                this.spuCode = product.spuCode
                                                this.groupName = product.sizeGroupName
                                                this.sourceGroupCode = product.sizeGroupCode
                                                this.sizeValues = listOf(sizeName)
                                                this.inspiraImgUrl = product.inspiraImgUrl
                                                this.supplyMode = product.supplyMode
                                                this.localPrice = skc.localPrice
                                                this.designImgUrl = skc.pictures
                                                this.mainImgUrl = skc.pictures
                                            }
                                            val barcodeResp = barCodeService.createBarcodeByForce(listOf(barcodeReq))
                                            if (CollectionUtils.isNotEmpty(barcodeResp)) {
                                                newBarcode = barcodeResp[0].barcode
                                            }
                                        } else {
                                            newBarcode = barCode.barcode
                                        }

                                        val sku = ProductTemplateLazadaSku().apply {
                                            this.lazadaSkuId = IdHelper.getId()
                                            this.lazadaSkcId = skc.lazadaSkcId
                                            this.lazadaSpuId = spu.lazadaSpuId
//                        this.sellerSku =
                                            this.barcode = newBarcode
                                            this.country = country
                                            this.stockQuantity = lazadaDefaultProperties.defaultStock?.toLong()
                                            this.sizeName = sizeName
                                            this.flagFrontend = Bool.YES.code
                                            this.enableState = Bool.YES.code
                                        }
                                        productTemplateLazadaSkuRepository.save(sku)
                                    } else {
                                        val updateSkuList = skuList
                                            .filter { it.sizeName == sizeName }
                                            .map { sku ->
                                                val updateSku = ProductTemplateLazadaSku().apply {
                                                    this.lazadaSkuId = sku.lazadaSkuId
                                                    this.enableState = Bool.YES.code
                                                }
                                                updateSku
                                            }
                                        if (CollectionUtils.isNotEmpty(updateSkuList)) {
                                            productTemplateLazadaSkuRepository.updateBatchById(updateSkuList)
                                        }
                                    }
                                }
                            }
                    }
                }
        }
    }

    /**
     * 更新定价类型
     */
    override fun updatePricingType(data: ProductUpdateDto.Data) {
        // 模板表没有定价类型, product表记录
    }

    /**
     * 更新款式风格
     */
    override fun updateClothingStyle(data: ProductUpdateDto.Data) {
        // 模板表没有款式风格, product表记录
    }
    /**
     * 更新商品属性
     */
    override fun updateAttributes(data: ProductUpdateDto.Data){

    }
}