package tech.tiangong.pop.service.product.impl

import com.baomidou.mybatisplus.extension.kotlin.KtQueryWrapper
import com.baomidou.mybatisplus.extension.plugins.pagination.Page
import org.apache.commons.collections4.CollectionUtils
import org.apache.commons.lang3.ObjectUtils
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.stereotype.Service
import org.springframework.transaction.PlatformTransactionManager
import org.springframework.transaction.annotation.Transactional
import org.springframework.transaction.support.TransactionTemplate
import team.aikero.blade.auth.withUser
import team.aikero.blade.core.enums.Bool
import team.aikero.blade.core.enums.Bool.NO
import team.aikero.blade.core.enums.Bool.YES
import team.aikero.blade.core.exception.BusinessException
import team.aikero.blade.core.protocol.PageVo
import team.aikero.blade.core.toolkit.isNotBlank
import team.aikero.blade.core.toolkit.isNotEmpty
import team.aikero.blade.core.toolkit.isNotNull
import team.aikero.blade.core.toolkit.isNull
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.sequence.id.IdHelper
import team.aikero.blade.user.entity.CurrentUser
import team.aikero.blade.user.holder.CurrentUserHolder
import team.aikero.blade.util.assertion.Assert
import team.aikero.blade.util.async.runAsync
import team.aikero.blade.util.json.parseJsonList
import team.aikero.blade.util.json.toJson
import tech.tiangong.pop.common.constant.AliexpressConstants
import tech.tiangong.pop.common.enums.ChannelEnum.ALIBABA
import tech.tiangong.pop.common.enums.PlatformEnum.AE
import tech.tiangong.pop.common.enums.ProductAePublishStateEnum
import tech.tiangong.pop.common.enums.ProductPublishStateEnum
import tech.tiangong.pop.common.exception.BaseBizException
import tech.tiangong.pop.common.exception.PublishGlobalBizException
import tech.tiangong.pop.common.req.BatchCreateBarCodeReq
import tech.tiangong.pop.component.GenerateSellerSkuComponent
import tech.tiangong.pop.component.MarketStyleComponent
import tech.tiangong.pop.component.ae.AeUpdateProductComponent
import tech.tiangong.pop.component.ae.CallAeComponent
import tech.tiangong.pop.config.AliexpressProperties
import tech.tiangong.pop.dao.BaseEntity
import tech.tiangong.pop.dao.entity.*
import tech.tiangong.pop.dao.repository.*
import tech.tiangong.pop.dto.AeNationalQuoteConfigDto
import tech.tiangong.pop.dto.image.ImageCollectionDTO
import tech.tiangong.pop.dto.mq.LazadaPriceMqDto
import tech.tiangong.pop.dto.product.ComboBarcodeInfoDto
import tech.tiangong.pop.dto.product.ProductAePropertyValueItemDTO
import tech.tiangong.pop.enums.*
import tech.tiangong.pop.external.InspirationClientExternal
import tech.tiangong.pop.helper.*
import tech.tiangong.pop.helper.cache.AliexpressServiceHelper
import tech.tiangong.pop.req.product.UpdateProductGrossMarginByShopReq
import tech.tiangong.pop.req.product.ae.*
import tech.tiangong.pop.req.product.lazada.UpdateSkuByUpdateStockPriceReq
import tech.tiangong.pop.resp.category.PublishAttributePairResp
import tech.tiangong.pop.resp.image.ImageAniVo
import tech.tiangong.pop.resp.product.ProductFrontUrlResp
import tech.tiangong.pop.resp.product.ae.*
import tech.tiangong.pop.resp.sdk.aliexpress.AliexpressCategoryAttributeResponse
import tech.tiangong.pop.service.AliexpressService
import tech.tiangong.pop.service.product.BarCodeService
import tech.tiangong.pop.service.product.ProductAeService
import tech.tiangong.pop.service.product.ProductManageService
import tech.tiangong.pop.service.product.price.sale.AeSalePricingService
import tech.tiangong.pop.service.regionpricerule.RegionPriceRuleService
import tech.tiangong.pop.utils.getRootMessage
import java.math.BigDecimal
import java.time.LocalDateTime
import java.time.LocalTime
import java.util.*
import java.util.concurrent.ExecutorService

/**
 * 商品-AE
 * <AUTHOR>
 * @date 2025-2-12 10:31:11
 */
@Slf4j
@Service
class ProductAeServiceImpl(
    private val productRepository: ProductRepository,
    private val productSkcRepository: ProductSkcRepository,
    private val transactionManager: PlatformTransactionManager,
    private val aeSaleGoodsRepository: AeSaleGoodsRepository,
    private val aeSaleSkcRepository: AeSaleSkcRepository,
    private val aeSaleSkuRepository: AeSaleSkuRepository,
    private val inspirationClientExternal: InspirationClientExternal,
    private val productAttributesRepository: ProductAttributesRepository,
    private val imageRepositoryRepository: ImageRepositoryRepository,
    private val publishCategoryMappingRepository: PublishCategoryMappingRepository,
    private val productBarCodeRepository: ProductBarCodeRepository,
    private val barCodeService: BarCodeService,
    private val productPictureRepository: ProductPictureRepository,
    private val aeUpdateProductComponent: AeUpdateProductComponent,
    private val productSourceDataRepository: ProductSourceDataRepository,
    private val aeSalePricingService: AeSalePricingService,
    private val callAeComponent: CallAeComponent,
    private val aliexpressService: AliexpressService,
    private val shopRepository: ShopRepository,
    private val imageCollectionHelper: ImageCollectionHelper,
    private val imagePackCollectionHelper: ImagePackCollectionHelper,
    private val aliexpressServiceHelper: AliexpressServiceHelper,
    private val productOperateLogRepository: ProductOperateLogRepository,
    private val aliexpressProperties: AliexpressProperties,
    private val productAttributesAeHelper: ProductAttributesAeHelper,
    private val productPublishAeHelper: ProductPublishAeHelper,
    private val productTagRepository: ProductTagRepository,
    private val generateSellerSkuComponent: GenerateSellerSkuComponent,
    private val productSyncLogRepository: ProductSyncLogRepository,
    @Qualifier("asyncExecutor")
    private val asyncExecutor: ExecutorService,
    private val marketStyleComponent: MarketStyleComponent,
    private val regionPriceRuleLogisticsRepository: RegionPriceRuleLogisticsRepository,
    private val regionPriceRuleService: RegionPriceRuleService,
    private val productManageService: ProductManageService,
) : ProductAeService {

    override fun detail(req: ProductAeDetailReq): ProductAeDetailResp {

        if (req.saleGoodsId == null) {
            return ProductAeDetailResp()
        }
        // 获取商品基础信息
        val saleGoods = aeSaleGoodsRepository.getById(req.saleGoodsId) ?: throw IllegalArgumentException("销售商品不存在")
        val product = productRepository.getById(saleGoods.productId) ?: throw IllegalArgumentException("商品不存在")

        // 获取sale_skc信息
        val saleSkcList = aeSaleSkcRepository.findBySaleGoodsId(saleGoods.saleGoodsId!!).filter { it.state == YES.code }
        // 获取sale_sku信息
        val saleSkus = aeSaleSkuRepository.findBySaleSkcIds(saleSkcList.mapNotNull { it.saleSkcId }).filter { aeSku -> aeSku.enableState == YES.code }

        // 获取商品属性
        val attributes = productAttributesRepository.listByProductId(product.productId!!, AE.platformId)

        // 获取图片信息
        val imageRepository = imageRepositoryRepository.getBySpuCode(product.spuCode!!)
        val images = imageRepository?.imageUrls?.parseJsonList(ImageAniVo::class.java) ?: emptyList()

        // 为已上架商品构建图包集合（根据shopId获取最新规则）
        val imagePackageCollection = imagePackCollectionHelper.buildImageCollectionForPublished(
            platformId = AE.platformId,
            shopId = saleGoods.shopId!!,
            images = images,
            spuCode = product.spuCode!!,
            currentRuleId = saleGoods.imagePackRuleId,
            currentRuleVersion = saleGoods.imagePackRuleVersion
        )

        // 批量获取product标签
        val saleSkcIds = saleSkcList.mapNotNull { it.saleSkcId }
        val saleSkcIdTagMap = if (saleSkcIds.isNotEmpty()) {
            productTagRepository.getTagMapByAeSaleSkcIds(saleSkcIds)
        } else {
            mapOf()
        }
        // 市场(一级节点)
        val marketMap = marketStyleComponent.getMarketNodeMap(1)
        // 系列(二级节点) 市场-系列
        val marketSeriesMap = marketStyleComponent.getMarketNodeMap(2)
        // 风格(三级节点) 市场-系列-风格
        val marketStyleMap = marketStyleComponent.getMarketNodeMap(3)
        // 构造返回结果
        return ProductAeDetailResp().apply {
            // 基础商品信息
            this.productId = product.productId
            this.shopId = saleGoods.shopId
            this.mainImgUrl = product.mainImgUrl
            this.spuCode = product.spuCode
            this.supplyMode = product.supplyMode
            this.spotTypeCode = product.spotTypeCode
            this.planningType = product.planningType
            this.marketCode = product.marketCode
            this.marketName = marketMap?.get(product.marketCode)
            this.marketSeriesCode = product.marketSeriesCode
            this.marketSeriesName = marketSeriesMap?.get(product.marketSeriesCode)
            // TODO 兼容历史数据，后续字典维护好可去掉
            this.clothingStyleName = product.clothingStyleName ?: marketStyleMap?.get(product.clothingStyleCode)

            this.planSourceName = product.planSourceName
            this.goodsRepType = product.goodsRepType
            this.categoryCode = product.categoryCode
            this.categoryName = product.categoryName
            this.inspiraSource = product.inspiraSource
            this.inspiraImgUrl = product.inspiraImgUrl
            this.selectStyleName = product.selectStyleName
            this.selectStyleTime = product.selectStyleTime
            this.buyerRemark = product.buyerRemark

            this.prototypeNum = product.prototypeNum
            this.goodsType = product.goodsType
            this.pricingType = product.pricingType
            this.spotType = product.spotType
            this.waves = product.waves
            this.printType = product.printType
            this.imagePackageState = product.imagePackageState
            this.styleType = product.styleType

            this.creatorName = saleGoods.creatorName
            this.createdTime = saleGoods.createdTime
            this.update = saleGoods.updateState
            this.costPriceUpdateState = saleGoods.costPriceUpdateState
            this.firstPublishTime = saleGoods.publishTime
            this.firstPublishUserName = saleGoods.publishUserName
            this.latestPublishTime = saleGoods.latestPublishTime
            this.latestPublishUserId = saleGoods.latestPublishUserId
            this.latestPublishUserName = saleGoods.latestPublishUserName
            this.latestOfflineTime = saleGoods.latestOfflineTime
            this.latestOfflineUserName = saleGoods.latestOfflineUserName
            this.reviserName = saleGoods.reviserName
            this.revisedTime = saleGoods.revisedTime

            // 灵感来源信息
            if (product.inspiraSourceId != null) {
                try {
                    val data = inspirationClientExternal.getByInspirationOrPickingId(product.inspiraSourceId!!)
                    if (data?.inspirationInfo != null) {
                        this.inspiraSource = data.inspirationInfo?.inspirationImageSource
                        this.inspiraCountry = data.inspirationInfo?.countrySiteName
                        this.planSourceName = data.inspirationInfo?.planningSourceName
                        this.inspiraImgUrl = data.inspirationInfo?.inspirationImage
                    }
                    if (data?.pickingInfo != null) {
                        this.countryList = data.pickingInfo?.countrySiteName?.let { listOf(it) }
                        this.shopList = mutableListOf<ProductAeDetailShopResp>().apply {
                            this.add(ProductAeDetailShopResp().apply {
                                this.shopId = data.pickingInfo?.shopId
                                this.shopName = data.pickingInfo?.shopName
                            })
                        }.toList()
                        this.selectStyleTime = data.pickingInfo?.pickingTime
                        this.buyerRemark = data.pickingInfo?.remark
                    }
                } catch (e: Exception) {
                    log.error(e) { "getByInspirationOrPickingId 获取灵感详情报错或者数据为空，Exception=${e.message}" }
                }
            }

            // AE详情
            this.detail = ProductAeSpecificDetailResp().apply {
                // SPU信息
                this.saleGoodsId = saleGoods.saleGoodsId
                this.productTitle = saleGoods.productTitle
                this.brandId = saleGoods.brandId
                this.brandName = saleGoods.brandName
                this.stockTypeName = saleGoods.stockType?.let { StockTypeEnum.getByCode(it) }?.desc
                this.delayDeliveryDays = saleGoods.delayDeliveryDays
                this.sizeGroupName = product.sizeGroupName
                this.sizeGroupCode = product.sizeGroupCode

                // 类目信息
                this.categoryId = product.categoryId
                this.categoryMappingId = getPlatformCategory(product, AE.platformId)?.categoryMappingId
                this.platformName = AE.platformName
                this.invDeduction = saleGoods.invDeduction
                this.originPlaceName = saleGoods.originPlaceName
                this.platformCategoryName = saleGoods.platformCategoryName

                // 包裹信息
                this.packageDimensionsLength = saleGoods.packageDimensionsLength
                this.packageDimensionsWidth = saleGoods.packageDimensionsWidth
                this.packageDimensionsHeight = saleGoods.packageDimensionsHeight
                this.packageWeight = saleGoods.packageWeight

                this.taxType = saleGoods.taxType

                // 商品属性
                this.attributes = attributes.map {
                    PublishAttributePairResp().apply {
                        this.attributeId = it.attributeId
                        this.attributeValueId = it.attributeValueId
                    }
                }

                // 图片包集合信息
                this.imagePackCollection = imagePackageCollection
                this.promiseTemplateId = saleGoods.promiseTemplateId
                this.promiseTemplateName = saleGoods.promiseTemplateName
                this.freightTemplateId = saleGoods.freightTemplateId
                this.freightTemplateName = saleGoods.freightTemplateName
                this.msrId = saleGoods.msrId
                this.msrName = saleGoods.msrName
                this.manufactureId = saleGoods.manufactureId
                this.manufactureName = saleGoods.manufactureName
                this.productGroups = saleGoods.getProductGroupList()
                this.hsCode = saleGoods.hsCode
                this.hsPvList = saleGoods.getParsedHsPvList()
                this.hsExtendInfo = saleGoods.hsExtendInfo
                this.originPropertyValueItems = saleGoods.getParsedOriginPropertyValueItems()

                val logisticsRuleConfigMap = regionPriceRuleLogisticsRepository.getAeDefaultLogisticsCostConfig()

                // 按颜色分组SKC数据 - 一个颜色可能对应多个销售站点的SKC
                val skcsByColor = saleSkcList.groupBy { it.color }

                // 将SKU按SKC ID分组，用于快速查找
                val skuMapBySkcId = saleSkus.groupBy { it.saleSkcId }

                // 重构SKC列表，以颜色为单位进行聚合
                this.skcList = saleSkcList.map { skc ->
                    ProductAeSpecificDetailResp.ProductAeSkcResp().apply {
                        this.saleSkcId = skc.saleSkcId
                        this.skc = skc.skc
                        this.color = skc.color
                        this.colorCode = skc.colorCode
                        this.platformColor = skc.platformColor
                        this.pictures = skc.pictures
                        this.cbPrice = skc.cbPrice
                        this.localPrice = skc.localPrice
                        this.purchasePrice = skc.purchasePrice
                        this.costPrice = skc.costPrice
                        this.combo = skc.combo
                        this.allowedUpdateUnit = skc.allowedUpdateUnit
                        this.tagCodes = ProductTagEnum.getDescListByPage(saleSkcIdTagMap[skc.saleSkcId] ?: emptyMap()).distinct()

                        // 按尺码名称分组SKU
                        this.skuList = skuMapBySkcId[skc.saleSkcId]?.map { sku ->
                            ProductAeSpecificDetailResp.ProductAeSkuResp().apply {
                                this.sellerSku = sku.sellerSku
                                this.barcode = sku.barcode
                                this.barcodes = sku.barcodes?.parseJsonList(ComboBarcodeInfoDto::class.java)
                                this.stockQuantity = sku.stockQuantity
                                this.saleSkuId = sku.saleSkuId
                                this.platformProductId = sku.platformProductId
                                this.platformSkuId = sku.platformSkuId
                                this.sizeName = sku.sizeName
                                this.salePrice = sku.salePrice
                                this.retailPrice = sku.retailPrice
                                this.lastSalePrice = sku.lastSalePrice
                                this.lastRetailPrice = sku.lastRetailPrice
                                this.nationalQuoteConfigList = sku.nationalQuoteConfig?.parseJsonList(AeNationalQuoteConfigDto::class.java)
                                    ?.map { nqc ->
                                        AeNationalQuoteConfigDto().apply {
                                            this.shipToCountry = nqc.shipToCountry
                                            this.price = nqc.price
                                            this.defaultFLag = Bool.NO.code
                                            // 发货地-目的地 获取配置, 得到是否默认
                                            val config = logisticsRuleConfigMap[sku.shipsFromAttributeValueId]
                                            if (config != null && config.receivingPlace == nqc.shipToCountry) {
                                                this.defaultFLag = Bool.YES.code
                                            }
                                        }
                                    }
                                this.purchaseSalePrice = sku.purchaseSalePrice
                                this.purchaseRetailPrice = sku.purchaseRetailPrice
                                this.regularSalePrice = sku.regularSalePrice
                                this.regularRetailPrice = sku.regularRetailPrice
                                this.enable = sku.enableState
                                this.publishState = sku.publishState
                                this.shipsFromPropertyValueItem = sku.getParsedShipsFromPropertyValueItem()
                            }
                        }
                    }
                }
            }
        }
    }

    override fun page(req: ProductAePageQueryReq): PageVo<ProductAePageResp> {
        // 处理时间条件
        if (Objects.equals(YES.code, req.today)) {
            req.createdTimeStart = LocalDateTime.now().with(LocalTime.MIN)
            req.createdTimeEnd = LocalDateTime.now().with(LocalTime.MAX)
        }

        // 1. 查询基础数据（按product_id+shop_id分组）
        val page = aeSaleGoodsRepository.getAePage(Page(req.pageNum.toLong(), req.pageSize.toLong()), req)
        val baseData = page.records

        if (CollectionUtils.isEmpty(baseData)) {
            return PageRespHelper.empty()
        }

        val productIds = baseData.mapNotNull { it.productId }.distinct()
        val saleGoodsIds = baseData.mapNotNull { it.saleGoodsId }.distinct()

        // 批量查询saleGoods数据
        val allSaleGoods = aeSaleGoodsRepository.listByIds(saleGoodsIds)
        // 按product_id和shop_id分组saleGoods
        val groupedSaleGoods = allSaleGoods.groupBy { Pair(it.productId!!, it.shopId) }

        // 批量获取sale商品详情
        val saleGoodsMap = allSaleGoods.associateBy { it.saleGoodsId }

        // 批量获取商品详情
        val products = productRepository.listByIds(productIds)
        val productMap = products.associateBy { it.productId }

        val saleGoodsIdTagMap = if (saleGoodsIds.isNotEmpty()) {
            productTagRepository.getTagMapByAeSaleGoodsIds(saleGoodsIds)
        } else {
            mapOf()
        }

        val productIdTagMap = if (productIds.isNotEmpty()) {
            productTagRepository.getTagMapByProductIds(productIds)
        } else {
            mapOf()
        }

        //  构建结果数据
        val result = baseData.map { baseItem ->
            ProductAePageResp().apply {

                val product = productMap[baseItem.productId!!] ?: Product()
                val saleGoods = saleGoodsMap[baseItem.saleGoodsId!!] ?: AeSaleGoods()
                val saleSkuList = aeSaleSkuRepository.findBySaleGoodsId(baseItem.saleGoodsId!!)

                val saleSkcIdList = aeSaleSkcRepository.findBySaleGoodsId(baseItem.saleGoodsId!!).mapNotNull { it.saleSkcId }

                // 批量获取sale_skc标签
                val saleSkcIdTagMap = if (saleSkcIdList.isNotEmpty()) {
                    productTagRepository.getTagMapByAeSaleSkcIds(saleSkcIdList)
                } else {
                    mapOf()
                }

                val skcTagCodes: List<String> = saleSkcIdTagMap.values
                    .flatMap { ProductTagEnum.getDescListByPage(it) }

                val saleGoodsTagCodes: List<String> = saleGoodsIdTagMap[baseItem.saleGoodsId]
                    ?.let { ProductTagEnum.getDescListByPage(it).toList() }
                    ?: emptyList()

                val productIdTagCodes: List<String> = productIdTagMap[baseItem.productId]
                    ?.let { ProductTagEnum.getDescListByPage(it).toList() }
                    ?: emptyList()

                val tagCodes = (skcTagCodes + saleGoodsTagCodes + productIdTagCodes).distinct()

                // 基础信息
                this.productId = saleGoods.productId
                this.productTitle = saleGoods.productTitle
                this.mainImgUrl = product.mainImgUrl
                this.spuCode = saleGoods.spuCode
                this.supplyMode = product.supplyMode
                this.waves = product.waves
                this.channelId = saleGoods.channelId
                this.platformId = saleGoods.platformId
                this.shopId = saleGoods.shopId
                this.shopName = saleGoods.shopName
                this.categoryId = product.categoryId
                this.categoryCode = saleGoods.categoryCode
                this.categoryName = product.categoryName
                this.update = saleGoods.updateState == YES.code
                this.costPriceUpdateState = saleGoods.costPriceUpdateState
                this.priceException = product.priceException
                this.publishState = saleGoods.publishState
                this.syncState = saleGoods.platformSyncState
                this.tagCodes = tagCodes
                this.frontUrlList = mutableListOf<ProductFrontUrlResp>().apply {
                    // spu+shopId获取所有saleGoods(包括deleted)
                    val allSaleGoods = aeSaleGoodsRepository.getAllAndDeletedBySpuCodeAndShopId(saleGoods.spuCode!!, saleGoods.shopId!!)
                        .filter { it.platformProductId != null }
                    if (allSaleGoods.isNotEmpty()) {
                        allSaleGoods.forEach { s ->
                            this.add(ProductFrontUrlResp().apply {
                                this.url = AliexpressConstants.getFrontUrl(s.platformProductId.toString())
                                this.platformProductId = s.platformProductId?.toString()
                                this.publishState = s.publishState
                                this.createdTime = s.createdTime
                            })
                        }
                    }
                }
                this.firstPublishTime = saleGoods.publishTime
                this.publishUserName = saleGoods.publishUserName
                this.latestPublishTime = saleGoods.latestPublishTime
                this.latestPublishUserName = saleGoods.latestPublishUserName
                this.latestOfflineTime = saleGoods.latestOfflineTime
                this.latestOfflineUserName = saleGoods.latestOfflineUserName
                this.isError = baseItem.isError
                this.errorInfoDtoList = baseItem.errorInfoDtoList

                val productShopPair = Pair(baseItem.productId!!, baseItem.shopId)
                val saleGoodsList = groupedSaleGoods.getOrDefault(productShopPair, emptyList())
                // 从所有的站点中获取最早的创建时间和创建人
                saleGoodsList.filter { it.createdTime != null }
                    .maxByOrNull { it.createdTime!! }
                    ?.let { createdSaleGoods ->
                        this.createdTime = createdSaleGoods.createdTime
                        this.creatorName = createdSaleGoods.creatorName
                        this.creatorId = createdSaleGoods.creatorId
                    }
                this.revisedTime = saleGoods.revisedTime
                this.reviserName = saleGoods.reviserName
                this.reviserId = saleGoods.reviserId
                this.saleGoodsId = saleGoods.saleGoodsId
                this.productTitle = saleGoods.productTitle ?: product.productTitle
                // 库存类型 货期
                this.stockType = saleGoods.stockType
                this.stockTypeName = saleGoods.stockType?.let { StockTypeEnum.getByCode(it) }?.desc
                this.delayDeliveryDays = saleGoods.delayDeliveryDays
                this.salePrice = saleSkuList.filter { it.retailPrice != null && it.retailPrice!! > BigDecimal.ZERO }.minByOrNull { it.retailPrice!! }?.let { it.retailPrice?.toEngineeringString() }
                this.stockQuantity = saleSkuList
                    .filter { it.enableState == YES.code }
                    .sumOf { it.stockQuantity ?: 0L }

                this.imagePackageState = product.imagePackageState
                this.styleType = product.styleType
            }
        }

        return PageRespHelper.of(page.current.toInt(), page.total, result)
    }

    /**
     * AE-统计数量
     */
    override fun getProductCounts(req: ProductAePageQueryReq): ProductAeCountResp {
        // 处理时间条件
        if (Objects.equals(YES.code, req.today)) {
            req.createdTimeStart = LocalDateTime.now().with(LocalTime.MIN)
            req.createdTimeEnd = LocalDateTime.now().with(LocalTime.MAX)
        }

        // 统计数量
        val countDtoList = aeSaleGoodsRepository.getAeCount(req)
        val totalCount = countDtoList.sumOf { it.countNum?.toLong() ?: 0L }

        return ProductAeCountResp().apply {
            this.totalCount = totalCount.toInt()
            this.activeCount = countDtoList.find { it.publishState == ProductAePublishStateEnum.ACTIVE.code }?.countNum ?: 0
            this.inActiveCount = countDtoList.find { it.publishState == ProductAePublishStateEnum.IN_ACTIVE.code }?.countNum ?: 0
            this.auditingCount = countDtoList.find { it.publishState == ProductAePublishStateEnum.AUDITING.code }?.countNum ?: 0
            this.refuseCount = countDtoList.find { it.publishState == ProductAePublishStateEnum.REFUSE.code }?.countNum ?: 0
        }
    }

    /**
     * 更新AE商品
     * @param req
     */
    override fun update(req: ProductAePublishedUpdateReq) {

        req.skcList?.forEach { skc ->
            if (skc.combo == YES.code) {
                if (skc.skuList.isNullOrEmpty()) {
                    throw IllegalArgumentException("组合商品必须包含SKU信息")
                }
                skc.skuList?.forEach { skuInfo ->
                    if (skuInfo.barcodes.isNullOrEmpty()) {
                        throw IllegalArgumentException("组合商品的SKU必须包含条码")
                    }
                    skuInfo.barcodes?.forEach { barcodeInfo ->
                        if (barcodeInfo.barcode.isNullOrBlank()) {
                            throw IllegalArgumentException("组合商品的SKU条码不能为空")
                        }
                        if (barcodeInfo.unit == null) {
                            throw IllegalArgumentException("件数不能为空")
                        }
                    }
                }
            }
        }

        // 验证商品存在性
        val product = productRepository.getById(req.productId!!) ?: throw IllegalArgumentException("商品不存在, productId=${req.productId}")
        val currentUser = CurrentUserHolder.get()
        log.info { "开始更新已上架Lazada商品, productId: ${req.productId}" }
        // 更新商品图库
        savePicture(product)

        // 3. 更新商品属性（如果传入了属性）
        req.attributes?.takeIf { it.isNotEmpty() }?.let { attrs ->
            // 先删除当前平台的属性，再批量保存
            productAttributesRepository.removeByProductIdAndPlatformId(product.productId!!, AE.platformId)
            val attrEntities = attrs.map { attr ->
                ProductAttributes().apply {
                    attributeId = attr.attributeId
                    attributeValueId = attr.attributeValueId
                    productId = product.productId
                    categoryId = product.categoryId
                    platformId = AE.platformId
                }
            }
            productAttributesRepository.saveBatch(attrEntities)
            log.info { "更新商品属性, productId: ${product.productId}" }
        }

        val saleGoods = aeSaleGoodsRepository.getById(req.saleGoodsId!!)

        // 更新SaleGoods信息
        updateSaleGoodsList(req, listOf(saleGoods))
        // 更新SaleSkc和SaleSku信息
        // 手动事务
        TransactionTemplate(transactionManager).execute { status ->
            try {

                productOperateLogRepository.saveProductOperateLog("更新商品信息", PlatformOperatorTypeEnum.UPDATE_PRODUCT, saleGoods)

                // 状态-同步中
                saleGoods.platformSyncState = PlatformSyncStateEnum.PROCESSING.code
                aeSaleGoodsRepository.updateById(saleGoods)

                // 需要enable=0的sku
                val noEnableSkuList = mutableListOf<AeSaleSku>()

                req.skcList
                    ?.forEach { skc ->
                        val noEnableSkuTmpList = updateSaleSkcAndSkuSingleCountry(saleGoods, product, skc)
                        if (CollectionUtils.isNotEmpty(noEnableSkuTmpList)) {
                            noEnableSkuList.addAll(noEnableSkuTmpList)
                        }
                    }

                // 绑定AE条码
                callAeComponent.bindBarcode(product, saleGoods)

                // 更新商品上架时间，上架人
                aeSaleGoodsRepository.updateById(AeSaleGoods().apply {
                    this.saleGoodsId = saleGoods.saleGoodsId
                    this.latestPublishTime = LocalDateTime.now()
                    this.latestPublishUserId = currentUser.id
                    this.latestPublishUserName = currentUser.name
                })

                // 更新AE: 调用updateProduct更新商品状态
                aeUpdateProductComponent.createOrUpdateProduct(saleGoods.shopId!!, aeSaleGoodsRepository.getById(saleGoods.saleGoodsId))

                try {
                    // 修复barcode关系表的skc图片(该图片只有上架后才会更新到sale_skc表)
                    callAeComponent.setBarcodeImage(saleGoods.productId!!, saleGoods.shopId!!)
                } catch (e: Exception) {
                    log.error(e) { "修复barcode关系表的SKC图片异常: ${e.message}" }
                }

//                // 更新需要enable=0的数据
//                if (noEnableSkuList.isNotEmpty()) {
//                    val finalDisableList = noEnableSkuList.map { sku ->
//                        AeSaleSku().apply {
//                            saleSkuId = sku.saleSkuId
//                            enableState = NO.code
//                        }
//                    }
//                    aeSaleSkuRepository.updateBatchById(finalDisableList)
//                    log.info { "批量禁用SKU完成, spuCode: ${saleGoods.spuCode}, saleGoodsId: ${saleGoods.saleGoodsId}, 数量: ${finalDisableList.size}" }
//                }

                // saleGoods更新字段改为0
                val updateSaleGoods = AeSaleGoods().apply {
                    this.saleGoodsId = saleGoods.saleGoodsId
                    this.updateState = NO.code
                    this.platformSyncState = PlatformSyncStateEnum.SUCCESS.code
                    this.publishState = ProductAePublishStateEnum.AUDITING.code
                }
                aeSaleGoodsRepository.updateById(updateSaleGoods)
                null
            } catch (e: Exception) {
                // 记录异常日志
                e.printStackTrace()
                log.error(e) { "事务执行过程中出现异常，触发回滚，spuCode: ${saleGoods.spuCode}, saleGoodsId: ${saleGoods.saleGoodsId}，error: ${e.message}" }
                // 手动标记事务回滚
                status.setRollbackOnly()

                // 状态-同步失败
                val updateSaleGoods = AeSaleGoods().apply {
                    this.saleGoodsId = saleGoods.saleGoodsId
                    this.platformSyncState = PlatformSyncStateEnum.FAILURE.code
                }
                aeSaleGoodsRepository.updateById(updateSaleGoods)
                null
            }
        }
    }

    /**
     * 更新skc和sku-单站点(包含组合商品)
     * sale_skc_id必定有, 组合商品可能没有, 没有则新增, 有则更新
     * sale_sku_id, 没有则新增, 有则更新 (req只传enable=1的数据, 比较本地不存在req的都是enable=0, 但是更新AE逻辑只筛选enable=1的sku, 所以要先更新AE,再把本地需要禁用enable=0)
     */
    private fun updateSaleSkcAndSkuSingleCountry(saleGoods: AeSaleGoods, product: Product, skcReq: ProductAePublishedAeSkcInfoReq): List<AeSaleSku> {
        var saleSkc: AeSaleSkc? = null
        if (skcReq.saleSkcId == null) {
            // 是否组合商品
            if (skcReq.combo == YES.code) {

                if (skcReq.colorCode.isNullOrBlank()) {
                    throw IllegalArgumentException("组合商品必须有颜色")
                }

                // 提取sku下的所有barcode
                val barcodeList = skcReq.skuList?.mapNotNull { it.barcodes }?.flatten()?.mapNotNull { it.barcode }?.distinct()
                require(!(barcodeList.isNullOrEmpty())) { "组合商品必须有条码" }
                val barcodeInfoList = productBarCodeRepository.getListByBarcodes(barcodeList)
                val skcCodeList = barcodeInfoList?.mapNotNull { it.skc }?.distinct()
                val productSkcList = productSkcRepository.listBySkc(skcCodeList!!)
                val comboColor = productSkcList.mapNotNull { it.color?.trim() }.distinct().sorted().joinToString("+")

                // 组合商品, 创建SKC
                saleSkc = AeSaleSkc().apply {
                    this.saleSkcId = IdHelper.getId()
                    this.saleGoodsId = saleGoods.saleGoodsId
                    this.color = comboColor
                    this.colorCode = skcReq.colorCode?.trim()
                    this.platformColor = skcReq.platformColor
                    this.combo = YES.code
                    this.state = YES.code
                }
                aeSaleSkcRepository.save(saleSkc)
            }
        } else {
            saleSkc = aeSaleSkcRepository.getById(skcReq.saleSkcId!!)
            saleSkc.platformColor = skcReq.platformColor
            aeSaleSkcRepository.updateById(saleSkc)
        }

        if (saleSkc == null) {
            throw IllegalArgumentException("SKC不存在")
        }

        // 重算价格 - 使用统一定价服务
        val priceResp = aeSalePricingService.autoCalPriceBySale(saleGoods.saleGoodsId!!, saleGoods.shopId!!)
        if (!priceResp.success) {
            log.error { "updateSaleSkcAndSkuSingleCountry, spuCode: ${saleGoods.spuCode}, saleGoodsId: ${saleGoods.saleGoodsId}, 计算售价失败:${priceResp.errorMessage}" }
        }
        // 创建价格查找器，避免重复filter操作
        val priceLookup = priceResp.createPriceLookup()

        // 需要enable=1的sku
        val noEnableSkuList = mutableListOf<AeSaleSku>()

        val saveSaleSkuList = mutableListOf<AeSaleSku>()
        val updateSaleSkuList = mutableListOf<AeSaleSku>()

        // 更新SKU信息
        skcReq.skuList?.forEach { skuReq ->

            // 判断尺码是否有条码
            val barCode = productBarCodeRepository.getBySpuCodeAndSkcAndSize(saleGoods.spuCode, saleSkc.skc, skuReq.sizeName!!)
            var newBarcode: String? = null
            if (barCode == null && skcReq.combo != YES.code) {
                // 添加条码
                val barcodeReq = BatchCreateBarCodeReq().apply {
                    this.categoryCode = product.categoryCode
                    this.categoryName = product.categoryName
                    this.skcCode = saleSkc.skc
                    this.color = saleSkc.color
                    this.spuCode = product.spuCode
                    this.groupName = product.sizeGroupName
                    this.sourceGroupCode = product.sizeGroupCode
                    this.sizeValues = listOf(skuReq.sizeName!!)
                    this.inspiraImgUrl = product.inspiraImgUrl
                    this.supplyMode = product.supplyMode
                    this.localPrice = saleSkc.localPrice
                    this.designImgUrl = saleSkc.pictures
                    this.mainImgUrl = saleSkc.pictures
                }
                val barcodeResp = barCodeService.createBarcodeByForce(listOf(barcodeReq))
                if (CollectionUtils.isNotEmpty(barcodeResp)) {
                    newBarcode = barcodeResp[0].barcode
                }
            } else {
                newBarcode = barCode?.barcode
            }

            val saleSku = skuReq.saleSkuId?.let { aeSaleSkuRepository.getById(it) }
            if (saleSku == null) {
                // 新增
                val newSaleSku = AeSaleSku().apply {
                    this.saleSkuId = IdHelper.getId()
                    this.saleGoodsId = saleGoods.saleGoodsId
                    this.saleSkcId = saleSkc.saleSkcId
                    this.productId = saleGoods.productId
                    this.productSkcId = saleSkc.productSkcId
                    this.stockQuantity = skuReq.stockQuantity
                    this.sizeName = skuReq.sizeName
                    this.shopName = saleGoods.shopName
                    this.brandId = saleGoods.brandId
                    this.brandName = saleGoods.brandName
                    this.salePrice = skuReq.salePrice ?: skuReq.retailPrice
                    this.retailPrice = skuReq.retailPrice
                    this.lastSalePrice = skuReq.lastSalePrice
                    this.lastRetailPrice = skuReq.lastRetailPrice
                    this.nationalQuoteConfig = skuReq.nationalQuoteConfigList?.toJson()
                    this.purchaseSalePrice = skuReq.purchaseSalePrice
                    this.purchaseRetailPrice = skuReq.purchaseRetailPrice
                    this.regularSalePrice = skuReq.regularSalePrice
                    this.regularRetailPrice = skuReq.regularRetailPrice
                    this.platformCategoryId = saleGoods.platformCategoryId
                    this.platformCategoryName = saleGoods.platformCategoryName
                    this.delayDeliveryDays = saleGoods.delayDeliveryDays
                    this.barcode = newBarcode
                    if (skcReq.combo == YES.code) {
                        if (skuReq.barcodes.isNullOrEmpty()) {
                            throw IllegalArgumentException("组合商品 ${skuReq.sizeName} 必须有条码")
                        }
                        this.barcodes = skuReq.barcodes?.toJson()
                    }
                    this.enableState = YES.code
                    this.publishState = if (skuReq.flagFrontend == YES.code) {
                        ProductPublishStateEnum.ACTIVE.code
                    } else {
                        ProductPublishStateEnum.IN_ACTIVE.code
                    }
                    this.sellerSku = generateSellerSkuComponent.generateSellerSku(product, saleGoods, saleSkc, this)
                    val shipsFromDto = productAttributesAeHelper.processShipsFromAttribute(skuReq.shipsFromPropertyValueItem)
                    if (shipsFromDto != null) {
                        this.shipsFromAttributeId = shipsFromDto.attributeId
                        this.shipsFromAttributeName = shipsFromDto.attributeName
                        this.shipsFromAttributeValueId = shipsFromDto.attributeValueId
                        this.shipsFromAttributeValueName = shipsFromDto.attributeValueName
                    }

                    // 考虑组合商品 skc 为空的情况就不调用applyUnifiedPricingToSku
                    val skcCode = saleSkc.skc ?: skcReq.skc
                    if (skcCode.isNotBlank()) {
                        productPublishAeHelper.applyUnifiedPricingToSku(
                            this,
                            skcCode!!,
                            priceLookup,
                            saleGoods.shopId!!,
                        )
                    }
                }
                saveSaleSkuList.add(newSaleSku)
            } else {
                // 更新
                saleSku.salePrice = skuReq.salePrice ?: skuReq.retailPrice
                saleSku.retailPrice = skuReq.retailPrice
                saleSku.lastSalePrice = skuReq.lastSalePrice
                saleSku.lastRetailPrice = skuReq.lastRetailPrice
                saleSku.nationalQuoteConfig = skuReq.nationalQuoteConfigList?.toJson()
                saleSku.purchaseSalePrice = skuReq.purchaseSalePrice
                saleSku.purchaseRetailPrice = skuReq.purchaseRetailPrice
                saleSku.regularSalePrice = skuReq.regularSalePrice
                saleSku.regularRetailPrice = skuReq.regularRetailPrice
                saleSku.stockQuantity = skuReq.stockQuantity
                saleSku.publishState = if (skuReq.flagFrontend == YES.code) {
                    ProductPublishStateEnum.ACTIVE.code
                } else {
                    ProductPublishStateEnum.IN_ACTIVE.code
                }
                saleSku.enableState = YES.code
                if (skcReq.combo == YES.code) {
                    saleSku.barcodes = skuReq.barcodes?.toJson()
                }
                // 考虑组合商品 skc 为空的情况就不调用了
                skcReq.skc?.let {
                    productPublishAeHelper.applyUnifiedPricingToSku(
                        saleSku,
                        skcCode = it,
                        priceLookup,
                        saleGoods.shopId!!
                    )
                }
                updateSaleSkuList.add(saleSku)
            }
        }
        if (CollectionUtils.isNotEmpty(saveSaleSkuList)) {
            aeSaleSkuRepository.saveBatch(saveSaleSkuList)
        }
        if (CollectionUtils.isNotEmpty(updateSaleSkuList)) {
            aeSaleSkuRepository.updateBatchById(updateSaleSkuList)
        }

        // 不在req的sku, 标记为enable=0
        val existSkuList = aeSaleSkuRepository.list(
            KtQueryWrapper(AeSaleSku::class.java)
                .eq(AeSaleSku::saleGoodsId, saleGoods.saleGoodsId)
                .eq(AeSaleSku::saleSkcId, saleSkc.saleSkcId)
                .eq(AeSaleSku::enableState, YES.code)
        )
        val saveSaleSkuId = (saveSaleSkuList + updateSaleSkuList).map { it.saleSkuId }
        existSkuList
            .filter { existSku -> !saveSaleSkuId.contains(existSku.saleSkuId) }
            .forEach { existSku ->
                // 是否存在于req
                var exist = false
                skcReq.skuList
                    ?.filter { skuReq -> skuReq.saleSkuId != null }
                    ?.forEach { skuReq ->
                        if (Objects.equals(skuReq.saleSkuId, existSku.saleSkuId)
                            && Objects.equals(skuReq.sizeName, existSku.sizeName)
                            && existSku.enableState == YES.code
                        ) {
                            exist = true
                            return@forEach
                        }
                    }
                if (!exist) {
                    // 不存在的标记 改为 删除
                    existSku.publishState = ProductPublishStateEnum.DELETED.code
                    existSku.enableState = NO.code
                    noEnableSkuList.add(existSku)
                }
            }
        if (noEnableSkuList.isNotEmpty()) {
            aeSaleSkuRepository.updateBatchById(noEnableSkuList)
            noEnableSkuList.addAll(noEnableSkuList)
        }
        return noEnableSkuList
    }

    /**
     * 批量下架-弹窗-商品SKU列表查询
     *
     * @param request
     */
    override fun pageOnlineSkuList(request: ProductAeOnlineSkuListReq): PageVo<ProductAeOnlineSkuListResp> {
        log.info { "AE 开始查询在线商品SKU列表, 请求参数: $request" }

        request.validateRequest()

        val page = Page<ProductAeOnlineSkuListResp>(request.pageNum.toLong(), request.pageSize.toLong())
        val resultPage = aeSaleSkuRepository.pageOnlineSkuList(page, request)

        val result = PageRespHelper.of(request.pageNum, resultPage.total, resultPage.records)

        log.info { "AE 查询在线商品SKU列表成功, 总记录数: ${result.total}, 当前页数据量: ${result.list.size}" }
        return result
    }

    /**
     * 批量下架-弹窗-商品SKU列表查询-统计数量
     *
     * @param req
     */
    override fun countOnlineSku(req: ProductAeOfflineSubmitReq): ProductAeOnlineSkuCountResp {
        val saleSkuList = aeSaleSkuRepository.listByIds(req.saleSkuIds)
        if (saleSkuList.isNullOrEmpty()) {
            throw IllegalArgumentException("销售SKU不存在")
        }
        // 暂时是只有勾选一个SKU, 则整个SPU下架
        val saleGoodsIds = saleSkuList.mapNotNull { it.saleGoodsId }.distinct()
        val saleGoodsList = aeSaleGoodsRepository.listByIds(saleGoodsIds)
        if (saleGoodsList.isNullOrEmpty()) {
            throw IllegalArgumentException("销售商品不存在")
        }
        return ProductAeOnlineSkuCountResp().apply {
            this.spuCount = saleGoodsList.mapNotNull { it.spuCode }.distinct().count()
            this.skcCount = 0
        }
    }

    /**
     * 批量下架
     */
    override fun batchOffline(req: ProductAeOfflineSubmitReq) {
        val saleSkuList = aeSaleSkuRepository.listByIds(req.saleSkuIds)
        if (saleSkuList.isNullOrEmpty()) {
            throw IllegalArgumentException("销售SKU不存在")
        }

        // 暂时是只有勾选一个SKU, 则整个SPU下架
        val saleGoodsIds = saleSkuList.mapNotNull { it.saleGoodsId }.distinct()
        aeUpdateProductComponent.batchOffline(saleGoodsIds)
    }

    /**
     * 定时刷新AE商品状态(审核中)
     */
    override fun scheduleRefreshProductStatus() {
        val auditingSpuList = aeSaleGoodsRepository.list(
            KtQueryWrapper(AeSaleGoods::class.java)
                .eq(AeSaleGoods::publishState, ProductAePublishStateEnum.AUDITING.code)
                .isNotNull(AeSaleGoods::platformProductId)
        )
        if (auditingSpuList.isNullOrEmpty()) {
            return
        }
        auditingSpuList.forEach { saleGoods ->
            val shop = shopRepository.getById(saleGoods.shopId!!) ?: throw IllegalArgumentException("店铺不存在")
            val resp = aliexpressService.queryProduct(saleGoods.platformProductId!!, shop.token!!)
            if (resp.isSuccess()) {

                // 把任务的创建人和租户设为任务用户
                val user = CurrentUser(
                    saleGoods.reviserId!!,
                    saleGoods.reviserName!!,
                    "",
                    saleGoods.tenantId!!,
                    false
                )

                val productDetail = resp.result
                val productStatus = ProductAePublishStateEnum.getByPlatformStateValue(productDetail!!.productStatusType!!)
                when (productStatus) {
                    ProductAePublishStateEnum.ACTIVE -> {
                        withUser(user) {
                            // 审核通过
                            saleGoods.publishState = ProductAePublishStateEnum.ACTIVE.code
                            aeSaleGoodsRepository.updateById(saleGoods)

                            // SKU也要改成上架
                            val saleSkuList = aeSaleSkuRepository.findBySaleGoodsId(saleGoods.saleGoodsId!!)
                            if (saleSkuList.isNotEmpty()) {
                                saleSkuList.forEach { saleSku ->
                                    saleSku.publishState = ProductPublishStateEnum.ACTIVE.code
                                }
                                aeSaleSkuRepository.updateBatchById(saleSkuList)
                            }
                        }
                    }

                    ProductAePublishStateEnum.REFUSE -> {
                        withUser(user) {
                            // 审核不通过
                            saleGoods.publishState = ProductAePublishStateEnum.REFUSE.code
                            aeSaleGoodsRepository.updateById(saleGoods)
                        }
                    }

                    else -> {
                        return@forEach
                    }
                }
            }
        }
    }

    override fun queryFreightTemplateList(shopId: Long, channelSellerId: String?): List<ProductAeFreightTemplateResp> {
        return aliexpressServiceHelper.getCachedFreightTemplateList(shopId, channelSellerId)
    }

    override fun queryPromiseTemplates(shopId: Long, templateId: Long): List<ProductAePromiseTemplateResp> {
        return aliexpressServiceHelper.getCachedPromiseTemplates(shopId, templateId)
    }

    override fun queryMsrList(shopId: Long, channelSellerId: Long?, channel: String?): List<ProductAeMsrItemResp> {
        return aliexpressServiceHelper.getCachedMsrList(shopId, channelSellerId, channel)
    }

    override fun queryManufactureList(shopId: Long, channelSellerId: Long?, channel: String?): List<ProductAeManufactureItemResp> {
        return aliexpressServiceHelper.getCachedManufactureList(shopId, channelSellerId, channel)
    }

    override fun queryProductGroups(shopId: Long): List<ProductAeProductGroupResp> {
        return aliexpressServiceHelper.getCachedProductGroups(shopId)
    }

    override fun getOriginAttribute(req: ProductAeAttributeQueryReq): ProductAeAttributeResp {
        log.info { "查询产地属性列表（旧接口），请求参数: $req" }

        // 将旧请求转换为新的通用请求格式
        val newReq = ProductAeAttributeQueryReq(
            productId = req.productId,
            shopId = req.shopId,
            attrValueId = req.attrValueId,
            attributeType = ProductAeAttributeTypeEnum.ORIGIN
        )

        // 调用新接口
        return getAttributes(newReq)
    }

    override fun getAttributes(req: ProductAeAttributeQueryReq): ProductAeAttributeResp {
        log.info { "查询商品属性，请求参数: $req" }
        val attributeType = req.attributeType ?: throw BusinessException("attributeType 不能为空")

        // 根据属性类型获取属性ID
        val attributeId = attributeType.getPropertyId(aliexpressProperties)
        log.info { "使用属性ID: $attributeId, 属性类型: ${req.attributeType}" }

        // 获取类目ID（优先使用传入的categoryId）
        val categoryId = req.categoryId ?: run {
            val product = req.productId?.let { productRepository.getById(it) }
            product?.categoryId
        }

        // 获取平台类目ID映射
        val categoryMapping = categoryId?.let {
            publishCategoryMappingRepository.getByPublishCategoryId(it, AE.platformId, ALIBABA.channelId)
        }
        val platformCategoryId = categoryMapping?.platformCategoryId?.toLong()
            ?: aliexpressProperties.aePlatform.defaultCategoryId

        // 构建param2参数（如果attrValueId不为空）
        val param2 = req.attrValueId?.let { "$attributeId=$it" }
        log.info { "构建的param2参数: ${param2 ?: "无"}" }

        // 获取店铺及token
        val shop = req.shopId?.let { shopRepository.getById(it) } ?: shopRepository.getById(aliexpressProperties.aePlatform.tokenShopId)!!

        try {
            // 查询类目属性
            val categoryAttributes = aliexpressServiceHelper.getCachedCategoryAttributes(
                shopId = shop.shopId,
                categoryId = platformCategoryId,
                param2 = param2
            )

            // 检查结果是否成功
            if (!categoryAttributes.isSuccess() || categoryAttributes.result?.attributes.isNullOrEmpty()) {
                log.info { "未找到属性数据或API调用失败，属性ID: $attributeId" }
                return attributeType.createEmptyResponse(attributeId)
            }

            val attribute = if (req.attrValueId.isNull()) {
                // 查找指定属性
                categoryAttributes.result?.attributes?.find { it.id == attributeId }
            } else {
                // 查找子属性
                categoryAttributes.result?.attributes?.firstOrNull()
            }

            // 如果没有找到属性或者没有属性值，返回空结果
            if (attribute == null || attribute.values.isNullOrEmpty()) {
                log.info { "未找到属性(ID:$attributeId)或其属性值为空" }
                return attributeType.createEmptyResponse(attributeId)
            }

            // 转换属性值为响应对象
            val attributeValues = attribute.values.map { value ->
                ProductAeAttributeValueResp(
                    id = value.id,
                    name = value.getName(),
                    chineseName = value.getChineseName(),
                    hasSubAttr = value.hasSubAttr
                )
            }.also {
                log.info { "查询到${it.size}个属性值，属性类型: ${req.attributeType}" }
            }

            // 构建完整的响应对象
            return ProductAeAttributeResp(
                attributeId = attribute.id,
                attributeName = attribute.getName(),
                attributeChineseName = attribute.getChineseName(),
                attributeType = req.attributeType!!,
                values = attributeValues
            )
        } catch (e: Exception) {
            log.error(e) { "查询属性异常，属性类型: ${req.attributeType}, 错误: ${e.message}" }
            throw BusinessException("查询属性异常: ${e.message}", e)
        }
    }

    override fun getAllAttributes(req: ProductAeCategoryAttributesQueryReq): AliexpressCategoryAttributeResponse {
        return aliexpressServiceHelper.getCachedCategoryAttributes(
            shopId = req.shopId,
            categoryId = req.categoryId,
            param2 = req.param2
        )
    }
    @Transactional(rollbackFor = [Exception::class])
    override fun updateProductGrossMargin(req: UpdateAEProductGrossMarginReq){
        var aeSaleGoodsList = aeSaleGoodsRepository.listByIds(req.aeSaleGoodsIds)
        Assert.isNotNull(aeSaleGoodsList, "商品不存在")
        //只处理V2版本的商品
        aeSaleGoodsList = aeSaleGoodsList.filter { it.priceCalculateRule==PriceCalculateRuleEnum.V2.code }
        Assert.isNotNull(aeSaleGoodsList, "商品的计价规则不是V2版本")
        //更新毛利率之前先清一下缓存
        regionPriceRuleService.invalidateAllCache()

        val updateAeSaleGoodsList = mutableListOf<AeSaleGoods>()
        aeSaleGoodsList.forEach {
            //有变化才去更新
            if(it.grossMargin==null || it.grossMargin!!.compareTo(req.grossMargin)!=0){
                it.grossMargin = req.grossMargin
                updateAeSaleGoodsList.add(it)
            }
        }
        if(updateAeSaleGoodsList.isNotEmpty()){
            aeSaleGoodsRepository.updateBatchById(updateAeSaleGoodsList)
            //记录标识
            updateAeSaleGoodsList.forEach {saleGoods->
                productTagRepository.addTagByAeSaleGoodsId(
                    saleGoods.saleGoodsId!!,
                    ProductTagEnum.TAG_POP_GROSS_MARGIN.code,
                    TagPopGrossMarginEnum.MODIFY_GROSS_MARGIN.code
                )
                //同步毛利率到商品列表
                var req = UpdateProductGrossMarginByShopReq(mutableListOf(saleGoods.productId!!),saleGoods.grossMargin!!,mutableListOf(saleGoods.shopId!!))
                productManageService.updateProductGrossMarginByShop(req)

                //重新计算AE商品的划线价
                val priceResp = aeSalePricingService.autoCalPriceBySale(saleGoods.saleGoodsId!!, saleGoods.shopId!!)
                if (!priceResp.success) {
                    log.error { "confirmUpdateSupplyPriceUpdate, spuCode: ${saleGoods.spuCode}, saleGoodsId: ${saleGoods.saleGoodsId}, 计算售价失败:${priceResp.errorMessage}" }
                }
                // 创建价格查找器，避免重复filter操作
                val priceLookup = priceResp.createPriceLookup()

                val newSkcList = aeSaleSkcRepository.findBySaleGoodsId(saleGoods.saleGoodsId!!)
                if (newSkcList.isNotEmpty()) {
                    newSkcList.forEach { skc ->
                        val skuList = aeSaleSkuRepository.findBySaleSkcId(skc.saleSkcId!!)
                        if (skuList.isNotEmpty()) {
                            skuList.forEach {sku->
                                val skcCode = skc.skc
                                if (skcCode.isNotBlank()) {
                                    productPublishAeHelper.applyUnifiedPricingToSku(sku, skcCode!!, priceLookup, saleGoods.shopId!!, coverOriginalPrice = true)
                                    aeSaleSkuRepository.updateById(sku)
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * 获取图片分类集合
     * @param spuCode
     */
    private fun getImages(spuCode: String): ImageCollectionDTO {
        val imageRepository = imageRepositoryRepository.getBySpuCode(spuCode)
        return if (imageRepository != null) {
            imageCollectionHelper.buildImageCollection(spuCode, imageRepository)
        } else {
            ImageCollectionDTO()
        }
    }

    /**
     * 获取映射的平台品类
     *
     * @param product
     * @param platformId
     * @return
     */
    private fun getPlatformCategory(product: Product, platformId: Long): PublishCategoryMapping? {
        if (product.categoryId != null) {
            val categoryMapping = publishCategoryMappingRepository.getByPublishCategoryIdAndPlatformId(product.categoryId!!, platformId)
            if (categoryMapping != null) {
                return categoryMapping
            }
        }
        return null
    }

    private fun savePicture(product: Product) {
        val imageRepository = imageRepositoryRepository.ktQuery()
            .eq(ImageRepository::spuCode, product.spuCode)
            .one()

        if (imageRepository == null) {
            return
        }

        val picture = productPictureRepository.getOneByProductId(product.productId!!)
        if (Objects.isNull(picture)) {
            val productPicture = ProductPicture().apply {
                this.productId = product.productId
                this.spuCode = product.spuCode
                this.sourceImageUrl = imageRepository.mainUrl
            }
            productPictureRepository.save(productPicture)
        }
    }

    /**
     * 更新SaleGoods列表信息
     * @param req 更新请求
     * @param saleGoodsList 销售商品列表
     */
    private fun updateSaleGoodsList(req: ProductAePublishedUpdateReq, saleGoodsList: List<AeSaleGoods>) {
        saleGoodsList.forEach { saleGoods ->
            var update = false
            val updateEntity = AeSaleGoods().apply {
                this.saleGoodsId = saleGoods.saleGoodsId
            }
            // 更新标题
            if (req.productTitle?.isNotBlank() == true && ObjectUtils.notEqual(req.productTitle, saleGoods.productTitle)) {
                updateEntity.productTitle = req.productTitle
                saleGoods.productTitle = req.productTitle
                update = true
            }
            // 更新品牌名
            if (req.brandName?.isNotBlank() == true && ObjectUtils.notEqual(req.brandName, saleGoods.brandName)) {
                updateEntity.brandName = req.brandName
                saleGoods.brandName = req.brandName
                update = true
            }
            // 更新包裹重量
            if (req.packageDimensionsLength?.isNotBlank() == true && ObjectUtils.notEqual(req.packageDimensionsLength, saleGoods.packageDimensionsLength)) {
                updateEntity.packageDimensionsLength = req.packageDimensionsLength
                saleGoods.packageDimensionsLength = req.packageDimensionsLength
                update = true
            }
            if (req.packageDimensionsWidth?.isNotBlank() == true && ObjectUtils.notEqual(req.packageDimensionsWidth, saleGoods.packageDimensionsWidth)) {
                updateEntity.packageDimensionsWidth = req.packageDimensionsWidth
                saleGoods.packageDimensionsWidth = req.packageDimensionsWidth
                update = true
            }
            if (req.packageDimensionsHeight?.isNotBlank() == true && ObjectUtils.notEqual(req.packageDimensionsHeight, saleGoods.packageDimensionsHeight)) {
                updateEntity.packageDimensionsHeight = req.packageDimensionsHeight
                saleGoods.packageDimensionsHeight = req.packageDimensionsHeight
                update = true
            }
            if (req.packageWeight?.isNotBlank() == true && ObjectUtils.notEqual(req.packageWeight, saleGoods.packageWeight)) {
                updateEntity.packageWeight = req.packageWeight
                saleGoods.packageWeight = req.packageWeight
                update = true
            }
            if (req.taxType?.isNotNull() == true && ObjectUtils.notEqual(req.taxType, saleGoods.taxType)) {
                updateEntity.taxType = req.taxType
                saleGoods.taxType = req.taxType
                update = true
            }
            if (req.invDeduction != null && ObjectUtils.notEqual(req.invDeduction, saleGoods.invDeduction)) {
                updateEntity.invDeduction = req.invDeduction
                saleGoods.invDeduction = req.invDeduction
                update = true
            }
            if (req.originPlaceName != null && ObjectUtils.notEqual(req.originPlaceName, saleGoods.originPlaceName)) {
                updateEntity.originPlaceName = req.originPlaceName
                saleGoods.originPlaceName = req.originPlaceName
                update = true
            }
            if (req.promiseTemplateId.isNotNull() && ObjectUtils.notEqual(req.promiseTemplateId, saleGoods.promiseTemplateId)) {
                updateEntity.promiseTemplateId = req.promiseTemplateId
                updateEntity.promiseTemplateName = req.promiseTemplateName
                saleGoods.promiseTemplateId = req.promiseTemplateId
                saleGoods.promiseTemplateName = req.promiseTemplateName
                update = true
            }
            if (req.msrId.isNotNull() && ObjectUtils.notEqual(req.msrId, saleGoods.msrId)) {
                updateEntity.msrId = req.msrId
                updateEntity.msrName = req.msrName
                saleGoods.msrId = req.msrId
                saleGoods.msrName = req.msrName
                update = true
            }
            if (req.freightTemplateId.isNotNull() && ObjectUtils.notEqual(req.freightTemplateId, saleGoods.freightTemplateId)) {
                updateEntity.freightTemplateId = req.freightTemplateId
                updateEntity.freightTemplateName = req.freightTemplateName
                saleGoods.freightTemplateId = req.freightTemplateId
                saleGoods.freightTemplateName = req.freightTemplateName
                update = true
            }
            if (req.manufactureId.isNotNull() && ObjectUtils.notEqual(req.manufactureId, saleGoods.manufactureId)) {
                updateEntity.manufactureId = req.manufactureId
                updateEntity.manufactureName = req.manufactureName
                saleGoods.manufactureId = req.manufactureId
                saleGoods.manufactureName = req.manufactureName
                update = true
            }
            if (req.productGroups.isNotEmpty() && ObjectUtils.notEqual(req.productGroups, saleGoods.getProductGroupList())) {
                updateEntity.setProductGroupList(req.productGroups)
                saleGoods.setProductGroupList(req.productGroups)
                update = true
            }
            if (req.hsCode.isNotBlank() && ObjectUtils.notEqual(req.hsCode, saleGoods.hsCode)) {
                updateEntity.hsCode = req.hsCode
                saleGoods.hsCode = req.hsCode
                update = true
            }
            if (req.hsPvList.isNotEmpty() && ObjectUtils.notEqual(req.hsPvList, saleGoods.getProductGroupList())) {
                updateEntity.setParsedHsPvList(req.hsPvList)
                saleGoods.setParsedHsPvList(req.hsPvList)
                update = true
            }
            if (req.hsExtendInfo.isNotBlank() && ObjectUtils.notEqual(req.hsExtendInfo, saleGoods.hsExtendInfo)) {
                updateEntity.hsExtendInfo = req.hsExtendInfo
                saleGoods.hsExtendInfo = req.hsExtendInfo
                update = true
            }
            val originPropertyValueItems = productAttributesAeHelper.processOriginAttributes(req.originPropertyValueItems)
            if (originPropertyValueItems.isNotEmpty() && ObjectUtils.notEqual(originPropertyValueItems, saleGoods.getParsedOriginPropertyValueItems())) {
                updateEntity.setParsedOriginPropertyValueItems(originPropertyValueItems)
                saleGoods.setParsedOriginPropertyValueItems(originPropertyValueItems)
                update = true
            }
            if (update) {
                aeSaleGoodsRepository.updateById(updateEntity)
                log.info { "更新saleGoods信息, saleGoodsId: ${saleGoods.saleGoodsId}" }
            }
        }
    }

    /**
     * 弹窗查询-编辑库存/价格
     *
     * @param req
     */
    override fun querySkuByUpdateStockPrice(req: AeQuerySkuByUpdateStockPriceReq): List<AeQuerySkuByUpdateStockPriceResp> {
        // 提取sale_goods
        val saleGoods = aeSaleGoodsRepository.getById(req.saleGoodsId) ?: throw IllegalArgumentException("销售商品不存在")

        val saleSkuList = aeSaleSkuRepository.querySkuByUpdateStockPrice(listOf(saleGoods.saleGoodsId!!))
        if (CollectionUtils.isEmpty(saleSkuList)) {
            throw IllegalArgumentException("销售SKU不存在")
        }
        return saleSkuList.map {
            AeQuerySkuByUpdateStockPriceResp().apply {
                this.productSkcId = it.productSkcId
                this.skc = it.skc
                this.color = it.color
                this.combo = it.combo
                this.barcode = it.barcode
                this.saleSkuId = it.saleSkuId
                this.sizeName = it.sizeName
                this.quantity = it.stockQuantity
                this.salePrice = it.salePrice
                this.retailPrice = it.retailPrice
                this.lastSalePrice = it.lastSalePrice
                this.lastRetailPrice = it.lastRetailPrice
            }
        }
    }

    /**
     * 编辑库存/价格
     *
     * @param req
     */
    override fun updateSkuByUpdateStockPrice(req: List<UpdateSkuByUpdateStockPriceReq>) {
        if (req.isEmpty()) {
            throw IllegalArgumentException("销售SKU ID不能为空")
        }
        val saleSkuIds = req.map { it.saleSkuId!! }
        if (saleSkuIds.isEmpty()) {
            throw IllegalArgumentException("销售SKU ID不能为空")
        }
        val saleSkuList = aeSaleSkuRepository.listByIds(saleSkuIds)
        if (saleSkuList.isEmpty()) {
            throw IllegalArgumentException("销售SKU不存在")
        }
        val updateSaleSkuList = req.map {
            AeSaleSku().apply {
                this.saleSkuId = it.saleSkuId
                this.stockQuantity = it.quantity
                this.salePrice = it.salePrice
                this.retailPrice = it.retailPrice
                this.lastSalePrice = it.lastSalePrice
                this.lastRetailPrice = it.lastRetailPrice
            }
        }
        aeSaleSkuRepository.updateBatchById(updateSaleSkuList)

        // 组装MQ数据, 更新Lazada库存/价格
        val lazadaDataList: MutableList<LazadaPriceMqDto> = mutableListOf()

        val saleGoodsIds = saleSkuList.mapNotNull { it.saleGoodsId }
        val saleGoodsList = aeSaleGoodsRepository.listByIds(saleGoodsIds)
        // 找出审核中的SPU
        val auditSpuCodeList = saleGoodsList.filter { it.publishState == ProductAePublishStateEnum.AUDITING.code }.mapNotNull { it.spuCode }
        if (auditSpuCodeList.isNotEmpty()) {
            throw IllegalArgumentException("${auditSpuCodeList.toJson()} 为审核中状态, 不允许更新")
        }
        saleGoodsList.forEach { saleGoods ->
            val shop = shopRepository.getById(saleGoods.shopId!!)
            saleSkuList
                .filter { sku -> sku.saleGoodsId == saleGoods.saleGoodsId }
                .forEach { sku ->
                    val dto = LazadaPriceMqDto().apply {
                        this.platformId = saleGoods.platformId
                        this.shopId = saleGoods.shopId
                        this.shopName = saleGoods.shopName
                        this.productId = saleGoods.productId
                        this.saleProductId = saleGoods.saleGoodsId
                        this.saleSkuId = sku.saleSkuId
                        this.lazadaShopToken = shop.token
                        this.lazadaItemId = saleGoods.platformProductId?.toString()
                        this.lazadaSkuId = sku.platformSkuId
                        this.lazadaSellerSku = sku.sellerSku
                        this.lazadaPrice = sku.retailPrice?.toString()
                        this.lazadaSalePrice = sku.salePrice?.toString()
                        this.lazadaStockQuantity = sku.stockQuantity?.toString()
                    }
                    lazadaDataList.add(dto)
                }
        }
        if (lazadaDataList.isNotEmpty()) {
            // 更新AE 库存/价格
            // 遍历这次更新的所有spu
            saleGoodsList.forEach { saleGoods ->
                val shop = shopRepository.getById(saleGoods.shopId!!)
                // 批量更新价格
                aeUpdateProductComponent.updatePrice(saleGoods, shop)
                // 批量更新库存
                aeUpdateProductComponent.updateStock(saleGoods, shop)
            }
        }
    }

    override fun invalidateAllShopCache(shopId: Long) {
        aliexpressServiceHelper.invalidateAllShopCache(shopId)
    }

    override fun invalidateAllCache() {
        aliexpressServiceHelper.invalidateAllCache()
    }

    /**
     * 批量同步其他店铺
     */
    override fun syncOtherShop(req: AeCloneProductToOtherShopReq) {
        val saleGoodsList = aeSaleGoodsRepository.listByIds(req.saleGoodsIds!!)
        if (CollectionUtils.isEmpty(saleGoodsList)) {
            throw BaseBizException("商品不存在")
        }
        val shop = shopRepository.getById(req.shopId!!) ?: throw BaseBizException("店铺不存在")
        // 判断店铺是否和平台一致
        if (shop.platformId != AE.platformId) {
            throw BaseBizException("店铺不属于[${AE.platformName}]平台")
        }

        // 找出审核中的SPU
        val auditSpuCodeList = saleGoodsList.filter { it.publishState == ProductAePublishStateEnum.AUDITING.code }.mapNotNull { it.spuCode }
        if (auditSpuCodeList.isNotEmpty()) {
            throw IllegalArgumentException("${auditSpuCodeList.toJson()} 为审核中状态, 不允许更新")
        }

        val user = CurrentUserHolder.get()

        val saleGoodsItemMap = req.saleGoodsItems.associateBy { it.saleGoodsId }
        // 复制到指定的店铺和站点
        saleGoodsList.forEach { saleGoods ->

            val product = productRepository.getById(saleGoods.productId!!)
            val saleGoodsItem = saleGoodsItemMap[saleGoods.saleGoodsId!!]

            // 判断是否存在, 存在则不复制
            val existSaleGoodsList = aeSaleGoodsRepository.listBySpuCodeAndShopId(saleGoods.spuCode!!, req.shopId)
            if (CollectionUtils.isNotEmpty(existSaleGoodsList)) {
                return@forEach
            }

            // 手动事务
            TransactionTemplate(transactionManager).execute { status ->
                try {
                    val now = LocalDateTime.now()
                    // 复制saleGoods
                    val newSaleGoods = saleGoods.copy(
                        saleGoodsId = IdHelper.getId(),
                        shopId = req.shopId,
                        shopName = shop.shopName,
                        publishUserId = user.id,
                        publishUserName = user.name,
                        publishTime = now,
                        latestPublishUserId = user.id,
                        latestPublishUserName = user.name,
                        latestPublishTime = now,
                        promiseTemplateId = req.promiseTemplateId,
                        promiseTemplateName = req.promiseTemplateName,
                        freightTemplateId = req.freightTemplateId,
                        freightTemplateName = req.freightTemplateName,
                        msrId = req.msrId,
                        msrName = req.msrName,
                        manufactureId = req.manufactureId,
                        manufactureName = req.manufactureName,
                        productGroups = req.productGroups?.toJson(),
                        latestOfflineTime = null,
                        latestOfflineUserId = null,
                        latestOfflineUserName = null,
                        platformProductId = null,
                        productTitle = saleGoodsItem?.generatedProductTitle.takeIf { it.isNotBlank() } ?: saleGoods.productTitle,
                        priceCalculateRule = PriceCalculateRuleEnum.V2.code
                    )
                    BaseEntity.reset(newSaleGoods)
                    newSaleGoods.createdTime = now
                    newSaleGoods.revisedTime = now
                    aeSaleGoodsRepository.save(newSaleGoods)

                    // 复制saleSkc(非组合商品) -> 批量保存
                    val newSaleSkcList = mutableListOf<AeSaleSkc>()
                    // 复制saleSku -> 批量保存
                    val newSaleSkuList = mutableListOf<AeSaleSku>()
                    aeSaleSkcRepository.findBySaleGoodsId(saleGoods.saleGoodsId!!)
                        .filter { it.combo == NO.code }
                        .forEach { saleSkc ->
                            val newSaleSkc = saleSkc.copy(
                                saleSkcId = IdHelper.getId(),
                                saleGoodsId = newSaleGoods.saleGoodsId,
                            )
                            BaseEntity.reset(newSaleSkc)
                            newSaleSkcList.add(newSaleSkc)

                            aeSaleSkuRepository.findBySaleSkcId(saleSkc.saleSkcId!!).forEach { saleSku ->
                                val newSaleSku = saleSku.copy(
                                    saleSkuId = IdHelper.getId(),
                                    saleSkcId = newSaleSkc.saleSkcId,
                                    saleGoodsId = newSaleGoods.saleGoodsId,
                                    shopSku = null,
                                    platformSkuId = null,
                                    salePrice = null,
                                    retailPrice = null,
                                    purchaseSalePrice = null,
                                    purchaseRetailPrice = null,
                                    regularSalePrice = null,
                                    regularRetailPrice = null,
                                    nationalQuoteConfig = null,
                                )
                                BaseEntity.reset(newSaleSku)
                                newSaleSkuList.add(newSaleSku)
                            }
                        }

                    if (newSaleSkcList.isNotEmpty()) {
                        aeSaleSkcRepository.saveBatch(newSaleSkcList)
                    }

                    if (newSaleSkuList.isNotEmpty()) {
                        aeSaleSkuRepository.saveBatch(newSaleSkuList)
                    }

                    // 绑定Lazada条码
                    callAeComponent.bindBarcode(product, saleGoods)

                    // 重算价格 - 使用统一定价服务（在目标店铺、目标saleGoods上）
                    val priceResp = aeSalePricingService.autoCalPriceBySale(newSaleGoods.saleGoodsId!!, newSaleGoods.shopId!!)
                    if (!priceResp.success) {
                        log.error { "syncOtherShop, spuCode: ${saleGoods.spuCode}, newSaleGoodsId: ${newSaleGoods.saleGoodsId}, 计算售价失败:${priceResp.errorMessage}" }
                    } else if (newSaleSkuList.isNotEmpty()) {
                        val priceLookup = priceResp.createPriceLookup()

                        val priceUpdateEntities = newSaleSkuList.map { sku ->
                            val skcCode = newSaleSkcList.firstOrNull { it.saleSkcId == sku.saleSkcId }?.skc
                            if (skcCode.isNotBlank()) {
                                productPublishAeHelper.applyUnifiedPricingToSku(
                                    saleSku = sku,
                                    skcCode = skcCode!!,
                                    priceLookup = priceLookup,
                                    targetShopId = newSaleGoods.shopId!!,
                                    coverOriginalPrice = true,
                                    coverDestinationCountries = true,
                                )
                            }
                            AeSaleSku().apply {
                                this.saleSkuId = sku.saleSkuId
                                this.salePrice = sku.salePrice
                                this.retailPrice = sku.retailPrice
                                this.purchaseSalePrice = sku.purchaseSalePrice
                                this.purchaseRetailPrice = sku.purchaseRetailPrice
                                this.nationalQuoteConfig = sku.nationalQuoteConfig
                                this.regularSalePrice = sku.regularSalePrice
                                this.regularRetailPrice = sku.regularRetailPrice
                            }
                        }
                        aeSaleSkuRepository.updateBatchById(priceUpdateEntities)
                    }

                    // 判断是否有更新, 有则复制更新
                    val productSourceDataList = productSourceDataRepository.list(
                        KtQueryWrapper(ProductSourceData::class.java)
                            .eq(ProductSourceData::productId, saleGoods.productId)
                            .eq(ProductSourceData::shopId, saleGoods.shopId)
                            .eq(ProductSourceData::processState, NO.code)
                    )
                    if (productSourceDataList.isNotEmpty()) {
                        val newData = productSourceDataList.map { productSourceData ->
                            val newProductSourceData = productSourceData.copy(
                                productSourceDataId = IdHelper.getId(),
                                shopId = newSaleGoods.shopId,
                            )
                            BaseEntity.reset(newProductSourceData)
                            newProductSourceData
                        }
                        if (CollectionUtils.isNotEmpty(newData)) {
                            productSourceDataRepository.saveBatch(newData)
                        }
                    }
                    // 创建/更新 Ae商品
                    aeUpdateProductComponent.createOrUpdateProduct(newSaleGoods.shopId!!, newSaleGoods)

                    productOperateLogRepository.saveProductOperateLog("同步到其他店铺[来源:${saleGoods.saleGoodsId}]", PlatformOperatorTypeEnum.SYNC_TO_OTHER_SHOP, newSaleGoods)

                    null
                } catch (e: Exception) {
                    log.error(e) { "事务执行过程中出现异常，触发回滚 ${e.message}" }

                    if (e !is PublishGlobalBizException) {
                        runAsync(asyncExecutor) {
                            productSyncLogRepository.addErrorSyncLog(
                                product, AE.platformName, shop.shopName, e.getRootMessage(),
                                PlatformOperatorTypeEnum.ACTIVE.code
                            )
                        }
                    }

                    // 手动标记事务回滚
                    status.setRollbackOnly()
                    null
                }
            }

        }
    }

    /**
     * 批量修改库存
     */
    override fun batchStockQuantity(req: AeBatchUpdateQuantityReq) {
        if (req.saleGoodsIds.isNullOrEmpty()) {
            throw BaseBizException("销售商品不能为空")
        }
        val saleGoodsList = aeSaleGoodsRepository.listByIds(req.saleGoodsIds!!)
        if (CollectionUtils.isEmpty(saleGoodsList)) {
            throw BaseBizException("销售商品不存在")
        }
        val saleSkuList = aeSaleSkuRepository.findBySaleGoodsIds(saleGoodsList.mapNotNull { it.saleGoodsId })
        if (CollectionUtils.isEmpty(saleSkuList)) {
            throw BaseBizException("销售SKU不存在")
        }

        // 找出审核中的SPU
        val auditSpuCodeList = saleGoodsList.filter { it.publishState == ProductAePublishStateEnum.AUDITING.code }.mapNotNull { it.spuCode }
        if (auditSpuCodeList.isNotEmpty()) {
            throw IllegalArgumentException("${auditSpuCodeList.toJson()} 为审核中状态, 不允许更新")
        }

        val updateSaleSkuList = saleSkuList.map { saleSku ->
            AeSaleSku().apply {
                this.saleSkuId = saleSku.saleSkuId
                this.stockQuantity = req.stockQuantity
            }
        }
        aeSaleSkuRepository.updateBatchById(updateSaleSkuList)

        // 调用更新AE库存
        saleGoodsList.forEach { saleGoods ->
            val shop = shopRepository.getById(saleGoods.shopId!!)
            // 批量更新库存
            aeUpdateProductComponent.updateStock(saleGoods, shop)
        }
    }

    /**
     * 批量修改分组/模板等
     */
    override fun batchUpdateAeInfo(req: AeBatchUpdateAeInfoReq) {
        if (req.saleGoodsIds.isNullOrEmpty()) {
            throw BaseBizException("销售商品不能为空")
        }
        val saleGoodsList = aeSaleGoodsRepository.listByIds(req.saleGoodsIds!!)
        if (CollectionUtils.isEmpty(saleGoodsList)) {
            throw BaseBizException("销售商品不存在")
        }

        // 找出非审核中的saleGoods
        val noAuditSaleGoodsList = saleGoodsList.filter { it.publishState != ProductAePublishStateEnum.AUDITING.code }
        if (noAuditSaleGoodsList.isEmpty()) {
            throw IllegalArgumentException("无可执行数据, 请确认SPU状态是否非审核中")
        }

        val errorInfoList = mutableMapOf<String, String?>()

        val originPropertyValueItems = req.originPropertyValueItems?.let { productAttributesAeHelper.processOriginAttributes(it) }
        val updateSaleGoodsList = noAuditSaleGoodsList.mapNotNull { saleGoods ->
            var isUpdate = false
            // SPU级别的更新
            val updateSale = AeSaleGoods().apply {
                this.saleGoodsId = saleGoods.saleGoodsId
                if (req.promiseTemplateId.isNotNull() && req.promiseTemplateName.isNotBlank()) {
                    this.promiseTemplateId = req.promiseTemplateId
                    this.promiseTemplateName = req.promiseTemplateName
                    isUpdate = true
                }
                if (req.freightTemplateId.isNotNull() && req.freightTemplateName.isNotBlank()) {
                    this.freightTemplateId = req.freightTemplateId
                    this.freightTemplateName = req.freightTemplateName
                    isUpdate = true
                }
                if (req.msrId.isNotNull() && req.msrName.isNotBlank()) {
                    this.msrId = req.msrId
                    this.msrName = req.msrName
                    isUpdate = true
                }
                if (req.manufactureId.isNotNull() && req.manufactureName.isNotBlank()) {
                    this.manufactureId = req.manufactureId
                    this.manufactureName = req.manufactureName
                    isUpdate = true
                }
                if (req.productGroups.isNotEmpty()) {
                    this.productGroups = req.productGroups!!.toJson()
                    isUpdate = true
                }
                if (originPropertyValueItems.isNotEmpty()) {
                    this.setParsedOriginPropertyValueItems(originPropertyValueItems)
                    isUpdate = true
                }
            }

            // SKU级别的更新(发货地)
            if (req.shipsFromPropertyValueItems.isNotEmpty()) {

                // 解析入参发货地(新)
                val shipsFromPropertyValueItems = req.shipsFromPropertyValueItems?.let { it.mapNotNull { it -> productAttributesAeHelper.processShipsFromAttribute(it) } }

                // 处理SKU发货地
                val saleSkuList = aeSaleSkuRepository.findBySaleGoodsId(saleGoods.saleGoodsId!!)
                if (saleSkuList.isNotNull() && shipsFromPropertyValueItems.isNotEmpty()) {
                    // SKU不为空 且 入参发货地不为空

                    // 提取AE发货地
                    val allShipsFrom = saleSkuList
                        .filter { sku -> sku.enableState == YES.code }
                        .map { it.getParsedShipsFromPropertyValueItem() }.filter { it != null }
                    val foreachShipsFrom = mutableListOf<ProductAePropertyValueItemDTO?>()
                    if (allShipsFrom.count() == 0) {
                        // 都是空, 则只有默认发货地
                        foreachShipsFrom.add(ProductAePropertyValueItemDTO())
                    } else {
                        // 不为空, 则多发货地
                        val tmpShipsFrom = allShipsFrom.associateBy { it!!.attributeValueId }
                        if (tmpShipsFrom.values.isNotEmpty()) {
                            foreachShipsFrom.addAll(tmpShipsFrom.values)
                        } else {
                            foreachShipsFrom.add(ProductAePropertyValueItemDTO())
                        }
                    }

                    if (foreachShipsFrom.size > 1) {
                        // 数据库已有SKU是多发货地, 报错
                        throw BaseBizException("${saleGoods.spuCode} 已是多发货地, 不允许修改")
                    }

                    // 判断是否有多发货地 foreachShipsFrom
                    if (foreachShipsFrom.size == 1) {
                        // 单个发货地: 处理, 取出第一个为模板, 复制多发货地sku, 并把模板enable=0

                        if (shipsFromPropertyValueItems?.size == 1 && shipsFromPropertyValueItems.firstOrNull()?.attributeValueId == foreachShipsFrom.firstOrNull()?.attributeValueId) {
                            // 一致不处理: 如果入参发货地数量=1 和 数据库发货地数量=1, 且 入参发货地的attributeValueId = 数据库发货地的attributeValueId
                            log.warn { "批量更新发货地-SPU:${saleGoods.spuCode} 已有发货地和入参发货地一致, 不处理" }
                        } else {

                            val newSaleSkuList = mutableListOf<AeSaleSku>()
                            // 循环入参发货地, 保存SKU
                            shipsFromPropertyValueItems?.forEach { shipsFrom ->
                                saleSkuList.forEach { saleSku ->
                                    val newSaleSku = saleSku.copy(
                                        saleSkuId = IdHelper.getId(),
                                        platformProductId = null,
                                        platformSkuId = null,
                                        shopSku = null,
                                        shipsFromAttributeId = shipsFrom.attributeId,
                                        shipsFromAttributeName = shipsFrom.attributeName,
                                        shipsFromAttributeValueId = shipsFrom.attributeValueId,
                                        shipsFromAttributeValueName = shipsFrom.attributeValueName,
                                    )
                                    BaseEntity.reset(newSaleSku)
                                    newSaleSkuList.add(newSaleSku)
                                }
                            }
                            if (newSaleSkuList.isNotEmpty()) {
                                aeSaleSkuRepository.saveBatch(newSaleSkuList)
                            }

                            // 模版SKU 设置为 enable=0
                            val noEnableSkuList = saleSkuList.map { sku ->
                                AeSaleSku().apply {
                                    this.saleSkuId = sku.saleSkuId
                                    this.enableState = NO.code
                                }
                            }
                            if (noEnableSkuList.isNotEmpty()) {
                                aeSaleSkuRepository.updateBatchById(noEnableSkuList)
                            }

                            isUpdate = true
                        }

                    }
                }
            }
            if (isUpdate) {
                // 有更新
                updateSale
            } else {
                // 无更新
                null
            }
        }
        if (updateSaleGoodsList.isNotEmpty()) {
            updateSaleGoodsList.forEach { saleGoods ->
                // 手动事务
                TransactionTemplate(transactionManager).execute { status ->
                    // 更新本地
                    aeSaleGoodsRepository.updateById(saleGoods)
                    // 重新查询一次(刷新信息)
                    val newSaleGoods = aeSaleGoodsRepository.getById(saleGoods.saleGoodsId!!)
                    productOperateLogRepository.saveProductOperateLog("批量修改分组/模板等", PlatformOperatorTypeEnum.UPDATE_PRODUCT, newSaleGoods)
                    try {
                        aeUpdateProductComponent.createOrUpdateProduct(
                            newSaleGoods.shopId!!,
                            newSaleGoods
                        )
                    } catch (e: Exception) {
                        log.error(e) { "批量修改分组/模板等, 更新AE失败 saleGoodsId:${newSaleGoods.saleGoodsId}" }
                        errorInfoList.put(newSaleGoods.spuCode!!, e.message)
                        status.setRollbackOnly()
                    }
                }
            }
        }

        // 异常抛出
        if (errorInfoList.isNotEmpty()) {
            throw BaseBizException("批量修改分组/模板等, 更新AE失败, 失败原因:${errorInfoList.toJson()}")
        }
    }
}
