package tech.tiangong.pop.service.category.impl

import cn.yibuyun.framework.collection.Collections
import jakarta.annotation.Resource
import org.springframework.beans.BeanUtils
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import org.springframework.util.CollectionUtils
import team.aikero.blade.core.enums.Bool
import team.aikero.blade.core.exception.BusinessException
import team.aikero.blade.core.protocol.PageVo
import team.aikero.blade.core.toolkit.isBlank
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.util.async.runAsync
import team.aikero.blade.util.json.parseJsonList
import team.aikero.blade.util.json.toJson
import tech.tiangong.pop.common.enums.PlatformEnum
import tech.tiangong.pop.common.exception.BaseBizException
import tech.tiangong.pop.common.req.PlatformCategoryQueryReq
import tech.tiangong.pop.common.resp.BasePlatformCategoryResp
import tech.tiangong.pop.common.resp.CategoryAttributeResp
import tech.tiangong.pop.common.resp.LazadaCategoryResp
import tech.tiangong.pop.config.PublishProperties
import tech.tiangong.pop.dao.entity.PublishCategory
import tech.tiangong.pop.dao.entity.PublishCategoryMapping
import tech.tiangong.pop.dao.repository.LazadaCategoryRepository
import tech.tiangong.pop.dao.repository.PublishCategoryAttrRepository
import tech.tiangong.pop.dao.repository.PublishCategoryMappingRepository
import tech.tiangong.pop.dao.repository.PublishCategoryRepository
import tech.tiangong.pop.dao.repository.PublishPlatformAttrRepository
import tech.tiangong.pop.enums.CategoryAssociatePlatformStateEnum
import tech.tiangong.pop.helper.PageRespHelper
import tech.tiangong.pop.req.category.*
import tech.tiangong.pop.req.product.temu.TemuBaseReq
import tech.tiangong.pop.resp.category.LzdCategoryVO
import tech.tiangong.pop.resp.category.PublishCategoryMappingDetailVo
import tech.tiangong.pop.resp.category.PublishCategoryMappingVo
import tech.tiangong.pop.resp.category.PublishPlatformCategoryVo
import tech.tiangong.pop.service.AlibabaCategoryService
import tech.tiangong.pop.service.AliexpressCategoryService
import tech.tiangong.pop.service.TemuService
import tech.tiangong.pop.service.category.PublishCategoryAttributeService
import tech.tiangong.pop.service.category.PublishCategoryMappingService
import tech.tiangong.pop.utils.CategoryUtils
import java.util.*
import java.util.concurrent.ExecutorService
import java.util.concurrent.atomic.AtomicInteger
import java.util.function.Function
import java.util.stream.Collectors

@Slf4j
@Service
class PublishCategoryMappingServiceImpl(
    private val publishCategoryMappingRepository: PublishCategoryMappingRepository,
    private val publishPlatformAttrRepository: PublishPlatformAttrRepository,
    private val lazadaCategoryRepository: LazadaCategoryRepository,
    private val publishCategoryRepository: PublishCategoryRepository,
    private val publishProperties: PublishProperties,
    private val publishCategoryAttributeService: PublishCategoryAttributeService,
    private val aliexpressCategoryService: AliexpressCategoryService,
    private val alibabaCategoryService: AlibabaCategoryService,
    private val temuService: TemuService,
    private val publishCategoryAttrRepository: PublishCategoryAttrRepository,
) : PublishCategoryMappingService {

    @Resource(name = "asyncExecutor")
    private lateinit var asyncExecutor: ExecutorService

    override fun page(req: PublishCategoryMappingQueryReq): PageVo<PublishCategoryMappingVo> {
        log.info{"开始查询关联平台品类分页数据，请求参数：${req}"}

        val page = publishCategoryMappingRepository.pageQuery(req)
        val records = page.records

        if (records.isEmpty()) {
            log.info { "关联平台品类分页查询结果为空" }
            return PageRespHelper.empty()
        }
        //补充是否已映射第三方属性
        val countAttributeMap =
            publishPlatformAttrRepository.countAttributeMappingByCategoryMappingId(records.mapNotNull { it.categoryMappingId })
                .associate { it.categoryMappingId to it.amount }
        records.forEach {
            it.hasAttributeMapping = if((countAttributeMap[it.categoryMappingId]?:0)!=0){
                Bool.YES.code
            }else{
                Bool.NO.code
            }
        }
        log.info { "选款任务分页查询完成，总记录数：${page.total}" }
        return PageRespHelper.of(page.current.toInt(), page.total, records)
    }

    override fun treePlatformCategory(req: PublishPlatformCategoryQueryReq): List<PublishPlatformCategoryVo> {
        log.info { "开始查询平台品类树，请求参数：${req}" }
        val platformEnum = PlatformEnum.getByPlatformId(req.platformId!!)
            ?: throw BaseBizException("不支持的平台ID: ${req.platformId}")

        return when (platformEnum) {
            PlatformEnum.LAZADA -> fetchLazadaCategories(req.country!!)
            PlatformEnum.AE -> aliexpressCategoryService.getPlatformCategoryTree()
            PlatformEnum.YI_LIU_BA_BA -> alibabaCategoryService.getPlatformCategoryTree()
            PlatformEnum.TEMU -> temuService.getTemuPlatformCategoryTree()
            else -> throw IllegalArgumentException("不支持的平台ID: ${req.platformId}")
        }
    }

    override fun save(req: SavePublishCategoryMappingReq) {
        val platform = PlatformEnum.getByPlatformId(req.platformId!!)
            ?: throw BaseBizException("不支持的平台ID: ${req.platformId}")
        // 检查发布类别是否已绑定到同一平台的同一个站点
        checkPublishCategoryBinding(req.platformId!!,req.publishCategoryId!!,req.country!!)

        /*val country = when (platform) {
            PlatformEnum.LAZADA -> {
                publishProperties.lazadaCategoryCountry
            }
            PlatformEnum.AE -> {
                CountryEnum.US.code
            }
            PlatformEnum.YI_LIU_BA_BA -> {
                CountryEnum.CN.code
            }
            PlatformEnum.TEMU -> {
                CountryEnum.CN.code
            }
            else -> {
                throw BaseBizException("暂不支持配置的平台ID: ${req.platformId}")
            }
        }*/

        // 保存新的发布类别映射
        val publishCategoryMapping = PublishCategoryMapping().apply {
            this.publishCategoryId = req.publishCategoryId
            this.platformId = platform.platformId
            this.platformCategoryId = req.platformCategoryId
            this.platformCategoryName = req.platformCategoryName
            this.channelId = req.channelId
            this.channelName = req.channelName
            this.platformName = platform.platformName
            this.remark = req.remark
            this.country = req.country
        }

        publishCategoryMappingRepository.save(publishCategoryMapping)
        //更新品类关联平台状态
        val updatePublishCategory = PublishCategory().apply {
            this.publishCategoryId = publishCategoryMapping.publishCategoryId
            this.platformAssociateState = CategoryAssociatePlatformStateEnum.ASSOCIATE_CATEGORY.code
        }
        publishCategoryRepository.updateById(updatePublishCategory)
    }

    @Transactional(rollbackFor = [Exception::class])
    override fun edit(req: EditPublishCategoryMappingReq) {
        val oldCategoryMappingId = req.categoryMappingId!!

        val oldCategoryMapping = publishCategoryMappingRepository.getById(oldCategoryMappingId)
            ?: throw BaseBizException("找不到平台品类映射记录")

        if (oldCategoryMapping.deleted != Bool.NO.code) {
            throw BaseBizException("当前平台品类映射记录已经删除，请刷新")
        }
        //如果关联没变（平台品类，本地品类，站点），就只更新备注内容和第三方品类名称
        if (isSameCategoryMapping(oldCategoryMapping, req)) {
            val updateEntity = PublishCategoryMapping().apply {
                categoryMappingId = oldCategoryMapping.categoryMappingId
                remark = req.remark
                if (!req.platformCategoryName.isNullOrBlank()) {
                    platformCategoryName = req.platformCategoryName
                }
            }
            // 执行更新操作
            publishCategoryMappingRepository.updateById(updateEntity)
        }
        //如果关联关系变了，则删除旧的保存新的关系
        else {
            // 检查新的关联是否已绑定到同一平台的同一个站点
            checkPublishCategoryBinding(req.platformId!!,req.publishCategoryId!!,req.country!!)
            val publishCategoryMapping = PublishCategoryMapping().apply {
                BeanUtils.copyProperties(req, this)
                categoryMappingId = null
            }

            publishCategoryMappingRepository.save(publishCategoryMapping)
            publishCategoryAttributeService.removePlatformAttr(oldCategoryMappingId)
            publishCategoryMappingRepository.logicDelete(oldCategoryMappingId)

            //更新品类关联平台状态
            publishCategoryAttributeService.changePublishCategoryPlatformAssociateState(publishCategoryMapping.publishCategoryId!!)
        }

        //更新品类关联平台状态
        publishCategoryAttributeService.changePublishCategoryPlatformAssociateState(oldCategoryMapping.publishCategoryId!!)
    }

    override fun syncAliexpressCategories(token: String?) {
        aliexpressCategoryService.syncAliexpressCategories(token)
    }

    override fun syncTemuCategories(req: TemuBaseReq) {
        temuService.syncTemuCategory(req)
    }

    override fun syncAlibabaCategories() {
        //考虑到逐层同步+组装树结果+缓存耗时较长，所以这里异步处理
        runAsync(asyncExecutor) {
            log.info { "开始异步获取1688类目" }
            alibabaCategoryService.syncAlibabaCategories()
            log.info { "结束异步获取1688类目" }
        }
    }

    override fun queryPublishCategoryByPlatformCategoryId(req: PublishCategoryMappingDetailQueryReq): List<PublishCategoryMappingDetailVo> {
        PlatformEnum.getByPlatformId(req.platformId!!)
            ?: throw BaseBizException("不支持的平台ID: ${req.platformId}")
        val mappingList: List<PublishCategoryMapping>? = publishCategoryMappingRepository.queryPublishCategoryByPlatformCategoryId(req)
        if(CollectionUtils.isEmpty(mappingList)) {
            return Collections.emptyList()
        }
        val publishCategories = publishCategoryRepository.listWithCache()
        val publishCategoryMap = publishCategories.stream().collect(
            Collectors.toMap(
                PublishCategory::publishCategoryId, Function.identity()
            ) { _: PublishCategory?, k2: PublishCategory -> k2 }
        )
        return mappingList!!.stream().map{ v: PublishCategoryMapping ->
            val vo = PublishCategoryMappingDetailVo()
            BeanUtils.copyProperties(v, vo)
            val publishCategory = publishCategoryMap[v.publishCategoryId]
            if (publishCategory != null) {
                vo.cateoryCode = publishCategory.cateoryCode
                vo.categoryName = publishCategory.categoryName
                vo.fullCategoryName =
                    Arrays.stream(publishCategory.parentPaths!!.split(",".toRegex()).dropLastWhile { it.isEmpty() }
                        .toTypedArray()).map innerMap@{ id: String ->
                        val parent = publishCategoryMap[id.toLong()]
                        if (parent != null) {
                            return@innerMap parent.categoryName
                        }
                        null
                    }.filter { p: String? -> p != null }.collect(Collectors.toList())
            }
            vo
        }.collect(Collectors.toList())
    }

    override fun queryPlatformCategoryByPlatformCategoryId(req: PlatformCategoryQueryReq): List<BasePlatformCategoryResp> {
        log.info { "开始根据第三方平台类目ID查询第三方平台类目信息，请求参数：${req.toJson()}" }
        val platformEnum = PlatformEnum.getByPlatformId(req.platformId!!)
            ?: throw BaseBizException("不支持的平台ID: ${req.platformId}")
        return when (platformEnum) {
            PlatformEnum.LAZADA -> listByPlatformCategoryIds(req.country?:publishProperties.lazadaCategoryCountry,req.platformCategoryIds)
            PlatformEnum.AE -> aliexpressCategoryService.listByPlatformCategoryIds(req.platformCategoryIds)
            PlatformEnum.YI_LIU_BA_BA -> alibabaCategoryService.listByPlatformCategoryIds(req.platformCategoryIds)
            PlatformEnum.TEMU -> temuService.listByPlatformCategoryIds(req.platformCategoryIds)
            else -> throw IllegalArgumentException("不支持的平台ID: ${req.platformId}")
        }
    }

    override fun updateAeCategoryPathLists() {
        aliexpressCategoryService.updateCategoryPathLists()
    }

    override fun listEnableAttributesByCategoryId(categoryName:String):List<CategoryAttributeResp>{
        val publishCategories = publishCategoryRepository.listWithCache()
        if (publishCategories.isEmpty()) {
            throw BusinessException("请维护品类管理!")
        }
        // 设置品类ID
        val tempCategoryName = categoryName.replace("-", ">")
        val publishCategoryIdByPath = CategoryUtils.findPublishCategoryIdByPath(publishCategories, tempCategoryName)
            ?: throw BusinessException("品类: ${categoryName} 未设置")

        val categoryId = publishCategoryIdByPath
        return publishCategoryAttrRepository.listEnableAttributesByCategoryId(categoryId)
    }

    private fun isSameCategoryMapping(oldCategoryMapping: PublishCategoryMapping, req: EditPublishCategoryMappingReq): Boolean {
        return oldCategoryMapping.publishCategoryId == req.publishCategoryId &&
                oldCategoryMapping.platformCategoryId == req.platformCategoryId &&
                oldCategoryMapping.country == req.country
    }
    //判断本地品类是否已绑定过
    //平台ID，本地品类ID，绑定站点
    private fun checkPublishCategoryBinding(platformId:Long,publishCategoryId:Long ,country:String) {
        val existingPublishMapping = publishCategoryMappingRepository.ktQuery()
            .eq(PublishCategoryMapping::deleted, Bool.NO.code)
            .eq(PublishCategoryMapping::publishCategoryId, publishCategoryId)
            .eq(PublishCategoryMapping::platformId, platformId)
            .eq(PublishCategoryMapping::country,country)
            .last("LIMIT 1")
            .one()

        existingPublishMapping?.let {
            throw BaseBizException("同一个平台同一个站点只能关联一个品类，该品类已绑定：${it.platformName} - ${it.platformCategoryName} - ${it.country}")
        }
    }

    private fun convertLazadaCategories(lzdCategories: List<LzdCategoryVO>): List<PublishPlatformCategoryVo> {
        return lzdCategories.map { convertSingleLazadaCategory(it, null) }
    }

    private fun convertSingleLazadaCategory(lzdCategory: LzdCategoryVO, parentCode: String?): PublishPlatformCategoryVo {
        return PublishPlatformCategoryVo().apply {
            platformCategoryCode = lzdCategory.categoryId.toString()
            platformCategoryName = lzdCategory.name
            leaf = lzdCategory.leaf == true
            parentPlatformCategoryCode = parentCode

            children = if (lzdCategory.children?.isNotEmpty() == true) {
                lzdCategory.children!!.map {
                    convertSingleLazadaCategory(it, platformCategoryCode)
                }
            } else {
                null
            }
        }
    }

    private fun fetchLazadaCategories(country:String): List<PublishPlatformCategoryVo> {
        val lazadaCategory = lazadaCategoryRepository.getByCountry(country)
            ?: throw BaseBizException("Lazada category data not found for country: ${country}")

        val categoryList = lazadaCategory.categoryData?.parseJsonList(LzdCategoryVO::class.java) ?: emptyList()

        return convertLazadaCategories(categoryList)
    }

    private fun listByPlatformCategoryIds(country:String,categoryIds: MutableList<Long>?): MutableList<LazadaCategoryResp> {
        try {
            val list = fetchLazadaCategories(country)
            val depthCounter = AtomicInteger(0)
            val result = mutableListOf<LazadaCategoryResp>()
            expandLazadaCategoryTreeToList(categoryIds, list, depthCounter, result)
            return result
        } catch (e: Exception) {
            log.error { "根据1688品类ID获取平台类目失败: ${e.cause?.message}" }
        }
        return mutableListOf()
    }

    private fun expandLazadaCategoryTreeToList(categoryIds: MutableList<Long>?,
                                               children:List<PublishPlatformCategoryVo>,
                                               depthCounter: AtomicInteger,
                                               result: MutableList<LazadaCategoryResp>) {
        // 检查递归深度
        val currentDepth = depthCounter.incrementAndGet()
        children.forEach{
            if(categoryIds?.isNotEmpty() == true) {
                if (it.platformCategoryCode.isBlank()
                    || !it.platformCategoryCode?.let { it1 -> categoryIds.contains(it1.toLong()) }!!
                ) {
                    return@forEach
                }
            }
            val lazadaCategory = LazadaCategoryResp().apply {
                categoryId = it.platformCategoryCode?.toLong()
                leaf = it.leaf
                categoryName = it.platformCategoryName
                parentId = it.parentPlatformCategoryCode?.toLong()
                level = currentDepth
                plantformId = PlatformEnum.LAZADA.platformId
            }
            result.add(lazadaCategory)
            if(it.children?.isNotEmpty() == true) {
                expandLazadaCategoryTreeToList(categoryIds, it.children!!,depthCounter,result)
            }
        }
    }
}