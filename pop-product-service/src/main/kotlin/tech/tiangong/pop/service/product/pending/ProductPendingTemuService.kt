package tech.tiangong.pop.service.product.pending

import org.springframework.web.multipart.MultipartFile
import team.aikero.blade.core.protocol.PageVo
import tech.tiangong.pop.dao.entity.Product
import tech.tiangong.pop.dao.entity.Shop
import tech.tiangong.pop.req.product.ProductCreatePublishTaskReq
import tech.tiangong.pop.req.product.ProductPendingPublishLogPageReq
import tech.tiangong.pop.req.product.ae.ProductPendingAePageReq
import tech.tiangong.pop.req.product.temu.*
import tech.tiangong.pop.resp.product.ProductPendingPublishLogPageResp
import tech.tiangong.pop.resp.product.ProductPendingPublishLogStatisticsResp
import tech.tiangong.pop.resp.product.ae.ProductPendingAeStatisticsResp
import tech.tiangong.pop.resp.product.temu.ProductPendingTemuDetailResp
import tech.tiangong.pop.resp.product.temu.ProductPendingTemuPageResp

/**
 * 待上架商品-Temu服务
 */
interface ProductPendingTemuService {

    /**
     * 创建待上架商品
     */
    fun createPendingTask(req: ProductCreatePublishTaskReq.PendingAeTaskChangeUserReq?, productList: List<Product>, shop: Shop)

    /**
     * 商品列表-分页
     */
    fun page(req: ProductPendingTemuPageReq): PageVo<ProductPendingTemuPageResp>

    /**
     * 商品列表-统计
     */
    fun statistics(req: ProductPendingAePageReq): ProductPendingAeStatisticsResp

    /**
     * 商品详情
     */
    fun detail(req: ProductPendingTemuDetailReq): ProductPendingTemuDetailResp

    /**
     * 商品更新
     */
    fun update(req: ProductPendingUpdateTemuReq)

    /**
     * 任务操作-开始任务
     */
    fun start(temuSpuIds: List<Long>)

    /**
     * 任务操作-更换人员
     */
    fun change(req: ProductPendingTemuTaskChangeReq)

    /**
     * 任务操作-取消任务
     */
    fun cancel(req: ProductPendingTemuTaskCancelReq)

    /**
     * 上架记录-统计
     */
    fun publishLogStatistics(): ProductPendingPublishLogStatisticsResp

    /**
     * 上架记录-分页列表
     */
    fun publishLogPage(req: ProductPendingPublishLogPageReq): PageVo<ProductPendingPublishLogPageResp>

    /**
     * 导入-待上架-商品数据
     */
    fun importExcel(file: MultipartFile)
}
