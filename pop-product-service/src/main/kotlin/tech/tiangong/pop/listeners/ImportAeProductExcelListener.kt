package tech.tiangong.pop.listeners

import com.alibaba.excel.context.AnalysisContext
import com.alibaba.excel.event.AnalysisEventListener
import org.apache.commons.lang3.StringUtils
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import tech.tiangong.pop.dto.product.ImportAeProductDTO

/**
 * 用于解析导入速卖通商品 Excel 文件的监听器
 */
@Slf4j
class ImportAeProductExcelListener : AnalysisEventListener<Map<Int, String>>() {

    // 存储所有解析的数据
    private val productList = mutableListOf<ImportAeProductDTO>()
    private val mapDataList = mutableListOf<Map<Int, String>>()

    // 存储表头列名与索引的映射关系
    private val headerMap = mutableMapOf<Int, String>()

    /**
     * 获取解析后的商品列表
     */
    fun getProductList(): List<ImportAeProductDTO> = productList

    override fun invokeHeadMap(headMap: Map<Int, String>, context: AnalysisContext) {
        // 记录表头信息
        headerMap.putAll(headMap)
    }

    /**
     * 处理每一行数据
     */
    override fun invoke(data: Map<Int, String>, context: AnalysisContext) {
        val productDTO = ImportAeProductDTO()

        // 遍历当前行数据并填充到 ImportAeProductDTO 对象中
        for ((columnIndex, cellValue) in data) {
            // 根据表头列名匹配到 ImportAeProductDTO 类的字段
            headerMap[columnIndex]?.let { columnName ->
                // 如果是动态列（以"属性-"开头），将其存入 dynamicAttributes 中
                if (columnName.startsWith("属性-")) {
                    // 去掉"属性-"前缀作为key，值为对应的列值
                    val dynamicKey = columnName.substring(3) // 去除 "属性-" 前缀
                    productDTO.addDynamicAttribute(dynamicKey, cellValue)
                } else {
                    // 根据列名将数据填充到对应的字段中
                    setFieldValue(productDTO, columnName, cellValue)
                }
            }
        }

        // 将当前数据对象添加到 productList 中
        productList.add(productDTO)
        mapDataList.add(data)

        // 打印当前商品标题
        log.info { "正在处理商品: ${productDTO.productTitle}" }
    }

    /**
     * 设置 ImportAeProductDTO 类字段的值
     */
    private fun setFieldValue(productDTO: ImportAeProductDTO, columnName: String, value: String?) {
        try {
            when (columnName) {
                "*供给方式" -> productDTO.supplyMode = value
                "*商品标题" -> productDTO.productTitle = value
                "*SPU" -> productDTO.spuCode = if (StringUtils.isNotEmpty(value)) value?.trim() else ""
                "*品类" -> productDTO.categoryName = value
                "建议店铺" -> productDTO.shopName = value
                "上架店铺" -> productDTO.listingShopName = value
                "品牌" -> productDTO.brandName = value
                "上架尺码标准" -> productDTO.sizeGroupName = value
                "尺码" -> productDTO.sizeNames = value
                "*skc" -> productDTO.skcCode = value
                "颜色" -> productDTO.color = value
                "供货价" -> productDTO.localPrice = value
                "采购价" -> productDTO.purchasePrice = value
                "现货类型" -> productDTO.spotType = value
                "定价类型" -> productDTO.pricingType = value
                "库存" -> productDTO.stockQuantity = value
                "发货地" -> productDTO.shipsFrom = value  // 支持多发货地，用逗号分隔
                "库存扣减方式" -> productDTO.invDeductionName = value
                "*产地(国家或地区)" -> productDTO.originPlaceName = value
                "包裹重量" -> productDTO.packageWeight = value
                "计税方式" -> productDTO.taxType = value
                "划线价(元)" -> productDTO.retailPrice = value  // 支持多价格，用逗号分隔
                "售价(元)" -> productDTO.salePrice = value      // 支持多价格，用逗号分隔
                "款式类型" -> productDTO.styleType = value
                "是否区域定价" -> productDTO.regionalPricingFlag = value
            }
        } catch (e: Exception) {
            // 如果出现异常或者赋值失败，记录错误
            log.error { "无法为字段 $columnName 赋值 $value: ${e.message}" }
        }
    }

    /**
     * 数据解析完成后的清理操作
     */
    override fun doAfterAllAnalysed(context: AnalysisContext) {
        // 这里可以执行一些结束操作
        log.info { "所有数据处理完毕！共解析 ${productList.size} 条商品数据" }
    }
}