package tech.tiangong.pop.listeners

import com.alibaba.excel.context.AnalysisContext
import com.alibaba.excel.event.AnalysisEventListener
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import tech.tiangong.pop.dto.AttributeMappingExcelDTO

/**
 * Excel读取监听器
 */
@Slf4j
class AttributeMappingExcelListener : AnalysisEventListener<AttributeMappingExcelDTO>() {

    // 用于存储有效的数据行
    private val dataList = mutableListOf<AttributeMappingExcelDTO>()

    override fun invoke(data: AttributeMappingExcelDTO, context: AnalysisContext) {
        if (data.isValid()) {
            dataList.add(data)
        } else {
            log.warn { "Skip invalid or marked row: $data" }
        }
    }

    override fun doAfterAllAnalysed(context: AnalysisContext) {
        log.info { "Excel reading completed. Total valid rows: ${dataList.size}" }
    }

    /**
     * 获取解析后的数据
     */
    fun getDataList(): List<AttributeMappingExcelDTO> = dataList.toList()
}
