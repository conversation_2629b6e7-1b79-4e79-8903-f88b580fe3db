package tech.tiangong.pop.listeners.excel

import com.alibaba.excel.context.AnalysisContext
import com.alibaba.excel.read.listener.ReadListener
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.sequence.id.IdHelper
import tech.tiangong.pop.constant.ProductInnerConstant.TITLE_MAX_EXTEND_LEN
import tech.tiangong.pop.dao.entity.ProductTitleDictionaryField
import tech.tiangong.pop.dao.entity.ProductTitleDictionaryFieldValue
import tech.tiangong.pop.dao.entity.PublishCategory
import tech.tiangong.pop.dao.repository.ProductTitleDictionaryFieldRepository
import tech.tiangong.pop.dao.repository.ProductTitleDictionaryFieldValueRepository
import tech.tiangong.pop.dto.settings.ProductTitleDictionaryFieldExcelDTO
import tech.tiangong.pop.enums.DisableEnum
import tech.tiangong.pop.enums.settings.ProductTitleDictionarySysFieldEnum
import tech.tiangong.pop.resp.ImportFailureDetail
import tech.tiangong.pop.resp.ImportResultResp
import tech.tiangong.pop.utils.CategoryUtils

/**
 * 商品标题词库字段Excel导入监听器
 */
class ProductTitleDictionaryFieldExcelListener(
    private val result: ImportResultResp,
    private val categories: List<PublishCategory>,
    private val fieldRepository: ProductTitleDictionaryFieldRepository,
    private val fieldValueRepository: ProductTitleDictionaryFieldValueRepository,
) : ReadListener<ProductTitleDictionaryFieldExcelDTO> {

    override fun invoke(dto: ProductTitleDictionaryFieldExcelDTO, context: AnalysisContext) {
        val rowIndex = context.readRowHolder().rowIndex + 1
        try {
            // --- 校验输入 ---
            val validationError = validateDto(dto)
            if (validationError != null) {
                recordFailure(rowIndex, validationError)
                return
            }

            val sysField = ProductTitleDictionarySysFieldEnum.getByDesc(dto.sysField!!.trim())?.code
            if (sysField == null) {
                recordFailure(rowIndex, "无效的关联字段: ${dto.sysField}")
                return
            }

            // --- 查找或创建字段 ---
            val field = findOrCreateField(sysField, dto.fieldName!!)

            // --- 处理特殊字段（如品类） ---
            val fieldValueId = if (sysField == ProductTitleDictionarySysFieldEnum.CATEGORY.code) {
                val categoryId = CategoryUtils.findPublishCategoryIdByPath(categories, dto.fieldValue!!)
                if (categoryId == null) {
                    recordFailure(rowIndex, "找不到匹配的品类ID: ${dto.fieldValue}")
                    return
                }
                categoryId.toString()
            } else null

            // --- 查重并保存或更新字段值 ---
            val existingValue = if (fieldValueId != null) {
                fieldValueRepository.ktQuery()
                    .eq(ProductTitleDictionaryFieldValue::productTitleDictionaryFieldId, field.productTitleDictionaryFieldId)
                    .eq(ProductTitleDictionaryFieldValue::fieldValueId, fieldValueId)
                    .oneOpt()
            } else {
                fieldValueRepository.ktQuery()
                    .eq(ProductTitleDictionaryFieldValue::productTitleDictionaryFieldId, field.productTitleDictionaryFieldId)
                    .eq(ProductTitleDictionaryFieldValue::fieldValue, dto.fieldValue)
                    .oneOpt()
            }

            if (existingValue.isPresent) {
                val value = existingValue.get()
                value.fieldValue = dto.fieldValue
                value.extend1 = dto.extend1
                value.extend2 = dto.extend2
                value.extend3 = dto.extend3
                value.fieldValueId = fieldValueId
                fieldValueRepository.updateById(value)
            } else {
                val value = ProductTitleDictionaryFieldValue().apply {
                    productTitleDictionaryFieldId = field.productTitleDictionaryFieldId
                    this.fieldValue = dto.fieldValue
                    this.extend1 = dto.extend1
                    this.extend2 = dto.extend2
                    this.extend3 = dto.extend3
                    this.fieldValueId = fieldValueId
                    this.enabled = DisableEnum.ENABLED.code
                }
                fieldValueRepository.save(value)
            }
            result.successCount++
        } catch (e: Exception) {
            recordFailure(rowIndex, "处理失败: ${e.message}")
        }
    }

    override fun doAfterAllAnalysed(context: AnalysisContext) {
        log.info { "Excel解析完成，成功: ${result.successCount}，失败: ${result.failCount}" }
    }

    private fun validateDto(dto: ProductTitleDictionaryFieldExcelDTO): String? {
        // 校验必填字段
        if (dto.fieldName.isNullOrBlank()) return "字段名不能为空"
        if (dto.sysField.isNullOrBlank())  return "关联字段不能为空"
        if (dto.fieldValue.isNullOrBlank()) return "字段值不能为空"

        // 校验扩展字段至少填一个
        val extendFields = listOf(dto.extend1, dto.extend2, dto.extend3)
        if (extendFields.all { it.isNullOrBlank() }) return "拓展字段至少需要填写一个"

        // 校验长度
        extendFields.forEachIndexed { idx, value ->
            if ((value?.length ?: 0) > TITLE_MAX_EXTEND_LEN) {
                return "拓展${idx + 1}长度不能超过$TITLE_MAX_EXTEND_LEN"
            }
        }
        return null
    }

    private fun recordFailure(rowNumber: Int, reason: String) {
        result.failCount++
        result.failureDetails.add(
            ImportFailureDetail(
                rowNumber = rowNumber,
                reason = reason
            )
        )
    }

    private fun findOrCreateField(sysField: String, fieldName: String): ProductTitleDictionaryField {
        return fieldRepository.ktQuery()
            .eq(ProductTitleDictionaryField::sysField, sysField)
            .oneOpt()
            .orElseGet {
                ProductTitleDictionaryField().apply {
                    productTitleDictionaryFieldId = IdHelper.getId()
                    this.fieldName = fieldName
                    this.sysField = sysField
                    this.enabled = DisableEnum.ENABLED.code
                }.also {
                    fieldRepository.save(it)
                }
            }
    }
}