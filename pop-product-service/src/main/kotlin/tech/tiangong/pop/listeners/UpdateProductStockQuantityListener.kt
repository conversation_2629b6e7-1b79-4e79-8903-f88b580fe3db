package tech.tiangong.pop.listeners

import com.rabbitmq.client.Channel
import jakarta.annotation.Resource
import org.springframework.amqp.core.Message
import org.springframework.amqp.rabbit.annotation.Exchange
import org.springframework.amqp.rabbit.annotation.Queue
import org.springframework.amqp.rabbit.annotation.QueueBinding
import org.springframework.amqp.rabbit.annotation.RabbitListener
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.util.json.parseJson
import tech.tiangong.pop.constant.MqConstants
import tech.tiangong.pop.core.lock.LockComponent
import tech.tiangong.pop.dto.product.UpdateProductStockDto
import tech.tiangong.pop.service.mq.MessageDistributeService
import tech.tiangong.pop.service.product.ProductCreateV2Service

/**
 * 更新库存
 */
@Slf4j
@Component
@RabbitListener(bindings = [QueueBinding(
    value = Queue(value = MqConstants.QUEUE_POP_LAZADA_QUANTITY_UPDATE, durable = "true"),
    exchange = Exchange(
        value = MqConstants.EXCHANGE_POP_LAZADA_QUANTITY_UPDATE,
        delayed = Exchange.TRUE,
        ignoreDeclarationExceptions = Exchange.TRUE
    ),
    key = [MqConstants.KEY_POP_LAZADA_QUANTITY_UPDATE]
)])
class UpdateProductStockQuantityListener: MqBaseListener() {
    @field:Resource(name = "messageDistributeServiceImplV2")
    override lateinit var messageDistributeService: MessageDistributeService
    @field:Resource
    override lateinit var lockComponent: LockComponent
    @field:Resource
    private lateinit var productCreateV2Service: ProductCreateV2Service

    @Transactional(rollbackFor = [Exception::class])
    override fun onMessage(message: Message, channel: Channel, headers: Map<String, Any>, deliveryTag: Long, payload: String) {
        log.info { "更新库存监听器 - queue=${message.messageProperties.consumerQueue}, messageId=${message.messageProperties.messageId}, body=${payload.take(256)}" }
        val updateProductStockDto = payload.parseJson<UpdateProductStockDto>()
        productCreateV2Service.updateLazadaStock(updateProductStockDto)
    }
}