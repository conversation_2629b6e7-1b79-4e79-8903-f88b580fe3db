package tech.tiangong.pop.listeners

import com.rabbitmq.client.Channel
import org.apache.commons.codec.digest.DigestUtils
import org.apache.commons.lang3.exception.ExceptionUtils
import org.springframework.amqp.core.Message
import org.springframework.amqp.rabbit.annotation.RabbitHandler
import org.springframework.amqp.support.AmqpHeaders
import org.springframework.messaging.handler.annotation.Header
import org.springframework.messaging.handler.annotation.Headers
import team.aikero.blade.auth.withSystemUser
import team.aikero.blade.auth.withUser
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import tech.tiangong.pop.constant.RedisConstants.FACTORY_MQ_LOCK
import tech.tiangong.pop.core.RabbitMqReply
import tech.tiangong.pop.core.lock.LockComponent
import tech.tiangong.pop.service.mq.MessageDistributeService
import tech.tiangong.pop.utils.MqThreadLocalUtils
import tech.tiangong.pop.utils.isEmptyJson
import tech.tiangong.pop.utils.isValidJson
import java.nio.charset.StandardCharsets
import java.time.Duration
import java.time.Instant

/**
 * <AUTHOR>
 * @since 2021/1/9 18:02
 */
@Slf4j
abstract class MqBaseListener {
    protected abstract var messageDistributeService: MessageDistributeService
    protected abstract var lockComponent: LockComponent
    protected abstract fun onMessage(
        message: Message,
        channel: Channel,
        @Headers headers: Map<String, Any>,
        @Header(AmqpHeaders.DELIVERY_TAG) deliveryTag: Long,
        payload: String
    )

    @RabbitHandler
    @Throws(Exception::class)
    fun handler(
        bytes: ByteArray,
        message: Message,
        channel: Channel,
        @Headers headers: Map<String, Any>,
        @Header(AmqpHeaders.DELIVERY_TAG) deliveryTag: Long
    ) {
        handler(String(bytes, StandardCharsets.UTF_8), message, channel, headers, deliveryTag)
    }

    @RabbitHandler
    @Throws(Exception::class)
    fun handler(
        payload: String,
        message: Message,
        channel: Channel,
        @Headers headers: Map<String, Any>,
        @Header(AmqpHeaders.DELIVERY_TAG) deliveryTag: Long
    ) {
        RabbitMqReply.ack(channel, deliveryTag)

        if (message.body.isEmpty()) {
            log.info { "消息内容为空，丢弃消息，queue=${message.messageProperties.consumerQueue}, messageId=${message.messageProperties.messageId}" }
            return
        }

        val queueName = message.messageProperties.consumerQueue
        val messageId = message.messageProperties.messageId

        try {
            withUser(MqThreadLocalUtils.getUser(message)) {
                if (!payload.isValidJson()) {
                    log.warn { "非法JSON，丢弃消息，queue=$queueName, messageId=$messageId, body=${payload.take(256)}" }
                    return@withUser
                }
                if (payload.isEmptyJson()) {
                    log.info { "消息内容为空（空JSON对象或数组），丢弃消息，queue=$queueName, messageId=$messageId, body=${payload.take(256)}" }
                    return@withUser
                }
                log.info { "mq处理开始, queue=$queueName, messageId=$messageId, payload=${payload.take(256)}" }
                val lockKey = buildLockKey(queueName, message, payload)
                lockComponent.doInLock(
                    key = lockKey,
                    inLockAction = {
                        messageDistributeService.initConsumerRecord(message, payload)

                        try {
                            val start = Instant.now()
                            onMessage(message, channel, headers, deliveryTag, payload)
                            val duration = Duration.between(start, Instant.now())

                            log.info {
                                "mq处理完成， queue=$queueName, messageId=$messageId, 执行时间: ${duration.toMillis()} ms (${duration.toSecondsPart()} 秒),交换机: ${message.messageProperties.receivedExchange}"
                            }
                        } catch (e: Exception) {
                            throw RuntimeException(e)
                        }

                        // 消费处理业务成功
                        messageDistributeService.completedConsumer(message)
                    },
                    unLockAction = {
                        throw RuntimeException("mq处理异常,原因是并发消费，messageId=$messageId")
                    }
                )
            }
        } catch (e: Exception) {
            log.error { "消息处理异常，queue=$queueName, messageId=$messageId, errorMsg: ${e.message}" }
            RabbitMqReply.reject(channel, deliveryTag)

            withSystemUser {
                messageDistributeService.failConsumer(message, ExceptionUtils.getStackTrace(e))
            }

            throw e
        } finally {
            MqThreadLocalUtils.cleanUserContent()
        }
    }

    /**
     * 生成锁的key，考虑messageId可能为空
     */
    private fun buildLockKey(
        queueName: String,
        message: Message,
        payload: String
    ): String {
        val messageId = message.messageProperties.messageId
        val correlationId = message.messageProperties.correlationId

        // 优先 messageId > correlationId > body MD5
        val keyPart = when {
            messageId.isNotBlank() -> messageId
            correlationId.isNotBlank() -> correlationId
            else -> DigestUtils.md5Hex(message.body)
        }
        return "$FACTORY_MQ_LOCK$queueName:$keyPart"
    }

}