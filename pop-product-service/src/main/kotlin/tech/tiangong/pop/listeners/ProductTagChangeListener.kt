package tech.tiangong.pop.listeners

import com.rabbitmq.client.Channel
import jakarta.annotation.Resource
import org.springframework.amqp.core.Message
import org.springframework.amqp.rabbit.annotation.Exchange
import org.springframework.amqp.rabbit.annotation.Queue
import org.springframework.amqp.rabbit.annotation.QueueBinding
import org.springframework.amqp.rabbit.annotation.RabbitListener
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.util.json.parseJson
import tech.tiangong.pop.common.dto.BarcodeInvChangeDto
import tech.tiangong.pop.constant.MqConstants
import tech.tiangong.pop.core.lock.LockComponent
import tech.tiangong.pop.service.mq.MessageDistributeService

/**
 * 商品标签变动监听
 */
@Slf4j
@Component
@RabbitListener(bindings = [QueueBinding(
    value = Queue(value = MqConstants.QUEUE_POP_PRODUCT_TAG_CHANGE, durable = "true"),
    exchange = Exchange(
        value = MqConstants.EXCHANGE_POP_PRODUCT_TAG_CHANGE,
        delayed = Exchange.TRUE,
        ignoreDeclarationExceptions = Exchange.TRUE
    ),
    key = [MqConstants.KEY_POP_PRODUCT_TAG_CHANGE]
)])
class ProductTagChangeListener : MqBaseListener() {
    @field:Resource(name = "messageDistributeServiceImplV2")
    override lateinit var messageDistributeService: MessageDistributeService
    @field:Resource
    override lateinit var lockComponent: LockComponent

    /**
     * 监听消费
     */
    @Transactional(rollbackFor = [Exception::class])
    override fun onMessage(message: Message, channel: Channel, headers: Map<String, Any>, deliveryTag: Long, payload: String) {
        log.info { "监听商品标签变动 - queue=${message.messageProperties.consumerQueue}, messageId=${message.messageProperties.messageId}, body=${payload.take(256)}" }
        val dto = payload.parseJson<BarcodeInvChangeDto>()
        // BarcodeInvChangeService.barcodeInvChange(dto)
    }
}