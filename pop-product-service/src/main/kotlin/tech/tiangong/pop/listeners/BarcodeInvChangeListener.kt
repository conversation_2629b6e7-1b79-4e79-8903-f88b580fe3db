package tech.tiangong.pop.listeners

import com.rabbitmq.client.Channel
import jakarta.annotation.Resource
import org.apache.commons.collections4.CollectionUtils
import org.springframework.amqp.core.Message
import org.springframework.amqp.rabbit.annotation.Exchange
import org.springframework.amqp.rabbit.annotation.Queue
import org.springframework.amqp.rabbit.annotation.QueueBinding
import org.springframework.amqp.rabbit.annotation.RabbitListener
import org.springframework.stereotype.Component
import team.aikero.blade.auth.withSystemUser
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.util.json.parseJson
import tech.tiangong.pop.common.dto.BarcodeInvChangeDto
import tech.tiangong.pop.constant.MqConstants
import tech.tiangong.pop.core.lock.LockComponent
import tech.tiangong.pop.service.mq.MessageDistributeService
import tech.tiangong.pop.service.product.BarcodeInvChangeService

/**
 * 监听条码库存变动
 * 用来监听实际的可用库存, 条码库存变动场景:
 * 1. 订单占用
 * 2. 出入库
 */
@Slf4j
@Component
@RabbitListener(bindings = [QueueBinding(
    value = Queue(value = MqConstants.QUEUE_POP_PRODUCT_INV_CHANGE, durable = "true"),
    exchange = Exchange(
        value = MqConstants.EXCHANGE_POP_PRODUCT_INV_CHANGE,
        delayed = Exchange.TRUE,
        ignoreDeclarationExceptions = Exchange.TRUE
    ),
    key = [MqConstants.KEY_POP_PRODUCT_INV_CHANGE]
)])
class BarcodeInvChangeListener : MqBaseListener() {
    @field:Resource(name = "messageDistributeServiceImplV2")
    override lateinit var messageDistributeService: MessageDistributeService
    @field:Resource
    override lateinit var lockComponent: LockComponent
    @field:Resource
    private lateinit var barcodeInvChangeService: BarcodeInvChangeService

    override fun onMessage(message: Message, channel: Channel, headers: Map<String, Any>, deliveryTag: Long, payload: String) {
        log.info { "监听条码库存变动 - queue=${message.messageProperties.consumerQueue}, messageId=${message.messageProperties.messageId}, body=${payload.take(256)}" }

        val dto = payload.parseJson(BarcodeInvChangeDto::class.java)
        if (CollectionUtils.isEmpty(dto.barcodes)) {
            log.warn { "监听条码库存变动-参数为空 - queue=${message.messageProperties.consumerQueue}, messageId=${message.messageProperties.messageId}, body=${payload.take(256)}" }
            return
        }

        withSystemUser { barcodeInvChangeService.barcodeInvChange(dto) }
    }
}