package tech.tiangong.pop.listeners

import com.alibaba.excel.context.AnalysisContext
import com.alibaba.excel.event.AnalysisEventListener
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import tech.tiangong.pop.dto.product.ImportRegionPriceRuleFreightRateDTO

/**
 * 用于解析导入AE区域定价-物流支出比例配置 Excel 文件的监听器
 */
@Slf4j
class ImportAERegionPriceRuleFreightRateExcelListener : AnalysisEventListener<Map<Int, String>>() {

    // 存储所有解析的数据
    private val freightRateList = mutableListOf<ImportRegionPriceRuleFreightRateDTO>()
    private val mapDataList = mutableListOf<Map<Int, String>>()

    // 存储表头列名与索引的映射关系
    private val headerMap = mutableMapOf<Int, String>()

    /**
     * 获取解析后的规则列表
     */
    fun getFreightRateList(): List<ImportRegionPriceRuleFreightRateDTO> = freightRateList

    override fun invokeHeadMap(headMap: Map<Int, String>, context: AnalysisContext) {
        // 记录表头信息
        headerMap.putAll(headMap)
    }

    /**
     * 处理每一行数据
     */
    override fun invoke(data: Map<Int, String>, context: AnalysisContext) {
        val freightRateDTO = ImportRegionPriceRuleFreightRateDTO()

        // 遍历当前行数据并填充到 ImportRegionPriceRuleFreightRateDTO 对象中
        for ((columnIndex, cellValue) in data) {
            // 根据表头列名匹配到 ImportRegionPriceRuleFreightRateDTO 类的字段
            headerMap[columnIndex]?.let { columnName ->
                // 根据列名将数据填充到对应的字段中
                setFieldValue(freightRateDTO, columnName, cellValue)
            }
        }

        // 将当前数据对象添加到 freightRateList 中
        freightRateList.add(freightRateDTO)
        mapDataList.add(data)

        // 打印当前规则标题
        log.info { "正在处理规则: ${freightRateDTO.shopName}-${freightRateDTO.shippingPlaceCn}-${freightRateDTO.receivingPlaceCn}" }
    }

    /**
     * 设置 ImportAeProductDTO 类字段的值
     */
    private fun setFieldValue(freightRateDTO: ImportRegionPriceRuleFreightRateDTO, columnName: String, value: String?) {
        try {
            when (columnName) {
                "店铺" -> freightRateDTO.shopName = value
                "发货地" -> freightRateDTO.shippingPlaceCn = value
                "目的地" -> freightRateDTO.receivingPlaceCn = value
                "物流支出(%)" -> freightRateDTO.rate = value
                "是否默认" -> freightRateDTO.defaultRule = value
            }
        } catch (e: Exception) {
            // 如果出现异常或者赋值失败，记录错误
            log.error { "无法为字段 $columnName 赋值 $value: ${e.message}" }
        }
    }

    /**
     * 数据解析完成后的清理操作
     */
    override fun doAfterAllAnalysed(context: AnalysisContext) {
        // 这里可以执行一些结束操作
        log.info { "所有数据处理完毕！共解析 ${freightRateList.size} 条规则数据" }
    }
}