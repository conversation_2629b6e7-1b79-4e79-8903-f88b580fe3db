package tech.tiangong.pop.listeners

import com.alibaba.fastjson2.JSON
import com.rabbitmq.client.Channel
import jakarta.annotation.Resource
import org.springframework.amqp.core.Message
import org.springframework.amqp.rabbit.annotation.Exchange
import org.springframework.amqp.rabbit.annotation.Queue
import org.springframework.amqp.rabbit.annotation.QueueBinding
import org.springframework.amqp.rabbit.annotation.RabbitListener
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import tech.tiangong.pop.common.dto.CreateProductDto
import tech.tiangong.pop.constant.MqConstants
import tech.tiangong.pop.core.lock.LockComponent
import tech.tiangong.pop.service.mq.MessageDistributeService
import tech.tiangong.pop.service.product.ProductCreateTaskService

/**
 * 批量创建商品监听
 */
@Slf4j
@Component
@RabbitListener(bindings = [QueueBinding(
    value = Queue(value = MqConstants.BATCH_QUEUE_POP_PRODUCT_CREATE, durable = "true"),
    exchange = Exchange(
        value = MqConstants.BATCH_EXCHANGE_POP_PRODUCT_CREATE,
        delayed = Exchange.TRUE,
        ignoreDeclarationExceptions = Exchange.TRUE
    ),
    key = [MqConstants.BATCH_KEY_POP_PRODUCT_CREATE]
)])
class BatchCreateProductListener : MqBaseListener() {
    @field:Resource(name = "messageDistributeServiceImplV2")
    override lateinit var messageDistributeService: MessageDistributeService
    @field:Resource
    override lateinit var lockComponent: LockComponent
    @field:Resource
    private lateinit var productCreateTaskService: ProductCreateTaskService

    @Transactional(rollbackFor = [Exception::class])
    override fun onMessage(message: Message, channel: Channel, headers: Map<String, Any>, deliveryTag: Long, payload: String) {
        log.info { "批量创建商品监听 - queue=${message.messageProperties.consumerQueue}, messageId=${message.messageProperties.messageId}, body=${payload.take(256)}" }

        // 批量创建商品，为了兼容之前通过http调用批量创建结构，因为事务原因，单个创建虽然兼容了更新的情况
        // 但是因为单个创建消息消费并发的原因，导致后续的中间保存时，前面的事务还没提交，抛出唯一索引异常Duplicate entry 'xxx' for key 'image_repository.spu_uniq_index'
        // 上游是用fastjson序列化的，保持一致
        val createProductDtoList = JSON.parseArray(payload, CreateProductDto::class.java)
        productCreateTaskService.batchCreateProductTask(createProductDtoList, false)
    }
}