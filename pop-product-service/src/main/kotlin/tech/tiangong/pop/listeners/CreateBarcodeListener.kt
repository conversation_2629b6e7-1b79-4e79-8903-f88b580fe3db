package tech.tiangong.pop.listeners

import com.rabbitmq.client.Channel
import jakarta.annotation.Resource
import org.springframework.amqp.core.Message
import org.springframework.amqp.rabbit.annotation.Exchange
import org.springframework.amqp.rabbit.annotation.Queue
import org.springframework.amqp.rabbit.annotation.QueueBinding
import org.springframework.amqp.rabbit.annotation.RabbitListener
import org.springframework.beans.BeanUtils
import org.springframework.stereotype.Component
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.util.json.parseJson
import tech.tiangong.pop.common.dto.CreateBarCodeDto
import tech.tiangong.pop.common.req.BatchCreateBarCodeReq
import tech.tiangong.pop.constant.MqConstants
import tech.tiangong.pop.core.lock.LockComponent
import tech.tiangong.pop.service.mq.MessageDistributeService
import tech.tiangong.pop.service.product.BarCodeService

/**
 * 创建条码
 */
@Slf4j
@Component
@RabbitListener(bindings = [QueueBinding(
    value = Queue(value = MqConstants.QUEUE_GENERATION_BARCODE, durable = "true"),
    exchange = Exchange(
        value = MqConstants.EXCHANGE_GENERATION_BARCODE,
        delayed = Exchange.TRUE,
        ignoreDeclarationExceptions = Exchange.TRUE
    ),
    key = [MqConstants.KEY_GENERATION_BARCODE]
)])
class CreateBarcodeListener : MqBaseListener() {
    @field:Resource(name = "messageDistributeServiceImplV2")
    override lateinit var messageDistributeService: MessageDistributeService
    @field:Resource
    override lateinit var lockComponent: LockComponent
    @field:Resource
    private lateinit var barCodeService: BarCodeService
    override fun onMessage(message: Message, channel: Channel, headers: Map<String, Any>, deliveryTag: Long, payload: String) {
        log.info { "创建条码监听 - queue=${message.messageProperties.consumerQueue}, messageId=${message.messageProperties.messageId}, body=${payload.take(256)}" }

        val createBarCodeDto = payload.parseJson<CreateBarCodeDto>()
        val batchCreateBarCodeReq = BatchCreateBarCodeReq().apply {
            BeanUtils.copyProperties(createBarCodeDto, this)
            skcCode = createBarCodeDto.skc
        }

        val list = mutableListOf(batchCreateBarCodeReq)
        barCodeService.createBarcodeByForce(list)
    }
}