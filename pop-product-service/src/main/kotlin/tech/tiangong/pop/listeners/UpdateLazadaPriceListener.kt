package tech.tiangong.pop.listeners
import com.rabbitmq.client.Channel
import jakarta.annotation.Resource
import org.springframework.amqp.core.Message
import org.springframework.amqp.rabbit.annotation.Exchange
import org.springframework.amqp.rabbit.annotation.Queue
import org.springframework.amqp.rabbit.annotation.QueueBinding
import org.springframework.amqp.rabbit.annotation.RabbitListener
import org.springframework.stereotype.Component
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.util.json.parseJsonList
import tech.tiangong.pop.component.lazada.PublishToLazadaComponent
import tech.tiangong.pop.constant.MqConstants
import tech.tiangong.pop.core.lock.LockComponent
import tech.tiangong.pop.dto.mq.LazadaPriceMqDto
import tech.tiangong.pop.service.mq.MessageDistributeService

/**
 * 更新lazada价格
 * @date 2024/11/22 18:41
 */
@Slf4j
@Component
@RabbitListener(bindings = [QueueBinding(
    value = Queue(value = MqConstants.QUEUE_POP_LAZADA_PRICE_UPDATE, durable = "true"),
    exchange = Exchange(
        value = MqConstants.EXCHANGE_POP_LAZADA_PRICE_UPDATE,
        delayed = Exchange.TRUE,
        ignoreDeclarationExceptions = Exchange.TRUE
    ),
    key = [MqConstants.KEY_POP_LAZADA_PRICE_UPDATE]
)])
class UpdateLazadaPriceListener : MqBaseListener() {
    @field:Resource(name = "messageDistributeServiceImplV2")
    override lateinit var messageDistributeService: MessageDistributeService
    @field:Resource
    override lateinit var lockComponent: LockComponent
    @field:Resource
    private lateinit var publishToLazadaComponent: PublishToLazadaComponent

    override fun onMessage(message: Message, channel: Channel, headers: Map<String, Any>, deliveryTag: Long, payload: String) {
        log.info { "更新lazada价格 - queue=${message.messageProperties.consumerQueue}, messageId=${message.messageProperties.messageId}, body=${payload.take(256)}" }

        try {
            val lazadaPriceMqDtoList = payload.parseJsonList(LazadaPriceMqDto::class.java)
            publishToLazadaComponent.processLazadaPriceUpdate(lazadaPriceMqDtoList)
        } catch (e: Exception) {
            log.error(e) { "更新lazada价格 异常 - queue=${message.messageProperties.consumerQueue}, messageId=${message.messageProperties.messageId}, body=${payload.take(256)}"  }
        }
    }
}