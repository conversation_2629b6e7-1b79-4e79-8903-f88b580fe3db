package tech.tiangong.pop.listeners

import com.alibaba.excel.context.AnalysisContext
import com.alibaba.excel.event.AnalysisEventListener
import org.apache.commons.lang3.StringUtils
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import tech.tiangong.pop.dto.product.ImportTemuProductDTO

/**
 * 用于解析导入Temu商品 Excel 文件的监听器
 */
@Slf4j
class ImportTemuProductExcelListener : AnalysisEventListener<Map<Int, String>>() {

    // 存储所有解析的数据
    private val productList = mutableListOf<ImportTemuProductDTO>()
    private val mapDataList = mutableListOf<Map<Int, String>>()

    // 存储表头列名与索引的映射关系
    private val headerMap = mutableMapOf<Int, String>()

    /**
     * 获取解析后的商品列表
     */
    fun getProductList(): List<ImportTemuProductDTO> = productList

    override fun invokeHeadMap(headMap: Map<Int, String>, context: AnalysisContext) {
        // 记录表头信息
        headerMap.putAll(headMap)
    }

    /**
     * 处理每一行数据
     */
    override fun invoke(data: Map<Int, String>, context: AnalysisContext) {
        val productDTO = ImportTemuProductDTO()

        // 遍历当前行数据并填充到 ImportProductDTO 对象中
        for ((columnIndex, cellValue) in data) {
            // 根据表头列名匹配到 ImportProductDTO 类的字段
            val columnName = headerMap[columnIndex]

            columnName?.let {
                // 如果是动态列（以"属性-"开头），将其存入 dynamicAttributes 中
                if (it.startsWith("属性-")) {
                    // 去掉"属性-"前缀作为key，值为对应的列值
                    val dynamicKey = it.substring(3) // 去除 "属性-" 前缀
                    productDTO.addDynamicAttribute(dynamicKey, cellValue)
                } else {
                    // 根据列名将数据填充到对应的字段中
                    setFieldValue(productDTO, it, cellValue)
                }
            }
        }

        // 将当前数据对象添加到 productList 中
        productList.add(productDTO)
        mapDataList.add(data)

        // 打印当前商品标题
        log.info { "正在处理商品: ${productDTO.productName}" }
    }

    /**
     * 设置 ImportProductDTO 类字段的值
     */
    private fun setFieldValue(productDTO: ImportTemuProductDTO, columnName: String, value: String?) {
        try {
            when (columnName) {
                "*SPU" -> productDTO.spuCode = if (StringUtils.isNotEmpty(value)) value?.trim() else ""
                "上架店铺" -> productDTO.listingShopName = value
                "*品类" -> productDTO.categoryName = value
                "商品名称" -> productDTO.productName = value
                "英文名称" -> productDTO.productNameEn = value
                "素材语言" -> productDTO.materialLanguages = value
                "(内部)商品类目" -> productDTO.categoryName = value
                "商品产地" -> productDTO.originPlaceName = value
                "SKC货号" -> productDTO.skcCode = value
                "库存" -> productDTO.stockQuantity = value?.toLong()
                "颜色(主规格)" -> productDTO.mainColor = value
                "尺码组别" -> productDTO.sizeGroup = value
                "规格类型2" -> productDTO.specificationType = value
                "尺码" -> productDTO.size = value
                "申报价(USD)" -> productDTO.declaredPriceUsd = value?.toBigDecimal()
                "申报价(CNY)" -> productDTO.declaredPriceCny = value?.toBigDecimal()
                "建议零售价(USD)" -> productDTO.retailPriceUsd = value?.toBigDecimal()
                "建议零售价(CNY)" -> productDTO.retailPriceCny = value?.toBigDecimal()
                "SKU分类" -> productDTO.skuClassification = value
                "SKU数量" -> productDTO.quantity = value?.toInt()
                "SKU数量单位" -> productDTO.unit = value
                "最长边(cm)" -> productDTO.maxLength = value?.toBigDecimal()
                "次长边(cm)" -> productDTO.middleLength = value?.toBigDecimal()
                "最短边(cm)" -> productDTO.minLength = value?.toBigDecimal()
                "重量(g)" -> productDTO.weight = value?.toBigDecimal()
                "商品轮播图1" -> productDTO.productImage1
                "商品轮播图2" -> productDTO.productImage2
                "商品轮播图3" -> productDTO.productImage3
                "商品轮播图4" -> productDTO.productImage4
                "商品轮播图5" -> productDTO.productImage5
                "详情图" -> productDTO.detailImages
            }
        } catch (e: Exception) {
            // 如果出现异常或者赋值失败，记录错误
            log.error { "无法为字段 $columnName 赋值 $value" }
        }
    }

    /**
     * 数据解析完成后的清理操作
     */
    override fun doAfterAllAnalysed(context: AnalysisContext) {
        // 这里可以执行一些结束操作
        log.info { "所有数据处理完毕！" }
    }
}