package tech.tiangong.pop.listeners

import org.springframework.retry.RetryCallback
import org.springframework.retry.RetryContext
import org.springframework.retry.RetryListener
import org.springframework.stereotype.Component
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log

/**
 * 上传重试监听器
 */
@Slf4j
@Component
class UploadRetryListener : RetryListener {

    override fun <T, E : Throwable> open(context: RetryContext, callback: RetryCallback<T, E>): <PERSON><PERSON><PERSON> {
        return true
    }

    override fun <T, E : Throwable> close(context: RetryContext, callback: RetryCallback<T, E>, throwable: Throwable?) {
        if (throwable == null) {
            log.info { "文件上传重试成功, 重试次数: ${context.retryCount}" }
        }
    }

    override fun <T, E : Throwable> onError(context: Retry<PERSON>ontext, callback: Retry<PERSON>allback<T, E>, throwable: Throwable) {
        log.warn { "文件上传重试失败: 第${context.retryCount}次重试, error=${throwable.message}" }
    }
}