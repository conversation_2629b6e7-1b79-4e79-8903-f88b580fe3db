<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tech.tiangong.pop.dao.mapper.ProductMapper">


    <select id="selectProductSyncLogsCount" resultType="int">
        SELECT
           count(*)
        FROM
            product p
                INNER JOIN product_sync_log log ON log.product_id = p.product_id
                INNER JOIN product_template_lazada_spu s ON s.product_id = p.product_id
        WHERE
            p.deleted = 0
          AND log.deleted = 0
          AND s.deleted = 0
          AND log.log_type = 0
          AND log.op_type = 1
          AND log.created_time  &gt;= #{req.publishDateStart}
          AND log.created_time  &lt;= #{req.publishDateEnd}
    </select>

    <select id="selectAwaitPublishFailProduct" resultType="tech.tiangong.pop.dto.product.ExportProductPublishFailedDto">
        SELECT log.platform_name,
               p.spu_code,
               log.shop_name,
               log.error_msg,
               log.created_time AS publish_date
        FROM product_sync_log log
                 JOIN product p ON log.product_id = p.product_id
        WHERE p.deleted = 0
          AND log.deleted = 0
          AND log.log_type = 0
          AND log.op_type = 1
          AND log.created_time  &gt;= #{req.publishDateStart}
          AND log.created_time  &lt;= #{req.publishDateEnd}
            <if test="req.platformName != null and req.platformName !=''">
              AND log.platform_name = #{req.platformName}
            </if>
    </select>

    <select id="productTotailCount" resultType="tech.tiangong.pop.resp.product.ProductCountResp">
            SELECT
            <!-- 上架和下架统计 -->
            (SELECT COUNT(1)
            FROM sale_goods
            WHERE publish_state = 1 AND deleted = 0  and platform_product_id IS NOT NULL
            <if test="req.shopId != null and req.shopId !=''">AND shop_id = #{req.shopId}</if>
            <if test="req.country != null and req.country != ''">AND country = #{req.country}</if>
            ) AS activeCount,

            (SELECT COUNT(1)
            FROM sale_goods
            WHERE publish_state = 2 AND deleted = 0  and platform_product_id IS NOT NULL
        <if test="req.shopId != null and req.shopId !=''">AND shop_id = #{req.shopId}</if>
            <if test="req.country != null and req.country != ''">AND country = #{req.country}</if>
            ) AS inActiveCount,

            <!-- 按国家统计 platform_product_id 不为空的数量 -->
            (SELECT JSON_ARRAYAGG(countryData)
            FROM (
            SELECT JSON_OBJECT(
            'country', sg.country,
            'count', COUNT(1)
            ) AS countryData
            FROM sale_goods sg
            WHERE sg.platform_product_id IS NOT NULL AND sg.deleted = 0 and sg.publish_state in (1,2)
            GROUP BY sg.country
            ) t) AS countryDataStr;

    </select>

    <select id="selectAttributeIds" resultType="tech.tiangong.pop.resp.product.AttributeIdValueResp">
        SELECT
        attr.attribute_id AS attributeId,
        val.attribute_value_id AS attributeValueId
        FROM
        publish_attribute attr
        JOIN
        publish_attribute_value val
        ON
        attr.attribute_id = val.attribute_id
        WHERE
        (attr.attribute_name, val.attribute_value) IN (
        <foreach collection="attributePairs" item="pair" separator=",">
            (#{pair.attributeName}, #{pair.attributeValue})
        </foreach>)
        AND attr.deleted = 0
        AND val.deleted = 0
    </select>

    <select id="selectAttributes" resultType="tech.tiangong.pop.resp.product.AttributePairResp">
        SELECT
        attr.attribute_name AS attributeName,
        val.attribute_value AS attributeValue
        FROM
        publish_attribute attr,
        publish_attribute_value val
        WHERE
        attr.attribute_id = val.attribute_id
        AND (attr.attribute_id, val.attribute_value_id) IN (
        <foreach collection="attributeValuePairs" item="pair" separator=",">
            (#{pair.attributeId}, #{pair.attributeValueId})
        </foreach>)
        AND attr.deleted = 0
        AND val.deleted = 0
    </select>

    <sql id="productSelectFieldsByExport">
        p.product_id AS productId,
        p.supply_mode AS supplyMode,
        p.cmp_sale_price AS cmpSalePrice,
        p.cmp_retail_price AS cmpRetailPrice,
        p.waves AS waves,
        ps.local_price AS localPrice,
        ps.product_skc_id AS productSkcId,
        p.spu_code AS spuCode,
        p.product_title AS productTitle,
        p.countrys AS countrys,
        p.category_name AS categoryName,
        p.shop_name AS shopName,
        ps.skc AS skc,
        ps.color AS color,
        p.package_weight AS packageWeight,
        p.size_group_name AS sizeGroupName,
        p.pricing_type as pricingType,
        p.spot_type_code as spotTypeCode,
        ps.purchase_price as purchasePrice
    </sql>


    <select id="listProductByExport" resultType="tech.tiangong.pop.resp.product.ProductExportResp">
        SELECT
        <include refid="productSelectFieldsByExport" />
        FROM product_skc ps
        LEFT JOIN product p ON ps.product_id = p.product_id  <!-- 先从 product_skc 连接到 product -->
        WHERE
        p.deleted = 0 AND ps.deleted = 0 AND ps.state = 1
        <if test="req.skcList != null and req.skcList.size > 1">
            AND ps.skc IN
            <foreach item="item" index="index" collection="req.skcList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

        <if test="req.skcList != null and req.skcList.size == 1">
            AND ps.skc
            <foreach item="item" index="index" collection="req.skcList">
                LIKE CONCAT('%', #{item}, '%')
            </foreach>
        </if>

        <if test="req.spuList != null and req.spuList.size > 1">
            AND p.spu_code IN
            <foreach item="item" index="index" collection="req.spuList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="req.spuList != null and req.spuList.size == 1">
            AND p.spu_code
            <foreach item="item" index="index" collection="req.spuList">
                LIKE CONCAT('%', #{item}, '%')
            </foreach>
        </if>
        <if test="req.updateFlag != null and req.updateFlag != ''">
            AND p.is_update = #{req.updateFlag}
        </if>
        <if test="req.creatorName != null and req.creatorName != ''">
            AND p.creator_name LIKE CONCAT('%', #{req.creatorName}, '%')
        </if>
        <if test="req.reviserName != null and req.reviserName != ''">
            AND p.reviser_name LIKE CONCAT('%', #{req.reviserName}, '%')
        </if>
        <if test="req.publishAuthor != null and req.publishAuthor != ''">
            AND p.publish_author LIKE CONCAT('%', #{req.publishAuthor}, '%')
        </if>
        <if test="req.selectStyleName != null and req.selectStyleName != ''">
            AND p.select_style_name  LIKE CONCAT('%', #{req.selectStyleName}, '%')
        </if>
        <if test="req.shopName != null and req.shopName != ''">
            AND p.shop_name  LIKE CONCAT('%', #{req.shopName}, '%')
        </if>
        <if test="req.categoryCode != null and req.categoryCode != ''">
            AND p.category_code = #{req.categoryCode}
        </if>
        <if test="req.shopId != null and req.shopId != ''">
            AND p.shop_id = #{req.shopId}
        </if>
        <if test="req.supplyMode != null  and req.supplyMode != ''">
            AND p.supply_mode = #{req.supplyMode}
        </if>
        <if test="req.createdTimeStart != null and req.createdTimeEnd != null  ">
            AND  p.created_time &gt;= #{req.createdTimeStart}  AND p.created_time &lt;= #{req.createdTimeEnd}
        </if>
        <if test="req.reviserTimeStart != null and req.reviserTimeEnd != null  ">
            AND  p.revised_time &gt;= #{req.reviserTimeStart}  AND p.revised_time &lt;= #{req.reviserTimeEnd}
        </if>

        <if test="req.waves != null and req.waves != ''">
            AND p.waves = #{req.waves}
        </if>
        <if test="req.isError != null">
            AND p.is_error = #{req.isError}
        </if>

        <!-- Include the optimized tag filtering condition -->
        <include refid="tagFilterCondition" />

        ORDER BY p.revised_time DESC
    </select>

    <select id="getManagePage" resultType="tech.tiangong.pop.dao.entity.Product">
        SELECT
            p.*
        FROM product p
                LEFT JOIN product_skc ps ON p.product_id = ps.product_id
                LEFT JOIN product_publish_store pps on p.product_id = pps.product_id
        <where>
            p.deleted = 0 and ps.deleted = 0
            <if test="req.shopName != null and req.shopName != ''">
                AND p.shop_name LIKE CONCAT('%', #{req.shopName}, '%')
            </if>
            <if test="req.country != null and req.country != ''">
                AND p.countrys LIKE CONCAT('%', #{req.country}, '%')
            </if>
            <if test="req.supplyMode != null and req.supplyMode != ''">
                AND p.supply_mode = #{req.supplyMode}
            </if>
            <if test="req.supplyModeList != null and req.supplyModeList.size > 0">
                AND p.supply_mode IN
                <foreach item="item" index="index" collection="req.supplyModeList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="req.selectStyleName != null and req.selectStyleName != ''">
                AND p.select_style_name  LIKE CONCAT('%', #{req.selectStyleName}, '%')
            </if>
            <if test="req.spuList != null and req.spuList.size > 1">
                AND p.spu_code IN
                <foreach item="item" index="index" collection="req.spuList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="req.spuList != null and req.spuList.size == 1">
                AND p.spu_code
                <foreach item="item" index="index" collection="req.spuList">
                    LIKE CONCAT('%', #{item}, '%')
                </foreach>
            </if>
            <if test="req.waves != null and req.waves != ''">
                AND p.waves = #{req.waves}
            </if>
            <if test="req.wavesList != null and req.wavesList.size > 0">
                AND p.waves IN
                <foreach item="item" index="index" collection="req.wavesList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="req.creatorName != null and req.creatorName != ''">
                AND p.creator_name LIKE CONCAT('%', #{req.creatorName}, '%')
            </if>
            <if test="req.createdTimeStart != null and req.createdTimeEnd != null  ">
                AND p.created_time BETWEEN #{req.createdTimeStart} AND #{req.createdTimeEnd}
            </if>
            <if test="req.skcList != null and req.skcList.size > 1">
                AND ps.skc IN
                <foreach item="item" index="index" collection="req.skcList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="req.skcList != null and req.skcList.size == 1">
                AND ps.skc
                <foreach item="item" index="index" collection="req.skcList">
                    LIKE CONCAT('%', #{item}, '%')
                </foreach>
            </if>
            <if test="req.updateFlag != null and req.updateFlag != ''">
                AND p.is_update = #{req.updateFlag}
            </if>
            <if test="req.submitStatus != null ">
                AND p.is_sync_platform = #{req.submitStatus}
            </if>
            <if test="req.clothingStyleCode != null and req.clothingStyleCode != ''">
                AND p.clothing_style_code = #{req.clothingStyleCode}
            </if>
            <if test="req.planningType != null ">
                AND p.planning_type = #{req.planningType}
            </if>
            <if test="req.marketCode != null and req.marketCode != ''">
                AND p.market_code = #{req.marketCode}
            </if>
            <if test="req.marketSeriesCode != null and req.marketSeriesCode != ''">
                AND p.market_series_code = #{req.marketSeriesCode}
            </if>
            <if test="req.categoryCodeList != null and req.categoryCodeList.size > 0">
                AND p.category_code IN
                <foreach item="item" index="index" collection="req.categoryCodeList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="req.goodsTypeList != null and req.goodsTypeList.size > 0">
                AND p.goods_type IN
                <foreach item="item" index="index" collection="req.goodsTypeList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="req.platformIdList != null and req.platformIdList.size > 0">
                AND
                <foreach item="item" index="index" collection="req.platformIdList" open="(" separator="or" close=")">
                    JSON_CONTAINS(p.submit_platform, CONVERT(${item.platformId},CHAR), '$')
                </foreach>
            </if>
            <if test="req.imagePackageStateList != null and req.imagePackageStateList.size > 0">
                AND p.image_package_state IN
                <foreach item="item" index="index" collection="req.imagePackageStateList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="req.planAuditStateList != null and req.planAuditStateList.size > 0">
                AND p.plan_audit_state IN
                <foreach item="item" index="index" collection="req.planAuditStateList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="req.videoStateList != null and req.videoStateList.size > 0">
                AND p.video_state IN
                <foreach item="item" index="index" collection="req.videoStateList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="req.costPriceMin != null">
                AND ps.cost_price <![CDATA[ >= ]]> #{req.costPriceMin}
            </if>
            <if test="req.costPriceMax != null">
                AND ps.cost_price <![CDATA[ <= ]]> #{req.costPriceMax}
            </if>
            <if test="req.planAuditorId != null ">
                AND p.plan_auditor_id = #{req.planAuditorId}
            </if>
            <if test="req.shopIds != null and req.shopIds.size > 0">
                AND pps.shop_id IN
                <foreach item="item" index="index" collection="req.shopIds" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="req.onSale != null and req.onSale == 1">
                AND p.publish_state = 1
            </if>
            <if test="req.onSale != null and req.onSale == 0">
                AND p.publish_state in (0,2)
            </if>
            <if test="req.productType != null">
                AND p.product_type = #{req.productType}
            </if>
            <if test="req.resourceState != null">
                AND p.resource_state = #{req.resourceState}
            </if>
            <if test="req.spuCanceled != null">
                AND p.spuCanceled = #{req.spuCanceled}
            </if>
            <!-- Include the tag filtering condition -->
            <include refid="tagFilterCondition" />
        </where>
        GROUP BY p.product_id
        ORDER BY p.revised_time DESC
    </select>


    <!-- Define the SQL fragment for tag filtering -->
    <sql id="tagFilterCondition">
        <if test="req.tagCodes != null and req.tagCodes.size > 0">
            AND (
                <!-- Check product level tags -->
                EXISTS (
                    SELECT 1 FROM product_tag pt_prod
                    WHERE pt_prod.target_id = p.product_id
                    AND pt_prod.target_type = 'PRODUCT_ID'
                    AND pt_prod.deleted = 0
                    AND (
                        <foreach collection="req.tagCodes" item="valueList" index="key" separator=" OR ">
                            (pt_prod.tag_key = #{key} AND pt_prod.tag_value IN
                            <foreach collection="valueList" item="value" open="(" close=")" separator=",">
                                #{value}
                            </foreach>
                            )
                        </foreach>
                    )
                )

                <!-- Check Lazada SPU level tags -->
                OR EXISTS (
                    SELECT 1 FROM product_template_lazada_spu ptls
                    JOIN product_tag pt_lazada ON ptls.lazada_spu_id = pt_lazada.target_id
                    WHERE ptls.product_id = p.product_id
                    AND ptls.deleted = 0
                    AND pt_lazada.target_type = 'LAZADA_SPU_ID'
                    AND pt_lazada.deleted = 0
                    AND (
                        <foreach collection="req.tagCodes" item="valueList" index="key" separator=" OR ">
                            (pt_lazada.tag_key = #{key} AND pt_lazada.tag_value IN
                            <foreach collection="valueList" item="value" open="(" close=")" separator=",">
                                #{value}
                            </foreach>
                            )
                        </foreach>
                    )
                )

                <!-- Check AE SPU level tags -->
                OR EXISTS (
                    SELECT 1 FROM product_template_ae_spu ptas
                    JOIN product_tag pt_ae ON ptas.ae_spu_id = pt_ae.target_id
                    WHERE ptas.product_id = p.product_id
                    AND ptas.deleted = 0
                    AND pt_ae.target_type = 'AE_SPU_ID'
                    AND pt_ae.deleted = 0
                    AND (
                        <foreach collection="req.tagCodes" item="valueList" index="key" separator=" OR ">
                            (pt_ae.tag_key = #{key} AND pt_ae.tag_value IN
                            <foreach collection="valueList" item="value" open="(" close=")" separator=",">
                                #{value}
                            </foreach>
                            )
                        </foreach>
                    )
                )

                <!-- Check Temu SPU level tags -->
                OR EXISTS (
                    SELECT 1 FROM product_template_temu_spu ptts
                    JOIN product_tag pt_temu ON ptts.temu_spu_id = pt_temu.target_id
                    WHERE ptts.product_id = p.product_id
                    AND ptts.deleted = 0
                    AND pt_temu.target_type = 'TEMU_SPU_ID'
                    AND pt_temu.deleted = 0
                    AND (
                        <foreach collection="req.tagCodes" item="valueList" index="key" separator=" OR ">
                            (pt_temu.tag_key = #{key} AND pt_temu.tag_value IN
                            <foreach collection="valueList" item="value" open="(" close=")" separator=",">
                                #{value}
                            </foreach>
                            )
                        </foreach>
                    )
                )
            )
        </if>
    </sql>

    <sql id="productSelectFields">
         p.product_id AS productId,
         sg.sale_goods_id AS saleGoodsId,
         sg.platform_id AS platformId,
         sg.channel_id AS channelId,
    CASE
        WHEN #{req.country} IS NOT NULL   AND #{req.country} != '' THEN sg.country
        ELSE ''
        END AS country,
        p.main_img_url AS mainImgUrl,
        p.supply_mode AS supplyMode,
        p.waves AS waves,
        p.spu_code AS spuCode,
        p.price_exception AS priceException,
        p.product_title AS productTitle,
        p.countrys AS countrys,
        p.category_code AS categoryCode,
        p.category_name AS categoryName,
         sg.shop_id AS shopId,
         sg.shop_name AS shopName,
         p.is_update AS `update`,
        CASE
        WHEN #{req.country} IS NOT NULL  AND #{req.country} != '' THEN sg.publish_state
        ELSE p.publish_state
        END AS publisheState,
        CASE
        WHEN #{req.country} IS NOT NULL  AND #{req.country} != '' THEN sg.platform_sync_state
        ELSE p.platform_sync_state
        END AS platformUpdateState,
        p.is_sync_platform AS isSyncPlatform,
        p.created_time AS createdTime,
        p.creator_id AS creatorId,
        p.creator_name AS creatorName,
        p.revised_time AS revisedTime,
        p.reviser_id AS reviserId,
        p.reviser_name AS reviserName,
        p.is_error,
        p.error_info,
        p.tag_available_inv,
        p.tag_reg_clear,
        p.publish_time,
        p.publish_user_name
    </sql>


    <select id="pageSaleGoodsByInner" resultType="tech.tiangong.pop.common.resp.SaleGoddsPageResp">

        SELECT
            p.product_id AS productId,
            p.size_group_name AS sizeGroupName,
            p.size_group_code AS sizeGroupCode,
            sg.sale_goods_id AS saleGoodsId,
            sg.platform_id AS platformId,
            sg.channel_id AS channelId,
            sg.country AS country,
            sg.platform_product_id AS platformProductId,
            DATEDIFF(CURRENT_DATE(), sg.publish_time) AS publishDay,
            DATEDIFF(CURRENT_DATE(), p.publish_time) AS ascPublishDay,
            NULL AS frontUrl,
            p.main_img_url AS mainImgUrl,
            p.spu_code AS spuCode,
            sg.publish_time AS firstPublishTime,
            sg.product_title AS productTitle,
            p.category_code AS categoryCode,
            p.category_name AS categoryName,
            p.supply_mode AS supplyMode,
            ssku.sale_price AS salePrice,
            ssku.retail_price AS retailPrice,
            sg.shop_id AS shopId,
            sp.short_code AS shopCode,
            sg.shop_name AS shopName,
            sg.latest_publish_time AS publishTime,
            skc.skc AS skc,
            skc.color AS color,
            skc.platform_color AS platformColor,
            skc.pictures AS pictures,
            skc.cb_price AS cbPrice,
            skc.local_price AS localPrice,
            ssku.sku_code AS skuCode,
            ssku.stock_quantity AS stockQuantity,
            ssku.seller_sku AS sellerSku,
            ssku.shop_sku AS shopSku,
            ssku.size_name AS sizeName,
            ssku.platform_sku_id AS platformSkuId,
            ssku.sale_price AS skuSalePrice,
            ssku.retail_price AS skuRetailPrice,
            ssku.publish_state AS skuPublishState,
            p.publish_state AS publisheState,
            sg.publish_state AS ascPublisheState,
            ssku.barcode AS barcode
        FROM sale_sku ssku
            join sale_goods sg on sg.sale_goods_id = ssku.sale_goods_id
            JOIN product p ON sg.product_id = p.product_id
            JOIN sale_skc skc ON ssku.sale_skc_id = skc.sale_skc_id
            JOIN shop sp ON sg.shop_id = sp.shop_id
        WHERE
            p.deleted = 0 AND
            sg.deleted = 0 AND
            skc.deleted = 0 AND
            ssku.deleted = 0 AND
            (ssku.barcode is not null OR ssku.barcodes is not null) AND
            sg.platform_product_id is not null AND
            ssku.seller_sku is not null
        <if test="req.publishTimeStart != null and req.publishTimeEnd != null">
            AND sg.publish_time BETWEEN #{req.publishTimeStart} AND #{req.publishTimeEnd}
        </if>
        <if test="req.skuState != null  ">
            AND ssku.publish_state = #{req.skuState}
        </if>
        <if test="req.shopSkuList != null and req.shopSkuList.size > 0">
            AND ssku.shop_sku IN
            <foreach item="item" index="index" collection="req.shopSkuList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="req.skcList != null and req.skcList.size > 0">
            AND skc.skc IN
            <foreach item="item" index="index" collection="req.skcList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="req.barcodeList != null and req.barcodeList.size > 0">
            AND ssku.barcode IN
            <foreach item="item" index="index" collection="req.barcodeList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="req.sellerSkuList != null and req.sellerSkuList.size > 0">
            AND ssku.seller_sku IN
            <foreach item="item" index="index" collection="req.sellerSkuList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="pageAeSaleGoodsByInner" resultType="tech.tiangong.pop.common.resp.SaleGoddsPageResp">

        SELECT
            p.product_id AS productId,
            p.size_group_name AS sizeGroupName,
            p.size_group_code AS sizeGroupCode,
            sg.sale_goods_id AS saleGoodsId,
            sg.platform_id AS platformId,
            sg.channel_id AS channelId,
            sg.platform_product_id AS platformProductId,
            DATEDIFF(CURRENT_DATE(), sg.publish_time) AS publishDay,
            DATEDIFF(CURRENT_DATE(), p.publish_time) AS ascPublishDay,
            NULL AS frontUrl,
            p.main_img_url AS mainImgUrl,
            p.spu_code AS spuCode,
            sg.publish_time AS firstPublishTime,
            sg.product_title AS productTitle,
            p.category_code AS categoryCode,
            p.category_name AS categoryName,
            p.supply_mode AS supplyMode,
            ssku.sale_price AS salePrice,
            ssku.retail_price AS retailPrice,
            sg.shop_id AS shopId,
            sp.short_code AS shopCode,
            sg.shop_name AS shopName,
            sg.latest_publish_time AS publishTime,
            skc.skc AS skc,
            skc.color AS color,
            skc.platform_color AS platformColor,
            skc.pictures AS pictures,
            skc.cb_price AS cbPrice,
            skc.local_price AS localPrice,
            ssku.sku_code AS skuCode,
            ssku.stock_quantity AS stockQuantity,
            ssku.seller_sku AS sellerSku,
            ssku.shop_sku AS shopSku,
            ssku.size_name AS sizeName,
            ssku.platform_sku_id AS platformSkuId,
            ssku.sale_price AS skuSalePrice,
            ssku.retail_price AS skuRetailPrice,
            ssku.publish_state AS skuPublishState,
            p.publish_state AS publisheState,
            sg.publish_state AS ascPublisheState,
            ssku.barcode AS barcode
        FROM ae_sale_sku ssku
            join ae_sale_goods sg on sg.sale_goods_id = ssku.sale_goods_id
            JOIN product p ON sg.product_id = p.product_id
            JOIN ae_sale_skc skc ON ssku.sale_skc_id = skc.sale_skc_id
            JOIN shop sp ON sg.shop_id = sp.shop_id
        WHERE
            p.deleted = 0 AND
            sg.deleted = 0 AND
            skc.deleted = 0 AND
            ssku.deleted = 0 AND
            (ssku.barcode is not null OR ssku.barcodes is not null) AND
            sg.platform_product_id is not null AND
            ssku.seller_sku is not null
        <if test="req.publishTimeStart != null and req.publishTimeEnd != null">
            AND sg.publish_time BETWEEN #{req.publishTimeStart} AND #{req.publishTimeEnd}
        </if>
        <if test="req.skuState != null  ">
            AND ssku.publish_state = #{req.skuState}
        </if>
        <if test="req.shopSkuList != null and req.shopSkuList.size > 0">
            AND ssku.shop_sku IN
            <foreach item="item" index="index" collection="req.shopSkuList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="req.skcList != null and req.skcList.size > 0">
            AND skc.skc IN
            <foreach item="item" index="index" collection="req.skcList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="req.barcodeList != null and req.barcodeList.size > 0">
            AND ssku.barcode IN
            <foreach item="item" index="index" collection="req.barcodeList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="req.sellerSkuList != null and req.sellerSkuList.size > 0">
            AND ssku.seller_sku IN
            <foreach item="item" index="index" collection="req.sellerSkuList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="pageTemuSaleGoodsByInner" resultType="tech.tiangong.pop.common.resp.SaleGoddsPageResp">

        SELECT
            p.product_id AS productId,
            p.size_group_name AS sizeGroupName,
            p.size_group_code AS sizeGroupCode,
            sg.sale_goods_id AS saleGoodsId,
            sg.platform_id AS platformId,
            sg.channel_id AS channelId,
            sg.platform_product_id AS platformProductId,
            DATEDIFF(CURRENT_DATE(), sg.publish_time) AS publishDay,
            DATEDIFF(CURRENT_DATE(), p.publish_time) AS ascPublishDay,
            NULL AS frontUrl,
            p.main_img_url AS mainImgUrl,
            p.spu_code AS spuCode,
            sg.publish_time AS firstPublishTime,
            sg.product_name AS productTitle,
            p.category_code AS categoryCode,
            p.category_name AS categoryName,
            p.supply_mode AS supplyMode,
            ssku.declared_price AS salePrice,
            ssku.declared_price AS retailPrice,
            sg.shop_id AS shopId,
            sp.short_code AS shopCode,
            sg.shop_name AS shopName,
            sg.latest_publish_time AS publishTime,
            skc.skc AS skc,
            skc.color AS color,
            skc.platform_color AS platformColor,
            skc.pictures AS pictures,
            skc.cb_price AS cbPrice,
            skc.local_price AS localPrice,
            ssku.sku_code AS skuCode,
            ssku.stock_quantity AS stockQuantity,
            ssku.seller_sku AS sellerSku,
            ssku.shop_sku AS shopSku,
            ssku.size_name AS sizeName,
            ssku.platform_sku_id AS platformSkuId,
            ssku.declared_price AS skuSalePrice,
            ssku.declared_price AS skuRetailPrice,
            ssku.publish_state AS skuPublishState,
            p.publish_state AS publisheState,
            sg.publish_state AS ascPublisheState,
            ssku.barcode AS barcode
        FROM temu_sale_sku ssku
            join temu_sale_goods sg on sg.sale_goods_id = ssku.sale_goods_id
            JOIN product p ON sg.product_id = p.product_id
            JOIN temu_sale_skc skc ON ssku.sale_skc_id = skc.sale_skc_id
            JOIN shop sp ON sg.shop_id = sp.shop_id
        WHERE
            p.deleted = 0 AND
            sg.deleted = 0 AND
            skc.deleted = 0 AND
            ssku.deleted = 0 AND
            (ssku.barcode is not null OR ssku.barcodes is not null) AND
            sg.platform_product_id is not null AND
            ssku.seller_sku is not null
        <if test="req.publishTimeStart != null and req.publishTimeEnd != null">
            AND sg.publish_time BETWEEN #{req.publishTimeStart} AND #{req.publishTimeEnd}
        </if>
        <if test="req.skuState != null  ">
            AND ssku.publish_state = #{req.skuState}
        </if>
        <if test="req.shopSkuList != null and req.shopSkuList.size > 0">
            AND ssku.shop_sku IN
            <foreach item="item" index="index" collection="req.shopSkuList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="req.skcList != null and req.skcList.size > 0">
            AND skc.skc IN
            <foreach item="item" index="index" collection="req.skcList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="req.barcodeList != null and req.barcodeList.size > 0">
            AND ssku.barcode IN
            <foreach item="item" index="index" collection="req.barcodeList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="req.sellerSkuList != null and req.sellerSkuList.size > 0">
            AND ssku.seller_sku IN
            <foreach item="item" index="index" collection="req.sellerSkuList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="getExportDataByTitle" resultType="tech.tiangong.pop.dto.product.ProductBatchUpdateTitleDTO">
        select g.shop_name      as shopName,
               g.country        as country,
               g.spu_code       as spuCode,
               g.product_title  as title
        from sale_goods g
                 join sale_sku s on g.sale_goods_id = s.sale_goods_id
                 join product p on g.product_id = p.product_id
                 join sale_skc c on s.sale_skc_id = c.sale_skc_id
        where g.shop_id in
            <foreach item="item" index="index" collection="shopIds" open="(" separator="," close=")">
                #{item}
            </foreach>
            <if test="countryCodes != null and countryCodes.size > 0">
                and g.country in
                <foreach item="item" index="index" collection="countryCodes" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="spuCodes != null and spuCodes.size > 0">
                and g.spu_code in
                <foreach item="item" index="index" collection="spuCodes" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="skcCodes != null and skcCodes.size > 0">
                and c.skc in
                <foreach item="item" index="index" collection="skcCodes" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="categoryCodes != null and categoryCodes.size > 0">
                and g.category_code in
                <foreach item="item" index="index" collection="categoryCodes" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="publishStateCodes != null and publishStateCodes.size > 0">
                and s.publish_state in
                <foreach item="item" index="index" collection="publishStateCodes" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="req.tagCodes != null and req.tagCodes.size > 0">
                AND
                (
                g.sale_goods_id in (select target_id from product_tag where deleted = 0 and
                <foreach collection="req.tagCodes" item="valueList" index="key" open="(" close=")" separator=" AND ">(tag_key = #{key} AND tag_value IN
                    <foreach collection=" valueList" item="value" open="(" close=")" separator=",">
                        #{value}
                    </foreach>
                    )
                </foreach>
                )
                OR
                c.sale_skc_id in (select target_id from product_tag where deleted = 0 and
                <foreach collection="req.tagCodes" item="valueList" index="key" open="(" close=")" separator=" AND ">(tag_key = #{key} AND tag_value IN
                    <foreach collection=" valueList" item="value" open="(" close=")" separator=",">
                        #{value}
                    </foreach>
                    )
                </foreach>
                )
                OR
                p.product_id in (select target_id from product_tag where deleted = 0 and
                <foreach collection="req.tagCodes" item="valueList" index="key" open="(" close=")" separator=" AND ">(tag_key = #{key} AND tag_value IN
                    <foreach collection=" valueList" item="value" open="(" close=")" separator=",">
                        #{value}
                    </foreach>
                    )
                </foreach>
                )
                )
            </if>
          and g.deleted = 0
          and s.deleted = 0
          and p.deleted = 0
          and c.deleted = 0
          and g.platform_product_id is not null
          and s.platform_product_id is not null
          and s.platform_sku_id is not null
        group by g.sale_goods_id
        order by g.sale_goods_id
    </select>

    <select id="getExportDataByPriceInventory" resultType="tech.tiangong.pop.dao.entity.dto.ProductPriceInventoryDto">
        select
               g.product_id              as productId,
               g.sale_goods_id           as saleGoodsId,
               c.sale_skc_id             as saleSkcId,
               g.shop_name               as shopName,
               g.country                 as country,
               g.spu_code                as spuCode,
               c.skc                     as skcCode,
               c.color                   as color,
               s.size_name               as size,
               s.publish_state           as status,
               c.cb_price                as cbPrice,
               c.local_price             as localPrice,
               c.purchase_price          as purchasePrice,
               c.cost_price              as costPrice,
               s.sale_price              as salePrice,
               s.retail_price            as retailPrice,
               s.stock_quantity          as stockQuantity,
               s.purchase_sale_price       as purchaseSalePrice,
               s.purchase_retail_price     as purchaseRetailPrice,
               s.regular_sale_price        as regularSalePrice,
               s.regular_retail_price      as regularRetailPrice,
               s.barcode                   as barcode
        from sale_goods g
                 join sale_sku s on g.sale_goods_id = s.sale_goods_id
                 join product p on g.product_id = p.product_id
                 join sale_skc c on s.sale_skc_id = c.sale_skc_id
        where g.shop_id in
            <foreach item="item" index="index" collection="shopIds" open="(" separator="," close=")">
                #{item}
            </foreach>
            <if test="countryCodes != null and countryCodes.size > 0">
                and g.country in
                <foreach item="item" index="index" collection="countryCodes" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="spuCodes != null and spuCodes.size > 0">
                and g.spu_code in
                <foreach item="item" index="index" collection="spuCodes" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="skcCodes != null and skcCodes.size > 0">
                and c.skc in
                <foreach item="item" index="index" collection="skcCodes" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="categoryCodes != null and categoryCodes.size > 0">
                and g.category_code in
                <foreach item="item" index="index" collection="categoryCodes" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="publishStateCodes != null and publishStateCodes.size > 0">
                and s.publish_state in
                <foreach item="item" index="index" collection="publishStateCodes" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="req.tagCodes != null and req.tagCodes.size > 0">
                AND
                (
                g.sale_goods_id in (select target_id from product_tag where deleted = 0 and
                <foreach collection="req.tagCodes" item="valueList" index="key" open="(" close=")" separator=" AND ">(tag_key = #{key} AND tag_value IN
                    <foreach collection=" valueList" item="value" open="(" close=")" separator=",">
                        #{value}
                    </foreach>
                    )
                </foreach>
                )
                OR
                c.sale_skc_id in (select target_id from product_tag where deleted = 0 and
                <foreach collection="req.tagCodes" item="valueList" index="key" open="(" close=")" separator=" AND ">(tag_key = #{key} AND tag_value IN
                    <foreach collection=" valueList" item="value" open="(" close=")" separator=",">
                        #{value}
                    </foreach>
                    )
                </foreach>
                )
                OR
                p.product_id in (select target_id from product_tag where deleted = 0 and
                <foreach collection="req.tagCodes" item="valueList" index="key" open="(" close=")" separator=" AND ">(tag_key = #{key} AND tag_value IN
                    <foreach collection=" valueList" item="value" open="(" close=")" separator=",">
                        #{value}
                    </foreach>
                    )
                </foreach>
                )
                )
            </if>
            and g.deleted = 0
            and s.deleted = 0
            and p.deleted = 0
            and c.deleted = 0
            and c.skc is not null
            and g.platform_product_id is not null
            and s.platform_product_id is not null
            and s.platform_sku_id is not null
        group by s.sale_sku_id
        order by s.sale_sku_id
    </select>

    <select id="getAeExportDataByTitle" resultType="tech.tiangong.pop.dto.product.ProductBatchUpdateTitleDTO">
        select g.shop_name      as shopName,
               g.spu_code       as spuCode,
               g.product_title  as title
        from ae_sale_goods g
                 join ae_sale_sku s on g.sale_goods_id = s.sale_goods_id
                 join product p on g.product_id = p.product_id
                 join ae_sale_skc c on s.sale_skc_id = c.sale_skc_id
        where g.shop_id in
            <foreach item="item" index="index" collection="shopIds" open="(" separator="," close=")">
                #{item}
            </foreach>
            <if test="spuCodes != null and spuCodes.size > 0">
                and g.spu_code in
                <foreach item="item" index="index" collection="spuCodes" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="skcCodes != null and skcCodes.size > 0">
                and c.skc in
                <foreach item="item" index="index" collection="skcCodes" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="categoryCodes != null and categoryCodes.size > 0">
                and g.category_code in
                <foreach item="item" index="index" collection="categoryCodes" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="publishStateCodes != null and publishStateCodes.size > 0">
                and g.publish_state in
                <foreach item="item" index="index" collection="publishStateCodes" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        <if test="req.tagCodes != null and req.tagCodes.size > 0">
            AND
            (
            g.sale_goods_id in (select target_id from product_tag where deleted = 0 and
            <foreach collection="req.tagCodes" item="valueList" index="key" open="(" close=")" separator=" AND ">(tag_key = #{key} AND tag_value IN
                <foreach collection=" valueList" item="value" open="(" close=")" separator=",">
                    #{value}
                </foreach>
                )
            </foreach>
            )
            OR
            c.sale_skc_id in (select target_id from product_tag where deleted = 0 and
            <foreach collection="req.tagCodes" item="valueList" index="key" open="(" close=")" separator=" AND ">(tag_key = #{key} AND tag_value IN
                <foreach collection=" valueList" item="value" open="(" close=")" separator=",">
                    #{value}
                </foreach>
                )
            </foreach>
            )
            OR
            p.product_id in (select target_id from product_tag where deleted = 0 and
            <foreach collection="req.tagCodes" item="valueList" index="key" open="(" close=")" separator=" AND ">(tag_key = #{key} AND tag_value IN
                <foreach collection=" valueList" item="value" open="(" close=")" separator=",">
                    #{value}
                </foreach>
                )
            </foreach>
            )
            )
        </if>
          and g.deleted = 0
          and s.deleted = 0
          and p.deleted = 0
          and c.deleted = 0
          and g.platform_product_id is not null
          and s.platform_product_id is not null
          and s.platform_sku_id is not null
        group by g.sale_goods_id
        order by g.sale_goods_id
    </select>

    <select id="getAeExportDataByPriceInventory" resultType="tech.tiangong.pop.dao.entity.dto.ProductPriceInventoryDto">
        select
               g.product_id              as productId,
               g.sale_goods_id           as saleGoodsId,
               c.sale_skc_id             as saleSkcId,
               g.shop_name               as shopName,
               null                      as country,
               g.spu_code                as spuCode,
               c.skc                     as skcCode,
               c.color                   as color,
               s.size_name               as size,
               g.publish_state           as status,
               c.cb_price                as cbPrice,
               c.local_price             as localPrice,
               c.purchase_price          as purchasePrice,
               c.cost_price              as costPrice,
               s.ships_from_attribute_value_name            as shipsFrom,
               s.sale_price              as salePrice,
               s.retail_price            as retailPrice,
               s.stock_quantity          as stockQuantity,
               s.purchase_sale_price       as purchaseSalePrice,
               s.purchase_retail_price     as purchaseRetailPrice,
               s.regular_sale_price        as regularSalePrice,
               s.regular_retail_price      as regularRetailPrice,
               s.barcode                   as barcode
        from ae_sale_goods g
                 join ae_sale_sku s on g.sale_goods_id = s.sale_goods_id
                 join product p on g.product_id = p.product_id
                 join ae_sale_skc c on s.sale_skc_id = c.sale_skc_id
        where g.shop_id in
            <foreach item="item" index="index" collection="shopIds" open="(" separator="," close=")">
                #{item}
            </foreach>
            <if test="spuCodes != null and spuCodes.size > 0">
                and g.spu_code in
                <foreach item="item" index="index" collection="spuCodes" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="skcCodes != null and skcCodes.size > 0">
                and c.skc in
                <foreach item="item" index="index" collection="skcCodes" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="categoryCodes != null and categoryCodes.size > 0">
                and g.category_code in
                <foreach item="item" index="index" collection="categoryCodes" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="publishStateCodes != null and publishStateCodes.size > 0">
                and g.publish_state in
                <foreach item="item" index="index" collection="publishStateCodes" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="req.tagCodes != null and req.tagCodes.size > 0">
                AND
                (
                g.sale_goods_id in (select target_id from product_tag where deleted = 0 and
                <foreach collection="req.tagCodes" item="valueList" index="key" open="(" close=")" separator=" AND ">(tag_key = #{key} AND tag_value IN
                    <foreach collection=" valueList" item="value" open="(" close=")" separator=",">
                        #{value}
                    </foreach>
                    )
                </foreach>
                )
                OR
                c.sale_skc_id in (select target_id from product_tag where deleted = 0 and
                <foreach collection="req.tagCodes" item="valueList" index="key" open="(" close=")" separator=" AND ">(tag_key = #{key} AND tag_value IN
                    <foreach collection=" valueList" item="value" open="(" close=")" separator=",">
                        #{value}
                    </foreach>
                    )
                </foreach>
                )
                OR
                p.product_id in (select target_id from product_tag where deleted = 0 and
                <foreach collection="req.tagCodes" item="valueList" index="key" open="(" close=")" separator=" AND ">(tag_key = #{key} AND tag_value IN
                    <foreach collection=" valueList" item="value" open="(" close=")" separator=",">
                        #{value}
                    </foreach>
                    )
                </foreach>
                )
                )
            </if>
            and g.deleted = 0
            and s.deleted = 0
            and p.deleted = 0
            and c.deleted = 0
            and c.skc is not null
            and g.platform_product_id is not null
            and s.platform_product_id is not null
            and s.platform_sku_id is not null
        group by s.sale_sku_id
        order by s.sale_sku_id
    </select>

    <update id="fillProductStyleType">
        <foreach collection="list" item="data" index="index" separator=";">
            update product
            set
            <if test="data.styleType !=null">
                style_type = ${data.styleType},
            </if>
            revised_time = now()
            where product_id = ${data.productId}
        </foreach>
    </update>

    <update id="refreshImagePackageState">
        <foreach collection="list" item="data" index="index" separator=";">
            update product
            set
            <if test="data.imagePackageState !=null">
                image_package_state = ${data.imagePackageState},
            </if>
            revised_time = now()
            where product_id = ${data.productId}
        </foreach>
    </update>

    <select id="getCenterPage" resultType="tech.tiangong.pop.dao.entity.Product">
        SELECT
        p.*
        FROM product p
        LEFT JOIN product_skc ps ON p.product_id = ps.product_id
        LEFT JOIN product_publish_store pps on p.product_id = pps.product_id
        <where>
            p.deleted = 0 and ps.deleted = 0
            <if test="req.submitStatus != null ">
                AND p.is_sync_platform = #{req.submitStatus}
            </if>
            <if test="req.spuList != null and req.spuList.size > 1">
                AND p.spu_code IN
                <foreach item="item" index="index" collection="req.spuList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="req.spuList != null and req.spuList.size == 1">
                AND p.spu_code
                <foreach item="item" index="index" collection="req.spuList">
                    LIKE CONCAT('%', #{item}, '%')
                </foreach>
            </if>
            <if test="req.skcList != null and req.skcList.size > 1">
                AND ps.skc IN
                <foreach item="item" index="index" collection="req.skcList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="req.skcList != null and req.skcList.size == 1">
                AND ps.skc
                <foreach item="item" index="index" collection="req.skcList">
                    LIKE CONCAT('%', #{item}, '%')
                </foreach>
            </if>
            <if test="req.imagePackageStateList != null and req.imagePackageStateList.size > 0">
                AND p.image_package_state IN
                <foreach item="item" index="index" collection="req.imagePackageStateList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="req.shopName != null and req.shopName != ''">
                AND p.shop_name LIKE CONCAT('%', #{req.shopName}, '%')
            </if>
            <if test="req.country != null and req.country != ''">
                AND p.countrys LIKE CONCAT('%', #{req.country}, '%')
            </if>
            <if test="req.selectStyleName != null and req.selectStyleName != ''">
                AND p.select_style_name  LIKE CONCAT('%', #{req.selectStyleName}, '%')
            </if>
            <if test="req.planAuditStateList != null and req.planAuditStateList.size > 0">
                AND p.plan_audit_state IN
                <foreach item="item" index="index" collection="req.planAuditStateList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="req.creatorName != null and req.creatorName != ''">
                AND p.creator_name LIKE CONCAT('%', #{req.creatorName}, '%')
            </if>
            <if test="req.createdTimeStart != null and req.createdTimeEnd != null  ">
                AND p.created_time BETWEEN #{req.createdTimeStart} AND #{req.createdTimeEnd}
            </if>
            <if test="req.videoStateList != null and req.videoStateList.size > 0">
                AND p.video_state IN
                <foreach item="item" index="index" collection="req.videoStateList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="req.clothingStyleCode != null and req.clothingStyleCode != ''">
                AND p.clothing_style_code = #{req.clothingStyleCode}
            </if>
            <if test="req.supplyMode != null and req.supplyMode != ''">
                AND p.supply_mode = #{req.supplyMode}
            </if>
            <if test="req.supplyModeList != null and req.supplyModeList.size > 0">
                AND p.supply_mode IN
                <foreach item="item" index="index" collection="req.supplyModeList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="req.waves != null and req.waves != ''">
                AND p.waves = #{req.waves}
            </if>
            <if test="req.wavesList != null and req.wavesList.size > 0">
                AND p.waves IN
                <foreach item="item" index="index" collection="req.wavesList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="req.costPriceMin != null">
                AND ps.cost_price <![CDATA[ >= ]]> #{req.costPriceMin}
            </if>
            <if test="req.costPriceMax != null">
                AND ps.cost_price <![CDATA[ <= ]]> #{req.costPriceMax}
            </if>
            <if test="req.categoryCodeList != null and req.categoryCodeList.size > 0">
                AND p.category_code IN
                <foreach item="item" index="index" collection="req.categoryCodeList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="req.goodsTypeList != null and req.goodsTypeList.size > 0">
                AND p.goods_type IN
                <foreach item="item" index="index" collection="req.goodsTypeList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="req.shopIds != null and req.shopIds.size > 0">
                AND pps.shop_id IN
                <foreach item="item" index="index" collection="req.shopIds" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="req.planAuditorId != null ">
                AND p.plan_auditor_id = #{req.planAuditorId}
            </if>
            <if test="req.onSale != null and req.onSale == 1">
                AND p.publish_state = 1
            </if>
            <if test="req.onSale != null and req.onSale == 0">
                AND p.publish_state in (0,2)
            </if>
            <if test="req.productType != null">
                AND p.product_type = #{req.productType}
            </if>
            <if test="req.spuCanceled != null">
                AND p.spuCanceled = #{req.spuCanceled}
            </if>
            <if test="req.resourceState != null">
                AND p.resource_state = #{req.resourceState}
            </if>

            <!-- Include the tag filtering condition -->
            <include refid="tagFilterCondition" />
        </where>
        GROUP BY p.product_id
        ORDER BY p.revised_time DESC
    </select>
</mapper>
