<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tech.tiangong.pop.dao.mapper.AeSelectedProductSpuMapper">

    <select id="getProductSnapshotByProductIds" resultType="tech.tiangong.pop.dao.entity.dto.TryOnTaskProductSnapshotDto">
        SELECT
            a.product_id,
            a.shop_id,
            a.version_watermark,
            a.version_num,
            a.pop_category_id,
            a.style_code,
            a.style_name,
            a.brand_name,
            a.market_code,
            a.market_name,
            a.try_on_rule_code,
            a.try_on_rule_name,
            a.is_platform_updated,
            a.is_main_push,
            a.is_active_sales,
            a.latest_sync_time,
            b.ae_image_urls,
            b.try_on_image_url
        FROM
            ae_selected_product_spu a JOIN recognition_task b ON a.product_id = b.product_id AND a.version_watermark = b.version_watermark
        where a.deleted=0 and b.deleted=0
        and a.product_id in
        <foreach collection="productIds" item="productId" open="(" separator="," close=")">
            #{productId}
        </foreach>
    </select>

    <select id="pageAe" resultType="tech.tiangong.pop.resp.selected.AeSelectedProductPageResp">

        select  ps.product_id         as aeProductId,
                ps.alibaba_id         as alibabaId,
                ps.shop_id            as shopId,
                ps.product_title      as productTitle,
                ps.ae_product_status  as publishState,
                ps.product_url        as productUrl,
                ps.created_time       as createdTime,
                ps.ae_category_id     as aeCategoryId,
                ps.pop_category_id    as categoryId,
                ps.pop_category_code  as categoryCode,
                ps.brand_name         as brandName,
                ps.market_code        as marketCode,
                ps.market_name        as marketName,
                ps.style_code         as styleCode,
                ps.style_name         as styleName,
                ps.is_main_push       as isMainPush,
                ps.product_image      as productImage,
                ps.result_images      as resultImagesStr,
                ps.try_on_rule_code   as tryOnRuleCode,
                ps.try_on_rule_name   as tryOnRuleName,

                upt.id                as updateTaskId,
                upt.version_num       as updateVersion,
                upt.task_status       as updateStatus,
                upt.exception_info    as updateErrorMsg,
                upt.created_time      as updateTime,
                upt.creator_name      as updateUserName,

                tot.task_id           as tryOnTaskId,
                tot.version_num       as tryOnVersion,
                tot.state             as tryOnStatus,
                tot.task_created_time as tryOnTime,
                tot.task_creator_name as tryOnUserName,

                toq.qc_id             as qcTaskId,
                toq.version_num       as qcVersion,
                toq.qc_result         as qcStatus,
                toq.qc_time           as qcTime,
                toq.inspector_name    as qcUserName
        from ae_selected_product_spu ps
            left join try_on_task tot on ps.try_on_task_id = tot.task_id
            left join try_on_task_qc toq on toq.task_id = tot.task_id
            left join ae_update_platform_task upt on ps.update_task_id = upt.id
        where ps.deleted = 0

            <if test="req.aeProductIdList != null and req.aeProductIdList.size > 0">
                AND ps.product_id IN
                <foreach item="item" index="index" collection="req.aeProductIdList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="req.shopId != null">
                AND ps.shop_id = #{req.shopId}
            </if>

            <choose>
                <!-- 当 req.tryonStatusList 不为空且包含 0 时 -->
                <when test="req.tryonStatusList != null and req.tryonStatusList.contains(0) and req.tryonStatusList.size > 0">
                    AND (tot.task_id IS NULL OR tot.state IN
                    <foreach item="item" index="index" collection="req.tryonStatusList" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    )
                </when>
                <!-- 当 req.tryonStatusList 不为空但不包含 0 时 -->
                <when test="req.tryonStatusList != null and req.tryonStatusList.size > 0">
                    AND tot.state IN
                    <foreach item="item" index="index" collection="req.tryonStatusList" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </when>
            </choose>

            <if test="req.qcStatusList != null and req.qcStatusList.size > 0">
                AND toq.qc_result IN
                <foreach item="item" index="index" collection="req.qcStatusList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="req.tryOnUserName != null and req.tryOnUserName != ''">
                AND tot.creator_name = #{req.tryOnUserName}
            </if>
            <if test="req.qcUserName != null and req.qcUserName != ''">
                AND toq.inspector_name = #{req.qcUserName}
            </if>
            <if test="req.brandName != null and req.brandName != ''">
                AND ps.brand_name = #{req.brandName}
            </if>
            <if test="req.marketCode != null and req.marketCode != ''">
                AND ps.market_code = #{req.marketCode}
            </if>
            <if test="req.styleCodeList != null and req.styleCodeList.size > 0">
                AND ps.style_code IN
                <foreach item="item" index="index" collection="req.styleCodeList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="req.categoryIdList != null and req.categoryIdList.size > 0">
                AND ps.pop_category_id IN
                <foreach item="item" index="index" collection="req.categoryIdList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="req.categoryCodeList != null and req.categoryCodeList.size > 0">
                AND ps.pop_category_code IN
                <foreach item="item" index="index" collection="req.categoryCodeList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="req.publishStateList != null and req.publishStateList.size > 0">
                AND ps.ae_product_status IN
                <foreach item="item" index="index" collection="req.publishStateList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="req.updateStatusList != null and req.updateStatusList.size > 0">
                AND upt.task_status IN
                <foreach item="item" index="index" collection="req.updateStatusList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="req.isMainPush != null and req.isMainPush == 1">
                and ps.is_main_push = #{req.isMainPush}
            </if>
            <if test="req.createdTimeStart != null and req.createdTimeEnd != null">
                AND ps.created_time BETWEEN #{req.createdTimeStart} AND #{req.createdTimeEnd}
            </if>
            <if test="req.updateTimeStart != null and req.updateTimeEnd != null">
                AND upt.complete_time BETWEEN #{req.updateTimeStart} AND #{req.updateTimeEnd}
            </if>
        group by ps.product_id, ps.shop_id
    </select>

    <select id="countAe" resultType="tech.tiangong.pop.dao.entity.dto.AeSelectedProductCountDto">

        select
            ps.ae_product_status as publishState,
            count(distinct ps.product_id) as countNum
        from ae_selected_product_spu ps
            left join try_on_task tot on ps.try_on_task_id = tot.task_id
            left join try_on_task_qc toq on toq.task_id = tot.task_id
            left join ae_update_platform_task upt on ps.update_task_id = upt.id
        where ps.deleted = 0

            <if test="req.aeProductIdList != null and req.aeProductIdList.size > 0">
                AND ps.product_id IN
                <foreach item="item" index="index" collection="req.aeProductIdList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="req.shopId != null">
                AND ps.shop_id = #{req.shopId}
            </if>

            <choose>
                <!-- 当 req.tryonStatusList 不为空且包含 0 时 -->
                <when test="req.tryonStatusList != null and req.tryonStatusList.contains(0) and req.tryonStatusList.size > 0">
                    AND (tot.task_id IS NULL OR tot.state IN
                    <foreach item="item" index="index" collection="req.tryonStatusList" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    )
                </when>
                <!-- 当 req.tryonStatusList 不为空但不包含 0 时 -->
                <when test="req.tryonStatusList != null and req.tryonStatusList.size > 0">
                    AND tot.state IN
                    <foreach item="item" index="index" collection="req.tryonStatusList" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </when>
            </choose>

            <if test="req.qcStatusList != null and req.qcStatusList.size > 0">
                AND toq.qc_result IN
                <foreach item="item" index="index" collection="req.qcStatusList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="req.tryOnUserName != null and req.tryOnUserName != ''">
                AND tot.creator_name = #{req.tryOnUserName}
            </if>
            <if test="req.qcUserName != null and req.qcUserName != ''">
                AND toq.inspector_name = #{req.qcUserName}
            </if>
            <if test="req.brandName != null and req.brandName != ''">
                AND ps.brand_name = #{req.brandName}
            </if>
            <if test="req.marketCode != null and req.marketCode != ''">
                AND ps.market_code = #{req.marketCode}
            </if>
            <if test="req.styleCodeList != null and req.styleCodeList.size > 0">
                AND ps.style_code IN
                <foreach item="item" index="index" collection="req.styleCodeList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="req.categoryIdList != null and req.categoryIdList.size > 0">
                AND ps.pop_category_id IN
                <foreach item="item" index="index" collection="req.categoryIdList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="req.categoryCodeList != null and req.categoryCodeList.size > 0">
                AND ps.pop_category_code IN
                <foreach item="item" index="index" collection="req.categoryCodeList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="req.publishStateList != null and req.publishStateList.size > 0">
                AND ps.ae_product_status IN
                <foreach item="item" index="index" collection="req.publishStateList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="req.updateStatusList != null and req.updateStatusList.size > 0">
                AND upt.task_status IN
                <foreach item="item" index="index" collection="req.updateStatusList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="req.isMainPush != null">
                and ps.is_main_push = #{req.isMainPush}
            </if>
            <if test="req.createdTimeStart != null and req.createdTimeEnd != null">
                AND ps.created_time BETWEEN #{req.createdTimeStart} AND #{req.createdTimeEnd}
            </if>
            <if test="req.updateTimeStart != null and req.updateTimeEnd != null">
                AND upt.complete_time BETWEEN #{req.updateTimeStart} AND #{req.updateTimeEnd}
            </if>
        group by ps.ae_product_status
    </select>
</mapper>

