<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tech.tiangong.pop.dao.mapper.ImageRepositoryMapper">
    <delete id="physicalDeleteByIds" parameterType="java.util.List">
        DELETE FROM image_repository
        WHERE image_repository_id IN
        <foreach collection="imageIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
