<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tech.tiangong.pop.dao.mapper.AeSaleSkuMapper">

    <select id="pageOnlineSkuList" resultType="tech.tiangong.pop.resp.product.ae.ProductAeOnlineSkuListResp">
        SELECT DISTINCT
        ss.sale_sku_id as saleSkuId,
        sg.spu_code as spuCode,
        ps.skc as skcCode,
        ss.seller_sku as sellerSku,
        ss.barcode,
        ps.color,
        ps.platform_color as platformColor,
        ss.size_name as sizeName
        FROM ae_sale_sku ss
        INNER JOIN ae_sale_goods sg ON ss.sale_goods_id = sg.sale_goods_id
        INNER JOIN ae_sale_skc ps ON ss.sale_skc_id = ps.sale_skc_id
        WHERE ss.deleted = 0
        AND sg.deleted = 0
        AND ps.deleted = 0
        AND ss.publish_state = 1
        AND ss.enable_state = 1
        AND sg.publish_state = 1
        AND sg.platform_product_id IS NOT NULL
        <!--AND ss.platform_sku_id IS NOT NULL-->
        AND ps.state = 1
        <if test="req.shopIds != null and !req.shopIds.isEmpty()">
            AND sg.shop_id IN
            <foreach collection="req.shopIds" item="shopId" open="(" separator="," close=")">
                #{shopId}
            </foreach>
        </if>
        <if test="req.spuCodes != null and !req.spuCodes.isEmpty()">
            AND sg.spu_code IN
            <foreach collection="req.spuCodes" item="spuCode" open="(" separator="," close=")">
                #{spuCode}
            </foreach>
        </if>
        <if test="req.skcCodes != null and !req.skcCodes.isEmpty()">
            AND ps.skc IN
            <foreach collection="req.skcCodes" item="skcCode" open="(" separator="," close=")">
                #{skcCode}
            </foreach>
        </if>
        <if test="req.skuCodes != null and !req.skuCodes.isEmpty()">
            AND ss.seller_sku IN
            <foreach collection="req.skuCodes" item="skuCode" open="(" separator="," close=")">
                #{skuCode}
            </foreach>
        </if>
        ORDER BY ss.created_time DESC
    </select>

    <select id="countOnlineSku" resultType="tech.tiangong.pop.resp.product.ae.ProductAeOnlineSkuCountResp">
        SELECT
        (
        SELECT COUNT(DISTINCT sg.spu_code)
        FROM ae_sale_sku ss
        INNER JOIN ae_sale_goods sg ON ss.sale_goods_id = sg.sale_goods_id
        INNER JOIN ae_sale_skc ps ON ss.sale_skc_id = ps.sale_skc_id
        WHERE ss.deleted = 0
        AND sg.deleted = 0
        AND ps.deleted = 0
        AND ss.publish_state = 1
        AND ss.enable_state = 1
        AND sg.publish_state = 1
        AND sg.platform_product_id IS NOT NULL
        AND ss.platform_sku_id IS NOT NULL
        AND ps.state = 1
        <if test="req.shopIds != null and !req.shopIds.isEmpty()">
            AND sg.shop_id IN
            <foreach collection="req.shopIds" item="shopId" open="(" separator="," close=")">
                #{shopId}
            </foreach>
        </if>
        <if test="req.spuCodes != null and !req.spuCodes.isEmpty()">
            AND sg.spu_code IN
            <foreach collection="req.spuCodes" item="spuCode" open="(" separator="," close=")">
                #{spuCode}
            </foreach>
        </if>
        <if test="req.skcCodes != null and !req.skcCodes.isEmpty()">
            AND ps.skc IN
            <foreach collection="req.skcCodes" item="skcCode" open="(" separator="," close=")">
                #{skcCode}
            </foreach>
        </if>
        <if test="req.skuCodes != null and !req.skuCodes.isEmpty()">
            AND ss.seller_sku IN
            <foreach collection="req.skuCodes" item="skuCode" open="(" separator="," close=")">
                #{skuCode}
            </foreach>
        </if>
        <if test="req.excludeSkuCodes != null and !req.excludeSkuCodes.isEmpty()">
            AND ss.seller_sku NOT IN
            <foreach collection="req.excludeSkuCodes" item="excludeSkuCode" open="(" separator="," close=")">
                #{excludeSkuCode}
            </foreach>
        </if>
        ) AS spuCount,
        (
        SELECT COUNT(DISTINCT ps.sale_skc_id)
        FROM ae_sale_sku ss
        INNER JOIN ae_sale_goods sg ON ss.sale_goods_id = sg.sale_goods_id
        INNER JOIN ae_sale_skc ps ON ss.sale_skc_id = ps.sale_skc_id
        WHERE ss.deleted = 0
        AND sg.deleted = 0
        AND ps.deleted = 0
        AND ss.publish_state = 1
        AND ss.enable_state = 1
        AND sg.publish_state = 1
        AND sg.platform_product_id IS NOT NULL
        AND ss.platform_sku_id IS NOT NULL
        AND ps.state = 1
        <if test="req.shopIds != null and !req.shopIds.isEmpty()">
            AND sg.shop_id IN
            <foreach collection="req.shopIds" item="shopId" open="(" separator="," close=")">
                #{shopId}
            </foreach>
        </if>
        <if test="req.spuCodes != null and !req.spuCodes.isEmpty()">
            AND sg.spu_code IN
            <foreach collection="req.spuCodes" item="spuCode" open="(" separator="," close=")">
                #{spuCode}
            </foreach>
        </if>
        <if test="req.skcCodes != null and !req.skcCodes.isEmpty()">
            AND ps.skc IN
            <foreach collection="req.skcCodes" item="skcCode" open="(" separator="," close=")">
                #{skcCode}
            </foreach>
        </if>
        <if test="req.skuCodes != null and !req.skuCodes.isEmpty()">
            AND ss.seller_sku IN
            <foreach collection="req.skuCodes" item="skuCode" open="(" separator="," close=")">
                #{skuCode}
            </foreach>
        </if>
        <if test="req.excludeSkuCodes != null and !req.excludeSkuCodes.isEmpty()">
            AND ss.seller_sku NOT IN
            <foreach collection="req.excludeSkuCodes" item="excludeSkuCode" open="(" separator="," close=")">
                #{excludeSkuCode}
            </foreach>
        </if>
        ) AS skcCount
    </select>

    <select id="getListSizeBySkc" resultType="tech.tiangong.pop.dao.entity.dto.SizeBySkcDto">
        select spu_code, skc, color, color_code, size_name
        from ae_sale_sku u
        join ae_sale_skc c on u.sale_skc_id = c.sale_skc_id
        join ae_sale_goods g on c.sale_goods_id = g.sale_goods_id
        where g.platform_product_id is not null
        and u.seller_sku is not null
        and c.combo != 1
        and skc in
        <foreach collection="req.skcList" item="skc" open="(" separator="," close=")">
            #{skc}
        </foreach>
    </select>

    <select id="querySkuByUpdateStockPrice" resultType="tech.tiangong.pop.dao.entity.dto.QuerySkuByUpdateStockPriceDto">
        select
            c.product_skc_id,
            c.skc,
            c.color,
            c.combo,
            barcode,
            sale_sku_id,
            size_name,
            stock_quantity,
            sale_price,
            retail_price,
            last_sale_price,
            last_retail_price
        from ae_sale_skc c
        join ae_sale_sku u on c.sale_skc_id = u.sale_skc_id
        where
        c.sale_goods_id in
        <foreach item="item" collection="saleGoodsIds" open="(" separator="," close=")">
            #{item}
        </foreach>
        and c.deleted = 0
        and u.deleted = 0
        and platform_product_id is not null
        and seller_sku is not null
    </select>

    <update id="fixEtaUpdate">
        <foreach collection="list" item="data" index="index" separator=";">
            update ae_sale_sku
            set
            <if test="data.delayDeliveryDays !=null">
                delay_delivery_days = ${data.delayDeliveryDays},
            </if>
                revised_time = now()
            where sale_sku_id = ${data.saleSkuId}
        </foreach>
    </update>
</mapper>

