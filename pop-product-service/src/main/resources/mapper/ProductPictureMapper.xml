<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tech.tiangong.pop.dao.mapper.ProductPictureMapper">

    <resultMap type="tech.tiangong.pop.dao.entity.ProductPicture" id="ProductPictureMap">
    <!--@mbg.generated-->
    <!--@Table product_picture-->
        <id property="productPictureId" column="product_picture_id" jdbcType="BIGINT"/>
        <result property="productId" column="product_id" jdbcType="BIGINT"/>
        <result property="spuCode" column="spu_code" jdbcType="VARCHAR"/>
        <result property="sourceImageUrl" column="source_image_url" jdbcType="VARCHAR"/>
        <result property="img101" column="img101" jdbcType="VARCHAR"/>
        <result property="img201" column="img201" jdbcType="VARCHAR"/>
        <result property="img301" column="img301" jdbcType="VARCHAR"/>
        <result property="img401" column="img401" jdbcType="VARCHAR"/>
        <result property="img501" column="img501" jdbcType="VARCHAR"/>
        <result property="img601" column="img601" jdbcType="VARCHAR"/>
        <result property="skcColorUrl" column="skc_color_url" jdbcType="VARCHAR"/>
        <result property="deleted" column="deleted" jdbcType="INTEGER"/>
        <result property="creatorId" column="creator_id" jdbcType="BIGINT"/>
        <result property="creatorName" column="creator_name" jdbcType="VARCHAR"/>
        <result property="createdTime" column="created_time" jdbcType="TIMESTAMP"/>
        <result property="reviserId" column="reviser_id" jdbcType="BIGINT"/>
        <result property="reviserName" column="reviser_name" jdbcType="VARCHAR"/>
        <result property="revisedTime" column="revised_time" jdbcType="TIMESTAMP"/>
    </resultMap>
    <sql id="Base_Column_List">
    <!--@mbg.generated-->
 product_picture_id
 ,product_id
 ,spu_code
 ,source_image_url
 ,img101
 ,img201
 ,img301
 ,img401
 ,img501
 ,img601
 ,skc_color_url
 ,deleted
 ,creator_id
 ,creator_name
 ,created_time
 ,reviser_id
 ,reviser_name
 ,revised_time
  </sql>
</mapper>

