<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tech.tiangong.pop.dao.mapper.SaleSkuMapper">

    <resultMap type="tech.tiangong.pop.dao.entity.SaleSku" id="SaleSkuMap">
        <!--@mbg.generated-->
        <!--@Table sale_sku-->
        <id property="saleSkuId" column="sale_sku_id" jdbcType="BIGINT"/>
        <result property="saleGoodsId" column="sale_goods_id" jdbcType="BIGINT"/>
        <result property="productId" column="product_id" jdbcType="BIGINT"/>
        <result property="platformProductId" column="platform_product_id" jdbcType="BIGINT"/>
        <result property="sellerSku" column="seller_sku" jdbcType="VARCHAR"/>
        <result property="shopSku" column="shop_sku" jdbcType="VARCHAR"/>
        <result property="productSkcId" column="product_skc_id" jdbcType="BIGINT"/>
        <result property="barcode" column="barcode" jdbcType="VARCHAR"/>
        <result property="country" column="country" jdbcType="VARCHAR"/>
        <result property="skuCode" column="sku_code" jdbcType="VARCHAR"/>
        <result property="stockQuantity" column="stock_quantity" jdbcType="BIGINT"/>
        <result property="sizeName" column="size_name" jdbcType="VARCHAR"/>
        <result property="salePrice" column="sale_price" jdbcType="VARCHAR"/>
        <result property="retailPrice" column="retail_price" jdbcType="VARCHAR"/>
        <result property="platformSkuId" column="platform_sku_id" jdbcType="VARCHAR"/>
        <result property="enable" column="enable" jdbcType="INTEGER"/>
        <result property="shopName" column="shop_name" jdbcType="VARCHAR"/>
        <result property="brandId" column="brand_id" jdbcType="VARCHAR"/>
        <result property="brandName" column="brand_name" jdbcType="VARCHAR"/>
        <result property="platformCategoryId" column="platform_category_id" jdbcType="VARCHAR"/>
        <result property="platformCategoryName" column="platform_category_name" jdbcType="VARCHAR"/>
        <result property="publishState" column="publish_state" jdbcType="INTEGER"/>
        <result property="deleted" column="deleted" jdbcType="INTEGER"/>
        <result property="creatorId" column="creator_id" jdbcType="BIGINT"/>
        <result property="creatorName" column="creator_name" jdbcType="VARCHAR"/>
        <result property="createdTime" column="created_time" jdbcType="TIMESTAMP"/>
        <result property="reviserId" column="reviser_id" jdbcType="BIGINT"/>
        <result property="reviserName" column="reviser_name" jdbcType="VARCHAR"/>
        <result property="revisedTime" column="revised_time" jdbcType="TIMESTAMP"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        sale_sku_id
        ,sale_goods_id
        ,product_id
        ,platform_product_id
        ,seller_sku
        ,shop_sku
        ,product_skc_id
        ,barcode
        ,country
        ,sku_code
        ,stock_quantity
        ,size_name
        ,sale_price
        ,retail_price
        ,platform_sku_id
        ,enable
        ,shop_name
        ,brand_id
        ,brand_name
        ,platform_category_id
        ,platform_category_name
        ,publish_state
        ,deleted
        ,creator_id
        ,creator_name
        ,created_time
        ,reviser_id
        ,reviser_name
        ,revised_time
    </sql>

    <select id="listByProductIdAndSkcAndSizeAndCountry" resultType="tech.tiangong.pop.dao.entity.SaleSku">
        select *
        from sale_sku
        where product_id = #{productId}
            and product_skc_id in
                <foreach collection="skcIdList" item="skcId" open="(" separator="," close=")">
                    #{skcId}
                </foreach>
            and size_name in
                <foreach collection="sizeNameList" item="sizeName" open="(" separator="," close=")">
                    #{sizeName}
                </foreach>
            and country in
                <foreach collection="countryList" item="country" open="(" separator="," close=")">
                    #{country}
                </foreach>
            and deleted = 0
    </select>

    <select id="listRepeatBarcode" resultType="tech.tiangong.pop.dao.entity.BaseSkuInfo">
        -- 子查询用于找出每个 spu_code 对应的不同 spu 数量
        WITH spu_code_spu_count AS (
            SELECT
                spu_code,
                COUNT(DISTINCT spu) AS spu_count
            FROM
                base_sku_info
            where deleted = 0
            GROUP BY
                spu_code
            HAVING
                spu_count > 1
        )
-- 主查询根据子查询结果筛选出有问题的数据
        SELECT
            bsi.*
        FROM
            base_sku_info bsi
                JOIN
            spu_code_spu_count scsc ON bsi.spu_code = scsc.spu_code where bsi.deleted = 0;
    </select>

    <!-- 分页查询在线SKU列表 -->
    <select id="pageOnlineSkuList" resultType="tech.tiangong.pop.resp.product.OnlineSkuListResp">
        SELECT DISTINCT
        ss.product_id,
        sg.spu_code as spuCode,
        ps.skc as skcCode,
        ss.seller_sku as sellerSku,
        ss.barcode,
        ps.color,
        ps.platform_color as platformColor,
        ss.size_name as sizeName
        FROM sale_sku ss
        INNER JOIN sale_goods sg ON ss.sale_goods_id = sg.sale_goods_id
        INNER JOIN sale_skc ps ON ss.sale_skc_id = ps.sale_skc_id
        WHERE ss.deleted = 0
        AND sg.deleted = 0
        AND ps.deleted = 0
        AND ss.publish_state = 1
        AND ss.enable = 1
        AND sg.publish_state = 1
        AND sg.platform_product_id IS NOT NULL
        AND ss.platform_sku_id IS NOT NULL
        AND ps.state = 1
        <if test="req.countryList != null and !req.countryList.isEmpty()">
            AND ss.country IN
            <foreach collection="req.countryList" item="countryCode" open="(" separator="," close=")">
                #{countryCode}
            </foreach>
        </if>
        <if test="req.shopIds != null and !req.shopIds.isEmpty()">
            AND sg.shop_id IN
            <foreach collection="req.shopIds" item="shopId" open="(" separator="," close=")">
                #{shopId}
            </foreach>
        </if>
        <if test="req.spuCodes != null and !req.spuCodes.isEmpty()">
            AND sg.spu_code IN
            <foreach collection="req.spuCodes" item="spuCode" open="(" separator="," close=")">
                #{spuCode}
            </foreach>
        </if>
        <if test="req.skcCodes != null and !req.skcCodes.isEmpty()">
            AND ps.skc IN
            <foreach collection="req.skcCodes" item="skcCode" open="(" separator="," close=")">
                #{skcCode}
            </foreach>
        </if>
        <if test="req.skuCodes != null and !req.skuCodes.isEmpty()">
            AND ss.seller_sku IN
            <foreach collection="req.skuCodes" item="skuCode" open="(" separator="," close=")">
                #{skuCode}
            </foreach>
        </if>
        ORDER BY ss.created_time DESC
    </select>

    <!-- 统计在线SPU和SKC数量 -->
    <select id="countOnlineSku" resultType="tech.tiangong.pop.resp.product.OnlineSkuCountResp">
        SELECT
            (
            SELECT COUNT(DISTINCT sg.spu_code)
            FROM sale_sku ss
            INNER JOIN sale_goods sg ON ss.sale_goods_id = sg.sale_goods_id
            INNER JOIN sale_skc ps ON ss.sale_skc_id = ps.sale_skc_id
            WHERE ss.deleted = 0
            AND sg.deleted = 0
            AND ps.deleted = 0
            AND ss.publish_state = 1
            AND ss.enable = 1
            AND sg.publish_state = 1
            AND sg.platform_product_id IS NOT NULL
            AND ss.platform_sku_id IS NOT NULL
            AND ps.state = 1
            <if test="req.countryList != null and !req.countryList.isEmpty()">
                AND ss.country IN
                <foreach collection="req.countryList" item="countryCode" open="(" separator="," close=")">
                    #{countryCode}
                </foreach>
            </if>
            <if test="req.shopIds != null and !req.shopIds.isEmpty()">
                AND sg.shop_id IN
                <foreach collection="req.shopIds" item="shopId" open="(" separator="," close=")">
                    #{shopId}
                </foreach>
            </if>
            <if test="req.spuCodes != null and !req.spuCodes.isEmpty()">
                AND sg.spu_code IN
                <foreach collection="req.spuCodes" item="spuCode" open="(" separator="," close=")">
                    #{spuCode}
                </foreach>
            </if>
            <if test="req.skcCodes != null and !req.skcCodes.isEmpty()">
                AND ps.skc IN
                <foreach collection="req.skcCodes" item="skcCode" open="(" separator="," close=")">
                    #{skcCode}
                </foreach>
            </if>
            <if test="req.skuCodes != null and !req.skuCodes.isEmpty()">
                AND ss.seller_sku IN
                <foreach collection="req.skuCodes" item="skuCode" open="(" separator="," close=")">
                    #{skuCode}
                </foreach>
            </if>
            <if test="req.excludeSkuCodes != null and !req.excludeSkuCodes.isEmpty()">
                AND ss.seller_sku NOT IN
                <foreach collection="req.excludeSkuCodes" item="excludeSkuCode" open="(" separator="," close=")">
                    #{excludeSkuCode}
                </foreach>
            </if>
            ) AS spuCount,
            (
                SELECT COUNT(DISTINCT ps.sale_skc_id)
                FROM sale_sku ss
                INNER JOIN sale_goods sg ON ss.sale_goods_id = sg.sale_goods_id
                INNER JOIN sale_skc ps ON ss.sale_skc_id = ps.sale_skc_id
                WHERE ss.deleted = 0
                AND sg.deleted = 0
                AND ps.deleted = 0
                AND ss.publish_state = 1
                AND ss.enable = 1
                AND sg.publish_state = 1
                AND sg.platform_product_id IS NOT NULL
                AND ss.platform_sku_id IS NOT NULL
                AND ps.state = 1
                <if test="req.countryList != null and !req.countryList.isEmpty()">
                    AND ss.country IN
                    <foreach collection="req.countryList" item="countryCode" open="(" separator="," close=")">
                        #{countryCode}
                    </foreach>
                </if>
                <if test="req.shopIds != null and !req.shopIds.isEmpty()">
                    AND sg.shop_id IN
                    <foreach collection="req.shopIds" item="shopId" open="(" separator="," close=")">
                        #{shopId}
                    </foreach>
                </if>
                <if test="req.spuCodes != null and !req.spuCodes.isEmpty()">
                    AND sg.spu_code IN
                    <foreach collection="req.spuCodes" item="spuCode" open="(" separator="," close=")">
                        #{spuCode}
                    </foreach>
                </if>
                <if test="req.skcCodes != null and !req.skcCodes.isEmpty()">
                    AND ps.skc IN
                    <foreach collection="req.skcCodes" item="skcCode" open="(" separator="," close=")">
                        #{skcCode}
                    </foreach>
                </if>
                <if test="req.skuCodes != null and !req.skuCodes.isEmpty()">
                    AND ss.seller_sku IN
                    <foreach collection="req.skuCodes" item="skuCode" open="(" separator="," close=")">
                        #{skuCode}
                    </foreach>
                </if>
                <if test="req.excludeSkuCodes != null and !req.excludeSkuCodes.isEmpty()">
                    AND ss.seller_sku NOT IN
                    <foreach collection="req.excludeSkuCodes" item="excludeSkuCode" open="(" separator="," close=")">
                        #{excludeSkuCode}
                    </foreach>
                </if>
        ) AS skcCount
    </select>

    <select id="pageByPageProductSkuReq" resultType="tech.tiangong.pop.dao.entity.dto.PageProductSkuDto">
        select u.shop_sku,
               u.seller_sku,
               c.skc,
               g.spu_code,
               u.size_name
        from sale_sku u
                 join sale_goods g on u.sale_goods_id = g.sale_goods_id
                 join sale_skc c on u.sale_skc_id = c.sale_skc_id
                 join shop s on g.shop_id = s.shop_id
        where u.platform_product_id is not null
          and platform_seller_id = #{req.platformSellerId}
          and u.deleted = 0
          and g.deleted = 0
          and s.deleted = 0
        order by u.sale_sku_id
    </select>

    <select id="getListSizeBySkc" resultType="tech.tiangong.pop.dao.entity.dto.SizeBySkcDto">
        select spu_code, skc, color, color_code, size_name
        from sale_sku u
                 join sale_skc c on u.sale_skc_id = c.sale_skc_id
                 join sale_goods g on c.sale_goods_id = g.sale_goods_id
        where g.platform_product_id is not null
          and u.seller_sku is not null
          and c.combo != 1
          and skc in
            <foreach collection="req.skcList" item="skc" open="(" separator="," close=")">
                #{skc}
            </foreach>
    </select>

    <select id="findSaleGoodsIdsByProductSkuInfoAndShopIds" resultType="java.lang.Long">
        SELECT DISTINCT ss.sale_goods_id
        FROM sale_sku ss
        JOIN sale_goods sg ON ss.sale_goods_id = sg.sale_goods_id
        WHERE ss.deleted = 0 and sg.deleted = 0
        AND ss.enable = #{enabledCode}
        AND ss.publish_state = #{activeStateCode}
        AND ss.platform_sku_id IS NOT NULL
        AND ss.product_id IN
        <foreach item="item" collection="productIds" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND ss.seller_sku IN
        <foreach item="item" collection="sellerSkus" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND ss.country IN
        <foreach item="item" collection="countries" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND sg.shop_id IN
        <foreach item="item" collection="shopIds" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="querySkuByUpdateStockPrice" resultType="tech.tiangong.pop.dao.entity.dto.QuerySkuByUpdateStockPriceDto">
        select
            country,
            c.product_skc_id,
            c.skc,
            c.color,
            c.combo,
            barcode,
            sale_sku_id,
            size_name,
            stock_quantity,
            sale_price,
            retail_price,
            last_sale_price,
            last_retail_price
        from sale_skc c
            join sale_sku u on c.sale_skc_id = u.sale_skc_id
        where
             c.sale_goods_id in
            <foreach item="item" collection="saleGoodsIds" open="(" separator="," close=")">
                #{item}
            </foreach>
            and c.deleted = 0
            and u.deleted = 0
            and platform_product_id is not null
            and seller_sku is not null
    </select>

    <update id="fixEtaUpdate">
        <foreach collection="list" item="data" index="index" separator=";">
            update sale_sku
            set
            <if test="data.delayDeliveryDays !=null">
                delay_delivery_days = ${data.delayDeliveryDays},
            </if>
                revised_time = now()
            where sale_sku_id = ${data.saleSkuId}
        </foreach>
    </update>
</mapper>
