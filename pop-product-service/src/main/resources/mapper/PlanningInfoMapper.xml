<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tech.tiangong.pop.dao.mapper.PlanningInfoMapper">

    <resultMap type="tech.tiangong.pop.dao.entity.PlanningInfo" id="PlanningInfoMap">
        <!--@mbg.generated-->
        <!--@Table planning_info-->
        <id property="planningId" column="planning_id" jdbcType="BIGINT"/>
        <result property="planningName" column="planning_name" jdbcType="VARCHAR"/>
        <result property="channelId" column="channel_id" jdbcType="BIGINT"/>
        <result property="channelName" column="channel_name" jdbcType="VARCHAR"/>
        <result property="platformId" column="platform_id" jdbcType="BIGINT"/>
        <result property="platformName" column="platform_name" jdbcType="VARCHAR"/>
        <result property="applicableMonth" column="applicable_month" jdbcType="VARCHAR"/>
        <result property="creatorId" column="creator_id" jdbcType="BIGINT"/>
        <result property="creatorName" column="creator_name" jdbcType="VARCHAR"/>
        <result property="createdTime" column="created_time" jdbcType="TIMESTAMP"/>
        <result property="reviserId" column="reviser_id" jdbcType="BIGINT"/>
        <result property="reviserName" column="reviser_name" jdbcType="VARCHAR"/>
        <result property="revisedTime" column="revised_time" jdbcType="TIMESTAMP"/>
        <result property="deleted" column="deleted" jdbcType="INTEGER"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        planning_id
        ,planning_name
        ,channel_id
        ,channel_name
        ,platform_id
        ,platform_name
        ,applicable_month
        ,creator_id
        ,creator_name
        ,created_time
        ,reviser_id
        ,reviser_name
        ,revised_time
        ,deleted
    </sql>

    <select id="selectListPage" resultType="tech.tiangong.pop.dao.entity.PlanningInfo">
        select *
        from planning_info
        <where>
            deleted = 0
            <if test="req.createdStartTime != null and req.createdEndTime != null">
                and created_time between #{req.createdStartTime} and #{req.createdEndTime}
            </if>
            <if test="req.planningName != null and req.planningName != ''">
                and planning_name like concat('%',#{req.planningName},'%')
            </if>
            <if test="req.creatorId != null">
                and creator_id = #{req.creatorId}
            </if>
        </where>
        order by created_time desc
    </select>
</mapper>

