<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tech.tiangong.pop.dao.mapper.PlanningCategoryPriceMapper">

    <select id="getSumQuantityByCode" resultType="integer">
        select
            sum(supply_quantity) as total_quantity
        from planning_category_price price
                 join planning_info p on price.planning_id = p.planning_id
        where price.deleted = 0
          and p.deleted = 0
          and p.applicable_month = DATE_FORMAT(NOW(), '%Y-%m')
          and price.last_level_category_code = #{req.categoryCode}
          and price.supply_mode_code = #{req.supplyModeCode}
          and price.shop_id = #{req.shopId}
    </select>

    <delete id="deleteByPlanningIdPhysical">
        delete from planning_category_price where planning_id = #{planningId}
        <if test="shopId != null">
            and shop_id = #{shopId}
        </if>
        <if test="countrySiteCode != null and countrySiteCode != ''">
            and country_site_code = #{countrySiteCode}
        </if>
    </delete>
</mapper>

