<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tech.tiangong.pop.dao.mapper.DownloadTaskMapper">
    <!-- 可以在这里添加自定义的SQL语句 -->

    <select id="pageQuery" resultType="tech.tiangong.pop.resp.settings.DownloadTaskPageVo">
        SELECT dt.download_task_id,
               dt.task_name,
               dt.task_type,
               dt.status,
               dt.result_files,
               dt.retry_count,
               dt.error_reason,
               dt.creator_name,
               dt.created_time
        FROM download_task dt
        WHERE dt.deleted = 0
        <if test="query.taskName != null and query.taskName != ''">
            AND dt.task_name LIKE CONCAT('%', #{query.taskName}, '%')
        </if>
        <if test="query.creatorName != null and query.creatorName != ''">
            AND dt.creator_name LIKE CONCAT('%', #{query.creatorName}, '%')
        </if>
        <if test="query.creatorId != null">
            AND dt.creator_id = #{query.creatorId}
        </if>
        ORDER BY dt.created_time DESC
    </select>
</mapper>
