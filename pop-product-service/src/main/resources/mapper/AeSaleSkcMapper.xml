<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tech.tiangong.pop.dao.mapper.AeSaleSkcMapper">

    <select id="pageBySkuRevisedTime" resultType="java.lang.Long">
        select c.sale_skc_id
        from ae_sale_sku u
                 join ae_sale_skc c on u.sale_skc_id = c.sale_skc_id
                 join ae_sale_goods g on c.sale_goods_id = g.sale_goods_id
        where u.revised_time between #{req.revisedStartTime} and #{req.revisedEndTime}
          and g.platform_product_id is not null
          and u.seller_sku is not null
          and u.deleted = 0
          and g.deleted = 0
          and c.deleted = 0
          and g.publish_state in
            <foreach collection="req.publishStateList" item="publishState" open="(" separator="," close=")">
                #{publishState}
            </foreach>
        group by c.sale_skc_id
        order by c.sale_skc_id
    </select>
</mapper>

