<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tech.tiangong.pop.dao.mapper.LazadaBrandMapper">
    <insert id="batchUpsertBrand">
        INSERT INTO lazada_brand
            (lazada_brand_id, global_identifier, brand_id, name_en, name, country, creator_id, creator_name, created_time, tenant_id)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.lazadaBrandId}, #{item.globalIdentifier}, #{item.brandId}, #{item.nameEn}, #{item.name}, #{item.country},
             #{item.creatorId}, #{item.creatorName}, #{item.createdTime}, #{item.tenantId})
        </foreach>
        ON DUPLICATE KEY UPDATE
            revised_time = NOW()
    </insert>
</mapper>
