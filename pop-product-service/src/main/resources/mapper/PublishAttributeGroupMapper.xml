<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tech.tiangong.pop.dao.mapper.PublishAttributeGroupMapper">
    <select id="countAttributeAmount" resultType="tech.tiangong.pop.dto.GroupCountAttributeDto">
        select count(1) as amount,attribute_group_id as groupId
        from publish_attribute
        where deleted=0
        <if test="groupIds != null and groupIds.size > 1">
            and attribute_group_id IN
            <foreach item="item" index="index" collection="groupIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        group by attribute_group_id
    </select>

    <select id="listAllGroupIdByAttributeIds">
        select distinct attribute_group_id
        from publish_attribute
        where attribute_id IN
        <foreach item="item" index="index" collection="attributeIds" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
</mapper>

