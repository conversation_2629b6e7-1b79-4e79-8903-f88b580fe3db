<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tech.tiangong.pop.dao.mapper.ProductOperateLogMapper">

    <resultMap type="tech.tiangong.pop.dao.entity.ProductOperateLog" id="ProductOperateLogMap">
    <!--@mbg.generated-->
    <!--@Table product_operate_log-->
        <id property="productUpdateLogId" column="product_update_log_id" jdbcType="BIGINT"/>
        <result property="logType" column="log_type" jdbcType="INTEGER"/>
        <result property="content" column="content" jdbcType="VARCHAR"/>
        <result property="deleted" column="deleted" jdbcType="INTEGER"/>
        <result property="creatorId" column="creator_id" jdbcType="BIGINT"/>
        <result property="creatorName" column="creator_name" jdbcType="VARCHAR"/>
        <result property="createdTime" column="created_time" jdbcType="TIMESTAMP"/>
        <result property="reviserId" column="reviser_id" jdbcType="BIGINT"/>
        <result property="reviserName" column="reviser_name" jdbcType="VARCHAR"/>
        <result property="revisedTime" column="revised_time" jdbcType="TIMESTAMP"/>
    </resultMap>
    <sql id="Base_Column_List">
    <!--@mbg.generated-->
 product_update_log_id
 ,log_type
 ,content
 ,deleted
 ,creator_id
 ,creator_name
 ,created_time
 ,reviser_id
 ,reviser_name
 ,revised_time
  </sql>
</mapper>

