<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tech.tiangong.pop.dao.mapper.TemuSaleSkuMapper">


    <select id="querySkuByUpdateStockPrice" resultType="tech.tiangong.pop.dao.entity.dto.TemuQuerySkuByCountryDto">

        SELECT c.product_skc_id,
        c.skc,
        c.color,
        sale_sku_id,
        country,
        country_name,
        min(c.local_price) as min_local_price,
        sale_price,
        declared_price,
        u.created_time,
        recommended_price,
        manufacturer_recommended_price
        FROM temu_sale_skc c
        JOIN temu_sale_sku u ON c.sale_skc_id = u.sale_skc_id
        WHERE
        c.sale_goods_id in
        <foreach item="item" collection="saleGoodsIds" open="(" separator="," close=")">
            #{item}
        </foreach>
        and c.deleted = 0
        and u.deleted = 0
        and platform_product_id is not null
        and seller_sku is not null
        GROUP BY country, skc
    </select>


</mapper>
