<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tech.tiangong.pop.dao.mapper.BarcodeUnitSnapshotMapper">
    <select id="getBarcodeUnitList" resultType="tech.tiangong.pop.dao.entity.dto.GetBarcodeUnitDto">
        select
            a.seller_sku_flat_id,
            b.barcode_ref_id,
            b.barcode,
            c.snapshot_id,
            c.unit,
            c.creator_name,
            c.created_time
        from seller_sku_flat_info a
                 join seller_sku_barcode_ref b on a.seller_sku_flat_id = b.seller_sku_flat_id
                 join barcode_unit_snapshot c on b.barcode_ref_id = c.barcode_ref_id
        where a.seller_sku_flat_id in
        <foreach collection="sellerSkuFlatIds" item="sellerSkuFlatId" open="(" separator="," close=")">
            #{sellerSkuFlatId}
        </foreach>
    </select>
</mapper>

