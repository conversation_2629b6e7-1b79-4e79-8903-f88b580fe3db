<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tech.tiangong.pop.dao.mapper.TemuSaleGoodsMapper">


    <select id="pageTemu" resultType="tech.tiangong.pop.resp.product.temu.ProductTemuPageResp">
        SELECT
        p.product_id,
        p.main_img_url,
        p.spu_code,
        p.supply_mode,
        p.waves,
        p.category_code,
        p.category_name,
        p.creator_id,
        p.creator_name,
        p.created_time,
        p.reviser_id,
        p.reviser_name,
        p.revised_time,
        p.price_exception,
        p.is_sync_platform as syncState,
        sg.update_state AS `update`,
        sg.cost_price_update_state,
        sg.platform_id,
        sg.channel_id,
        sg.error_state as is_error,
        sg.error_info,
        sg.sale_goods_id,
        sg.shop_id,
        sg.shop_name,
        group_concat(distinct ps.select_status) as selectStatusListStr
        FROM
        product p
        JOIN temu_sale_goods sg ON p.product_id = sg.product_id
        JOIN temu_sale_skc ps ON sg.sale_goods_id = ps.sale_goods_id
        JOIN shop s on sg.shop_id = s.shop_id
        WHERE
        p.deleted = 0
        AND sg.deleted = 0
        AND sg.shop_id is NOT NULL
        <if test="req.supplyMode != null and req.supplyMode != ''">
            AND p.supply_mode = #{req.supplyMode}
        </if>

        <if test="req.businessType != null and req.businessType != ''">
            AND sg.business_type = #{req.businessType}
        </if>

        <if test="req.platformSpuIdList != null and req.platformSpuIdList.size > 0">
            AND sg.platform_product_id in
            <foreach item="item" index="index" collection="req.platformSpuIdList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="req.platformSkcIdList != null and req.platformSkcIdList.size > 0">
            AND ps.platform_skc_id in
            <foreach item="item" index="index" collection="req.platformSkcIdList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

        <if test="req.storeSubject != null and req.storeSubject != ''">
            AND s.entity_code = #{req.storeSubject}
        </if>

        <if test="req.selectStyleName != null and req.selectStyleName != ''">
            AND p.select_style_name LIKE CONCAT('%', #{req.selectStyleName}, '%')
        </if>
        <if test="req.updateFlag != null and req.updateFlag == 1">
            AND sg.update_state = #{req.updateFlag}
        </if>
        <if test="req.costPriceUpdateState != null">
            AND sg.cost_price_update_state = #{req.costPriceUpdateState}
        </if>
        <if test="req.spuList != null and req.spuList.size > 1">
            AND p.spu_code IN
            <foreach item="item" index="index" collection="req.spuList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="req.spuList != null and req.spuList.size == 1">
            AND p.spu_code
            <foreach item="item" index="index" collection="req.spuList">
                LIKE CONCAT('%', #{item}, '%')
            </foreach>
        </if>
        <if test="req.selectStatusList != null and req.selectStatusList.size > 0">
            AND ps.select_status in
            <foreach item="item" index="index" collection="req.selectStatusList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="req.clothingStyleCode != null and req.clothingStyleCode != ''">
            AND p.clothing_style_code = #{req.clothingStyleCode}
        </if>
        <if test="req.planningType != null ">
            AND p.planning_type = #{req.planningType}
        </if>
        <if test="req.marketCode != null and req.marketCode != ''">
            AND p.market_code = #{req.marketCode}
        </if>
        <if test="req.marketSeriesCode != null and req.marketSeriesCode != ''">
            AND p.market_series_code = #{req.marketSeriesCode}
        </if>
        <if test="req.waves != null and req.waves != ''">
            AND p.waves = #{req.waves}
        </if>
        <if test="req.shopId != null">
            AND sg.shop_id = #{req.shopId}
        </if>
        <if test="req.businessType != null">
            AND sg.business_type = #{req.businessType}
        </if>
        <if test="req.productTitle != null and req.productTitle != ''">
            AND sg.product_name LIKE CONCAT('%', #{req.productTitle}, '%')
        </if>
        <if test="req.shopName != null and req.shopName != ''">
            AND sg.shop_name LIKE CONCAT('%', #{req.shopName}, '%')
        </if>
        <if test="req.createdTimeStart != null and req.createdTimeEnd != null">
            AND sg.created_time BETWEEN #{req.createdTimeStart} AND #{req.createdTimeEnd}
        </if>
        <if test="req.publishTimeStart != null and req.publishTimeEnd != null">
            AND sg.publish_time BETWEEN #{req.publishTimeStart} AND #{req.publishTimeEnd}
        </if>
        <if test="req.creatorName != null and req.creatorName != ''">
            AND sg.creator_name LIKE CONCAT('%', #{req.creatorName}, '%')
        </if>
        <if test="req.reviserName != null and req.reviserName != ''">
            AND sg.reviser_name LIKE CONCAT('%', #{req.reviserName}, '%')
        </if>
        <if test="req.publishAuthor != null and req.publishAuthor != ''">
            AND sg.publish_user_name LIKE CONCAT('%', #{req.publishAuthor}, '%')
        </if>

        <if test="req.reviserTimeStart != null  and req.reviserTimeEnd != null ">
            AND sg.revised_time &gt;= #{req.reviserTimeStart} AND sg.revised_time &lt;= #{req.reviserTimeEnd}
        </if>
        <if test="req.categoryCode != null and req.categoryCode != ''">
            AND ( sg.category_code = #{req.categoryCode} OR p.category_code = #{req.categoryCode} )
        </if>
        <if test="req.skcList != null and req.skcList.size > 0">
            AND ps.skc IN
            <foreach item="item" index="index" collection="req.skcList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="req.errorFlag != null">
            AND sg.error_state = #{req.errorFlag}
        </if>
        GROUP BY
        sg.sale_goods_id
        ORDER BY
        sg.revised_time DESC
    </select>


    <select id="countTemu" resultType="tech.tiangong.pop.dao.entity.dto.CountAePublishStateDto">
        SELECT  ps.select_status as publishState,
                count(distinct sg.sale_goods_id) as count_num,
                count(distinct sg.sale_goods_id) as spuCount,
                count(distinct ps.sale_skc_id) as skcCount,
                group_concat(distinct sg.sale_goods_id ) as concatSpus,
                group_concat(distinct ps.sale_skc_id ) as concatSkcs
        FROM
        product p
        JOIN temu_sale_goods sg ON p.product_id = sg.product_id
        JOIN temu_sale_skc ps ON sg.sale_goods_id = ps.sale_goods_id
        JOIN shop s on sg.shop_id = s.shop_id
        WHERE
        p.deleted = 0
        AND sg.deleted = 0
        AND sg.shop_id is NOT NULL
        <if test="req.supplyMode != null and req.supplyMode != ''">
            AND p.supply_mode = #{req.supplyMode}
        </if>
        <if test="req.platformSpuIdList != null and req.platformSpuIdList.size > 0">
            AND sg.platform_product_id in
            <foreach item="item" index="index" collection="req.platformSpuIdList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="req.platformSkcIdList != null and req.platformSkcIdList.size > 0">
            AND ps.platform_skc_id in
            <foreach item="item" index="index" collection="req.platformSkcIdList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="req.storeSubject != null and req.storeSubject != ''">
            AND s.entity_code = #{req.storeSubject}
        </if>
        <if test="req.selectStyleName != null and req.selectStyleName != ''">
            AND p.select_style_name LIKE CONCAT('%', #{req.selectStyleName}, '%')
        </if>
        <if test="req.updateFlag != null and req.updateFlag == 1">
            AND sg.update_state = #{req.updateFlag}
        </if>
        <if test="req.costPriceUpdateState != null">
            AND sg.cost_price_update_state = #{req.costPriceUpdateState}
        </if>
        <if test="req.spuList != null and req.spuList.size > 1">
            AND p.spu_code IN
            <foreach item="item" index="index" collection="req.spuList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="req.spuList != null and req.spuList.size == 1">
            AND p.spu_code
            <foreach item="item" index="index" collection="req.spuList">
                LIKE CONCAT('%', #{item}, '%')
            </foreach>
        </if>
        <if test="req.clothingStyleCode != null and req.clothingStyleCode != ''">
            AND p.clothing_style_code = #{req.clothingStyleCode}
        </if>
        <if test="req.waves != null and req.waves != ''">
            AND p.waves = #{req.waves}
        </if>
        <if test="req.shopId != null">
            AND sg.shop_id = #{req.shopId}
        </if>
        <if test="req.businessType != null">
            AND sg.business_type = #{req.businessType}
        </if>
        <if test="req.productTitle != null and req.productTitle != ''">
            AND sg.product_name LIKE CONCAT('%', #{req.productTitle}, '%')
        </if>
        <if test="req.shopName != null and req.shopName != ''">
            AND sg.shop_name LIKE CONCAT('%', #{req.shopName}, '%')
        </if>
        <if test="req.createdTimeStart != null and req.createdTimeEnd != null">
            AND sg.created_time BETWEEN #{req.createdTimeStart} AND #{req.createdTimeEnd}
        </if>
        <if test="req.publishTimeStart != null and req.publishTimeEnd != null">
            AND sg.publish_time BETWEEN #{req.publishTimeStart} AND #{req.publishTimeEnd}
        </if>
        <if test="req.creatorName != null and req.creatorName != ''">
            AND sg.creator_name LIKE CONCAT('%', #{req.creatorName}, '%')
        </if>
        <if test="req.reviserName != null and req.reviserName != ''">
            AND sg.reviser_name LIKE CONCAT('%', #{req.reviserName}, '%')
        </if>
        <if test="req.publishAuthor != null and req.publishAuthor != ''">
            AND sg.publish_user_name LIKE CONCAT('%', #{req.publishAuthor}, '%')
        </if>
        <if test="req.selectStatusList != null and req.selectStatusList.size > 0">
            AND ps.select_status in
            <foreach item="item" index="index" collection="req.selectStatusList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

        <if test="req.reviserTimeStart != null  and req.reviserTimeEnd != null ">
            AND sg.revised_time &gt;= #{req.reviserTimeStart} AND sg.revised_time &lt;= #{req.reviserTimeEnd}
        </if>
        <if test="req.categoryCode != null and req.categoryCode != ''">
            AND sg.category_code = #{req.categoryCode}
        </if>
        <if test="req.skcList != null and req.skcList.size > 0">
            AND ps.skc IN
            <foreach item="item" index="index" collection="req.skcList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="req.errorFlag != null">
            AND sg.error_state = #{req.errorFlag}
        </if>
        <!--        <if test="req.tagCodes != null and req.tagCodes.size > 0">-->
        <!--            AND-->
        <!--            <foreach collection="req.tagCodes" item="valueList" index="key" open="(" close=")" separator=" AND ">-->
        <!--                (ta.tag_key = #{key} AND ta.tag_value IN-->
        <!--                <foreach collection="valueList" item="value" open="(" close=")" separator=",">-->
        <!--                    #{value}-->
        <!--                </foreach>-->
        <!--                )-->
        <!--            </foreach>-->
        <!--        </if>-->
        group by ps.select_status
    </select>


    <select id="countSpuAndSkc" resultType="tech.tiangong.pop.req.product.temu.ProductStatsVO">
        SELECT
        COUNT(DISTINCT p.spu_code) AS totalSpu,
        COUNT(DISTINCT ps.skc) AS totalSkc
        FROM product p
        INNER JOIN temu_sale_goods sg ON p.product_id = sg.product_id
        LEFT JOIN temu_sale_skc ps ON sg.sale_goods_id = ps.sale_goods_id
        JOIN shop s on sg.shop_id = s.shop_id
        WHERE
        p.deleted = 0
        AND sg.deleted = 0
        AND sg.shop_id IS NOT NULL
        <if test="req.supplyMode != null and req.supplyMode != ''">
            AND p.supply_mode = #{req.supplyMode}
        </if>
        <if test="req.platformSpuIdList != null and req.platformSpuIdList.size > 0">
            AND sg.platform_product_id in
            <foreach item="item" index="index" collection="req.platformSpuIdList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="req.platformSkcIdList != null and req.platformSkcIdList.size > 0">
            AND ps.platform_skc_id in
            <foreach item="item" index="index" collection="req.platformSkcIdList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="req.storeSubject != null and req.storeSubject != ''">
            AND s.entity_code = #{req.storeSubject}
        </if>
        <if test="req.selectStyleName != null and req.selectStyleName != ''">
            AND p.select_style_name LIKE CONCAT('%', #{req.selectStyleName}, '%')
        </if>
        <if test="req.updateFlag != null and req.updateFlag == 1">
            AND sg.update_state = #{req.updateFlag}
        </if>
        <if test="req.costPriceUpdateState != null">
            AND sg.cost_price_update_state = #{req.costPriceUpdateState}
        </if>
        <if test="req.spuList != null and req.spuList.size > 1">
            AND p.spu_code IN
            <foreach item="item" index="index" collection="req.spuList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="req.spuList != null and req.spuList.size == 1">
            AND p.spu_code
            <foreach item="item" index="index" collection="req.spuList">
                LIKE CONCAT('%', #{item}, '%')
            </foreach>
        </if>
        <if test="req.clothingStyleCode != null and req.clothingStyleCode != ''">
            AND p.clothing_style_code = #{req.clothingStyleCode}
        </if>
        <if test="req.waves != null and req.waves != ''">
            AND p.waves = #{req.waves}
        </if>
        <if test="req.shopId != null">
            AND sg.shop_id = #{req.shopId}
        </if>
        <if test="req.productTitle != null and req.productTitle != ''">
            AND sg.product_name LIKE CONCAT('%', #{req.productTitle}, '%')
        </if>
        <if test="req.businessType != null">
            AND sg.business_type = #{req.businessType}
        </if>
        <if test="req.shopName != null and req.shopName != ''">
            AND sg.shop_name LIKE CONCAT('%', #{req.shopName}, '%')
        </if>
        <if test="req.createdTimeStart != null and req.createdTimeEnd != null">
            AND sg.created_time BETWEEN #{req.createdTimeStart} AND #{req.createdTimeEnd}
        </if>
        <if test="req.publishTimeStart != null and req.publishTimeEnd != null">
            AND sg.publish_time BETWEEN #{req.publishTimeStart} AND #{req.publishTimeEnd}
        </if>
        <if test="req.creatorName != null and req.creatorName != ''">
            AND sg.creator_name LIKE CONCAT('%', #{req.creatorName}, '%')
        </if>
        <if test="req.reviserName != null and req.reviserName != ''">
            AND sg.reviser_name LIKE CONCAT('%', #{req.reviserName}, '%')
        </if>
        <if test="req.publishAuthor != null and req.publishAuthor != ''">
            AND sg.publish_user_name LIKE CONCAT('%', #{req.publishAuthor}, '%')
        </if>
        <if test="req.selectStatusList != null and req.selectStatusList.size > 0">
            AND ps.select_status in
            <foreach item="item" index="index" collection="req.selectStatusList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

        <if test="req.reviserTimeStart != null  and req.reviserTimeEnd != null ">
            AND sg.revised_time &gt;= #{req.reviserTimeStart} AND sg.revised_time &lt;= #{req.reviserTimeEnd}
        </if>
        <if test="req.categoryCode != null and req.categoryCode != ''">
            AND sg.category_code = #{req.categoryCode}
        </if>
        <if test="req.skcList != null and req.skcList.size > 0">
            AND ps.skc IN
            <foreach item="item" index="index" collection="req.skcList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="req.errorFlag != null">
            AND sg.error_state = #{req.errorFlag}
        </if>
    </select>

</mapper>

