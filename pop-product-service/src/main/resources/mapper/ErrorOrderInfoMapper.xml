<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tech.tiangong.pop.dao.mapper.ErrorOrderInfoMapper">

    <resultMap type="tech.tiangong.pop.dao.entity.ErrorOrderInfo" id="ErrorOrderInfoMap">
        <!--@mbg.generated-->
        <!--@Table error_order_info-->
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="orderCode" column="order_code" jdbcType="VARCHAR"/>
        <result property="shopShortCode" column="shop_short_code" jdbcType="VARCHAR"/>
        <result property="shopName" column="shop_name" jdbcType="VARCHAR"/>
        <result property="countrySiteCode" column="country_site_code" jdbcType="VARCHAR"/>
        <result property="countrySiteName" column="country_site_name" jdbcType="VARCHAR"/>
        <result property="orderCreatedTime" column="order_created_time" jdbcType="TIMESTAMP"/>
        <result property="cancelTotalQuantity" column="cancel_total_quantity" jdbcType="INTEGER"/>
        <result property="orderCancelTime" column="order_cancel_time" jdbcType="TIMESTAMP"/>
        <result property="orderCancelReason" column="order_cancel_reason" jdbcType="VARCHAR"/>
        <result property="orderCancellerId" column="order_canceller_id" jdbcType="BIGINT"/>
        <result property="orderCancellerName" column="order_canceller_name" jdbcType="VARCHAR"/>
        <result property="opProcessingStatus" column="op_processing_status" jdbcType="INTEGER"/>
        <result property="opProcessorId" column="op_processor_id" jdbcType="BIGINT"/>
        <result property="opProcessorName" column="op_processor_name" jdbcType="VARCHAR"/>
        <result property="opContactTime" column="op_contact_time" jdbcType="TIMESTAMP"/>
        <result property="csProcessingStatus" column="cs_processing_status" jdbcType="INTEGER"/>
        <result property="csProcessorId" column="cs_processor_id" jdbcType="BIGINT"/>
        <result property="csProcessorName" column="cs_processor_name" jdbcType="VARCHAR"/>
        <result property="csContactTime" column="cs_contact_time" jdbcType="TIMESTAMP"/>
        <result property="creatorId" column="creator_id" jdbcType="BIGINT"/>
        <result property="creatorName" column="creator_name" jdbcType="VARCHAR"/>
        <result property="createdTime" column="created_time" jdbcType="TIMESTAMP"/>
        <result property="reviserId" column="reviser_id" jdbcType="BIGINT"/>
        <result property="reviserName" column="reviser_name" jdbcType="VARCHAR"/>
        <result property="revisedTime" column="revised_time" jdbcType="TIMESTAMP"/>
        <result property="deleted" column="deleted" jdbcType="INTEGER"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id
        ,order_code
        ,shop_short_code
        ,shop_name
        ,country_site_code
        ,country_site_name
        ,order_created_time
        ,cancel_total_quantity
        ,order_cancel_time
        ,order_cancel_reason
        ,order_canceller_id
        ,order_canceller_name
        ,op_processing_status
        ,op_processor_id
        ,op_processor_name
        ,op_contact_time
        ,cs_processing_status
        ,cs_processor_id
        ,cs_processor_name
        ,cs_contact_time
        ,creator_id
        ,creator_name
        ,created_time
        ,reviser_id
        ,reviser_name
        ,revised_time
        ,deleted
    </sql>


    <select id="selectListPage" resultType="tech.tiangong.pop.dao.entity.ErrorOrderInfo">
        select eoi.*
        from error_order_info eoi left join shop sp on eoi.shop_short_code = sp.short_code and sp.deleted = 0
        <where>
            eoi.deleted = 0
            <if test="req.orderCodes != null and req.orderCodes.size() > 0">
                and eoi.order_code in
                <foreach collection="req.orderCodes" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="req.orderCreatedStartTime != null and req.orderCreatedEndTime != null">
                and eoi.order_created_time between #{req.orderCreatedStartTime} and #{req.orderCreatedEndTime}
            </if>
            <if test="req.orderCancelStartTime != null and req.orderCancelEndTime != null">
                and eoi.order_cancel_time between #{req.orderCancelStartTime} and #{req.orderCancelEndTime}
            </if>
            <if test="req.orderCanceller != null and req.orderCanceller != ''">
                and eoi.order_canceller_id = #{req.orderCanceller}
            </if>
            <if test="req.opProcessingStatus != null">
                and eoi.op_processing_status = #{req.opProcessingStatus}
            </if>
            <if test="req.csProcessingStatus != null">
                and eoi.cs_processing_status = #{req.csProcessingStatus}
            </if>
            <if test="req.shopId != null and req.shopId != ''">
                and sp.shop_id = #{req.shopId}
            </if>
            <if test="req.countrySiteCode != null and req.countrySiteCode.size() > 0">
                and eoi.country_site_code in
                <foreach collection="req.countrySiteCode" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="req.spuCode != null and req.spuCode != ''">
                and eoi.order_code in (select order_code from error_order_spu_info where spu_code in (#{req.spuCode}) and deleted = 0)
            </if>
        </where>
        order by eoi.created_time desc
    </select>
</mapper>
