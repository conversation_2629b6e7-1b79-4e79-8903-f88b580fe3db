<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tech.tiangong.pop.dao.mapper.PublishPlatformAttrMapper">

    <select id="listPlatformAttributeMapperByCategoryMappingId" resultType="tech.tiangong.pop.resp.category.AttributeMapperResp">
        select pa.platform_attr_id,
               pa.category_id,
               pa.platform_id,
               pa.category_mapping_id,
               pa.platform_attribute_label_name,
               pa.platform_attribute_key_name,
               pa.attribute_id,
               pa.country,
               pa.request_flag,

               attr.attribute_group_id,
               attr.attribute_name,
               attr.attribute_code
        from publish_platform_attr pa
            left join publish_attribute attr on pa.attribute_id = attr.attribute_id
        where pa.deleted=0 and attr.deleted=0
          and pa.category_mapping_id = #{categoryMappingId}
    </select>

    <select id="countAttributeMappingByCategoryMappingId" resultType="tech.tiangong.pop.resp.category.CountAttributeMappingByCategoryMapperIdResp">
        select count(1) as amount,
               pa.category_mapping_id
        from publish_platform_attr pa
        where pa.deleted=0
        AND pa.category_mapping_id IN
        <foreach item="categoryMappingId" index="index" collection="categoryMappingIds" open="(" separator="," close=")">
            #{categoryMappingId}
        </foreach>
        group by pa.category_mapping_id
    </select>

    <select id="countAttributeMappingByCategoryId" resultType="tech.tiangong.pop.resp.category.CountAttributeMappingByCategoryIdResp">
        select count(1) as amount,
        pa.category_id
        from publish_platform_attr pa
        where pa.deleted=0
        AND pa.category_id IN
        <foreach item="categoryId" index="index" collection="categoryIds" open="(" separator="," close=")">
            #{categoryId}
        </foreach>
        <if test="categoryMappingIds!=null and categoryMappingIds.size>0">
            AND pa.category_mapping_id IN
            <foreach item="categoryMappingId" index="index" collection="categoryMappingIds" open="(" separator="," close=")">
                #{categoryMappingId}
            </foreach>
        </if>
        group by pa.category_id
    </select>

</mapper>

