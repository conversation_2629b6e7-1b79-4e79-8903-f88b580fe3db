package tech.tiangong.pop.service.category.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.listener.ReadListener;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.util.Assert;
import org.springframework.web.multipart.MultipartFile;
import team.aikero.blade.core.enums.Bool;
import team.aikero.blade.core.exception.BusinessException;
import team.aikero.blade.core.protocol.PageVo;
import team.aikero.blade.sequence.id.IdHelper;
import team.aikero.blade.user.entity.CurrentUser;
import team.aikero.blade.user.holder.CurrentUserHolder;
import tech.tiangong.pop.bo.PublishGroupAttributeBO;
import tech.tiangong.pop.common.constant.SerialNumConstant;
import tech.tiangong.pop.common.exception.BaseBizException;
import tech.tiangong.pop.component.SerialNumComponent;
import tech.tiangong.pop.convert.PublishGroupAttrConvert;
import tech.tiangong.pop.dao.entity.PublishAttribute;
import tech.tiangong.pop.dao.entity.PublishAttributeGroup;
import tech.tiangong.pop.dao.entity.PublishAttributeValue;
import tech.tiangong.pop.dao.entity.PublishPlatformAttrValue;
import tech.tiangong.pop.dao.repository.*;
import tech.tiangong.pop.dto.ImportPublishAttributeDTO;
import tech.tiangong.pop.enums.PublishAttributeShowTypeEnum;
import tech.tiangong.pop.enums.PublishAttributeValueStateEnum;
import tech.tiangong.pop.event.OperatePublishAttributeEvent;
import tech.tiangong.pop.helper.PageRespHelper;
import tech.tiangong.pop.req.category.*;
import tech.tiangong.pop.resp.category.PublishAttributeGroupVo;
import tech.tiangong.pop.resp.category.PublishAttributeVo;
import tech.tiangong.pop.resp.category.PublishGroupWithAttributeVo;
import tech.tiangong.pop.service.category.PublishGroupAttrService;

import java.util.*;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Function;
import java.util.function.LongUnaryOperator;
import java.util.stream.Collectors;

@RequiredArgsConstructor
@Slf4j
@Service
public class PublishGroupAttrServiceImpl implements PublishGroupAttrService {

    private final PublishAttributeGroupRepository publishAttributeGroupRepository;
    private final PublishAttributeRepository publishAttributeRepository;
    private final PublishAttributeValueRepository publishAttributeValueRepository;
    private final PublishPlatformAttrRepository publishPlatformAttrRepository;
    private final PublishPlatformAttrValueRepository publishPlatformAttrValueRepository;
    private final PublishCategoryAttrRepository publishCategoryAttrRepository;
    private final ApplicationContext applicationContext;
    private final PublishGroupAttrConvert groupAttrConvert;
    private final SerialNumComponent serialNumComponent;

    @Override
    public void saveGroup(SavePublishAttributeGroupReq req) {
        PublishAttributeGroup publishAttributeGroup;
        if (req.getGroupId() != null) {
            publishAttributeGroup = publishAttributeGroupRepository.getById(req.getGroupId());
            Assert.isTrue(publishAttributeGroup != null, "分组不存在");
        } else {
            publishAttributeGroup = new PublishAttributeGroup();
            publishAttributeGroup.setAttributeGroupId(IdHelper.getId());
            publishAttributeGroup.setState(Bool.YES.getCode());
        }
        int platformFlag = req.getPlatformFlag()!=null && Bool.YES.getCode()==req.getPlatformFlag()?Bool.YES.getCode():Bool.NO.getCode();
        publishAttributeGroup.setPlatformFlag(platformFlag);
        publishAttributeGroup.setGroupName(req.getGroupName());
        boolean existName = publishAttributeGroupRepository.existGroupName(publishAttributeGroup.getAttributeGroupId(),
                publishAttributeGroup.getGroupName());
        Assert.isTrue(!existName, "分组名称已存在");
        publishAttributeGroupRepository.saveOrUpdate(publishAttributeGroup);
    }

    @Override
    public List<PublishAttributeGroupVo> listGroup(PublishAttributeGroupQueryReq req) {
        List<PublishAttributeGroup> list = publishAttributeGroupRepository.list(new LambdaQueryWrapper<PublishAttributeGroup>()
                .eq(StringUtils.isNotBlank(req.getGroupName()), PublishAttributeGroup::getGroupName, req.getGroupName())
                .eq(PublishAttributeGroup::getDeleted, Bool.NO.getCode())
                .orderByDesc(PublishAttributeGroup::getCreatedTime));
        return groupAttrConvert.trans2PublishAttributeGroupVos(list);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteGroup(List<Long> groupIds) {
        if (CollectionUtils.isEmpty(groupIds)) {
            return;
        }
        //判断分组下是否有属性，有则不能删除
        groupIds.forEach(groupId -> {
            if (publishAttributeRepository.hasAttributeByGroupId(groupId)) {
                throw new BaseBizException("分组下有属性，不能删除");
            }
            publishAttributeGroupRepository.deleteGroupById(groupId);
        });
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveAttribute(SavePublishAttributeReq req) {
        PublishAttributeGroup group = publishAttributeGroupRepository.getById(req.getAttributeGroupId());
        Assert.isTrue(group != null, "分组不存在");
        PublishAttributeShowTypeEnum showType = PublishAttributeShowTypeEnum.SINGLE_SELECT;
        PublishAttribute publishAttribute;
        if (req.getAttributeId() != null) {
            publishAttribute = publishAttributeRepository.getById(req.getAttributeId());
            Assert.isTrue(publishAttribute != null, "属性不存在");
        } else {
            publishAttribute = new PublishAttribute();
            publishAttribute.setAttributeId(IdHelper.getId());
            publishAttribute.setAttributeCode(generateCode());
            publishAttribute.setState(Bool.YES.getCode());
            publishAttribute.setShowType(showType.getCode());
        }
        //属性值类型默认下拉多选
        if(StringUtils.isNotBlank(req.getShowType())){
            showType = PublishAttributeShowTypeEnum.getByCode(req.getShowType());
            Assert.isTrue(showType.equals(PublishAttributeShowTypeEnum.UNKNOWN),"属性值类型不正确");
            publishAttribute.setShowType(showType.getCode());
        }
        publishAttribute.setAttributeName(req.getAttributeName());
        publishAttribute.setAttributeGroupId(group.getAttributeGroupId());
        //保存或者更新之前再检验一下名称和编码是否重复
        boolean existName = publishAttributeRepository.existAttributeName(publishAttribute.getAttributeGroupId(), publishAttribute.getAttributeId(), publishAttribute.getAttributeName());
        Assert.isTrue(!existName, "属性名已存在");
        boolean existCode = publishAttributeRepository.existAttributeCode(publishAttribute.getAttributeId(), publishAttribute.getAttributeCode());
        Assert.isTrue(!existCode, "属性编码已存在");
        //需要保存的属性值
        List<PublishAttributeValue> saveValues = new ArrayList<>();
        //需要删除的属性值
        List<PublishAttributeValue> deletedValues = new ArrayList<>();
        //找出已知属性值
        List<PublishAttributeValue> oldValues = Optional.ofNullable(publishAttributeValueRepository.listByAttrId(publishAttribute.getAttributeId()))
                .orElse(new ArrayList<>());
        Map<Long, PublishAttributeValue> oldValueIdMap = oldValues.stream().collect(Collectors.toMap(PublishAttributeValue::getAttributeValueId, Function.identity()));

        //找出已被平台关联的属性值
        List<PublishPlatformAttrValue> platformAttrValues = publishPlatformAttrValueRepository.listByAttribateValueIds(oldValues.stream().map(PublishAttributeValue::getAttributeValueId).collect(Collectors.toList()));
        Set<Long> usedAttributeValueIds = platformAttrValues.stream().map(PublishPlatformAttrValue::getAttributeValueId).collect(Collectors.toSet());

        if (!CollectionUtils.isEmpty(req.getValues())) {
            Map<String, Long> valueCount = req.getValues().stream().collect(Collectors.groupingBy(SavePublishAttributeReq.SavePublishAttributeValueReq::getValue, Collectors.counting()));
            for (Map.Entry<String, Long> entry : valueCount.entrySet()) {
                if (entry.getValue() > 1) {
                    throw new BaseBizException("属性值重复");
                }
            }
            for (SavePublishAttributeReq.SavePublishAttributeValueReq reqValue : req.getValues()) {
                if(showType.equals(PublishAttributeShowTypeEnum.NUM)
                        || showType.equals(PublishAttributeShowTypeEnum.PROPERTY_CHOOSE_AND_NUM)){
                    Assert.isTrue(StringUtils.isNotBlank(reqValue.getUnit()),"单位不能为空");
                }
                //修改值
                if (reqValue.getAttributeValueId() != null) {
                    PublishAttributeValue oldValue = oldValueIdMap.get(reqValue.getAttributeValueId());
                    Assert.isTrue(oldValue != null, "属性值id不存在" + reqValue.getAttributeValueId());
                    if (usedAttributeValueIds.contains(oldValue.getAttributeValueId())
                            && !oldValue.getAttributeValue().equals(reqValue.getValue())) {
                        throw new BaseBizException("属性值" + reqValue.getValue() + "已被平台关联，不能修改");
                    } else {
                        oldValue.setAttributeValue(reqValue.getValue());
                        oldValue.setUnit(reqValue.getUnit());
                        PublishAttributeValueStateEnum valueStateEnum = PublishAttributeValueStateEnum.getByCode(reqValue.getState());
                        Assert.isTrue(valueStateEnum != null, "属性值状态不正确");
                        oldValue.setState(valueStateEnum.code);
                        oldValue.setDefaultFlag(reqValue.getDefaultFlag()!=null && reqValue.getDefaultFlag()==1 ? Bool.YES.getCode() : Bool.NO.getCode());
                        saveValues.add(oldValue);
                    }
                } else {//添加值
                    PublishAttributeValue publishValue = new PublishAttributeValue();
                    publishValue.setAttributeValueId(IdHelper.getId());
                    publishValue.setAttributeId(publishAttribute.getAttributeId());
                    publishValue.setAttributeValue(reqValue.getValue());
                    publishValue.setUnit(reqValue.getUnit());
                    publishValue.setState(PublishAttributeValueStateEnum.ENABLE.code);
                    publishValue.setDefaultFlag(reqValue.getDefaultFlag()!=null && reqValue.getDefaultFlag()==1 ? Bool.YES.getCode() : Bool.NO.getCode());
                    saveValues.add(publishValue);
                }
            }
            //找出不再需要保存的属性值进行删除
            Set<Long> saveValueIds = saveValues.stream().map(PublishAttributeValue::getAttributeValueId).collect(Collectors.toSet());
            List<PublishAttributeValue> needDeleted = oldValues.stream().filter(v -> !saveValueIds.contains(v.getAttributeValueId())).collect(Collectors.toList());
            boolean hasAttributeValueUsed = publishPlatformAttrValueRepository.hasUsedAttributeValueIds(needDeleted.stream().map(PublishAttributeValue::getAttributeValueId).collect(Collectors.toList()));
            Assert.isTrue(!hasAttributeValueUsed, "有属性值已被平台关联，不能删除");
            deletedValues.addAll(needDeleted);
        } else if (!CollectionUtils.isEmpty(oldValues)) {//如果新保存没有属性值，但旧记录有，则要删除
            boolean hasAttributeValueUsed = publishPlatformAttrValueRepository.hasUsedAttributeValueIds(oldValues.stream().map(PublishAttributeValue::getAttributeValueId).collect(Collectors.toList()));
            Assert.isTrue(!hasAttributeValueUsed, "有属性值已被平台关联，不能删除");
            deletedValues.addAll(oldValues);
        }
        publishAttributeRepository.saveOrUpdate(publishAttribute);

        if (!CollectionUtils.isEmpty(saveValues)) {
            publishAttributeValueRepository.saveOrUpdateBatch(saveValues);
        }
        if (!CollectionUtils.isEmpty(deletedValues)) {
            publishAttributeValueRepository.removeBatchByIds(deletedValues.stream().map(PublishAttributeValue::getAttributeValueId).collect(Collectors.toList()));
        }

        //重新统计分组下的属性数量
        PublishAttribute finalPublishAttribute = publishAttribute;
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCompletion(int status) {
                //status:0表示事务已经成功提交，1表示业务出现异常
                if (status == 0) {
                    CurrentUser currentUser = CurrentUserHolder.get();
                    applicationContext.publishEvent(new OperatePublishAttributeEvent(this, List.of(finalPublishAttribute.getAttributeId()),currentUser.getId(),currentUser.getName()));
                }
            }
        });
    }

    @Override
    public PageVo<PublishAttributeVo> pageAttribute(PublishAttributePageQueryReq req) {
        PublishAttributeQueryReq query = new PublishAttributeQueryReq();
        BeanUtils.copyProperties(req, query);
        Page<PublishAttribute> pageInfo = publishAttributeRepository.queryPublishAttribute(req.getPageNum(), req.getPageSize(), query);
        if (CollectionUtils.isEmpty(pageInfo.getRecords())) {
            return PageRespHelper.empty(req.getPageNum());
        }
        List<PublishAttributeVo> list = groupAttrConvert.trans2PublishAttributeVo(pageInfo.getRecords());
        return PageRespHelper.ofR(pageInfo, list);
    }

    @Override
    public List<PublishGroupWithAttributeVo> listGroupWithAttribute(PublishAttributeQueryReq req) {
        List<PublishAttribute> list = publishAttributeRepository.queryPublishAttribute(req);
        return groupAttrConvert.trans2PublishGroupWithAttributeVo(list);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void removeAttribute(List<Long> attributeIds) {
        if (CollectionUtils.isEmpty(attributeIds)) {
            return;
        }
        boolean attributeHasUsedForCategory = publishCategoryAttrRepository.hasUsedAttrIds(attributeIds);
        Assert.isTrue(!attributeHasUsedForCategory, "属性已关联品类，如需删除请先在品类中移除该属性");
        boolean attributeHasUsed = publishPlatformAttrRepository.hasUsedAttrIds(attributeIds);
        Assert.isTrue(!attributeHasUsed, "有属性已被关联平台，不能删除");
        boolean attributeValueHasUsed = publishPlatformAttrValueRepository.hasUsedAttrIds(attributeIds);
        Assert.isTrue(!attributeValueHasUsed, "有属性值已被关联平台，不能删除");

        publishAttributeValueRepository.deleteByAttrIds(attributeIds);
        publishAttributeRepository.deleteByAttributeIds(attributeIds);

        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCompletion(int status) {
                //status:0表示事务已经成功提交，1表示业务出现异常
                if (status == 0) {
                    CurrentUser currentUser = CurrentUserHolder.get();
                    applicationContext.publishEvent(new OperatePublishAttributeEvent(this, attributeIds,currentUser.getId(),currentUser.getName()));
                }
            }
        });
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public List<String> importAttribute(MultipartFile file) {
        final List<String> errorInfos = new ArrayList<>();
        final List<PublishAttribute> saveAttrList = new ArrayList<>();
        try {
            EasyExcel.read(file.getInputStream(), ImportPublishAttributeDTO.class, new ReadListener<ImportPublishAttributeDTO>() {
                private final List<ImportPublishAttributeDTO> cachedDataList = new ArrayList<>();
                int successCount = 0;
                List<String> tempErrorList = new ArrayList<>();

                @Override
                public void invoke(ImportPublishAttributeDTO importPublishAttributeDTO, AnalysisContext analysisContext) {
                    // 属性组名称 不能为空
                    if (StringUtils.isBlank(importPublishAttributeDTO.getGroupName())) {
                        tempErrorList.add(String.format("第 %s 行的”属性组名称“不能为空！", analysisContext.getCurrentRowNum()));
                        return;
                    } else if (importPublishAttributeDTO.getGroupName().length() > 100) {
                        tempErrorList.add(String.format("第 %s 行的”属性组名称“长度不能超过100个字符！", analysisContext.getCurrentRowNum()));
                        return;
                    }
                    // 属性名称 不能为空
                    if (StringUtils.isBlank(importPublishAttributeDTO.getAttributeName())) {
                        tempErrorList.add(String.format("第 %s 行的”属性名称“不能为空！", analysisContext.getCurrentRowNum()));
                        return;
                    } else if (importPublishAttributeDTO.getAttributeName().length() > 100) {
                        tempErrorList.add(String.format("第 %s 行的”属性名称“长度不能超过100个字符！", analysisContext.getCurrentRowNum()));
                        return;
                    }
                    // 属性值 不能为空
                    if (StringUtils.isBlank(importPublishAttributeDTO.getAttributeValue())) {
                        tempErrorList.add(String.format("第 %s 行的”属性值“不能为空！", analysisContext.getCurrentRowNum()));
                        return;
                    }
                    cachedDataList.add(importPublishAttributeDTO);
                    successCount++;
                }

                @Override
                public void doAfterAllAnalysed(AnalysisContext analysisContext) {
                    // 保存数据
                    saveData();

                    // 返回信息的第一条，是统计信息
                    errorInfos.add(String.format("属性导入成功 %s 条，失败 %s 条 数据！", successCount, tempErrorList.size()));
                    errorInfos.addAll(tempErrorList);
                }

                private void saveData() {
                    log.info("{}条数据，开始存储属性数据库！", cachedDataList.size());
                    Map<String, List<ImportPublishAttributeDTO>> mapList = cachedDataList.stream().collect(Collectors.groupingBy(ImportPublishAttributeDTO::getGroupName));
                    List<PublishGroupAttributeBO> publishGroupAttributeBOs = new ArrayList<>();
                    mapList.forEach((groupName, list) -> publishGroupAttributeBOs.add(prepareForImport(groupName, list)));
                    publishGroupAttributeBOs.forEach(bo -> {
                        if (bo.getGroup() != null) {
                            publishAttributeGroupRepository.saveOrUpdate(bo.getGroup());
                        }
                        if (bo.getAttributes() != null) {
                            publishAttributeRepository.saveOrUpdateBatch(bo.getAttributes());
                            saveAttrList.addAll(bo.getAttributes());
                        }
                        if (bo.getValues() != null) {
                            publishAttributeValueRepository.saveOrUpdateBatch(bo.getValues());
                        }
                    });
                    log.info("导入属性,存储属性数据库成功！");
                }
            }).sheet().doRead();

            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
                @Override
                public void afterCompletion(int status) {
                    //status:0表示事务已经成功提交，1表示业务出现异常
                    if (status == 0 && !CollectionUtils.isEmpty(saveAttrList)) {
                        CurrentUser currentUser = CurrentUserHolder.get();
                        applicationContext.publishEvent(new OperatePublishAttributeEvent(this, saveAttrList.stream().map(PublishAttribute::getAttributeId).collect(Collectors.toList()),currentUser.getId(),currentUser.getName()));
                    }
                }
            });
        } catch (Exception e) {
            log.error("导入属性失败", e);
            throw new BusinessException("导入属性失败");
        } finally {
            return errorInfos;
        }
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void modifyAttributeGroupState(ModifyAttributeGroupStateReq req){
        if (CollectionUtils.isEmpty(req.getGroupIds()) || req.getState() == null) {
            throw new BusinessException("参数为空");
        }
        if(Bool.YES.getCode()!=req.getState() && Bool.NO.getCode()!=req.getState()){
            throw new BusinessException("状态值不正确");
        }

        publishAttributeGroupRepository.modifyAttributeGroupState(req.getGroupIds(),req.getState());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void modifyAttributeState(ModifyAttributeStateReq req) {
        if (CollectionUtils.isEmpty(req.getAttributeIds()) || req.getState() == null) {
            throw new BusinessException("参数为空");
        }
        if(Bool.YES.getCode()!=req.getState() && Bool.NO.getCode()!=req.getState()){
            throw new BusinessException("状态值不正确");
        }

        publishAttributeRepository.modifyAttributeState(req.getAttributeIds(),req.getState());
    }

    private PublishGroupAttributeBO prepareForImport(String groupName, List<ImportPublishAttributeDTO> list) {
        PublishGroupAttributeBO bo = new PublishGroupAttributeBO();
        PublishAttributeGroup group = publishAttributeGroupRepository.getByName(groupName);
        if (group == null) {
            group = new PublishAttributeGroup();
            group.setAttributeGroupId(IdHelper.getId());
            group.setGroupName(groupName);
            group.setPlatformFlag(Bool.NO.getCode());
            group.setState(Bool.YES.getCode());
            group.setDeleted(Bool.NO.getCode());
            bo.setGroup(group);
        }
        List<PublishAttribute> saveAttrList = new ArrayList<>();
        List<PublishAttributeValue> saveValueList = new ArrayList<>();
        Map<String, PublishAttribute> existAttrMap = new HashMap<>();
        Set<String> existValueSet = new HashSet<>();
        for (ImportPublishAttributeDTO dto : list) {
            Set<String> valSet = new HashSet<>();
            if (StringUtils.isNotBlank(dto.getAttributeValue())) {
                String[] vals = dto.getAttributeValue().split(",");
                valSet.addAll(Arrays.stream(vals).filter(StringUtils::isNotBlank).collect(Collectors.toSet()));
            }

            String attrKey = group.getAttributeGroupId() + "-" + dto.getAttributeName();
            PublishAttribute attr = publishAttributeRepository.getByGroupIdAndAttributeName(group.getAttributeGroupId(), dto.getAttributeName());
            if (attr == null) {
                if (existAttrMap.containsKey(attrKey)) {
                    attr = existAttrMap.get(attrKey);
                } else {
                    attr = new PublishAttribute();
                    attr.setAttributeId(IdHelper.getId());
                    attr.setAttributeGroupId(group.getAttributeGroupId());
                    attr.setAttributeName(dto.getAttributeName());
                    attr.setAttributeCode(generateCode());
                    attr.setShowType(PublishAttributeShowTypeEnum.SINGLE_SELECT.getCode());
                    attr.setState(Bool.YES.getCode());
                    attr.setDeleted(Bool.NO.getCode());
                }
            } else {
                //找出要删除的值
                List<PublishAttributeValue> oldValueList = publishAttributeValueRepository.listByAttrId(attr.getAttributeId());
                if (!CollectionUtils.isEmpty(oldValueList)) {
                    if (StringUtils.isNotBlank(dto.getAttributeValue())) {
                        oldValueList.forEach(v -> {
                            if (!valSet.contains(v.getAttributeValue())) {
                                v.setDeleted(Bool.YES.getCode());
                            } else {
                                existValueSet.add(v.getAttributeValue());
                            }
                        });
                    } else {
                        oldValueList.forEach(v -> v.setDeleted(Bool.YES.getCode()));
                    }
                    //记录要删除的值
                    saveValueList.addAll(oldValueList);
                }
            }
            if (!existAttrMap.containsKey(attrKey)) {
                existAttrMap.put(attrKey, attr);
                saveAttrList.add(attr);
            }

            //记录值
            if (StringUtils.isNotBlank(dto.getAttributeValue())) {
                PublishAttribute finalAttr = attr;
                ////记录要新增的值
                saveValueList.addAll(valSet.stream().filter(v -> !existValueSet.contains(v)).map(val -> {
                    PublishAttributeValue value = new PublishAttributeValue();
                    value.setAttributeId(finalAttr.getAttributeId());
                    value.setDeleted(Bool.NO.getCode());
                    value.setAttributeValue(val);
                    value.setState(PublishAttributeValueStateEnum.ENABLE.code);
                    return value;
                }).collect(Collectors.toList()));
            }

            bo.setGroup(group);
            bo.setAttributes(saveAttrList);
            bo.setValues(saveValueList);
        }
        return bo;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateAttrAmount(List<Long> groupIds){
        if(CollectionUtils.isEmpty(groupIds)){
            return;
        }
        publishAttributeGroupRepository.updateAttrAmount(groupIds);
    }

    @Override
    public List<PublishAttributeVo> listAttributeWithValueByIds(List<Long> attrIds) {
        List<PublishAttribute> list = publishAttributeRepository.listByIds(attrIds);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        return groupAttrConvert.trans2PublishAttributeVo(list);
    }

    private String generateCode() {
        LongUnaryOperator updateFunction = oldValue -> {
            // 存在自增返回
            if (oldValue > 0) {
                return new AtomicLong(oldValue).incrementAndGet();
            }
            // 不存在查询数据库获取最大编码值自增返回
            String maxAttributeCode = publishAttributeRepository.maxAttributeCode();
            return new AtomicLong(Long.parseLong(maxAttributeCode)).incrementAndGet();
        };
        return "" + serialNumComponent.getSerialNumber(SerialNumConstant.AIGC_PUBLISH_ATTRIBUTE_CODE_REDIS_KEY, updateFunction);
    }
}
