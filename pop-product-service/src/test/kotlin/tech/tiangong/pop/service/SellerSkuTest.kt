package tech.tiangong.pop.service

import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.util.json.toJson
import tech.tiangong.pop.PopApplication
import tech.tiangong.pop.component.GenerateSellerSkuComponent
import tech.tiangong.pop.dao.repository.AeSaleGoodsRepository
import tech.tiangong.pop.dao.repository.AeSaleSkcRepository
import tech.tiangong.pop.dao.repository.AeSaleSkuRepository
import tech.tiangong.pop.dao.repository.ProductRepository
import test.BaseTest

@SpringBootTest(
    classes = [PopApplication::class],
    args = [
        "--spring.cloud.nacos.discovery.register-enabled=false",
        "--spring.profiles.active=qa-ola",
        "--spring.config.import=https://jv-devops.oss-cn-hangzhou.aliyuncs.com/\${spring.profiles.active}-yunwei-config.yml,classpath:local-bootstrap.yml"
    ]
)
@Disabled
@Slf4j
class SellerSkuTest : BaseTest() {

    @Autowired
    lateinit var generateSellerSkuComponent: GenerateSellerSkuComponent

    @Autowired
    lateinit var productRepository: ProductRepository

    @Autowired
    lateinit var aeSaleGoodsRepository: AeSaleGoodsRepository

    @Autowired
    lateinit var aeSaleSkcRepository: AeSaleSkcRepository

    @Autowired
    lateinit var aeSaleSkuRepository: AeSaleSkuRepository


    @Test
    fun testQueryCategoryTreeList() {
        val spuCode = "250611000101"
        val saleGoods = aeSaleGoodsRepository.getBySpuCode(spuCode).first()
        val product = productRepository.getById(saleGoods.productId!!)
        val saleSkc = aeSaleSkcRepository.findBySaleGoodsId(saleGoods.saleGoodsId!!).first()
        val saleSku = aeSaleSkuRepository.findBySaleSkcId(saleSkc.saleSkcId!!).first()
        val sellerSku = generateSellerSkuComponent.generateSellerSku(product, saleGoods, saleSkc, saleSku)
        log.info { "生成sellerSku: ${ sellerSku.toJson() }" }
    }
}