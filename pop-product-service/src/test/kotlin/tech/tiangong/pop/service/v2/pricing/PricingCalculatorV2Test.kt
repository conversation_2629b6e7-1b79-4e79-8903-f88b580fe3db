package tech.tiangong.pop.service.v2.pricing

import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import team.aikero.blade.logging.core.annotation.Slf4j
import tech.tiangong.pop.PopApplication
import tech.tiangong.pop.bo.v2.ProductPriceCalcV2BO
import tech.tiangong.pop.common.enums.CountryEnum
import tech.tiangong.pop.dao.entity.Product
import tech.tiangong.pop.dao.entity.Shop
import java.math.BigDecimal

/**
 * PricingCalculatorV2 测试类
 */
@SpringBootTest(
    classes = [PopApplication::class],
    args = [
        "--spring.cloud.nacos.discovery.register-enabled=false",
        "--spring.profiles.active=dev-ola",
        "--spring.config.import=https://jv-devops.oss-cn-hangzhou.aliyuncs.com/\${spring.profiles.active}-yunwei-config.yml,classpath:local-bootstrap.yml"
    ]
)
@Disabled
@Slf4j
class PricingCalculatorV2Test {

    @Autowired
    private lateinit var pricingCalculatorV2: PricingCalculatorV2

    @Test
    fun testCalculatePrice() {
        // 创建测试数据
        val product = Product().apply {
            productId = 1L
            supplyMode = "OBM仿款"
            cmpRetailPrice = BigDecimal("20.00")
            cmpSalePrice = BigDecimal("15.00")
        }

        val shop = Shop().apply {
            shopId = 1L
            shopName = "测试店铺"
            countryType = "cb"
            businessType = 1
            platformId = 1L
        }

        val calcBO = ProductPriceCalcV2BO(
            product = product,
            shop = shop,
            countries = listOf(CountryEnum.US),
            competitorRetailPrice = BigDecimal("20.00"),
            competitorSalePrice = BigDecimal("15.00"),
            costPrice = BigDecimal("8.00"),
            purchasePrice = BigDecimal("7.00"),
            platformId = 1L,
            supplyMode = "OBM仿款"
        )

        // 执行计算
        val results = pricingCalculatorV2.calculatePrice(calcBO)

        // 验证结果
        println("计算结果: $results")
    }

    @Test
    fun testCalculatePriceWithDefaultRuleGroup() {
        // 创建测试数据 - supplyMode为空，应该使用默认规则组
        val product = Product().apply {
            productId = 1L
            supplyMode = null // 测试默认规则组
            cmpRetailPrice = BigDecimal("20.00")
            cmpSalePrice = BigDecimal("15.00")
        }

        val shop = Shop().apply {
            shopId = 1L
            shopName = "测试店铺"
            countryType = "cb"
            businessType = 1
            platformId = 1L
        }

        val calcBO = ProductPriceCalcV2BO(
            product = product,
            shop = shop,
            countries = listOf(CountryEnum.US),
            competitorRetailPrice = BigDecimal("20.00"),
            competitorSalePrice = BigDecimal("15.00"),
            costPrice = BigDecimal("8.00"),
            purchasePrice = BigDecimal("7.00"),
            platformId = 1L,
            supplyMode = null // 测试默认规则组
        )

        // 执行计算
        val results = pricingCalculatorV2.calculatePrice(calcBO)

        // 验证结果
        println("默认规则组计算结果: $results")
    }
}