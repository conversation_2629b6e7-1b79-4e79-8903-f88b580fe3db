package tech.tiangong.pop.service

import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.util.json.toJson
import tech.tiangong.eis.client.TemuClient
import tech.tiangong.eis.temu.req.*
import tech.tiangong.pop.PopApplication
import test.BaseTest

@SpringBootTest(
    classes = [PopApplication::class],
    args = [
        "--spring.cloud.nacos.discovery.register-enabled=false",
        "--spring.profiles.active=qa-ola",
        "--spring.config.import=https://jv-devops.oss-cn-hangzhou.aliyuncs.com/\${spring.profiles.active}-yunwei-config.yml,classpath:local-bootstrap.yml"
    ]
)
@Disabled
@Slf4j
class TemuClientTest : BaseTest() {
    @Autowired
    lateinit var temuClient: TemuClient

    @Test
    fun categoryTest() {

        /*
        # business_type运营模式: 2半托 3全托
        select short_code,shop_name, business_type, country, token
        from shop
        where platform_id = 8;
         */
        val shortCode: String? = "634418222572577"
        val region: String? = "CN"

//        1. 调用查询所有品类 (最末级品类区别 catType = 1, isLeaf = true)
        val allCateResp = temuClient.catsGet(TemuCatsGetReq().apply {
            this.shortCode = shortCode
            this.region = region
            this.siteId = 1
            this.parentCatId = 29070
        })
        log.info { "allCateResp: ${allCateResp.toJson()}" }
//        2. 按类目查询属性("required": true确定必填属性, "isSale": true确定销售属性即颜色\尺码)
        val catAttrsResp = temuClient.attrsGet(TemuAttrsGetReq(29070).apply {
            this.shortCode = shortCode
            this.region = region
        })
        log.info { "catAttrsResp: ${catAttrsResp.toJson()}" }

//        3. 创建颜色 (经测试, 创建之后不会出现在上面的属性中, 可能规则:上面有则直接用, 没有则创建后返回id用)
//         "parentSpecId": 1001 颜色
//         "parentSpecId": 3001 尺码
//        val specCreateReq = temuClient.goodsSpecCreate(TemuGoodsSpecCreateReq().apply {
//            this.shortCode = shortCode
//            this.region = region
//            this.parentSpecId = 1001
//            this.specName = "奶白色"
//        })
//        log.info { "specCreateReq: ${specCreateReq.toJson()}" }
    }


    @Test
    fun sizechartsTest() {

        val shortCode: String? = "634418223015936"
        val region: String? = "CN"
        val catId = 29070

//        val sizechartsClassGet = temuClient.goodsSizechartsClassGet(TemuSizechartsClassGetReq().apply {
//            this.shortCode = shortCode
//            this.region = region
//            this.catId = catId
//        })
//        log.info { "sizechartsClassGet: ${sizechartsClassGet.toJson()}" }
        //"data":{"sizeSpecClassCat":{"catId":30472,"classId":3,"classType":0,"parentClassId":1,"relatedClassIds":null}}}
//        temuClient.goodsSizeChartsTemplateCreate(TemuCreateSizeChartReq().apply {
//            this.shortCode = shortCode
//            this.region = region
//            this.catId = catId
//            this.classId = 3
//        })


        //1. 查询类目对应尺码表分类
        val allCateResp = temuClient.sizeChartsGet(
            TemuSizeChartsGetReq(
                offset = 0,
                pageSize = 10,
                catId = 29070
            ).apply {
                this.region = region
                this.shortCode = shortCode
            })
        log.info { "allCateResp: ${allCateResp.toJson()}" }
        //"data":{"sizeSpecClassCat":{"catId":30472,"classId":3,"classType":0,"parentClassId":1,"relatedClassIds":null}}}
        //2. 查询尺码模板规则
//        val sizeChartsResp = temuClient.goodsSizeChartsSettings(TemuSizeChartsSettingsReq().apply {
//            this.shortCode = shortCode
//            this.region = region
//            this.catId = catId
//        })
//        log.info { "sizeChartsResp: ${sizeChartsResp.toJson()}" }
//        //{"groupChName":"尺码","groupEnName":"CN","code":1
        // 3. 查查meta信息
//        val sizeChartsResp = temuClient.goodsSizeChartsSettings(TemuSizeChartsSettingsReq().apply {
//            this.shortCode = shortCode
//            this.region = region
//            this.catId = catId
//        })
//        log.info { "sizeChartsResp: ${sizeChartsResp.toJson()}" }
        //{"groupChName":"尺码","groupEnName":"CN","code":1

    }

    /**
     * 商品选品状态
     */
    @Test
    fun getProductState() {
//        634418223015936 半托
//        634418222572577 全托

        val shortCode: String? = "634418223015936"
        val region: String? = "CN"
        val goodsSelectList = temuClient.productSearch(
            TemuProductSearchReq(
                pageNum = 1,
                pageSize = 50,
            ).apply {
                this.shortCode = shortCode
                this.region = region

            })
        log.info { "goodsSelectList: ${goodsSelectList.toJson()}" }
    }

    /**
     * 商品列表
     */
    @Test
    fun getGoodsListTest() {
//        634418223015936 半托
//        634418222572577 全托

        val shortCode: String? = "634418223015936"
        val region: String? = "CN"
        val goodsList = temuClient.goodsListGet(TemuGoodsListGetReq().apply {
            this.shortCode = shortCode
            this.region = region
            page = 1
            pageSize = 50
        })
        log.info { "goodsList: ${goodsList.toJson()}" }
    }

    /**
     * 商品详情
     */
    @Test
    fun getGoodsDetailTest() {
//        634418223015936 半托
//        634418222572577 全托

        val shortCode: String? = "634418223015936"
        val region: String? = "CN"
        val productId = "6257502298"
        val goodsList = temuClient.goodsDetailGet(TemuGoodsDetailGetReq(productId).apply {
            this.shortCode = shortCode
            this.region = region
        })
        log.info { "goodsDetail: ${goodsList.toJson()}" }
    }

    /**
     * 查询建议申报参考价
     */
    @Test
    fun goodsSuggestSupplyPriceGetTest() {
//        634418223015936 半托
//        634418222572577 全托

        val shortCode: String? = "634418223015936"
        val region: String? = "CN"
        val productId = "6257502298"
//        val goodsList = temuClient.goodsSuggestSupplyPriceGet(TemuGoodsSuggestSupplyPriceGetReq().apply {
//            this.shortCode = shortCode
//            this.region = region
//        })
//        log.info { "goodsDetail: ${goodsList.toJson()}" }
    }


}