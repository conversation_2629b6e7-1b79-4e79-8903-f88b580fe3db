package tech.tiangong.pop.service

import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import team.aikero.blade.auth.withSystemUser
import team.aikero.blade.core.enums.Bool
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.util.json.toJson
import tech.tiangong.pop.PopApplication
import tech.tiangong.pop.common.enums.PlatformEnum
import tech.tiangong.pop.common.enums.ProductShopBusinessType
import tech.tiangong.pop.config.CommonProperties
import tech.tiangong.pop.enums.RegionPriceRuleTaxRateTypeEnum
import tech.tiangong.pop.enums.SupplyModeEnum
import tech.tiangong.pop.req.regionpricerule.*
import tech.tiangong.pop.req.regionpricerule.SaveRegionPriceRuleFreightRateReq.*
import tech.tiangong.pop.req.regionpricerule.SaveRegionPriceRuleLogisticsReq.AEConfigReq
import tech.tiangong.pop.req.regionpricerule.SaveRegionPriceRuleLogisticsReq.SaveReceivingPlaceLogisticsCostReq
import tech.tiangong.pop.req.regionpricerule.SaveRegionPriceRuleStorageReq.AEStorageRuleReq
import tech.tiangong.pop.req.regionpricerule.SaveRegionPriceRuleTaxRateReq.TaxRateRuleReq
import tech.tiangong.pop.service.regionpricerule.RegionPriceRuleManageService
import java.math.BigDecimal

/**
 * AE 速卖通AliExpress服务测试类
 */
@SpringBootTest(
    classes = [PopApplication::class],
    args = [
        "--spring.cloud.nacos.discovery.register-enabled=false",
        "--spring.profiles.active=dev-ola",
        "--spring.config.import=https://jv-devops.oss-cn-hangzhou.aliyuncs.com/\${spring.profiles.active}-yunwei-config.yml,classpath:local-bootstrap.yml"
    ]
)
@Disabled
@Slf4j
class RegionPriceRuleServiceTest {

    @Autowired
    lateinit var regionPriceRuleManageService: RegionPriceRuleManageService
    @Autowired
    lateinit var commonProperties: CommonProperties

    @Test
    fun test() {
        log.info { "====TTT=====${commonProperties.marketDefaultShop.toJson()}" }
    }
    /**
     * 测试查询1688类目树列表
     */
    @Test
    fun saveLogisticsRule() {
        withSystemUser {
            //保存LAZADA配置
//            var req = SaveRegionPriceRuleLogisticsReq().apply {
//                this.platformId = PlatformEnum.LAZADA.platformId
//                var lazadaConfigReqs: MutableList<LazadaConfigReq> = mutableListOf()
//                lazadaConfigReqs.add(LazadaConfigReq().apply {
//                    this.country = "TH"
//                    this.cost = BigDecimal(4.3)
//                })
//                lazadaConfigReqs.add(LazadaConfigReq().apply {
//                    this.country = "PH"
//                    this.cost = BigDecimal(5.3)
//                })
//                lazadaConfigReqs.add(LazadaConfigReq().apply {
//                    this.country = "SG"
//                    this.cost = BigDecimal(0.9)
//                })
//                this.lazadaConfigReqList=lazadaConfigReqs
//            }
            //保存AE配置
            var req = SaveRegionPriceRuleLogisticsReq().apply {
                this.platform = PlatformEnum.AE
                var aeConfigReqs: MutableList<AEConfigReq> = mutableListOf()
                aeConfigReqs.add(AEConfigReq().apply {
                    this.shippingPlaceId = 201336100
                    this.shippingPlace = "CHINA"
                    this.shippingPlaceCn = "中国"
                    var list: MutableList<SaveReceivingPlaceLogisticsCostReq> = mutableListOf()
                    list.add(SaveReceivingPlaceLogisticsCostReq().apply {
                        this.defaultRule = Bool.NO.code
                        this.receivingPlace = "france"
                        this.receivingPlaceCn = "France"
                        this.cost = BigDecimal(41.35)
                    })
                    list.add(SaveReceivingPlaceLogisticsCostReq().apply {
                        this.defaultRule = Bool.YES.code
                        this.receivingPlace = "United States"
                        this.receivingPlaceCn = "United States"
                        this.cost = BigDecimal(111.34)
                    })
                    list.add(SaveReceivingPlaceLogisticsCostReq().apply {
                        this.defaultRule = Bool.NO.code
                        this.receivingPlace = "Australia"
                        this.receivingPlaceCn = "AU"
                        this.cost = BigDecimal(21.34)
                    })
                    this.receivingPlaceLogisticsCostList = list
                })

                aeConfigReqs.add(AEConfigReq().apply {
                    this.shippingPlaceId = *********
                    this.shippingPlace = "Poland"
                    this.shippingPlaceCn = "PL"
                    var list: MutableList<SaveReceivingPlaceLogisticsCostReq> = mutableListOf()
                    list.add(SaveReceivingPlaceLogisticsCostReq().apply {
                        this.defaultRule = Bool.YES.code
                        this.receivingPlace = "CHINA"
                        this.receivingPlaceCn = "中国"
                        this.cost = BigDecimal(0.35)
                    })
                    list.add(SaveReceivingPlaceLogisticsCostReq().apply {
                        this.defaultRule = Bool.NO.code
                        this.receivingPlace = "turkey"
                        this.receivingPlaceCn = "土耳其"
                        this.cost = BigDecimal(1.24)
                    })
                    list.add(SaveReceivingPlaceLogisticsCostReq().apply {
                        this.defaultRule = Bool.NO.code
                        this.receivingPlace = "Australia"
                        this.receivingPlaceCn = "AU"
                        this.cost = BigDecimal(21.34)
                    })
                    this.receivingPlaceLogisticsCostList = list
                })
                this.aeConfigReqList = aeConfigReqs
            }

            regionPriceRuleManageService.saveLogisticsRule(req)
        }
    }

    @Test
    fun getLogisticsRuleByPlatform() {
        var result = regionPriceRuleManageService.getLogisticsRuleByPlatform(PlatformEnum.AE)
        log.info { "result====: ${result?.toJson()}" }
    }

    @Test
    fun saveStorageRule() {
        withSystemUser {
            var req = SaveRegionPriceRuleStorageReq()
            //保存AE仓储成本配置
            req.platform = PlatformEnum.AE
            var aeStorageRuleList: MutableList<AEStorageRuleReq> = mutableListOf()
            aeStorageRuleList.add(AEStorageRuleReq().apply {
                this.shippingPlaceId = *********
                this.shippingPlace = "Australia"
                this.shippingPlaceCn = "AU"
                this.cost = BigDecimal(21.3401)
            })
            aeStorageRuleList.add(AEStorageRuleReq().apply {
                this.shippingPlaceId = *********
                this.shippingPlace = "Republic of Latvia"
                this.shippingPlaceCn = "LV"
                this.cost = BigDecimal(41.3)
            })
            aeStorageRuleList.add(AEStorageRuleReq().apply {
                this.shippingPlaceId = *********
                this.shippingPlace = "Saudi Arabia"
                this.shippingPlaceCn = "沙特"
                this.cost = BigDecimal(6.3015)
            })
            aeStorageRuleList.add(AEStorageRuleReq().apply {
                this.shippingPlaceId = *********
                this.shippingPlace = "Netherlands"
                this.shippingPlaceCn = "NL"
                this.cost = BigDecimal(3.8010)
            })
            req.aeStorageRuleList = aeStorageRuleList

            regionPriceRuleManageService.saveStorageRule(req)
        }
    }

    @Test
    fun queryStorageRuleByPlatform(){
        var result = regionPriceRuleManageService.queryStorageRuleByPlatform(PlatformEnum.AE)
        log.info { "result====: ${result?.toJson()}" }
    }

    @Test
    fun saveRejectRateRule(){
        withSystemUser {
            var req: MutableList<SaveRegionPriceRuleRejectRateReq> = mutableListOf()
            req.add(SaveRegionPriceRuleRejectRateReq().apply {
                this.platformId = PlatformEnum.AE.platformId
                this.channelId = 1L
                this.shopIds = mutableListOf<Long>(7276480307564389700L)
                this.rate = BigDecimal(1.5)
//                this.defaultRule = Bool.YES.code
            })
            req.add(SaveRegionPriceRuleRejectRateReq().apply {
                this.platformId = PlatformEnum.AE.platformId
                this.channelId = 1L
                this.shopIds = mutableListOf<Long>(7308304887945629727L)
                this.rate = BigDecimal(4.1)
//                this.defaultRule = Bool.NO.code
            })
            req.add(SaveRegionPriceRuleRejectRateReq().apply {
                this.platformId = PlatformEnum.LAZADA.platformId
                this.channelId = 1L
                this.shopIds = mutableListOf<Long>(1821899282974887938L)
                this.rate = BigDecimal(84)
//                this.defaultRule = Bool.YES.code
            })
            regionPriceRuleManageService.saveRejectRateRule(req)
        }
    }

    @Test
    fun queryRejectRateRule(){
        var result = regionPriceRuleManageService.queryRejectRateRule()
        log.info { "result====: ${result.toJson()}" }
    }

    @Test
    fun saveAdRule(){
        withSystemUser {
            var req: MutableList<SaveRegionPriceRuleAdReq> = mutableListOf()
            req.add(SaveRegionPriceRuleAdReq().apply {
                this.channelId = 1L
                this.platformId = PlatformEnum.AE.platformId
                this.shopIds = mutableListOf<Long>(7276480307564389700L)
                this.rate = BigDecimal(1.5)
                this.defaultRule = Bool.YES.code
            })
            req.add(SaveRegionPriceRuleAdReq().apply {
                this.channelId = 1L
                this.platformId = PlatformEnum.AE.platformId
                this.shopIds = mutableListOf<Long>(7308304887945629727L)
                this.rate = BigDecimal(23.5)
                this.defaultRule = Bool.NO.code
            })
            req.add(SaveRegionPriceRuleAdReq().apply {
                this.channelId = 1L
                this.platformId = PlatformEnum.LAZADA.platformId
                this.shopIds = mutableListOf<Long>(1821899282974887938L)
                this.rate = BigDecimal(2.5)
                this.defaultRule = Bool.YES.code
            })
            regionPriceRuleManageService.saveAdRule(req)
        }
    }

    @Test
    fun queryAdRule(){
        var result = regionPriceRuleManageService.queryAdRule()
        log.info { "result====: ${result.toJson()}" }
    }

    @Test
    fun saveCommissionRule(){
        withSystemUser {
            var req: MutableList<SaveRegionPriceRuleCommissionReq> = mutableListOf()
            req.add(SaveRegionPriceRuleCommissionReq().apply {
                this.platformId = PlatformEnum.AE.platformId
                this.channelId = 1L
//                this.shopIds = mutableListOf<Long>(7276480307564389700L)
                this.businessType = ProductShopBusinessType.SEMI_MANAGED.value
                this.rate = BigDecimal(1.2)
                this.defaultRule = Bool.YES.code
            })
            req.add(SaveRegionPriceRuleCommissionReq().apply {
                this.platformId = PlatformEnum.AE.platformId
                this.channelId = 1L
                this.shopIds = mutableListOf<Long>(7308304887945629727L)
                this.businessType = ProductShopBusinessType.SEMI_MANAGED.value
                this.rate = BigDecimal(3.5)
                this.defaultRule = Bool.NO.code
            })
            req.add(SaveRegionPriceRuleCommissionReq().apply {
                this.platformId = PlatformEnum.LAZADA.platformId
                this.channelId = 1L
                this.shopIds = mutableListOf<Long>(1819658755852206081L)
                this.businessType = ProductShopBusinessType.FULLY_MANAGED.value
                this.rate = BigDecimal(5.98)
                this.defaultRule = Bool.YES.code
            })
            regionPriceRuleManageService.saveCommissionRule(req)
        }
    }

    @Test
    fun queryCommissionRule(){
        var result = regionPriceRuleManageService.queryCommissionRule()
        log.info { "result====: ${result.toJson()}" }
    }

    @Test
    fun saveWithdrawRule(){
        withSystemUser {
            var req: MutableList<SaveRegionPriceRuleWithdrawReq> = mutableListOf()
            req.add(SaveRegionPriceRuleWithdrawReq().apply {
                this.platformId = PlatformEnum.AE.platformId
                this.channelId = 1L
                this.shopIds = mutableListOf<Long>(7276480307564389700L)
                this.businessType = ProductShopBusinessType.SEMI_MANAGED.value
                this.rate = BigDecimal(12.2)
                this.defaultRule = Bool.YES.code
            })
            req.add(SaveRegionPriceRuleWithdrawReq().apply {
                this.platformId = PlatformEnum.AE.platformId
                this.channelId = 1L
                this.shopIds = mutableListOf<Long>(7308304887945629727L)
                this.businessType = ProductShopBusinessType.SEMI_MANAGED.value
                this.rate = BigDecimal(33.5)
                this.defaultRule = Bool.NO.code
            })
            req.add(SaveRegionPriceRuleWithdrawReq().apply {
                this.platformId = PlatformEnum.LAZADA.platformId
                this.channelId = 1L
                this.shopIds = mutableListOf<Long>(1819658755852206081L)
                this.businessType = ProductShopBusinessType.FULLY_MANAGED.value
                this.rate = BigDecimal(11.98)
                this.defaultRule = Bool.YES.code
            })
            regionPriceRuleManageService.saveWithdrawRule(req)
        }
    }

    @Test
    fun queryWithdrawRule(){
        var result = regionPriceRuleManageService.queryWithdrawRule()
        log.info { "result====: ${result.toJson()}" }
    }

    @Test
    fun saveGrossMarginRule(){
        withSystemUser {
            var req: MutableList<SaveRegionPriceRuleGrossMarginReq> = mutableListOf()
            req.add(SaveRegionPriceRuleGrossMarginReq().apply {
                this.platformId = PlatformEnum.AE.platformId
                this.channelId = 1L
                this.shopIds = mutableListOf<Long>(7276480307564389700L)
                this.supplyType = SupplyModeEnum.AIGC.dictCode
                this.rate = BigDecimal(12.2)
                this.defaultRule = Bool.YES.code
            })
            req.add(SaveRegionPriceRuleGrossMarginReq().apply {
                this.platformId = PlatformEnum.AE.platformId
                this.channelId = 1L
                this.shopIds = mutableListOf<Long>(7308304887945629727L)
                this.supplyType = SupplyModeEnum.OEM.dictCode
                this.rate = BigDecimal(33.5)
                this.defaultRule = Bool.YES.code
            })
            req.add(SaveRegionPriceRuleGrossMarginReq().apply {
                this.platformId = PlatformEnum.LAZADA.platformId
                this.channelId = 1L
                this.shopIds = mutableListOf<Long>(1819658755852206081L)
                this.supplyType = SupplyModeEnum.TRY_ON.dictCode
                this.rate = BigDecimal(11.98)
                this.defaultRule = Bool.YES.code
            })
            regionPriceRuleManageService.saveGrossMarginRule(req)
        }
    }

    @Test
    fun queryGrossMarginRule(){
        var result = regionPriceRuleManageService.queryGrossMarginRule()
        log.info { "result====: ${result.toJson()}" }
    }

    @Test
    fun saveMarketingRule(){
        withSystemUser {
            var req: MutableList<SaveRegionPriceRuleMarketingReq> = mutableListOf()
            req.add(SaveRegionPriceRuleMarketingReq().apply {
                this.platformId = PlatformEnum.AE.platformId
                this.channelId = 1L
                this.shopIds = mutableListOf<Long>(7276480307564389700L)
                this.businessType = ProductShopBusinessType.SEMI_MANAGED.value
                this.rate = BigDecimal(12.2)
                this.defaultRule = Bool.YES.code
            })
            req.add(SaveRegionPriceRuleMarketingReq().apply {
                this.platformId = PlatformEnum.AE.platformId
                this.channelId = 1L
                this.shopIds = mutableListOf<Long>(7308304887945629727L)
                this.businessType = ProductShopBusinessType.FULLY_MANAGED.value
                this.rate = BigDecimal(33.5)
                this.defaultRule = Bool.YES.code
            })
            req.add(SaveRegionPriceRuleMarketingReq().apply {
                this.platformId = PlatformEnum.LAZADA.platformId
                this.channelId = 1L
                this.shopIds = mutableListOf<Long>(1819658755852206081L)
                this.businessType = ProductShopBusinessType.FULLY_MANAGED.value
                this.rate = BigDecimal(11.98)
                this.defaultRule = Bool.YES.code
            })
            regionPriceRuleManageService.saveMarketingRule(req)
        }
    }

    @Test
    fun queryMarketingRule(){
        var result = regionPriceRuleManageService.queryMarketingRule()
        log.info { "result====: ${result.toJson()}" }
    }

    @Test
    fun saveFreightRateRule(){
        withSystemUser {
           var req = SaveRegionPriceRuleFreightRateReq()
           req.apply {
               //保存LAZADA配置
//               this.platformId = PlatformEnum.LAZADA.platformId
//               var lazadaFreightRateRuleReqList: MutableList<LazadaFreightRateRuleReq> = mutableListOf()
//               lazadaFreightRateRuleReqList.add(LazadaFreightRateRuleReq().apply {
//                   this.shopId = 1821899281603350529L
//                   var feightRateReqList: MutableList<LazadaFeightRateReq> = mutableListOf()
//                   feightRateReqList.add(LazadaFeightRateReq().apply {
//                       this.country = "PH"
//                       this.rate = BigDecimal(32.4)
//                   })
//                   feightRateReqList.add(LazadaFeightRateReq().apply {
//                       this.country = "TH"
//                       this.rate = BigDecimal(3.4)
//                   })
//                   this.feightRateReqList = feightRateReqList
//               })
//               lazadaFreightRateRuleReqList.add(LazadaFreightRateRuleReq().apply {
//                   this.shopId = 1821899282974887938L
//                   var feightRateReqList: MutableList<LazadaFeightRateReq> = mutableListOf()
//                   feightRateReqList.add(LazadaFeightRateReq().apply {
//                       this.country = "TH"
//                       this.rate = BigDecimal(0.4)
//                   })
//                   feightRateReqList.add(LazadaFeightRateReq().apply {
//                       this.country = "SG"
//                       this.rate = BigDecimal(9.6)
//                   })
//                   this.feightRateReqList = feightRateReqList
//               })
//               this.lazadaFreightRateRuleReqList = lazadaFreightRateRuleReqList

               //保存AE配置
               this.platform = PlatformEnum.AE
               var receivingPlaceFreightRateList:MutableList<AEFreightRateRuleReq> = mutableListOf()
               receivingPlaceFreightRateList.add(AEFreightRateRuleReq().apply{
                   this.shopId = 7276480307564389700L
                   var receivingPlaceFreightRateList:MutableList<ReceivingPlaceFreightRateReq> = mutableListOf()
                   receivingPlaceFreightRateList.add(ReceivingPlaceFreightRateReq(*********L,"Australia","AU").apply {
                       var freightRateList: MutableList<FreightRateReq> = mutableListOf()
                       freightRateList.add(FreightRateReq().apply {
                           this.receivingPlace = "UKRAINE"
                           this.receivingPlaceCn = "UA"
                           this.rate = BigDecimal(2.4)
                           this.defaultRule = Bool.YES.code
                       })
                       freightRateList.add(FreightRateReq().apply {
                           this.receivingPlace = "VIETNAM"
                           this.receivingPlaceCn = "Vietnam"
                           this.rate = BigDecimal(21.2)
                           this.defaultRule = Bool.NO.code
                       })
                       this.freightRateList = freightRateList
                   })
                   receivingPlaceFreightRateList.add(ReceivingPlaceFreightRateReq(201336100L,"CHINA","中国").apply {
                       var freightRateList: MutableList<FreightRateReq> = mutableListOf()
                       freightRateList.add(FreightRateReq().apply {
                           this.receivingPlace = "Mexico"
                           this.receivingPlaceCn = "墨西哥"
                           this.rate = BigDecimal(2.4)
                           this.defaultRule = Bool.NO.code
                       })
                       freightRateList.add(FreightRateReq().apply {
                           this.receivingPlace = "NIGERIA"
                           this.receivingPlaceCn = "NG"
                           this.rate = BigDecimal(21.2)
                           this.defaultRule = Bool.YES.code
                       })
                       this.freightRateList = freightRateList
                   })
                   this.receivingPlaceFreightRateList = receivingPlaceFreightRateList
               })
               this.aeFreightRateRuleReqList = receivingPlaceFreightRateList
            }
            regionPriceRuleManageService.saveFreightRateRule(req)
        }
    }

    @Test
    fun getFreightRateRuleByPlatform(){
        var result = regionPriceRuleManageService.getFreightRateRuleByPlatform(PlatformEnum.TEMU)
        log.info { "result====: ${result?.toJson()}" }
    }

    @Test
    fun saveDiscountRateRule(){
        withSystemUser {
            var req: MutableList<SaveRegionPriceRuleDiscountRateReq> = mutableListOf()
            req.add(SaveRegionPriceRuleDiscountRateReq().apply {
                this.platformId = PlatformEnum.AE.platformId
                this.channelId = 1L
                this.shopIds = mutableListOf<Long>(7276480307564389700L)
                this.businessType = ProductShopBusinessType.SEMI_MANAGED.value
                this.supplyType = "OEM"
                this.rate = BigDecimal(12.2)
//                this.defaultRule = Bool.YES.code
            })
            req.add(SaveRegionPriceRuleDiscountRateReq().apply {
                this.platformId = PlatformEnum.AE.platformId
                this.channelId = 1L
                this.shopIds = mutableListOf<Long>(7308304887945629727L)
                this.businessType = ProductShopBusinessType.FULLY_MANAGED.value
                this.supplyType = "OEM"
                this.rate = BigDecimal(33.5)
//                this.defaultRule = Bool.YES.code
            })
            req.add(SaveRegionPriceRuleDiscountRateReq().apply {
                this.platformId = PlatformEnum.LAZADA.platformId
                this.channelId = 1L
                this.shopIds = mutableListOf<Long>(1819658755852206081L)
                this.businessType = ProductShopBusinessType.FULLY_MANAGED.value
                this.supplyType = "AIGC"
                this.rate = BigDecimal(11.98)
//                this.defaultRule = Bool.YES.code
            })
            regionPriceRuleManageService.saveDiscountRateRule(req)
        }
    }

    @Test
    fun queryDiscountRateRule(){
        var result = regionPriceRuleManageService.queryDiscountRateRule()
        log.info { "result====: ${result.toJson()}" }
    }

    @Test
    fun saveTaxRateRule(){
        withSystemUser {
            var req = SaveRegionPriceRuleTaxRateReq().apply {
                this.ruleType = RegionPriceRuleTaxRateTypeEnum.SHOP.code
                var taxRateRuleReqList: MutableList<TaxRateRuleReq> = mutableListOf()
                taxRateRuleReqList.add(TaxRateRuleReq().apply {
                    this.platformId = PlatformEnum.AE.platformId
                    this.shopId = 7276480307564389700L
                    this.shopName = "Mia Muse Store"
                    this.rate = BigDecimal(12.2)
                })
                taxRateRuleReqList.add(TaxRateRuleReq().apply {
                    this.platformId = PlatformEnum.AE.platformId
                    this.shopId = 7308304887945629727L
                    this.shopName = "UNIKTEE Store"
                    this.rate = BigDecimal(29.4)
                })
                this.ruleConfig = taxRateRuleReqList
            }
            regionPriceRuleManageService.saveTaxRateRule(req)
        }
    }

    @Test
    fun queryTaxRateRule(){
        var result = regionPriceRuleManageService.queryTaxRateRule()
        log.info { "result====: ${result?.toJson()}" }
    }
}
