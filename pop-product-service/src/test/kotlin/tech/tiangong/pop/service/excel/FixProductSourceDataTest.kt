package tech.tiangong.pop.service.excel

import com.alibaba.excel.EasyExcel
import com.alibaba.excel.annotation.ExcelProperty
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import team.aikero.blade.auth.withUser
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.sequence.id.IdHelper
import team.aikero.blade.user.entity.CurrentUser
import tech.tiangong.pop.PopApplication
import tech.tiangong.pop.dao.entity.ProductSourceData
import tech.tiangong.pop.dao.repository.ProductSourceDataRepository
import java.io.File
import java.time.LocalDateTime

@SpringBootTest(
    classes = [PopApplication::class],
    args = [
        "--spring.cloud.nacos.discovery.register-enabled=false",
        "--spring.profiles.active=dev-ola",
        "--spring.config.import=https://jv-devops.oss-cn-hangzhou.aliyuncs.com/\${spring.profiles.active}-yunwei-config.yml,classpath:local-bootstrap.yml"
    ]
)
@Disabled
@Slf4j
class FixProductSourceDataTest {
    @Autowired
    lateinit var productSourceDataRepository: ProductSourceDataRepository

    val user = CurrentUser(
        id = 0,
        name = "2025年4月27日-技术-修复旧数据应用更新",
        code = "",
        tenantId = 0,
        superAdmin = false
    )

    @Test
//    @Transactional(rollbackFor = [Exception::class])
    fun import() {
        val excelFile = File("C:\\Users\\<USER>\\Desktop\\修复应用更新源数据.xlsx")
        if (excelFile.exists()) {
            val listener = ExcelDataTestListener<ExcelData>()
            EasyExcel.read(excelFile, ExcelData::class.java, listener).sheet().doRead()
            val dataList = listener.getDataList()
            service(dataList)
        } else {
            error { "指定的 Excel 文件不存在" }
        }
    }

    fun service(dataList: List<ExcelData>) {
        if (dataList.isEmpty()) {
            return
        }
        // 设置用户
        withUser(user) {

            log.info { "开始处理数据" }
            val saveDataList = mutableListOf<ProductSourceData>()
            // 循环逻辑
            dataList.forEach {
                val data = ProductSourceData().apply {
                    this.productSourceDataId = IdHelper.getId()
                    this.productId = it.productId
                    this.shopId = it.shopId
                    this.spu = it.spu
                    this.dataType = it.dataType
                    this.sourceData = it.sourceData
                    this.editData = "2025年4月27日-技术-修复旧数据应用更新"
                    this.tenantId = it.tenantId
                    this.creatorId = it.creatorId
                    this.createdTime = it.createdTime
                    this.creatorName = it.creatorName
                    this.reviserId = it.reviserId
                    this.revisedTime = it.revisedTime
                    this.reviserName = it.reviserName
                    this.deleted = it.deleted
                }
                saveDataList.add(data)
            }

            // 批量插入, 500条一批
            log.info { "开始批量插入数据" }
            val batchSize = 500
            saveDataList.chunked(batchSize).forEach {
                productSourceDataRepository.saveBatch(it)
            }
            log.info { "结束批量插入数据" }
        }
    }

    // 定义一个数据类来映射 Excel 中的数据
    data class ExcelData(
        @ExcelProperty("product_source_data_id")
        var productSourceDataId: Long? = null,
        @ExcelProperty("product_id")
        var productId: Long? = null,
        @ExcelProperty("shop_id")
        var shopId: Long? = null,
        @ExcelProperty("spu")
        var spu: String? = null,
        @ExcelProperty("data_type")
        var dataType: Int? = null,
        @ExcelProperty("source_data")
        var sourceData: String? = null,
        @ExcelProperty("edit_data")
        var editData: String? = null,
        @ExcelProperty("deleted")
        var deleted: Int? = null,
        @ExcelProperty("creator_id")
        var creatorId: Long? = null,
        @ExcelProperty("created_time")
        var createdTime: LocalDateTime? = null,
        @ExcelProperty("tenant_id")
        var tenantId: Long? = null,
        @ExcelProperty("reviser_id")
        var reviserId: Long? = null,
        @ExcelProperty("revised_time")
        var revisedTime: LocalDateTime? = null,
        @ExcelProperty("creator_name")
        var creatorName: String? = null,
        @ExcelProperty("reviser_name")
        var reviserName: String? = null,
    )
}