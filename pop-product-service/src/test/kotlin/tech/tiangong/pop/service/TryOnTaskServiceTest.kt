package tech.tiangong.pop.service

import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import team.aikero.blade.auth.withSystemUser
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.util.json.parseJson
import team.aikero.blade.util.json.toJson
import tech.tiangong.pop.PopApplication
import tech.tiangong.pop.dao.entity.dto.TryOnTaskProductSnapshotDto
import tech.tiangong.pop.dao.entity.dto.TryOnTaskQcRecordDto
import tech.tiangong.pop.dao.repository.TryOnTaskQcRepository
import tech.tiangong.pop.dao.repository.TryOnTaskRepository
import tech.tiangong.pop.req.tryon.AiModelTypeReq
import tech.tiangong.pop.req.tryon.BackgroundReq
import tech.tiangong.pop.req.tryon.BatchConfigTryOnTaskReq
import tech.tiangong.pop.req.tryon.BatchSubmitTryOnQcTaskReq
import tech.tiangong.pop.req.tryon.GetAiTryOnDetailReq
import tech.tiangong.pop.req.tryon.ModelFaceReq
import tech.tiangong.pop.req.tryon.ModelReferenceImageReq
import tech.tiangong.pop.req.tryon.PostureFissionReq
import tech.tiangong.pop.req.tryon.SaveTryOnTaskByConfigReq
import tech.tiangong.pop.req.tryon.SaveTryOnTaskByRuleReq
import tech.tiangong.pop.req.tryon.SdpDesignStyleV2Req
import tech.tiangong.pop.req.tryon.SubmitTryOnQcTaskReq
import tech.tiangong.pop.service.selected.AeSelectedProductService
import tech.tiangong.pop.service.tryon.TryOnTaskQcService
import tech.tiangong.pop.service.tryon.TryOnTaskService

@SpringBootTest(
    classes = [PopApplication::class],
    args = [
        "--spring.cloud.nacos.discovery.register-enabled=false",
        "--spring.profiles.active=dev-ola",
        "--spring.config.import=https://jv-devops.oss-cn-hangzhou.aliyuncs.com/\${spring.profiles.active}-yunwei-config.yml,classpath:local-bootstrap.yml"
    ]
)
@Disabled
@Slf4j
class TryOnTaskServiceTest {

    @Autowired
    lateinit var tryOnTaskService:TryOnTaskService

    @Autowired
    lateinit var tryOnTaskQcService:TryOnTaskQcService
    @Autowired
    lateinit var tryOnTaskRepository:TryOnTaskRepository
    @Autowired
    lateinit var tryOnTaskQcRepository:TryOnTaskQcRepository
    @Autowired
    lateinit var aeSelectedProductService:AeSelectedProductService
    @Test
    fun afterPassQc(){
        withSystemUser {
            val tryOnTaskQc = tryOnTaskQcRepository.getById(7346498085932339203)
            val productId = tryOnTaskQc.productId
            val taskId = tryOnTaskQc.taskId
            val tryOnTask = tryOnTaskRepository.getById(taskId)
            val productSnapshotDto = tryOnTask.productSnapshot?.parseJson<TryOnTaskProductSnapshotDto>()
            aeSelectedProductService.updatePlatform(
                productId!!,
                productSnapshotDto!!,
                tryOnTaskQc
            )
        }
    }
    @Test
    fun reDoTryOnTask(){
        withSystemUser {
            tryOnTaskService.reDoTryOnTask(7346437287381921793L)
        }
    }

    @Test
    fun batchSubmitQc(){
        withSystemUser {
            val req = BatchSubmitTryOnQcTaskReq(listOf(7346109611760713729L), 2, 0)
            tryOnTaskQcService.batchSubmitQc(req)
        }
    }

    @Test
    fun submitQc(){
        withSystemUser {
            val qcRecord = mutableListOf<TryOnTaskQcRecordDto>()
            val record = TryOnTaskQcRecordDto().apply {
                this.imageUrl="https://oss.yunbanfang.cn/tiangong_8a18fe376faf4440a2714c4d7790b0da.jpg"
                this.qcResult = 1
                this.taskImageId = 111
            }
            qcRecord.add(record)
            val req = SubmitTryOnQcTaskReq(7346109611760713729L,qcRecord,0)
            tryOnTaskQcService.submitQc(req)
        }
    }

    @Test
    fun getQcDetailById(){
        val x = tryOnTaskQcService.getQcDetailById(7346109611760713729L)
        log.info{"qcTask:${x.toJson()}"}
    }

    @Test
    fun updateTaskState(){
        withSystemUser {
            tryOnTaskService.updateTaskState(listOf(7346056720849022977L))
        }
    }

    @Test
    fun syncAiTryOnDetail(){
        withSystemUser {
            val req = GetAiTryOnDetailReq(listOf<Long>(7346056720849022977L))
            tryOnTaskService.syncAiTryOnDetail(req)
        }
    }

    @Test
    fun saveTryOnTaskByRule() {
        withSystemUser {
            val ruleReq = SaveTryOnTaskByRuleReq(1005009553623529L,"https://ae-pic-a1.aliexpress-media.com/kf/Sf35f60a7630d460eb6a3bd6f3ad83b78F.jpg","tryon_rule_name1")
            tryOnTaskService.saveTryOnTaskByRule(listOf(ruleReq))
        }
    }

    @Test
    fun saveTryOnTaskByConfig(){
        withSystemUser {
            val styleCode = "7264883597411537579"
            val category = "01-0101-010102"
            val categoryName = "女装-上衣-T恤"
            val marketCode = ""
            val marketStyleCode = ""
            val marketSeriesCode = ""
            val styleParams: MutableList<SaveTryOnTaskByConfigReq> = mutableListOf()
            var postureFissionList: MutableList<PostureFissionReq> = mutableListOf()
            val configReq = SaveTryOnTaskByConfigReq()

            //款式信息
            val styleReq = SdpDesignStyleV2Req(styleCode,category)
            styleReq.storeId
            styleReq.storeName
            styleReq.categoryName = categoryName
            styleReq.marketCode = marketCode
            styleReq.marketStyleCode = marketStyleCode
            styleReq.marketSeriesCode = marketSeriesCode

            //换装类型：10-上装 20-下装 30-连体
            configReq.outfitType = 10
            //模型类型编码 (来自字典)
            val aiModelType = "chaoji"
            //生成数量
            val generateCount = 1
            configReq.aiModelTypes = listOf<AiModelTypeReq>(AiModelTypeReq(aiModelType,generateCount))
            //服装图 潮际主设的时候 组合换装的时候传 连体 上装传clothingImg 下装传
            configReq.clothingImg = "https://oss.yunbanfang.cn/tiangong_6aa69c1841dc4e7fbb0e32e6ff30ef40.jpg"
            //模特参考图片
            configReq.modelReferenceImages = mutableListOf<ModelReferenceImageReq>()
            //开启AI参考图生成，moodboardId
            configReq.moodboardId = "--profile zoa2uwa --p m7306265713268752420"
            //模特MJ图json
            configReq.modelMjInfos = listOf<String>("https://oss.yunbanfang.cn/tiangong_6aa69c1841dc4e7fbb0e32e6ff30ef40.jpg")
            //模特脸
            configReq.modelFace = ModelFaceReq()
            //背景
            configReq.background = BackgroundReq()


            configReq.postureFissionList = postureFissionList
            configReq.styleInfo = styleReq

            styleParams.add(configReq)
            val batchConfigTryOnTaskReq = BatchConfigTryOnTaskReq(styleParams)
            batchConfigTryOnTaskReq.postureFissionList = postureFissionList
            tryOnTaskService.saveTryOnTaskByConfig(batchConfigTryOnTaskReq)

            Thread.sleep(2000)
        }
    }

    @Test
    fun prepareTryOnTaskByProductId() {
         val x = tryOnTaskService.prepareTryOnTaskByProductId(listOf(1005009553623529L))
        log.info { "prepareTryOnTaskByProductId:${x.toJson()}" }
    }
}