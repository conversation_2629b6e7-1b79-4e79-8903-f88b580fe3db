package tech.tiangong.pop.service.v2.calculator

import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.TestConstructor
import java.math.BigDecimal
import java.math.RoundingMode

/**
 * Formula calculation service test cases
 */
@SpringBootTest
@TestConstructor(autowireMode = TestConstructor.AutowireMode.ALL)
@Disabled
class FormulaCalculationServiceTest(
    private val formulaCalculationService: FormulaCalculationService
) {

    @Test
    fun testSimpleExpressionCalculation() {
        val expression = "supplyPrice + logisticsCost"
        val variables = mapOf(
            "supplyPrice" to BigDecimal("100"),
            "logisticsCost" to BigDecimal("20")
        )
        
        val result = formulaCalculationService.calculate(expression, variables)
        
        assertTrue(result.success)
        assertEquals(BigDecimal("120"), result.value)
        assertEquals(variables, result.variables)
        assertEquals(expression, result.expression)
    }

    @Test
    fun testBracketNestedExpressionCalculation() {
        val expression = "( supplyPrice + storageCost ) * ( 1 + targetMargin )"
        val variables = mapOf(
            "supplyPrice" to BigDecimal("100"),
            "storageCost" to BigDecimal("10"), 
            "targetMargin" to BigDecimal("0.2")
        )
        
        val result = formulaCalculationService.calculate(expression, variables)
        
        assertTrue(result.success)
        assertEquals(BigDecimal("132.0"), result.value)
    }

    @Test
    fun testComplexMultiLevelBracketExpression() {
        val expression = "( supplyPrice + [ storageCost - discountRate ] ) * { 1 + targetMargin }"
        val variables = mapOf(
            "supplyPrice" to BigDecimal("100"),
            "storageCost" to BigDecimal("15"),
            "discountRate" to BigDecimal("5"),
            "targetMargin" to BigDecimal("0.3")
        )
        
        val result = formulaCalculationService.calculate(expression, variables)
        
        assertTrue(result.success)
        // ( 100 + [ 15 - 5 ] ) * { 1 + 0.3 } = ( 100 + 10 ) * 1.3 = 110 * 1.3 = 143.0
        assertEquals(BigDecimal("143.0"), result.value)
    }

    @Test
    fun testMissingVariableCase() {
        val expression = "supplyPrice + logisticsCost + adCost"
        val variables = mapOf(
            "supplyPrice" to BigDecimal("100"),
            "logisticsCost" to BigDecimal("20")
            // missing adCost
        )
        
        val result = formulaCalculationService.calculate(expression, variables)
        
        assertFalse(result.success)
        assertEquals(listOf("adCost"), result.missingVariables)
        assertTrue(result.errorMessage!!.contains("缺少必需的变量"))
    }

    @Test
    fun testInvalidExpression() {
        val expression = "supplyPrice +"  // missing right operand
        val variables = mapOf("supplyPrice" to BigDecimal("100"))
        
        val result = formulaCalculationService.calculate(expression, variables)
        
        assertFalse(result.success)
        assertNotNull(result.errorMessage)
    }

    @Test
    fun testDivisionByZeroError() {
        val expression = "supplyPrice / zeroDivisor"
        val variables = mapOf(
            "supplyPrice" to BigDecimal("100"),
            "zeroDivisor" to BigDecimal("0")
        )
        
        val result = formulaCalculationService.calculate(expression, variables)
        
        assertFalse(result.success)
        assertTrue(result.errorMessage!!.contains("除数不能为零"))
    }

    @Test
    fun testPrecisionCalculationTwoDecimalPlaces() {
        val expression = "supplyPrice / divisor"
        val variables = mapOf(
            "supplyPrice" to BigDecimal("100"),
            "divisor" to BigDecimal("3")
        )
        
        val result = formulaCalculationService.calculateWithPrecision(
            expression, variables, precision = 2
        )
        
        assertTrue(result.success)
        assertEquals(BigDecimal("33.33"), result.value)
    }

    @Test
    fun testPrecisionCalculationRoundUp() {
        val expression = "supplyPrice / divisor"
        val variables = mapOf(
            "supplyPrice" to BigDecimal("100"),
            "divisor" to BigDecimal("3")
        )
        
        val result = formulaCalculationService.calculateWithPrecision(
            expression, variables, precision = 2, roundingMode = RoundingMode.UP
        )
        
        assertTrue(result.success)
        assertEquals(BigDecimal("33.34"), result.value)
    }

    @Test
    fun testExpressionValidationValidExpressions() {
        val validExpressions = listOf(
            "supplyPrice + logisticsCost",
            "( supplyPrice + storageCost ) * targetMargin",
            "supplyPrice * ( 1 + targetMargin ) + adCost",
            "[ supplyPrice + storageCost ] / exchangeRateCny",
            "{ supplyPrice - discountRate } * commission"
        )
        
        validExpressions.forEach { expression ->
            assertTrue(formulaCalculationService.validateExpression(expression),
                "Expression should be valid: $expression")
        }
    }

    @Test
    fun testExpressionValidationInvalidExpressions() {
        val invalidExpressions = listOf(
            "supplyPrice +",
            "( supplyPrice + logisticsCost",
            "supplyPrice + logisticsCost )",
            "[ supplyPrice + logisticsCost }",
            "+ supplyPrice logisticsCost",
            ""
        )
        
        invalidExpressions.forEach { expression ->
            assertFalse(formulaCalculationService.validateExpression(expression),
                "Expression should be invalid: $expression")
        }
    }

    @Test
    fun testBatchCalculation() {
        val expressions = mapOf(
            "salePrice" to "( supplyPrice + logisticsCost ) * ( 1 + targetMargin )",
            "retailPrice" to "salePrice * ( 1 + discountRate )",
            "profit" to "salePrice - supplyPrice - logisticsCost"
        )
        val variables = mapOf(
            "supplyPrice" to BigDecimal("100"),
            "logisticsCost" to BigDecimal("20"),
            "targetMargin" to BigDecimal("0.2"),
            "salePrice" to BigDecimal("144"),  // pre-calculated result
            "discountRate" to BigDecimal("0.1")
        )
        
        val results = formulaCalculationService.batchCalculate(expressions, variables)
        
        assertEquals(3, results.size)
        
        // verify salePrice calculation
        val salePriceResult = results["salePrice"]!!
        assertTrue(salePriceResult.success)
        assertEquals(BigDecimal("144.0"), salePriceResult.value)
        
        // verify retailPrice calculation
        val retailPriceResult = results["retailPrice"]!!
        assertTrue(retailPriceResult.success)
        assertEquals(BigDecimal("158.4"), retailPriceResult.value)
        
        // verify profit calculation
        val profitResult = results["profit"]!!
        assertTrue(profitResult.success)
        assertEquals(BigDecimal("24"), profitResult.value)
    }

    @Test
    fun testUnaryOperatorExpression() {
        val expression = "-supplyPrice + targetMargin"
        val variables = mapOf(
            "supplyPrice" to BigDecimal("100"),
            "targetMargin" to BigDecimal("20")
        )
        
        val result = formulaCalculationService.calculate(expression, variables)
        
        assertTrue(result.success)
        assertEquals(BigDecimal("-80"), result.value)
    }

    @Test
    fun testV2NewVariablesUsage() {
        val expression = "supplyPrice + storageCost + logisticsCost + adCost + commission + withdrawFee"
        val variables = mapOf(
            "supplyPrice" to BigDecimal("100"),
            "storageCost" to BigDecimal("5"),
            "logisticsCost" to BigDecimal("15"),
            "adCost" to BigDecimal("8"),
            "commission" to BigDecimal("12"),
            "withdrawFee" to BigDecimal("3")
        )
        
        val result = formulaCalculationService.calculate(expression, variables)
        
        assertTrue(result.success) 
        assertEquals(BigDecimal("143"), result.value)
    }

    @Test
    fun testHighPrecisionDecimalCalculation() {
        val expression = "supplyPrice * targetMargin + taxRate * marketingCost"
        val variables = mapOf(
            "supplyPrice" to BigDecimal("999.999"),
            "targetMargin" to BigDecimal("0.*********"),
            "taxRate" to BigDecimal("0.065"),
            "marketingCost" to BigDecimal("45.67")
        )
        
        val result = formulaCalculationService.calculateWithPrecision(
            expression, variables, precision = 4
        )
        
        assertTrue(result.success)
        // 999.999 * 0.********* + 0.065 * 45.67 = 123.4568 + 2.9686 = 126.4254
        assertEquals(BigDecimal("126.4254"), result.value)
    }
}