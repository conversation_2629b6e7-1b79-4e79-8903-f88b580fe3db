package tech.tiangong.pop.service.v2.calculator

import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.TestConstructor
import tech.tiangong.pop.service.v2.expression.ast.*
import java.math.BigDecimal
import java.math.RoundingMode

/**
 * Expression calculator test cases
 */
@SpringBootTest
@TestConstructor(autowireMode = TestConstructor.AutowireMode.ALL)
@Disabled
class ExpressionCalculatorTest(
    private val expressionCalculator: ExpressionCalculator
) {

    @Test
    fun testNumberNodeCalculation() {
        val node = NumberNode(BigDecimal("123.45"))
        val result = expressionCalculator.calculate(node, emptyMap())
        
        assertEquals(BigDecimal("123.45"), result)
    }

    @Test
    fun testVariableNodeCalculation() {
        val node = VariableNode("supplyPrice")
        val variables = mapOf("supplyPrice" to BigDecimal("100.00"))
        val result = expressionCalculator.calculate(node, variables)
        
        assertEquals(BigDecimal("100.00"), result)
    }

    @Test
    fun testUndefinedVariableException() {
        val node = VariableNode("undefinedVariable")
        val variables = emptyMap<String, BigDecimal>()
        
        assertThrows<IllegalArgumentException> {
            expressionCalculator.calculate(node, variables)
        }
    }

    @Test
    fun testAdditionOperation() {
        val node = BinaryOperationNode(
            left = NumberNode(BigDecimal("10.5")),
            operator = "+",
            right = NumberNode(BigDecimal("20.3"))
        )
        val result = expressionCalculator.calculate(node, emptyMap())
        
        assertEquals(BigDecimal("30.8"), result)
    }

    @Test
    fun testSubtractionOperation() {
        val node = BinaryOperationNode(
            left = NumberNode(BigDecimal("50.0")),
            operator = "-",
            right = NumberNode(BigDecimal("20.3"))
        )
        val result = expressionCalculator.calculate(node, emptyMap())
        
        assertEquals(BigDecimal("29.7"), result)
    }

    @Test
    fun testMultiplicationOperation() {
        val node = BinaryOperationNode(
            left = NumberNode(BigDecimal("10")),
            operator = "*",
            right = NumberNode(BigDecimal("2.5"))
        )
        val result = expressionCalculator.calculate(node, emptyMap())
        
        assertEquals(BigDecimal("25.0"), result)
    }

    @Test
    fun testDivisionOperation() {
        val node = BinaryOperationNode(
            left = NumberNode(BigDecimal("100")),
            operator = "/",
            right = NumberNode(BigDecimal("4"))
        )
        val result = expressionCalculator.calculate(node, emptyMap())
        
        assertEquals(BigDecimal("25"), result)
    }

    @Test
    fun testDivisionByZeroException() {
        val node = BinaryOperationNode(
            left = NumberNode(BigDecimal("100")),
            operator = "/",
            right = NumberNode(BigDecimal("0"))
        )
        
        assertThrows<ArithmeticException> {
            expressionCalculator.calculate(node, emptyMap())
        }
    }

    @Test
    fun testUnaryOperationPositive() {
        val node = UnaryOperationNode(
            operator = "+",
            operand = NumberNode(BigDecimal("123.45"))
        )
        val result = expressionCalculator.calculate(node, emptyMap())
        
        assertEquals(BigDecimal("123.45"), result)
    }

    @Test
    fun testUnaryOperationNegative() {
        val node = UnaryOperationNode(
            operator = "-",
            operand = NumberNode(BigDecimal("123.45"))
        )
        val result = expressionCalculator.calculate(node, emptyMap())
        
        assertEquals(BigDecimal("-123.45"), result)
    }

    @Test
    fun testUnsupportedUnaryOperatorException() {
        val node = UnaryOperationNode(
            operator = "*",
            operand = NumberNode(BigDecimal("123"))
        )
        
        assertThrows<IllegalArgumentException> {
            expressionCalculator.calculate(node, emptyMap())
        }
    }

    @Test
    fun testUnsupportedBinaryOperatorException() {
        val node = BinaryOperationNode(
            left = NumberNode(BigDecimal("10")),
            operator = "%",
            right = NumberNode(BigDecimal("3"))
        )
        
        assertThrows<IllegalArgumentException> {
            expressionCalculator.calculate(node, emptyMap())
        }
    }

    @Test
    fun testParenthesesExpressionCalculation() {
        // ( 10 + 5 )
        val innerExpression = BinaryOperationNode(
            left = NumberNode(BigDecimal("10")),
            operator = "+",
            right = NumberNode(BigDecimal("5"))
        )
        val node = ParenthesesNode(innerExpression, ParenthesesNode.BracketType.ROUND)
        val result = expressionCalculator.calculate(node, emptyMap())
        
        assertEquals(BigDecimal("15"), result)
    }

    @Test
    fun testComplexExpressionWithVariables() {
        // ( supplyPrice + storageCost ) * ( 1 + targetMargin )
        val leftExpression = BinaryOperationNode(
            left = VariableNode("supplyPrice"),
            operator = "+",
            right = VariableNode("storageCost")
        )
        val leftParen = ParenthesesNode(leftExpression, ParenthesesNode.BracketType.ROUND)
        
        val rightExpression = BinaryOperationNode(
            left = NumberNode(BigDecimal("1")),
            operator = "+",
            right = VariableNode("targetMargin")
        )
        val rightParen = ParenthesesNode(rightExpression, ParenthesesNode.BracketType.ROUND)
        
        val rootExpression = BinaryOperationNode(
            left = leftParen,
            operator = "*",
            right = rightParen
        )
        
        val variables = mapOf(
            "supplyPrice" to BigDecimal("100"),
            "storageCost" to BigDecimal("10"),
            "targetMargin" to BigDecimal("0.2")
        )
        
        val result = expressionCalculator.calculate(rootExpression, variables)
        
        // ( 100 + 10 ) * ( 1 + 0.2 ) = 110 * 1.2 = 132
        assertEquals(BigDecimal("132.0"), result)
    }

    @Test
    fun testVariableValidationAllVariablesExist() {
        val expression = BinaryOperationNode(
            left = VariableNode("supplyPrice"),
            operator = "+",
            right = VariableNode("logisticsCost")
        )
        val variables = mapOf(
            "supplyPrice" to BigDecimal("100"),
            "logisticsCost" to BigDecimal("20")
        )
        
        val missingVariables = expressionCalculator.validateVariables(expression, variables)
        assertTrue(missingVariables.isEmpty())
    }

    @Test
    fun testVariableValidationMissingVariables() {
        val expression = BinaryOperationNode(
            left = VariableNode("supplyPrice"),
            operator = "+",
            right = VariableNode("logisticsCost")
        )
        val variables = mapOf(
            "supplyPrice" to BigDecimal("100")
            // missing logisticsCost
        )
        
        val missingVariables = expressionCalculator.validateVariables(expression, variables)
        assertEquals(listOf("logisticsCost"), missingVariables)
    }

    @Test
    fun testPrecisionRoundingDefault() {
        val expression = BinaryOperationNode(
            left = NumberNode(BigDecimal("10")),
            operator = "/",
            right = NumberNode(BigDecimal("3"))
        )
        
        val result = expressionCalculator.calculateWithRounding(
            expression, 
            emptyMap(), 
            precision = 2
        )
        
        assertEquals(BigDecimal("3.33"), result)
    }

    @Test
    fun testPrecisionRoundingUp() {
        val expression = BinaryOperationNode(
            left = NumberNode(BigDecimal("10")),
            operator = "/",
            right = NumberNode(BigDecimal("3"))
        )
        
        val result = expressionCalculator.calculateWithRounding(
            expression, 
            emptyMap(), 
            precision = 2, 
            roundingMode = RoundingMode.UP
        )
        
        assertEquals(BigDecimal("3.34"), result)
    }

    @Test
    fun testMultiLevelNestedBrackets() {
        // { [ ( 10 + 5 ) * 2 ] - 3 }
        val innermost = BinaryOperationNode(
            left = NumberNode(BigDecimal("10")),
            operator = "+",
            right = NumberNode(BigDecimal("5"))
        )
        val roundParen = ParenthesesNode(innermost, ParenthesesNode.BracketType.ROUND)
        
        val mulExpression = BinaryOperationNode(
            left = roundParen,
            operator = "*",
            right = NumberNode(BigDecimal("2"))
        )
        val squareParen = ParenthesesNode(mulExpression, ParenthesesNode.BracketType.SQUARE)
        
        val subExpression = BinaryOperationNode(
            left = squareParen,
            operator = "-",
            right = NumberNode(BigDecimal("3"))
        )
        val curlyParen = ParenthesesNode(subExpression, ParenthesesNode.BracketType.CURLY)
        
        val result = expressionCalculator.calculate(curlyParen, emptyMap())
        
        // { [ ( 10 + 5 ) * 2 ] - 3 } = { [ 15 * 2 ] - 3 } = { 30 - 3 } = 27
        assertEquals(BigDecimal("27"), result)
    }

    @Test
    fun testHighPrecisionCalculation() {
        val expression = BinaryOperationNode(
            left = NumberNode(BigDecimal("999999999999999999.123456789")),
            operator = "+",
            right = NumberNode(BigDecimal("0.000000001"))
        )
        
        val result = expressionCalculator.calculate(expression, emptyMap())
        
        // verify precision is maintained
        assertTrue(result > BigDecimal("999999999999999999.123456789"))
        assertTrue(result < BigDecimal("999999999999999999.123456790"))
    }
}