package tech.tiangong.pop.service

import cn.hutool.core.io.FileUtil
import com.aliexpress.open.domain.AliexpressPostproductRedefiningFindproductinfolistqueryAeopAEProductListQuery
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import team.aikero.blade.auth.withSystemUser
import team.aikero.blade.core.toolkit.isNotEmpty
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.util.json.parseJsonList
import team.aikero.blade.util.json.toJson
import tech.tiangong.pop.PopApplication
import tech.tiangong.pop.common.enums.ProductAeSellerBusinessType
import tech.tiangong.pop.component.AeProductSkuSyncComponent
import tech.tiangong.pop.component.MarketStyleComponent
import tech.tiangong.pop.dao.entity.AeOriginalProductSku
import tech.tiangong.pop.dao.entity.AeOriginalProductSpu
import tech.tiangong.pop.dao.repository.AeOriginalProductSkuRepository
import tech.tiangong.pop.dao.repository.AeOriginalProductSpuRepository
import tech.tiangong.pop.dao.repository.AeSelectedProductSpuRepository
import tech.tiangong.pop.dao.repository.PlatformProductPullRepository
import tech.tiangong.pop.dao.repository.ShopRepository
import tech.tiangong.pop.enums.ae.AeProductStatusType
import tech.tiangong.pop.helper.ProductPublishAeHelper
import tech.tiangong.pop.req.category.PublishAttributePairReq
import tech.tiangong.pop.req.product.ae.AeSelectRegulatoryAttributesOptionsReq
import tech.tiangong.pop.req.product.ae.PropertyValueItem
import tech.tiangong.pop.req.sdk.ae.AliexpressSetProductGroupsRequest
import tech.tiangong.pop.service.product.AePullSyncProductService
import tech.tiangong.pop.service.product.ProductAeRegulatoryService
import tech.tiangong.pop.service.selected.AePullProductDataService
import java.io.File
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import java.util.*
import kotlin.collections.isNotEmpty

/**
 * AE 速卖通AliExpress服务测试类
 */
@SpringBootTest(
    classes = [PopApplication::class],
    args = [
        "--spring.cloud.nacos.discovery.register-enabled=false",
        "--spring.profiles.active=qa-ola",
        "--spring.config.import=https://jv-devops.oss-cn-hangzhou.aliyuncs.com/\${spring.profiles.active}-yunwei-config.yml,classpath:local-bootstrap.yml"
    ]
)
@Disabled
@Slf4j
class AliexpressServiceTest {

    @Autowired
    private lateinit var aeOriginalProductSkuRepository: AeOriginalProductSkuRepository

    @Autowired
    private lateinit var aeOriginalProductSpuRepository: AeOriginalProductSpuRepository

    @Autowired
    private lateinit var aeProductSkuSyncComponent: AeProductSkuSyncComponent

    @Autowired
    private lateinit var aePullSyncProductService: AePullSyncProductService

    @Autowired
    private lateinit var platformProductPullRepository: PlatformProductPullRepository

    @Autowired
    private lateinit var marketStyleComponent: MarketStyleComponent

    @Autowired
    private lateinit var aeSelectedProductSpuRepository: AeSelectedProductSpuRepository

    @Autowired
    lateinit var aliexpressService: AliexpressService

    @Autowired
    lateinit var shopRepository: ShopRepository

    @Autowired
    lateinit var productAeRegulatoryService: ProductAeRegulatoryService

    @Autowired
    lateinit var aePullProductDataService: AePullProductDataService

    @Autowired
    lateinit var productPublishAeHelper: ProductPublishAeHelper

    /**
     * 测试用的AliExpress店铺ID
     */
    private var testShopId = 7307956352479162877L

    @Test
    fun testQueryProductInfo() {
        withSystemUser {
            log.info { "开始测试AliExpress商品信息查询" }
            val accessToken = shopRepository.getById(testShopId).token
                ?: throw IllegalArgumentException("Access token is null")
            val platformProductId = 1005008807553494L // 测试时替换为有效的商品ID

            try {
                // 调用服务方法
                val result = aliexpressService.queryProduct(
                    platformProductId = platformProductId,
                    accessToken = accessToken
                )

                log.info { "查询结果: ${if (result.isSuccess()) "成功" else "失败: ${result.getJoinErrorMsg()}"}" }
                assert(result.isSuccess()) { "查询商品信息失败" }

            } catch (e: Exception) {
                log.error(e) { "测试查询AliExpress商品信息失败" }
                throw e
            }
        }
    }

    /**
     * 测试查询AE类目树列表
     */
    @Test
    fun testQueryCategoryTreeList() {
        withSystemUser {
            log.info { "开始测试AliExpress类目树列表查询" }

            val accessToken = shopRepository.getById(testShopId).token

            try {
                // 调用服务方法
                val result = aliexpressService.queryCategoryTreeList(
                    accessToken = accessToken,
                    null,
                    onlyWithPermission = false,
                    channel = "AE_GLOBAL",
                    categoryId = 0L
                )

                log.info { "查询结果: ${if (Objects.nonNull(result)) "成功获取响应" else "空响应"}" }
                assert(Objects.nonNull(result)) { "返回结果不应为空" }

            } catch (e: Exception) {
                log.error(e) { "测试查询AE类目树列表失败" }
                throw e
            }
        }
    }

    @Test
    fun testQueryCategoryAttribute() {
        withSystemUser {
            log.info { "开始测试AliExpress类目属性查询" }
            val accessToken = shopRepository.getById(testShopId).token
            val categoryId = "202220802".toLong()
            val locale = "en_US"
            val channel = "AE_GLOBAL"
            try {
                // 调用服务方法
                val result = aliexpressService.queryCategoryAttributes(
                    accessToken = accessToken,
                    categoryId = categoryId,
                    locale = locale,
                    channel = channel
                )
                log.info { "结果：${result.responseBody?.toJson()}" }
                log.info { "查询结果: ${if (Objects.nonNull(result)) "成功获取响应" else "空响应"}" }
                assert(Objects.nonNull(result)) { "返回结果不应为空" }

            } catch (e: Exception) {
                log.error(e) { "测试查询AliExpress类目属性失败" }
                throw e
            }
        }
    }

    @Test
    fun testUploadImageToTempDirectory() {
        withSystemUser {
            log.info { "开始测试AliExpress图片上传" }
            val accessToken = shopRepository.getById(testShopId).token

            try {
                // 准备测试图片文件
                val testImageFile = prepareTestImage()
                log.info { "测试文件准备完成: ${testImageFile.absolutePath}" }

                // 调用服务方法
                val result = aliexpressService.uploadImageFileToTempDirectory(
                    accessToken = accessToken,
                    file = testImageFile
                )

                // 验证结果
                log.info { "上传结果: ${if (Objects.nonNull(result)) "成功获取响应" else "空响应"}" }
                assert(Objects.nonNull(result)) { "返回结果不应为空" }

                // 检查上传是否成功
                if (result.isSuccess()) {
                    log.info { "图片上传成功，图片URL: ${result.getImageUrl()}" }
                    assert(!result.getImageUrl().isNullOrBlank()) { "上传成功后应返回有效的图片URL" }
                } else {
                    log.warn { "图片上传失败: ${result.errorMessage ?: "未知错误"}" }
                }

                log.info { "完整响应内容: ${result.toJson()}" }

            } catch (e: Exception) {
                log.error(e) { "测试上传图片到AliExpress临时目录失败" }
                throw e
            }
        }
    }

    @Test
    fun testQueryFreightTemplateList() {
        withSystemUser {
            log.info { "开始测试AliExpress运费模板查询" }
            val accessToken = shopRepository.getById(testShopId).token
                ?: throw IllegalArgumentException("Access token is null")

            try {
                // 调用服务方法
                val result = aliexpressService.queryFreightTemplateList(accessToken)

                // 打印模板信息
                result.freightTemplateList?.forEach { template ->
                    log.info { "模板ID: ${template.templateId}, 名称: ${template.templateName}, 是否默认: ${template.isDefault}" }
                }
                if (result.isSuccess()) {
                    log.info { "查询运费模板成功" }
                } else {
                    log.info { "查询运费模板失败: ${result.resultErrorDesc}" }
                }

            } catch (e: Exception) {
                log.error(e) { "查询运费模板失败: ${e.message}" }

            }
        }
    }

    @Test
    fun testQueryPromiseTemplates() {
        withSystemUser {
            log.info { "开始测试AliExpress服务模板查询" }
            val accessToken = shopRepository.getById(testShopId).token
                ?: throw IllegalArgumentException("Access token is null")

            try {
                // 查询所有服务模板
                val result = aliexpressService.queryPromiseTemplates(accessToken)

                log.info { "查询结果: ${if (result.isSuccess()) "成功" else "失败: ${result.getJoinErrorMsg()}"}" }

                // 打印模板列表
                result.result?.templateList?.let { templates ->
                    log.info { "共查询到 ${templates.size} 个服务模板:" }
                    templates.forEach { template ->
                        log.info { "模板ID: ${template.id}, 名称: ${template.name}" }
                    }
                }

            } catch (e: Exception) {
                log.error(e) { "测试查询AliExpress服务模板失败" }
                throw e
            }
        }
    }

    @Test
    fun testMsrList() {
        withSystemUser {
            log.info { "开始测试AliExpress欧盟责任人列表查询" }
            val accessToken = shopRepository.getById(testShopId).token
                ?: throw IllegalArgumentException("Access token is null")

            try {
                val result = aliexpressService.queryMsrList(accessToken)

                log.info { "查询结果: ${if (result.isSuccess()) "成功" else "失败: ${result.getResultErrorMessage()}"}" }

                result.result?.data?.let { msrItems ->
                    log.info { "共查询到 ${msrItems.size} 个欧盟责任人:" }
                    msrItems.forEach { msr ->
                        log.info { "欧盟责任人ID: ${msr.id}, 名称: ${msr.name}" }
                    }
                }

            } catch (e: Exception) {
                log.error(e) { "测试查询AliExpress欧盟责任人列表失败" }
                throw e
            }
        }
    }

    @Test
    fun testManufactureDetail() {
        withSystemUser {
            log.info { "开始测试AliExpress制造商信息详情查询" }
            val accessToken = shopRepository.getById(testShopId).token
                ?: throw IllegalArgumentException("Access token is null")

            val manufactureId = 177444631L // 测试时替换为有效的制造商ID

            try {
                val result = aliexpressService.queryManufactureDetail(
                    accessToken = accessToken,
                    manufactureId = manufactureId
                )

                log.info { "查询结果: ${if (result.isSuccess()) "成功" else "失败: ${result.getResultErrorMessage()}"}" }

                result.getManufactureData()?.let { manufactureData ->
                    log.info { "制造商信息详情:" }
                    log.info { "ID: ${manufactureData.id}" }
                    log.info { "名称: ${manufactureData.name}" }
                    log.info { "国家/地区: ${manufactureData.country}" }
                    log.info { "地址: ${manufactureData.address}" }
                    log.info { "电话: ${manufactureData.getFullPhoneNumber()}" }
                    log.info { "邮箱: ${manufactureData.email}" }
                } ?: run {
                    log.warn { "未获取到制造商信息" }
                }

            } catch (e: Exception) {
                log.error(e) { "测试查询AliExpress制造商信息详情失败" }
                throw e
            }
        }
    }

    @Test
    fun testProductStatus() {
        withSystemUser {
            log.info { "开始测试AliExpress商品状态查询" }
            val accessToken = shopRepository.getById(testShopId).token
                ?: throw IllegalArgumentException("Access token is null")
            val productId = 1005008807553494L // 测试时替换为有效的商品ID

            try {
                val result = aliexpressService.queryProductStatus(
                    accessToken = accessToken,
                    productId = productId
                )

                log.info { "查询结果: ${if (result.isSuccess()) "成功" else "失败: ${result.getJoinErrorMsg()}"}" }

                // 显示商品状态信息
                val productStatus = result.getProductStatus()
                log.info { "商品ID: ${result.getProductId()}" }
                log.info { "商品状态: ${productStatus?.description} (${productStatus?.value})" }
                log.info { "状态更新时间: ${result.getTimestamp()}" }

                // 如果有错误信息，也显示出来
                result.getResultErrorCode()?.let { errorMsg ->
                    log.info { "附加信息: $errorMsg" }
                }

                result.getResultErrorCode()?.let { errorCode ->
                    if (errorCode != 0) {
                        log.info { "错误代码: $errorCode" }
                    }
                }

            } catch (e: Exception) {
                log.error(e) { "测试查询AliExpress商品状态失败" }
                throw e
            }
        }
    }

    @Test
    fun testManufactureList() {
        withSystemUser {
            log.info { "开始测试AliExpress制造商信息列表查询" }
            val accessToken = shopRepository.getById(testShopId).token
                ?: throw IllegalArgumentException("Access token is null")

            try {
                val result = aliexpressService.queryManufactureList(accessToken)

                log.info { "查询结果: ${if (result.isSuccess()) "成功" else "失败: ${result.getResultErrorMessage()}"}" }

                // 显示查询到的制造商信息
                result.getManufactureList()?.let { manufactureItems ->
                    log.info { "共查询到 ${manufactureItems.size} 个制造商:" }
                    manufactureItems.forEach { manufacture ->
                        log.info { "制造商ID: ${manufacture.id}, 名称: ${manufacture.name}" }
                    }

                    // 如果查询成功且有数据，尝试查询第一个制造商的详情
                    if (manufactureItems.isNotEmpty() && result.isSuccess()) {
                        manufactureItems.firstOrNull()?.id?.let { manufactureId ->
                            log.info { "尝试查询制造商详情，ID: $manufactureId" }
                            val detailResult = aliexpressService.queryManufactureDetail(
                                accessToken,
                                manufactureId
                            )

                            if (detailResult.isSuccess()) {
                                val detail = detailResult.getManufactureData()
                                log.info { "制造商详情查询成功: ${detail?.name}, 地址: ${detail?.address}, 邮箱: ${detail?.email}" }
                            } else {
                                log.warn { "制造商详情查询失败: ${detailResult.getResultErrorMessage()}" }
                            }
                        }
                    }
                } ?: run {
                    log.warn { "未查询到制造商信息" }
                }

            } catch (e: Exception) {
                log.error(e) { "测试查询AliExpress制造商信息列表失败" }
                throw e
            }
        }
    }

    @Test
    fun testQuerySellerRelations() {
        withSystemUser {
            log.info { "开始测试AliExpress商家账号关系列表查询" }
            val accessToken = shopRepository.getById(testShopId).token
                ?: throw IllegalArgumentException("Access token is null")

            try {
                val result = aliexpressService.querySellerRelations(accessToken)

                log.info { "查询结果: ${if (result.isSuccess()) "成功" else "失败"}" }
                log.info { "全球币种: ${result.globalCurrency}" }

                // 显示查询到的账号关系信息
                result.sellerRelationList?.let { relations ->
                    log.info { "共查询到 ${relations.size} 个商家账号关系:" }

                    // 按渠道分组显示
                    val channelGroups = relations.groupBy { it.channel ?: "未知渠道" }
                    channelGroups.forEach { (channel, relationList) ->
                        log.info { "渠道 [$channel] 共 ${relationList.size} 个账号:" }
                        relationList.forEach { relation ->
                            log.info { "  - 店铺名称: ${relation.channelShopName}" }
                            log.info { "    渠道卖家ID: ${relation.channelSellerId}" }
                            log.info { "    全球卖家ID: ${relation.sellerId}" }
                            log.info { "    商品币种: ${relation.channelCurrency}" }
                            log.info {
                                "    业务类型: ${relation.businessType} (${
                                    ProductAeSellerBusinessType.fromValue(
                                        relation.businessType
                                    ).description
                                })"
                            }
                        }
                    }
                } ?: run {
                    log.warn { "未查询到商家账号关系" }
                }

            } catch (e: Exception) {
                log.error(e) { "测试查询AliExpress商家账号关系列表失败" }
                throw e
            }
        }
    }

    @Test
    fun testQueryProductGroups() {
        withSystemUser {
            log.info { "开始测试AliExpress产品分组查询" }
            val accessToken = shopRepository.getById(testShopId).token
                ?: throw IllegalArgumentException("Access token is null")

            try {
                // 调用服务方法
                val result = aliexpressService.queryProductGroups(accessToken)

                if (result.isSuccess()) {
                    log.info { "查询产品分组成功" }

                    // 获取父级分组列表
                    val parentGroups = result.getParentGroups()
                    log.info { "共有 ${parentGroups.size} 个父级分组" }

                    // 打印分组信息
                    parentGroups.forEach { parentGroup ->
                        log.info { "父分组: ID=${parentGroup.groupId}, 名称=${parentGroup.groupName}" }

                        // 打印子分组信息
                        parentGroup.childGroupList?.let { childGroups ->
                            if (childGroups.isNotEmpty()) {
                                log.info { "  包含 ${childGroups.size} 个子分组:" }
                                childGroups.forEach { childGroup ->
                                    log.info { "    - 子分组: ID=${childGroup.groupId}, 名称=${childGroup.groupName}" }
                                }
                            } else {
                                log.info { "  无子分组" }
                            }
                        }
                    }
                } else {
                    log.info { "查询产品分组失败: ${result.getJoinErrorMsg()}" }
                }

            } catch (e: Exception) {
                log.error(e) { "查询产品分组失败: ${e.message}" }
            }
        }
    }


    @Test
    fun selectRegulatoryAttributesOptions() {
        withSystemUser {
            log.info { "开始测试AliExpress产品 获取选择监管属性项参数" }
            val req = AeSelectRegulatoryAttributesOptionsReq().apply {
                shopId = 7308304887945629727
                productId = 7313928885892431900
                attributes = listOf(PublishAttributePairReq().apply {
                    attributeId = 7313445818543284538
                    attributeValueId = 7313445818610393403
                })
                productTitle = "dddd"
                selectedPropertyValueList = listOf(PropertyValueItem().apply {
                    valueId = "9380"
                    valueName = "Casual shirt"
                    propertyName = "Product name"
                    propertyId = "731"
                    multiLangPropertyName = "品名"
                    multiLangValueName = "休闲衬衫"
                })
            }
            val result = productAeRegulatoryService.selectRegulatoryAttributesOptions(req)
            log.info { "====================${result?.toJson()}" }
        }
    }

    @Test
    // 批量设置多个分组的示例
    fun batchSetGroups() {
        withSystemUser {
            val accessToken = shopRepository.getById(7308304887945629727L).token
                ?: throw IllegalArgumentException("Access token is null")

            val productId = 7326956358922264662
            val request = AliexpressSetProductGroupsRequest(
                platformProductId = 1005009235312059,
                groupIds = listOf(40000007619007, 40000007842933, 40000007846927) // 最多3个分组
            )

            val response = aliexpressService.setProductGroups(productId, request, accessToken)

            when {
                response.isSuccess() -> {
                    log.info { "批量设置分组成功，${response.toJson()}" }
                }

                response.getResultErrorCode() == "30250021" -> {
                    log.info { "必须选择子分组，请检查分组ID是否为叶子节点" }
                }

                else -> {
                    log.info { "设置失败: ${response.getResultErrorMessage()}" }
                }
            }
        }
    }

    /**
     * 准备测试图片文件
     * 先尝试从classpath资源目录加载，如果不存在则创建测试图片
     */
    private fun prepareTestImage(): File {
        // 创建临时测试图片
        val tempImageFile = FileUtil.createTempFile("-test-image.jpg", false)

        // 方法2: 生成一个简单的测试图片
        createTestImageWithRandomText(tempImageFile)

        return tempImageFile
    }

    /**
     * 创建一个带有随机文本的测试图片
     */
    private fun createTestImageWithRandomText(outputFile: File) {
        val width = 400
        val height = 300

        val bufferedImage = java.awt.image.BufferedImage(
            width,
            height,
            java.awt.image.BufferedImage.TYPE_INT_RGB
        )

        val graphics = bufferedImage.createGraphics()

        // 设置抗锯齿渲染
        graphics.setRenderingHint(
            java.awt.RenderingHints.KEY_ANTIALIASING,
            java.awt.RenderingHints.VALUE_ANTIALIAS_ON
        )

        // 创建渐变背景
        val gradient = java.awt.GradientPaint(
            0f, 0f, java.awt.Color(30, 144, 255),  // 深天蓝色
            width.toFloat(), height.toFloat(), java.awt.Color(138, 43, 226)  // 紫罗兰色
        )
        graphics.paint = gradient
        graphics.fillRect(0, 0, width, height)

        // 生成随机文本内容
        val randomTexts = listOf(
            "AE Product ${UUID.randomUUID().toString().substring(0, 6)}",
            "POP Test ${LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd-HHmmss"))}",
            "Aliexpress Image #${System.currentTimeMillis() % 10000}",
            "Test Upload ${(Math.random() * 1000).toInt()}",
            "Sample ${UUID.randomUUID().toString().substring(0, 8)}"
        )
        val randomText = randomTexts.random()

        // 绘制主要文本
        graphics.font = java.awt.Font("Arial", java.awt.Font.BOLD, 24)
        graphics.color = java.awt.Color.WHITE
        val fontMetrics = graphics.fontMetrics
        val textWidth = fontMetrics.stringWidth(randomText)
        val textX = (width - textWidth) / 2
        graphics.drawString(randomText, textX, height / 2)

        // 添加日期时间标记
        val dateTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))
        graphics.font = java.awt.Font("Arial", java.awt.Font.PLAIN, 14)
        graphics.drawString(dateTime, 20, height - 20)

        // 添加POP系统标记
        graphics.drawString("POP System Test Image", 20, 30)

        graphics.dispose()

        // 保存为JPEG图片
        javax.imageio.ImageIO.write(bufferedImage, "jpg", outputFile)
    }

    @Test
    fun testProductPage() {
        /*
        TODO 注意
        page_size 每页查询商品数量。输入值小于100，默认20。
        product_status_type String 必填 商品业务状态，目前提供6种，输入参数分别是：上架:onSelling ；下架:offline ；审核中:auditing ；审核不通过:editingRequired；客服删除:service_delete ; 所有删除商品：deleted

        每个店铺, 每个类型都要单独分页
        - AE Job time表需要增加 product_status_type 维度
         */
        withSystemUser {
            val formatter: DateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
            AeProductStatusType.entries
                .filter { it.code != AeProductStatusType.DELETED.code && it.code != AeProductStatusType.SERVICE_DELETE.code }
                .forEach { productStatusType ->

                    val token = "50000001733j8gioatRfaGCRBrvwdgGOi0HS1FiWkSqbS14b1ad2buZwMOAfLAQd4udp"
                    log.info { "分页查询AE商品列表" }
                    val resp = aliexpressService.pageProduct(
                        AliexpressPostproductRedefiningFindproductinfolistqueryAeopAEProductListQuery().apply {
                            this.currentPage = 1
                            this.pageSize = 100
                            this.productStatusType = productStatusType.code
                            this.gmtModifiedStart = LocalDateTime.of(2024, 1, 1, 0, 0, 0).format(formatter)
                            this.gmtModifiedEnd = LocalDateTime.now().format(formatter)
                        },
                        token
                    )
//                    val r = resp.result
//                    log.info { "====================商品列表 ${r?.toJson()}" }
                    val pageDto = resp.result
                    if (pageDto != null && pageDto.dtoList.isNotEmpty()) {
                        log.info { "查询AE商品列表成功, 商品类型: ${productStatusType.desc}, 共${pageDto.productCount}条数据, 共${pageDto.totalPage}页, 当前页为${pageDto.currentPage}页, 当前页数量为${pageDto.dtoList?.size}条" }
                    }
//                    log.info { "查询AE商品详情" }
//                    r?.dtoList?.forEach { dto ->
//                        val detail = aliexpressService.queryProduct(dto.productId!!, token)
//                        log.info { "====================商品详情 ${detail.result?.toJson()}" }
//                    }
                }
        }
    }

    @Test
    fun aePull() {
        withSystemUser {
//            aePullProductDataService.pullSelected()
//            aePullProductDataService.pullSelectedSingle(listOf(1005009523781344L), 7308304887945629727L)
//            aePullProductDataService.updatePlatform()
//            aePullProductDataService.pullRecognitionSelected()
//            aePullProductDataService.pullRecognitionStyle()

            val token = shopRepository.getById(7331214241912852616L).token
            val detail = aliexpressService.queryProduct(1005009677056822L, token!!)
            log.info { "========> 详情结果: ${detail.toJson()}" }
            val dr = detail.result
            if (dr != null) {
                log.info { "========> AAA详情结果: ${dr.toJson()}" }
            }
//            log.info { "结束" }

//            val token = "50000001733j8gioatRfaGCRBrvwdgGOi0HS1FiWkSqbS14b1ad2buZwMOAfLAQd4udp"
//            val resp = aliexpressService.editSimpleProductFiled(
//                1005009523781344L,
//                AeEditFiedEnum.IMAGE_URLS.code,
//                "https://ae01.alicdn.com/kf/Sdd7a07eb0c974fae97f4ca71e5dae43fd.jpg;https://ae01.alicdn.com/kf/S2142b0dedffd48a58ca3fb68da3c25efe.jpg;https://ae01.alicdn.com/kf/S88c4d70871c14f09a942c0bbb1434c1al.jpg;https://ae01.alicdn.com/kf/Sbb18e521f44a40b0876b098eec66b02di.jpg;https://ae01.alicdn.com/kf/S9f6eb84522ab431388cac9684c842f1eN.jpg;https://ae01.alicdn.com/kf/S4a99c844ddf94f4bad63d39f32a990e9m.jpg",
//                token
//            )
//            log.info { "=============>>>> ${resp.toJson()}" }
        }
    }

    @Test
    fun loadImgTest() {
        val img = """
            ["https://chuangxin-oss-cdn.tiangong.tech/5e88434a1fb248c5bf2042f112e0cbad.png","https://chuangxin-oss-cdn.tiangong.tech/70b8320d4d994e2ba1612635fa3acd19.png","https://chuangxin-oss-cdn.tiangong.tech/a9f02efd5814422c90508b0d914be160.png","https://chuangxin-oss-cdn.tiangong.tech/a2e1ff0dd6434be7abdb02e95019c386.png","https://chuangxin-oss-cdn.tiangong.tech/6d12bcb354664ad8a2453469479bd8f0.png","https://chuangxin-oss-cdn.tiangong.tech/0079026005a1479089db3db83f092cc2.png"]
        """.trimIndent()
        val imgList = img.parseJsonList(String::class.java)

        val aeImageList = imgList.map { img ->
            // 判断图片是否是AE域名, 不是则上传(需要压缩)
            if (img.startsWith("https://ae-pic-a1.aliexpress-media.com") || img.startsWith("https://ae01.alicdn.com/")) {
                img
            } else {
                val newAeImg = productPublishAeHelper.buildAliExpressAddImage(img)
                if (newAeImg == null) {
                    throw RuntimeException("图片上传失败 $img")
                }
                newAeImg
            }
        }
        if (aeImageList.isEmpty()) {
            log.error { "图片地址为空" }
            throw RuntimeException("图片地址为空")
        }
        log.info { "图片地址: ${aeImageList.toJson()}" }
    }


    @Test
    fun style() {
        withSystemUser {
            // 更新风格
            val styleMap = marketStyleComponent.getMarketNodeMap(3)

            val updateSelected = aeSelectedProductSpuRepository
                .ktQuery()
                .list()
            val up = updateSelected.map { pro ->
                // resultStyle匹配value, 拿到code
                val styleCode = styleMap!!.entries.firstOrNull { it.value == pro.styleName }?.key
                pro.styleCode = styleCode
                pro
            }
            aeSelectedProductSpuRepository.updateBatchById(up)
        }
    }

    @Test
    fun pullSync() {
        withSystemUser {
//            val data = platformProductPullRepository.getById(7352507975709507706L)
//            aePullSyncProductService.pullSync(data)

           val shopId: Long = 7325779076249538647L
           val platformProductId: String = "1005009694290581"
           val aeSpu: AeOriginalProductSpu = aeOriginalProductSpuRepository.getById(platformProductId.toLong())
           val aeSkuList: List<AeOriginalProductSku> = aeOriginalProductSkuRepository.ktQuery()
               .eq(AeOriginalProductSku::productId, aeSpu.productId)
               .list()
            aeProductSkuSyncComponent.syncSku(shopId, platformProductId, aeSpu, aeSkuList)
        }
    }

}
