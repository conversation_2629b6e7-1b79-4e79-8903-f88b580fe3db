package tech.tiangong.pop.service

import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import team.aikero.blade.auth.withSystemUser
import team.aikero.blade.logging.core.annotation.Slf4j
import tech.tiangong.pop.PopApplication
import tech.tiangong.pop.req.product.RefreshProductImagePackageStateReq
import tech.tiangong.pop.req.product.UpdateProductGrossMarginByShopReq
import tech.tiangong.pop.service.product.ProductManageService
import java.math.BigDecimal

/**
 * 商品管理服务测试类
 */
@SpringBootTest(
    classes = [PopApplication::class],
    args = [
        "--spring.cloud.nacos.discovery.register-enabled=false",
        "--spring.profiles.active=dev-ola",
        "--spring.config.import=https://jv-devops.oss-cn-hangzhou.aliyuncs.com/\${spring.profiles.active}-yunwei-config.yml,classpath:local-bootstrap.yml"
    ]
)
@Disabled
@Slf4j
class ProductManageServiceTest {

    @Autowired
    lateinit var productManageService: ProductManageService

    @Test
    fun refreshImagePackageState() {
        withSystemUser {
            val req = RefreshProductImagePackageStateReq()
            req.productIds = listOf<Long>(7338153754204704773L)
            productManageService.refreshImagePackageState(req)
        }
    }

    @Test
    fun updateProductGrossMarginByShop() {
        withSystemUser {
            val req = UpdateProductGrossMarginByShopReq(mutableListOf(7354766801996608959),
                BigDecimal(15),mutableListOf(7308304887945629727))
            productManageService.updateProductGrossMarginByShop(req)
        }
    }
}
