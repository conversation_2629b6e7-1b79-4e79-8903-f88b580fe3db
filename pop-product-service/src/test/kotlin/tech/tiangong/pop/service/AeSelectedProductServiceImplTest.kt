package tech.tiangong.pop.service

import com.alibaba.fastjson2.parseObject
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import team.aikero.blade.auth.withSystemUser
import team.aikero.blade.logging.core.annotation.Slf4j
import tech.tiangong.pop.PopApplication
import tech.tiangong.pop.dao.entity.TryOnTaskQc
import tech.tiangong.pop.dao.entity.dto.TryOnTaskProductSnapshotDto
import tech.tiangong.pop.dao.repository.TryOnTaskQcRepository
import tech.tiangong.pop.dao.repository.TryOnTaskRepository
import tech.tiangong.pop.service.selected.AeSelectedProductService

@SpringBootTest(
    classes = [PopApplication::class],
    args = [
        "--spring.cloud.nacos.discovery.register-enabled=false",
        "--spring.profiles.active=qa-ola",
        "--spring.config.import=https://jv-devops.oss-cn-hangzhou.aliyuncs.com/\${spring.profiles.active}-yunwei-config.yml,classpath:local-bootstrap.yml"
    ]
)
@Disabled
@Slf4j
class AeSelectedProductServiceImplTest {
    @Autowired
    private lateinit var tryOnTaskRepository: TryOnTaskRepository

    @Autowired
    private lateinit var tryOnTaskQcRepository: TryOnTaskQcRepository

    @Autowired
    lateinit var aeSelectedProductService: AeSelectedProductService

    @Test
    fun test() {
        withSystemUser {
            val qc = tryOnTaskQcRepository.getById(7346776717658308964L)
            val tryOn = tryOnTaskRepository.getById(qc.taskId)
            val productId: Long = qc.productId!!
            val productSnapshotDto: TryOnTaskProductSnapshotDto = tryOn.productSnapshot.parseObject<TryOnTaskProductSnapshotDto>()
            val tryOnTaskQc: TryOnTaskQc = qc!!
            aeSelectedProductService.updatePlatform(productId, productSnapshotDto, tryOnTaskQc)
        }
    }
}