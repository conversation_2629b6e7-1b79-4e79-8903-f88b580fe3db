package tech.tiangong.pop.service

import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import team.aikero.blade.auth.withSystemUser
import team.aikero.blade.logging.core.annotation.Slf4j
import tech.tiangong.pop.PopApplication
import tech.tiangong.pop.dao.entity.dto.CategoryPackageConfigVolumeDto
import tech.tiangong.pop.req.category.CopyPublishCategoryPackageConfigReq
import tech.tiangong.pop.req.category.SavePublishCategoryPackageConfigReq
import tech.tiangong.pop.service.category.PublishCategoryPackageConfigService
import java.math.BigDecimal

/**
 * 品类包装配置服务测试类
 */
@SpringBootTest(
    classes = [PopApplication::class],
    args = [
        "--spring.cloud.nacos.discovery.register-enabled=false",
        "--spring.profiles.active=dev-ola",
        "--spring.config.import=https://jv-devops.oss-cn-hangzhou.aliyuncs.com/\${spring.profiles.active}-yunwei-config.yml,classpath:local-bootstrap.yml"
    ]
)
//@Disabled
@Slf4j
class PublishCategoryPackageConfigServiceTest {

    @Autowired
    lateinit var categoryPackageConfigService: PublishCategoryPackageConfigService
    /**
     * 测试保存配置
     */
    @Test
    fun save() {
        withSystemUser {
            var req = SavePublishCategoryPackageConfigReq()
            req.publishCategoryIds = listOf<Long>(7270576779181158479)
            req.packingVolume = CategoryPackageConfigVolumeDto().apply {
                this.maxSideLength = BigDecimal(20)
                this.minSideLength = BigDecimal(5)
                this.secondSideLength = BigDecimal(15)
            }
            req.weight = BigDecimal(20)
            req.outerPackingShape = 1
            req.outerPackingType = 2
            req.outerPackingImage = mutableListOf("https://chuangxin-oss-cdn.tiangong.tech/tiangong_79292eeb65a74b168fee1186770d5340.jpg")
            req.remark = "测试"
            categoryPackageConfigService.save(req)
        }
    }

    @Test
    fun copy() {
        withSystemUser {
            val req = CopyPublishCategoryPackageConfigReq()
            req.sourcePublishCategoryId = 7270576779181158479
            req.targetPublishCategoryIds = mutableListOf<Long>(7270576779206324304)
            categoryPackageConfigService.copy(req)
        }
    }

}
