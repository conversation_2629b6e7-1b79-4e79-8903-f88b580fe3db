package tech.tiangong.pop.service

import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import team.aikero.blade.auth.withSystemUser
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.util.json.toJson
import tech.tiangong.pop.PopApplication
import tech.tiangong.pop.dao.entity.Product
import tech.tiangong.pop.dao.repository.ProductRepository
import test.BaseTest

@SpringBootTest(
    classes = [PopApplication::class],
    args = [
        "--spring.cloud.nacos.discovery.register-enabled=false",
        "--spring.profiles.active=qa-ola",
        "--spring.config.import=https://jv-devops.oss-cn-hangzhou.aliyuncs.com/\${spring.profiles.active}-yunwei-config.yml,classpath:local-bootstrap.yml"
    ]
)
@Disabled
@Slf4j
class LazadaTest : BaseTest() {

    @Autowired
    lateinit var productRepository: ProductRepository

    @Test
    fun testAutoFill() {
        withSystemUser {
            val product = Product().apply {
                productTitle = "测试商品"
                // 故意不设置deleted
            }

            productRepository.save(product)

            val inserted = productRepository.getById(product.productId)
            log.info { "插入的商品: ${inserted.toJson()}" }
            assert(inserted.deleted == 0) { "deleted应自动填充为0，实际值为${inserted.deleted}" }
        }
    }
}
