package tech.tiangong.pop.service

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import team.aikero.blade.auth.withSystemUser
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.util.json.toJson
import tech.tiangong.pop.PopApplication
import tech.tiangong.pop.resp.sdk.alibaba.CategoryResponse

/**
 * AE 速卖通AliExpress服务测试类
 */
@SpringBootTest(
    classes = [PopApplication::class],
    args = [
        "--spring.cloud.nacos.discovery.register-enabled=false",
        "--spring.profiles.active=dev-ola",
        "--spring.config.import=https://jv-devops.oss-cn-hangzhou.aliyuncs.com/\${spring.profiles.active}-yunwei-config.yml,classpath:local-bootstrap.yml"
    ]
)
@Disabled
@Slf4j
class AlibabaServiceTest {

    @Autowired
    lateinit var alibabaCategoryService: AlibabaCategoryService
    @Test
    fun test(){
        var responseStr = "{\"categoryInfo\":[{\"categoryID\":312,\"name\":\"内衣\",\"isLeaf\":false,\"parentIDs\":[0],\"childCategorys\":[{\"id\":122478003,\"name\":\"背心、裹胸、抹胸、冰丝袖套\",\"isLeaf\":false,\"categoryType\":\"1\"},{\"id\":122450001,\"name\":\"女士内裤\",\"isLeaf\":false,\"categoryType\":\"1\"},{\"id\":201835213,\"name\":\"男士内裤\",\"isLeaf\":false,\"categoryType\":\"1\"},{\"id\":201909602,\"name\":\"丝袜/打底袜\",\"isLeaf\":false,\"categoryType\":\"1\"},{\"id\":1043349,\"name\":\"袜子\",\"isLeaf\":false,\"categoryType\":\"1\"},{\"id\":124188007,\"name\":\"文胸\",\"isLeaf\":false,\"categoryType\":\"1\"},{\"id\":122500001,\"name\":\"睡衣、家居服、睡袍、浴袍\",\"isLeaf\":false,\"categoryType\":\"1\"},{\"id\":122484001,\"name\":\"塑身内衣\",\"isLeaf\":false,\"categoryType\":\"1\"},{\"id\":122450002,\"name\":\"轻薄材质内衣\",\"isLeaf\":false,\"categoryType\":\"1\"},{\"id\":202057216,\"name\":\"内衣礼盒装/套装\",\"isLeaf\":true,\"categoryType\":\"1\"},{\"id\":122528003,\"name\":\"胸垫、胸贴、肩带、吊袜带\",\"isLeaf\":false,\"categoryType\":\"1\"},{\"id\":202053009,\"name\":\"秋衣秋裤\",\"isLeaf\":false,\"categoryType\":\"1\"},{\"id\":122558002,\"name\":\"保暖内衣\",\"isLeaf\":false,\"categoryType\":\"1\"},{\"id\":1031904,\"name\":\"其他内衣\",\"isLeaf\":true,\"categoryType\":\"3\"},{\"id\":202053806,\"name\":\"内衣加工定制\",\"isLeaf\":false,\"categoryType\":\"1\"}],\"minOrderQuantity\":3,\"featureInfos\":[{\"key\":\"BEGIN_AMOUNT_LIMIT\",\"value\":\"3\",\"status\":0,\"hierarchy\":false},{\"key\":\"lmchangjingbiao\",\"value\":\"xianhuo,jiagong,dinghuo\",\"status\":0,\"hierarchy\":false},{\"key\":\"SEVEN_DAYS_REFUNDS\",\"value\":\"n\",\"status\":0,\"hierarchy\":true}],\"categoryType\":\"1\",\"isSupportProcessing\":true}],\"succes\":\"true\"}"
        val categoryResponse: CategoryResponse = ObjectMapper().readValue(responseStr)
        log.info { "xxxxxxxxxx====${categoryResponse.toJson()} "}
    }
    /**
     * 测试查询1688类目树列表
     */
    @Test
    fun testQueryCategoryTreeList() {
        withSystemUser {
            log.info { "开始测试Alibaba类目树列表查询" }

            try {
                // 调用服务方法
                alibabaCategoryService.syncAlibabaCategories()

            } catch (e: Exception) {
                log.error(e) { "测试查询Alibaba类目树列表失败:${e.message}" }
                throw e
            }
        }
    }

}
