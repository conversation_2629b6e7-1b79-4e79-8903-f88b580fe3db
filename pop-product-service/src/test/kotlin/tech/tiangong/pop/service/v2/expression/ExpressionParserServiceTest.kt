package tech.tiangong.pop.service.v2.expression

import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.TestConstructor
import tech.tiangong.pop.service.v2.expression.ast.*

/**
 * Expression parser service test cases
 */
@SpringBootTest
@TestConstructor(autowireMode = TestConstructor.AutowireMode.ALL)
@Disabled
class ExpressionParserServiceTest(
    private val expressionParserService: ExpressionParserService
) {

    @Test
    fun testBasicNumberParsing() {
        val expression = "123.45"
        val ast = expressionParserService.parseExpression(expression)
        
        assertTrue(ast is NumberNode)
        assertEquals("123.45", (ast as NumberNode).value.toString())
    }

    @Test
    fun testVariableParsing() {
        val expression = "supplyPrice"
        val ast = expressionParserService.parseExpression(expression)
        
        assertTrue(ast is VariableNode)
        assertEquals("supplyPrice", (ast as VariableNode).name)
    }

    @Test
    fun testSimpleBinaryOperationParsing() {
        val expression = "supplyPrice + 10"
        val ast = expressionParserService.parseExpression(expression)
        
        assertTrue(ast is BinaryOperationNode)
        val binOp = ast as BinaryOperationNode
        assertEquals("+", binOp.operator)
        assertTrue(binOp.left is VariableNode)
        assertTrue(binOp.right is NumberNode)
    }

    @Test
    fun testOperatorPrecedenceMultiplicationOverAddition() {
        val expression = "supplyPrice + logisticsCost * 2"
        val ast = expressionParserService.parseExpression(expression)
        
        assertTrue(ast is BinaryOperationNode)
        val rootOp = ast as BinaryOperationNode
        assertEquals("+", rootOp.operator)
        assertTrue(rootOp.left is VariableNode)
        assertTrue(rootOp.right is BinaryOperationNode)
        
        val rightOp = rootOp.right as BinaryOperationNode
        assertEquals("*", rightOp.operator)
    }

    @Test
    fun testRoundBracketsHighestPrecedence() {
        val expression = "( supplyPrice + logisticsCost ) * 2"
        val ast = expressionParserService.parseExpression(expression)
        
        assertTrue(ast is BinaryOperationNode)
        val rootOp = ast as BinaryOperationNode
        assertEquals("*", rootOp.operator)
        assertTrue(rootOp.left is ParenthesesNode)
        
        val parenNode = rootOp.left as ParenthesesNode
        assertEquals(ParenthesesNode.BracketType.ROUND, parenNode.bracketType)
        assertTrue(parenNode.expression is BinaryOperationNode)
    }

    @Test
    fun testSquareBracketsMediumPrecedence() {
        val expression = "supplyPrice * [ logisticsCost + adCost ]"
        val ast = expressionParserService.parseExpression(expression)
        
        assertTrue(ast is BinaryOperationNode)
        val rootOp = ast as BinaryOperationNode
        assertEquals("*", rootOp.operator)
        assertTrue(rootOp.right is ParenthesesNode)
        
        val parenNode = rootOp.right as ParenthesesNode
        assertEquals(ParenthesesNode.BracketType.SQUARE, parenNode.bracketType)
    }

    @Test
    fun testCurlyBracketsLowestPrecedence() {
        val expression = "{ supplyPrice + logisticsCost } / targetMargin"
        val ast = expressionParserService.parseExpression(expression)
        
        assertTrue(ast is BinaryOperationNode)
        val rootOp = ast as BinaryOperationNode
        assertEquals("/", rootOp.operator)
        assertTrue(rootOp.left is ParenthesesNode)
        
        val parenNode = rootOp.left as ParenthesesNode
        assertEquals(ParenthesesNode.BracketType.CURLY, parenNode.bracketType)
    }

    @Test
    fun testMultiLevelBracketNestingHighToLow() {
        val expression = "( supplyPrice + [ logisticsCost - { adCost + commission } ] ) * targetMargin"
        val ast = expressionParserService.parseExpression(expression)
        
        assertTrue(ast is BinaryOperationNode)
        val rootOp = ast as BinaryOperationNode
        assertEquals("*", rootOp.operator)
        
        // verify round bracket on the left
        assertTrue(rootOp.left is ParenthesesNode)
        val roundParen = rootOp.left as ParenthesesNode
        assertEquals(ParenthesesNode.BracketType.ROUND, roundParen.bracketType)
        
        // verify square bracket inside
        assertTrue(roundParen.expression is BinaryOperationNode)
        val addOp = roundParen.expression as BinaryOperationNode
        assertTrue(addOp.right is ParenthesesNode)
        val squareParen = addOp.right as ParenthesesNode
        assertEquals(ParenthesesNode.BracketType.SQUARE, squareParen.bracketType)
        
        // verify curly bracket inside
        assertTrue(squareParen.expression is BinaryOperationNode)
        val subOp = squareParen.expression as BinaryOperationNode
        assertTrue(subOp.right is ParenthesesNode)
        val curlyParen = subOp.right as ParenthesesNode
        assertEquals(ParenthesesNode.BracketType.CURLY, curlyParen.bracketType)
    }

    @Test
    fun testUnaryOperatorParsing() {
        val expression = "-supplyPrice + targetMargin"
        val ast = expressionParserService.parseExpression(expression)
        
        assertTrue(ast is BinaryOperationNode)
        val binOp = ast as BinaryOperationNode
        assertEquals("+", binOp.operator)
        assertTrue(binOp.left is UnaryOperationNode)
        
        val unaryOp = binOp.left as UnaryOperationNode
        assertEquals("-", unaryOp.operator)
        assertTrue(unaryOp.operand is VariableNode)
    }

    @Test
    fun testComplexExpressionParsing() {
        val expression = "( supplyPrice + [ storageCost - discountRate ] ) * ( 1 + targetMargin ) - returnRate"
        val ast = expressionParserService.parseExpression(expression)
        
        assertNotNull(ast)
        assertTrue(ast is BinaryOperationNode)
        
        // verify this is a subtraction operation
        val rootOp = ast as BinaryOperationNode
        assertEquals("-", rootOp.operator)
        
        // verify left side is multiplication
        assertTrue(rootOp.left is BinaryOperationNode)
        val leftMul = rootOp.left as BinaryOperationNode
        assertEquals("*", leftMul.operator)
        
        // verify right side is a variable
        assertTrue(rootOp.right is VariableNode)
        assertEquals("returnRate", (rootOp.right as VariableNode).name)
    }

    @Test
    fun testExpressionValidationValidExpressions() {
        val validExpressions = listOf(
            "supplyPrice + logisticsCost",
            "( supplyPrice + storageCost ) * targetMargin",
            "[ adCost + commission ] / exchangeRateCny",
            "{ supplyPrice * ispRate } + withdrawFee",
            "supplyPrice * ( 1 + targetMargin ) + adCost"
        )
        
        validExpressions.forEach { expression ->
            assertTrue(expressionParserService.validateExpression(expression), 
                "Expression should be valid: $expression")
        }
    }

    @Test
    fun testExpressionValidationInvalidExpressions() {
        val invalidExpressions = listOf(
            "supplyPrice +",              // missing right operand
            "( supplyPrice + logisticsCost",  // missing right bracket
            "supplyPrice + logisticsCost )",  // missing left bracket
            "[ supplyPrice + logisticsCost }", // bracket mismatch
            "+ supplyPrice logisticsCost",     // missing operator
            "supplyPrice ++ logisticsCost",    // invalid operator
            "",                               // empty expression
            "   ",                            // whitespace only
            "supplyPrice + @",                // invalid character
            "( [ } supplyPrice",              // bracket order error
        )
        
        invalidExpressions.forEach { expression ->
            assertFalse(expressionParserService.validateExpression(expression), 
                "Expression should be invalid: $expression")
        }
    }

    @Test
    fun testVariableExtraction() {
        val expression = "( supplyPrice + storageCost ) * targetMargin + adCost - discountRate"
        val variables = expressionParserService.extractVariables(expression)
        
        val expectedVariables = setOf("supplyPrice", "storageCost", "targetMargin", "adCost", "discountRate")
        assertEquals(expectedVariables, variables)
    }

    @Test
    fun testInvalidCharacterException() {
        assertThrows<IllegalArgumentException> {
            expressionParserService.parseExpression("supplyPrice + @invalidChar")
        }
    }

    @Test
    fun testBracketMismatchException() {
        assertThrows<IllegalArgumentException> {
            expressionParserService.parseExpression("( supplyPrice + logisticsCost ]")
        }
    }

    @Test
    fun testMissingOperandException() {
        assertThrows<IllegalArgumentException> {
            expressionParserService.parseExpression("supplyPrice + ")
        }
    }
}