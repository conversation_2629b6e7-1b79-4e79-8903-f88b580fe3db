package tech.tiangong.pop.service

import com.aliexpress.open.domain.AliexpressPostproductRedefiningOfflineaeproductAeopModifyProductResponse
import com.aliexpress.open.domain.AliexpressPostproductRedefiningOfflineaeproductErrorDetail
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import team.aikero.blade.core.exception.BusinessException
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.util.json.parseJson
import team.aikero.blade.util.json.toJson
import tech.tiangong.pop.common.exception.PublishGlobalBizException
import tech.tiangong.pop.resp.sdk.aliexpress.AeUpdateSkuPricesResp
import tech.tiangong.pop.utils.DateTimeUtils
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime

@Disabled
@Slf4j
class MainTest {

    @Test
    fun jsonTest() {
        val re = AliexpressPostproductRedefiningOfflineaeproductAeopModifyProductResponse().apply {
            this.errorDetails = mutableListOf<AliexpressPostproductRedefiningOfflineaeproductErrorDetail>().apply {
                add(AliexpressPostproductRedefiningOfflineaeproductErrorDetail().apply {
                    this.errorCode = "Product does not exist"
                    this.errorMessage = "090014"
                    this.productIds = mutableListOf<Long>().apply {
                        add(1005008871437591L)
                    }
                })
            }
            this.modifyCount = 0
            this.productId = null
            this.success = null
            this.errorCode = null
            this.errorMessage = null
        }
        log.info { re.toJson() }
    }

    @Test
    fun jsonTest2() {
        val str = "{\"error_response\":{\"type\":\"ISP\",\"sub_msg\":\"partSku:CHK_SKU_PROPS_DUPLICATE:Duplicate attribute \",\"msg\":\"Remote service error\",\"request_id\":\"2141155017455044010904599\"}}"
        val result = str.parseJson(AeUpdateSkuPricesResp::class.java)
        log.info { result.toJson() }
    }

    @Test
    fun wpsTest() {
        val sheet1 = "'女装>裙装类>套裙/连衣裙'"
        println(replaceSheetName(sheet1))
    }

    // 在类中添加校验方法
    private fun replaceSheetName(sheetName: String): String {
        var temSheetName = sheetName
        // 确认名称不为空
        if (temSheetName.isBlank()) {
            log.warn { "工作表名称不能为空" }
            temSheetName = "sheet1"
        }
        // 确认名称中不包含以下字符: :\/?*[或]
        if (temSheetName.contains(Regex("[:\\\\/?*\\[\\]]"))) {
            log.warn { "工作表名称不能包含 :\\/?*[] 这些字符" }
            temSheetName = temSheetName.replace(Regex("[:\\\\/?*\\[\\]]"), "|")
        }
        // 确认名称的第一个或者最后一个字符不能是单引号
        if (temSheetName.startsWith('\'') || temSheetName.endsWith('\'')) {
            log.warn { "工作表名称的第一个或最后一个字符不能是单引号" }
            // 移除首尾单引号
            temSheetName = temSheetName.removeSurrounding("\'")
        }
        // 确认输入的名称不多于31个字符
        if (temSheetName.length > 31) {
            log.warn { "工作表名称不能超过31个字符，当前名称长度为 ${temSheetName.length}" }
            temSheetName = temSheetName.substring(0, 31)
        }
        return temSheetName
    }


    @Test
    fun priceTest() {
        compareCost(
            BigDecimal(12),
            BigDecimal(50),
            { currentCost, lastCost ->
                println("当前成本 $currentCost 比较低，与上次成本 $lastCost 的差值大于 10%")
            },
            { currentCost, lastCost ->
                println("当前成本 $currentCost 比较高，与上次成本 $lastCost 的差值小于 20%")
            },
            { currentCost, lastCost ->
                println("当前成本 $currentCost 在上次成本 $lastCost 的 10% 和 20% 之间")
            }
        )
    }

    fun compareCost(
        currentCost: BigDecimal?,
        lastCost: BigDecimal?,
        onCostTooHigh: (BigDecimal, BigDecimal) -> Unit,
        onCostTooLow: (BigDecimal, BigDecimal) -> Unit,
        onCostWithinRange: (BigDecimal, BigDecimal) -> Unit,
    ) {
        if (currentCost != null && lastCost != null) {
            // 计算成本差值
            val costDifference = currentCost.subtract(lastCost)
            // 计算 10% 和 20% 的阈值
            val tenPercent = BigDecimal(0.1)    // 10%
            val twentyPercent = BigDecimal(-0.2) // -20%
            val highThreshold = lastCost.multiply(tenPercent)
            val lowThreshold = lastCost.multiply(twentyPercent)

            when {
                costDifference > highThreshold -> onCostTooHigh(currentCost, lastCost)
                costDifference < lowThreshold -> onCostTooLow(currentCost, lastCost)
                else -> onCostWithinRange(currentCost, lastCost)
            }
        }
    }

    @Test
    fun dateTimeBetweenTest() {
        val startTime = LocalDateTime.of(2025, 7, 1, 1, 1, 1)
        val endTime = LocalDateTime.of(2025, 7, 7, 23, 3, 3)
        val timeList = DateTimeUtils.dateTimeBetween(startTime, endTime)
        log.info { timeList.toJson() }
    }

    @Test
    fun exceptionTest() {
        try {
            try {
                throw BusinessException("更新失败")
            } catch (e: Exception) {
                throw PublishGlobalBizException(e)
            }
        } catch (e: Exception) {
            log.error { e.message }
            e.printStackTrace()
        }
    }
}