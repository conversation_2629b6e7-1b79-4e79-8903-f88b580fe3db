package tech.tiangong.pop.service

import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import team.aikero.blade.auth.withSystemUser
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.util.json.toJson
import tech.tiangong.pop.PopApplication
import tech.tiangong.pop.external.ComfyuiTaskClientExternal
import tech.tiangong.pop.external.MarketTaskClientExternal

@SpringBootTest(
    classes = [PopApplication::class],
    args = [
        "--spring.cloud.nacos.discovery.register-enabled=false",
        "--spring.profiles.active=dev-ola",
        "--spring.config.import=https://jv-devops.oss-cn-hangzhou.aliyuncs.com/\${spring.profiles.active}-yunwei-config.yml,classpath:local-bootstrap.yml"
    ]
)
@Disabled
@Slf4j
class AigcClientExternalTest {

    @Autowired
    lateinit var comfyuiTaskClientExternal: ComfyuiTaskClientExternal

    @Autowired
    lateinit var marketTaskClientExternal: MarketTaskClientExternal

    @Test
    fun test() {
        withSystemUser {
            // 发起识别任务
//                val req = ComfyuiTaskReq().apply {
//                    this.busId = 7345687187421208577L
////                    this.busId = IdHelper.getId()
//                    this.busCode = this.busId.toString()
//                    this.refImgUrl =
//                        "https://ae-pic-a1.aliexpress-media.com/kf/S5b8648883d1c426fbabda066f7c60146s.jpg;https://ae-pic-a1.aliexpress-media.com/kf/S2d7165ff10424afeb5dec821b002543e5.png;https://ae-pic-a1.aliexpress-media.com/kf/S43c2804f4c4d4ebe879f90f4ef9f303eu.jpg;https://ae-pic-a1.aliexpress-media.com/kf/S1606d57fc38744b3ae52b9d69894bc8c2.jpg;https://ae-pic-a1.aliexpress-media.com/kf/S499745fda06543a0a806db0b6a121b3dJ.jpg"
//                            .replace(";", ",")
//                    this.workflowType = "710"
//                }
//                val respList = comfyuiTaskClientExternal.batchCreate(listOf(req))
//                log.info { "拉取AE-识别-[ComfyUI任务]批量新增任务 成功，结果: ${respList.toJson()}" }

//                if (respList.isNotEmpty()) {
            try {
                val resp = comfyuiTaskClientExternal.listByBusId(listOf(7345687187421208577L))
                log.info { "拉取AE-识别-[ComfyUI任务]批量查询任务busId 批量查询结果: ${resp.toJson()}" }
            } catch (e: Exception) {
                log.error(e) { "拉取AE-识别-[ComfyUI任务]批量新增任务 失败，错误信息: ${e.message}" }
            }
            try {
                val resp2 = comfyuiTaskClientExternal.listById(listOf(7345980082793768859L))
                log.info { "拉取AE-识别-[ComfyUI任务]批量查询任务taskId 批量查询结果: ${resp2.toJson()}" }
//                }
            } catch (e: Exception) {
                log.error(e) { "拉取AE-识别-[ComfyUI任务]批量新增任务 失败，错误信息: ${e.message}" }
            }
        }
    }

    @Test
    fun test3() {
        withSystemUser {

//            val createResp = marketTaskClientExternal.createStyle("https://ae-pic-a1.aliexpress-media.com/kf/S5b8648883d1c426fbabda066f7c60146s.jpg")
//            log.info { "创建识别风格-结果:${createResp?.toJson()}" }
/*
{
  "successful": true,
  "code": 200,
  "message": "请求成功",
  "data": {
    "predLabelsInfo": {
      "name": null,
      "value": null
    },
    "taskId": 7346056443864568536,
    "taskStatus": 10,
    "taskProgress": 20,
    "rankPosition": null,
    "message": "Complete image preprocessing",
    "failTaskMode": null,
    "aiStartTime": "2025-07-22T14:56:42",
    "aiEndTime": null,
    "createdTime": "2025-07-22T14:56:41",
    "pushTime": "2025-07-22T14:56:41"
  }
}
 */
            try {
                val resp = marketTaskClientExternal.detailStyle(7346056443864568536)
                log.info { "拉取AE-识别-[识别风格]批量查询任务busId 查询结果: ${resp?.toJson()}" }
            } catch (e: Exception) {
                log.error(e) { "拉取AE-识别-[识别风格]查询任务 失败，错误信息: ${e.message}" }
            }
            try {
                val resp2 = marketTaskClientExternal.qetByIdsStyle(listOf(7346056443864568536))
                log.info { "拉取AE-识别-[识别风格]批量查询任务taskId 批量查询结果: ${resp2?.toJson()}" }
            } catch (e: Exception) {
                log.error(e) { "拉取AE-识别-[识别风格]批量查询任务 失败，错误信息: ${e.message}" }
            }
        }
    }
}