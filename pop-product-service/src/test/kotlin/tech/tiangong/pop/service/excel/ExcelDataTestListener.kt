package tech.tiangong.pop.service.excel

import com.alibaba.excel.context.AnalysisContext
import com.alibaba.excel.read.listener.ReadListener
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log

// 定义一个读取监听器
@Slf4j
class ExcelDataTestListener<T> : ReadListener<T> {
    private val dataList = mutableListOf<T>()

    override fun invoke(data: T, context: AnalysisContext) {
        dataList.add(data)
    }

    override fun doAfterAllAnalysed(context: AnalysisContext) {
        // 所有数据读取完成后的操作
        log.info { "Excel 数据读取完成，共 ${dataList.size} 条数据" }
    }

    fun getDataList(): List<T> {
        return dataList
    }
}