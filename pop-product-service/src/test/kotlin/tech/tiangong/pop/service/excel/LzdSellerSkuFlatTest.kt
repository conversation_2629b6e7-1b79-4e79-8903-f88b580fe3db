package tech.tiangong.pop.service.excel

import com.alibaba.excel.EasyExcel
import com.alibaba.excel.annotation.ExcelProperty
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import team.aikero.blade.auth.withUser
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.sequence.id.IdHelper
import team.aikero.blade.user.entity.CurrentUser
import tech.tiangong.pop.PopApplication
import tech.tiangong.pop.common.enums.PlatformEnum
import tech.tiangong.pop.dao.entity.BarcodeUnitSnapshot
import tech.tiangong.pop.dao.entity.SellerSkuBarcodeRef
import tech.tiangong.pop.dao.entity.SellerSkuFlatInfo
import tech.tiangong.pop.dao.repository.BarcodeUnitSnapshotRepository
import tech.tiangong.pop.dao.repository.SellerSkuBarcodeRefRepository
import tech.tiangong.pop.dao.repository.SellerSkuFlatInfoRepository
import java.io.File

@SpringBootTest(
    classes = [PopApplication::class],
    args = [
        "--spring.cloud.nacos.discovery.register-enabled=false",
        "--spring.profiles.active=dev-ola",
        "--spring.config.import=https://jv-devops.oss-cn-hangzhou.aliyuncs.com/\${spring.profiles.active}-yunwei-config.yml,classpath:local-bootstrap.yml"
    ]
)
@Disabled
@Slf4j
class LzdSellerSkuFlatTest {
    @Autowired
    lateinit var sellerSkuFlatInfoRepository: SellerSkuFlatInfoRepository

    @Autowired
    lateinit var sellerSkuBarcodeRefRepository: SellerSkuBarcodeRefRepository

    @Autowired
    lateinit var barcodeUnitSnapshotRepository: BarcodeUnitSnapshotRepository

    val user = CurrentUser(
        id = 0,
        name = "2025年5月2日-技术-导入LZD条码",
        code = "",
        tenantId = 0,
        superAdmin = false
    )

    @Test
//    @Transactional(rollbackFor = [Exception::class])
    fun import() {
        val excelFile = File("C:\\Users\\<USER>\\Desktop\\截止0430组合商品.xlsx")
        if (excelFile.exists()) {
            val listener = ExcelDataTestListener<LzdTestExcelData>()
            EasyExcel.read(excelFile, LzdTestExcelData::class.java, listener).sheet("LZD组合商品").doRead()
            val dataList = listener.getDataList()
            service(dataList)
        } else {
            error { "指定的 Excel 文件不存在" }
        }
    }

    fun service(dataList: List<LzdTestExcelData>) {
        if (dataList.isEmpty()) {
            return
        }
        // 设置用户
        withUser(user) {

            log.info { "开始处理数据" }
            val saveFlatList = mutableListOf<SellerSkuFlatInfo>()
            val saveBarcodeRefList = mutableListOf<SellerSkuBarcodeRef>()
            val saveBarcodeUnit = mutableListOf<BarcodeUnitSnapshot>()
            // 循环逻辑
            dataList
                .filter { data -> data.shopId != "#N/A" }
                // 使用 sellerSku, spuCode, shopId 三个字段进行分组
                .groupBy { Triple(it.sellerSku!!, it.spuCode!!, it.shopId!!) }
                .forEach { (key, dtoList) ->
                    val (sellerSku, spuCode, shopId) = key
                    val flat = SellerSkuFlatInfo().apply {
                        this.sellerSkuFlatId = IdHelper.getId()
                        this.image = ""
                        this.color = dtoList.first().platformColor?.trim()
                        this.colorCode = dtoList.first().platformColor?.trim()
                        this.sellerSku = sellerSku
                        this.spuCode = spuCode
                        this.combo = 1
                        this.platformId = PlatformEnum.LAZADA.platformId // 写死平台
                        this.shopId = shopId.toLong()
                    }
                    saveFlatList.add(flat)

                    // 多条码
                    dtoList
                        .mapNotNull { it.barcode }
                        .distinct()
                        .forEach { barcode ->
                            // 处理barcode
                            val barcodeRef = SellerSkuBarcodeRef().apply {
                                this.barcodeRefId = IdHelper.getId()
                                this.sellerSkuFlatId = flat.sellerSkuFlatId
                                this.barcode = barcode.trim()
                            }
                            saveBarcodeRefList.add(barcodeRef)

                            // 处理unit
                            val unit = BarcodeUnitSnapshot().apply {
                                this.snapshotId = IdHelper.getId()
                                this.barcodeRefId = barcodeRef.barcodeRefId
                                this.unit = 1
                            }
                            saveBarcodeUnit.add(unit)
                        }
                }

            // 批量插入, 500条一批
            log.info { "开始批量插入数据" }
            val batchSize = 500
            saveFlatList.chunked(batchSize).forEach {
                sellerSkuFlatInfoRepository.saveBatch(it)
            }
            saveBarcodeRefList.chunked(batchSize).forEach {
                sellerSkuBarcodeRefRepository.saveBatch(it)
            }
            saveBarcodeUnit.chunked(batchSize).forEach {
                barcodeUnitSnapshotRepository.saveBatch(it)
            }
            log.info { "结束批量插入数据" }
        }
    }


    // 定义一个数据类来映射 Excel 中的数据
    data class LzdTestExcelData(
        @ExcelProperty("组合商品编码（这是组合seller_sku)")
        var sellerSku: String? = null,
        @ExcelProperty("商品编码（这是条码）")
        var barcode: String? = null,
        @ExcelProperty("SPU")
        var spuCode: String? = null,
        @ExcelProperty("platform_color")
        var platformColor: String? = null,
        @ExcelProperty("shop_id")
        var shopId: String? = null,
        @ExcelProperty("shop_name")
        var shopName: String? = null,
    )
}
