package tech.tiangong.pop.service

import com.alibaba.excel.EasyExcel
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import team.aikero.blade.logging.core.annotation.Slf4j
import tech.tiangong.pop.dto.product.TemuPlatformProductExportDTO
import java.io.File

@Disabled
@Slf4j
class ExcelTest {

    @Test
    fun exportExcelTest() {
        val data = listOf(
            TemuPlatformProductExportDTO().apply {
                this.spuCode = "ssss"
            }
        )

        try {
            val excelFile = File("C:\\Users\\<USER>\\Desktop", "dynamic_columns.xlsx")
            EasyExcel.write(excelFile, TemuPlatformProductExportDTO::class.java)
//                .head(buildHeaders(data)) // 动态生成表头
                .sheet("商品列表")
                .doWrite(data) // 动态生成内容
        } catch (e: Exception) {
            // ... 异常处理保持不变 ...
        }
    }

}