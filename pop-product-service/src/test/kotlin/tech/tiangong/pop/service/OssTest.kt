package tech.tiangong.pop.service

import org.apache.commons.io.FileUtils
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import team.aikero.blade.auth.withSystemUser
import team.aikero.blade.core.toolkit.isNotNull
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.util.io.FileUtil
import team.aikero.blade.util.json.toJson
import tech.tiangong.pop.PopApplication
import tech.tiangong.pop.constant.DownloadTaskConstants.MB_IN_BYTES
import tech.tiangong.pop.helper.ImageDownloadHelper
import tech.tiangong.pop.helper.UploaderOssHelper
import java.io.File
import java.util.*

@SpringBootTest(
    classes = [PopApplication::class],
    args = [
        "--spring.cloud.nacos.discovery.register-enabled=false",
        "--spring.profiles.active=dev-ola",
        "--spring.config.import=https://jv-devops.oss-cn-hangzhou.aliyuncs.com/\${spring.profiles.active}-yunwei-config.yml,classpath:local-bootstrap.yml"
    ]
)
@Disabled
@Slf4j
class OssTest {
    @Autowired
    lateinit var uploaderOssHelper: UploaderOssHelper
    @Autowired
    lateinit var imageDownloadHelper: ImageDownloadHelper

    @Test
    fun testUpload() {
        withSystemUser{
            log.info { "开始测试上传文件" }
            val file = File("D:\\Downloads\\test.png")
            // 请用dev，qa 配置的 oss 地址是阿里云内网
            val result = uploaderOssHelper.createFileUploadDTO(file)
            log.info { "上传结果: ${result.toJson()}" }
        }
    }

    @Test
    fun testDownloadAndCompressImage() {
        withSystemUser{
            log.info { "开始测试下载并压缩文件" }
            val fileDir = FileUtil.createTempDir().toFile()

            val imageUrl = "https://chuangxin-oss-cdn.tiangong.tech/72ba20cc0839374fac92abe732f4775c.png"
            val file = imageDownloadHelper.downloadAndCompressImage(imageUrl, fileDir, 2 * MB_IN_BYTES)
            if (file != null) {
                log.info { "开始测试下载并压缩文件 成功, ${file.absolutePath}" }
                FileUtils.deleteQuietly(file)
            } else {
                log.error { "开始测试下载并压缩文件 失败" }
            }
        }
    }

    @Test
    fun testCompress() {
        withSystemUser{
            log.info { "开始测试压缩文件" }
            val fileDir = FileUtil.createTempDir().toFile()
            val file = File("D:\\Downloads\\test.png")
            val newFile = File(fileDir, UUID.randomUUID().toString() + ".jpg")
            file.copyTo(newFile)
            val compressed = imageDownloadHelper.compressLargeImageToTargetSize(newFile, 2 * MB_IN_BYTES)
            if (compressed.isNotNull()) {
                log.info { "压缩文件成功, ${newFile.absolutePath}" }
                FileUtils.deleteQuietly(newFile)
            } else {
                log.error { "压缩失败" }
            }
        }
    }
}