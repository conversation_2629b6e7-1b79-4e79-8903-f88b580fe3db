package tech.tiangong.pop.req.sdk.ae

import org.junit.jupiter.api.Test
import org.junit.jupiter.api.Assertions.*
import tech.tiangong.pop.resp.sdk.aliexpress.*

/**
 * 海外托管数据转换测试类
 * 测试双向转换功能的正确性
 */
class LocalServiceConvertsTest {

    @Test
    fun `test toAliexpressProductQueryResponse with successful response`() {
        // 准备测试数据 - 海外托管商品查询响应
        val localServiceResponse = createTestLocalServiceProductQueryResponse()
        
        // 执行转换
        val standardResponse = localServiceResponse.toAliexpressProductQueryResponse()
        
        // 验证转换结果
        assertNotNull(standardResponse)
        assertTrue(standardResponse.isSuccess())
        assertNotNull(standardResponse.result)
        
        val result = standardResponse.result!!
        
        // 验证基本信息转换
        assertEquals(12345L, result.categoryId?.toLong())
        assertEquals(67890L, result.freightTemplateId)
        assertEquals("USD", result.currencyCode)
        assertEquals("en_US", result.locale)
        
        // 验证多媒体信息转换
        assertEquals("https://image1.jpg,https://image2.jpg", result.imageUrls)
        assertNotNull(result.multimedia)
        assertEquals(1, result.multimedia?.videos?.size)
        
        // 验证营销图转换
        assertNotNull(result.marketImages)
        assertEquals(1, result.marketImages?.size)
        assertEquals("https://market1.jpg", result.marketImages?.get(0)?.url)
        
        // 验证标题转换
        assertNotNull(result.subjectList)
        assertEquals(1, result.subjectList?.size)
        assertEquals("Test Product", result.subjectList?.get(0)?.value)
        
        // 验证 SKU 转换
        assertNotNull(result.productSkus)
        assertEquals(1, result.productSkus?.size)
        val sku = result.productSkus?.get(0)!!
        assertEquals("TEST-SKU-001", sku.skuCode)
        assertEquals("99.99", sku.skuPrice)
        assertEquals(100L, sku.ipmSkuStock)
    }

    @Test
    fun `test toAliexpressProductQueryResponse with failed response`() {
        // 准备失败的测试数据
        val failedResponse = AliexpressLocalServiceProductQueryResponse(
            success = false,
            product = null
        )
        
        // 执行转换
        val standardResponse = failedResponse.toAliexpressProductQueryResponse()
        
        // 验证转换结果
        assertNotNull(standardResponse)
        assertNull(standardResponse.result)
        assertEquals("海外托管商品查询失败或商品不存在", standardResponse.msg)
    }

    @Test
    fun `test toAliexpressProductQueryResponse with null product`() {
        // 准备空商品的测试数据
        val nullProductResponse = AliexpressLocalServiceProductQueryResponse(
            success = true,
            product = null
        )
        
        // 执行转换
        val standardResponse = nullProductResponse.toAliexpressProductQueryResponse()
        
        // 验证转换结果
        assertNotNull(standardResponse)
        assertNull(standardResponse.result)
        assertEquals("海外托管商品查询失败或商品不存在", standardResponse.msg)
    }

    /**
     * 创建测试用的海外托管商品查询响应
     */
    private fun createTestLocalServiceProductQueryResponse(): AliexpressLocalServiceProductQueryResponse {
        val productDto = AliexpressLocalServiceProductDto(
            multimedia = MultimediaDto(
                mainImageList = listOf("https://image1.jpg", "https://image2.jpg"),
                marketImageList = listOf(
                    MarketImageDto(url = "https://market1.jpg", imageType = 2)
                ),
                videoList = listOf(
                    VideoDto(
                        mediaType = "MAIN_IMAGE_VIDEO",
                        posterUrl = "https://video-poster.jpg",
                        mediaId = 123456L
                    )
                )
            ),
            productSkuList = listOf(
                ProductSkuDto(
                    supplyPrice = "99.99",
                    skuCode = "TEST-SKU-001",
                    status = "active",
                    packageWeight = "0.5",
                    packageLength = "10",
                    packageWidth = "8",
                    packageHeight = "5",
                    sellableQuantity = 100,
                    skuPropertyList = listOf(
                        SkuPropertyDto(
                            skuPropertyId = 1L,
                            propertyValueId = 101L,
                            propertyValueDefinitionName = "Red",
                            skuImage = "https://sku-image.jpg"
                        )
                    ),
                    destCountrySupplyPriceList = listOf(
                        DestCountrySupplyPriceDto(
                            countryCode = "US",
                            moneyValue = "89.99"
                        )
                    )
                )
            ),
            packageDto = PackageDto(
                lotNum = 1,
                productUnit = 1,
                packageType = false
            ),
            productInfo = ProductInfoDto(
                subjectList = listOf(
                    SubjectDto(locale = "en_US", value = "Test Product")
                ),
                categoryId = 12345L,
                postageId = 67890L,
                locale = "en_US",
                currencyCode = "USD",
                supportCountrySupplyPrice = true
            ),
            productPropertyList = listOf(
                ProductPropertyDto(
                    attrNameId = 1L,
                    attrValueId = 101L,
                    attrName = "Color",
                    attrValue = "Red"
                )
            ),
            detailSourceList = listOf(
                DetailSourceDto(
                    locale = "en_US",
                    mobileDetail = "Mobile detail content",
                    webDetail = "Web detail content"
                )
            ),
            productExt = ProductExtDto(
                manufacturerId = 999L,
                msrEuId = 888L,
                deliveryTime = 7,
                sizeChartId = 777L,
                qualificationList = listOf(
                    QualificationDto(
                        type = "image",
                        value = "https://qualification.jpg",
                        key = "CE_MARK"
                    )
                )
            )
        )
        
        return AliexpressLocalServiceProductQueryResponse(
            success = true,
            product = productDto
        )
    }
}
