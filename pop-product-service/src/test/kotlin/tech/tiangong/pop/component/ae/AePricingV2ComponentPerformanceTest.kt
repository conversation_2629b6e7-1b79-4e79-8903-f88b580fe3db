package tech.tiangong.pop.component.ae

import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import team.aikero.blade.auth.withSystemUser
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import tech.tiangong.pop.PopApplication
import tech.tiangong.pop.bo.v2.AePricingV2ComponentCalcBO
import tech.tiangong.pop.dao.entity.Product
import tech.tiangong.pop.dao.entity.Shop
import tech.tiangong.pop.dto.v2.pricing.AePricingSkcDto
import tech.tiangong.pop.dto.v2.pricing.PricingVariablesOverrideConfigDto
import java.math.BigDecimal
import kotlin.system.measureTimeMillis

/**
 * AE定价V2组件性能测试
 * 验证四层循环优化效果
 */
@SpringBootTest(
    classes = [PopApplication::class],
    args = [
        "--spring.cloud.nacos.discovery.register-enabled=false",
        "--spring.profiles.active=qa-ola",
        "--spring.config.import=https://jv-devops.oss-cn-hangzhou.aliyuncs.com/\${spring.profiles.active}-yunwei-config.yml,classpath:local-bootstrap.yml"
    ]
)
@Disabled
@Slf4j
class AePricingV2ComponentPerformanceTest {

    @Autowired
    private lateinit var aePricingV2Component: AePricingV2Component

    @Test
    fun testFourLayerLoopOptimization() = withSystemUser {
        // 模拟大量数据的场景测试优化效果
        val product = createMockProduct()
        val shops = createMockShops(10) // 10个店铺
        val shipFromList = listOf("201336106", "201336100")
        val shipToList = listOf("US", "CA", "GB", "DE", "FR", "IT", "ES", "AU") // 8个目的地
        val skcDtoList = createMockSkcs(5) // 5个SKC

        val totalCombinations = shops.size * shipFromList.size * shipToList.size * skcDtoList.size
        log.info { "开始性能测试 - 总组合数: $totalCombinations (${shops.size}店铺 × ${shipFromList.size}发货地 × ${shipToList.size}目的地 × ${skcDtoList.size}SKC)" }

        val request = AePricingV2ComponentCalcBO(
            product = product,
            shops = shops,
            shipFromList = shipFromList,
            shipToList = shipToList,
            skcDtoList = skcDtoList,
            overrideConfig = PricingVariablesOverrideConfigDto(),
            enableVerboseCalculationDetails = false
        )

        // 预热JVM
        repeat(2) {
            aePricingV2Component.calculatePrice(request)
        }

        // 性能测试
        val executionTime = measureTimeMillis {
            val results = aePricingV2Component.calculatePrice(request)
            log.info { "计算完成 - 结果数量: ${results.size}, 成功数量: ${results.count { it.success }}" }
        }

        log.info { "优化后四层循环执行时间: ${executionTime}ms" }
        log.info { "平均每个组合计算时间: ${executionTime.toDouble() / totalCombinations}ms" }

        // 性能基准：优化后应该比未优化版本快很多
        // 1600个组合应该在合理时间内完成（预期 < 5秒）
        assert(executionTime < 8000) { "执行时间过长: ${executionTime}ms，优化可能存在问题" }
    }

    @Test
    fun testSkcCostCacheOptimization() = withSystemUser {
        // 测试SKC成本缓存优化效果
        val product = createMockProduct()
        val shops = createMockShops(5) // 5个店铺
        val shipFromList = listOf("201336106", "201336100")
        val shipToList = listOf("US", "GB", "DE", "FR") // 4个目的地
        
        log.info { "测试场景1: 所有SKC成本相同（最大缓存效果）" }
        val sameCostSkcs = createSameCostSkcs(8) // 8个相同成本的SKC
        testSkcScenario("所有成本相同", product, shops, shipFromList, shipToList, sameCostSkcs)

        log.info { "测试场景2: 部分SKC成本相同（中等缓存效果）" }
        val mixedCostSkcs = createMixedCostSkcs(8) // 8个SKC，3种不同成本
        testSkcScenario("部分成本相同", product, shops, shipFromList, shipToList, mixedCostSkcs)

        log.info { "测试场景3: 所有SKC成本不同（最小缓存效果）" }
        val differentCostSkcs = createDifferentCostSkcs(8) // 8个不同成本的SKC
        testSkcScenario("所有成本不同", product, shops, shipFromList, shipToList, differentCostSkcs)
    }

    private fun testSkcScenario(
        scenarioName: String,
        product: Product,
        shops: List<Shop>,
        shipFromList: List<String>,
        shipToList: List<String>,
        skcDtoList: List<AePricingSkcDto>
    ) {
        val totalCombinations = shops.size * shipFromList.size * shipToList.size * skcDtoList.size
        
        val request = AePricingV2ComponentCalcBO(
            product = product,
            shops = shops,
            shipFromList = shipFromList,
            shipToList = shipToList,
            skcDtoList = skcDtoList,
            overrideConfig = PricingVariablesOverrideConfigDto(),
            enableVerboseCalculationDetails = false,
        )

        // 预热
        aePricingV2Component.calculatePrice(request)

        // 性能测试
        val executionTime = measureTimeMillis {
            val results = aePricingV2Component.calculatePrice(request)
            log.info { "$scenarioName - 总组合数: $totalCombinations, 结果数量: ${results.size}, 成功数量: ${results.count { it.success }}" }
        }

        log.info { "$scenarioName - 执行时间: ${executionTime}ms, 平均每组合: ${executionTime.toDouble() / totalCombinations}ms" }
    }

    /**
     * 创建模拟产品
     */
    private fun createMockProduct(): Product {
        return Product().apply {
            productId = 1001L
            supplyMode = "SELF_SUPPORT"
            cmpRetailPrice = BigDecimal("99.99")
            cmpSalePrice = BigDecimal("79.99")
        }
    }

    /**
     * 创建模拟店铺列表
     */
    private fun createMockShops(count: Int): List<Shop> {
        return (1..count).map { index ->
            Shop().apply {
                shopId = index.toLong()
                shopName = "测试店铺_$index"
                businessType = 1
            }
        }
    }

    /**
     * 创建模拟SKC列表
     */
    private fun createMockSkcs(count: Int): List<AePricingSkcDto> {
        return (1..count).map { index ->
            AePricingSkcDto(
                skc = "SKC_$index",
                costPrice = BigDecimal("10.00").add(BigDecimal(index)),
                purchasePrice = BigDecimal("8.00").add(BigDecimal(index))
            )
        }
    }

    /**
     * 创建相同成本的SKC列表（测试最大缓存效果）
     */
    private fun createSameCostSkcs(count: Int): List<AePricingSkcDto> {
        val fixedCostPrice = BigDecimal("15.00")
        val fixedPurchasePrice = BigDecimal("12.00")
        
        return (1..count).map { index ->
            AePricingSkcDto(
                skc = "SAME_COST_SKC_$index",
                costPrice = fixedCostPrice,
                purchasePrice = fixedPurchasePrice
            )
        }
    }

    /**
     * 创建混合成本的SKC列表（测试中等缓存效果）
     * 3种不同成本，每种成本有多个SKC
     */
    private fun createMixedCostSkcs(count: Int): List<AePricingSkcDto> {
        val costOptions = listOf(
            BigDecimal("10.00") to BigDecimal("8.00"),   // 成本组合1
            BigDecimal("15.00") to BigDecimal("12.00"),  // 成本组合2  
            BigDecimal("20.00") to BigDecimal("16.00")   // 成本组合3
        )
        
        return (1..count).map { index ->
            val (costPrice, purchasePrice) = costOptions[index % costOptions.size]
            AePricingSkcDto(
                skc = "MIXED_COST_SKC_$index",
                costPrice = costPrice,
                purchasePrice = purchasePrice
            )
        }
    }

    /**
     * 创建不同成本的SKC列表（测试最小缓存效果）
     */
    private fun createDifferentCostSkcs(count: Int): List<AePricingSkcDto> {
        return (1..count).map { index ->
            AePricingSkcDto(
                skc = "DIFF_COST_SKC_$index",
                costPrice = BigDecimal("10.00").add(BigDecimal(index * 2)),
                purchasePrice = BigDecimal("8.00").add(BigDecimal(index * 2))
            )
        }
    }
}