#register = false
spring:
  cloud:
    nacos:
      discovery:
        register-enabled: false

blade:
  mybatis-plus:
    logging:
      enabled: true
booster:
  #是否打印sql
  allow-print-mybatis-sql: true

mybatis-plus:
  mapper-locations: classpath*:mapper/**/*Mapper.xml
  configuration:
    map-underscore-to-camel-case: true
  global-config:
    db-config:
      logic-delete-field: deleted  # 全局逻辑删除的实体字段名(since 3.3.0,配置后可以忽略不配置步骤2)
      logic-delete-value: 1 # 逻辑已删除值(默认为 1)
      logic-not-delete-value: 0 # 逻辑未删除值(默认为 0)
      select-strategy: not_empty