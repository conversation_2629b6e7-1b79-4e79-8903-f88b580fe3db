import java.time.LocalDate
import java.time.format.DateTimeFormatter

plugins {
    alias(commonLibs.plugins.springboot)
    alias(commonLibs.plugins.google.ksp)
    alias(commonLibs.plugins.kapt)
    alias(commonLibs.plugins.kotlin.jvm)
    alias(commonLibs.plugins.kotlin.spring)
    alias(commonLibs.plugins.publish.conf)
    alias(commonLibs.plugins.common.conf)
    alias(commonLibs.plugins.api.version.generator)
}

dependencies {
    implementation(projects.popProductCommon)
    implementation(commonLibs.blade.web.cloud.spring.boot.starter) {
        exclude(group = "me.ahoo.cosid", module = "cosid-dependencies")
        exclude(group = "com.sun.xml.bind", module = "jaxb-impl")
        exclude(group = "com.sun.xml.bind", module = "jaxb-core")
    }
    implementation(commonLibs.mysql.connector)
    implementation(commonLibs.blade.data.mybatis.plus.spring.boot.starter)
    implementation(commonLibs.blade.data.redis.spring.boot.starter)
    implementation(commonLibs.blade.auth.spring.boot.starter)
    implementation(commonLibs.blade.feign.spring.boot.starter)
    implementation(commonLibs.blade.file.spring.boot.starter)

    // Jimmer
    ksp(commonLibs.jimmer.ksp)
    implementation(commonLibs.blade.jimmer.spring.boot.starter) {
        exclude(group = "me.ahoo.cosid", module = "cosid-dependencies")
        exclude(group = "com.sun.xml.bind", module = "jaxb-impl")
        exclude(group = "com.sun.xml.bind", module = "jaxb-core")
    }

    implementation(springBootLibs.spring.springBootStarterAmqp)
    testImplementation(commonLibs.blade.test.spring.boot.starter)
    //lombok
    compileOnly(libs.lombok)
    annotationProcessor(libs.lombok)
    kapt(libs.lombok)
    testCompileOnly(libs.lombok)
    testAnnotationProcessor(libs.lombok)
    //其他工具包
    implementation(libs.google.guava)
    implementation(libs.easyexcel)
    implementation(libs.easypoi.base) {
        exclude(group = "org.apache.poi", module = "poi-ooxml-schemas")
    }
    implementation(libs.pagehelper)
    implementation(libs.commons.lang3)
    implementation(commonLibs.hutool.core)
    implementation(libs.alimt) {
        exclude(group = "pull-parser", module = "pull-parser")
    }
    implementation(libs.commons.io)
    implementation(libs.commons.compress)
    implementation(libs.apache.poi)
    implementation(libs.xstream)
    implementation(libs.thumbnailator)

    // 内部包
    implementation(libs.ofp.sdk)
    implementation(libs.sdp.curation.sdk)
    implementation(libs.sdp.material.sdk)
    implementation(libs.sdp.sample.clothes.sdk)
    implementation(libs.sdp.design.sdk)
    implementation(libs.dict.sdk)
    implementation(libs.pigeon.sdk)
    implementation(libs.aigc.digital.print.sdk)
    implementation(libs.aigc.server.client)
    implementation(libs.aigc.server.common)
    implementation(libs.butted.sdk.client)

    // 外部三方包
    implementation(libs.eis.center.sdk)
    implementation(libs.lazada.sdk)
    implementation(libs.aliexpress.sdk)
}

// 标定使用JUnit平台
tasks.test {
    useJUnitPlatform()
}

kapt {
    keepJavacAnnotationProcessors = true
}

tasks.register("updateVersionProperties") {
    doLast {
        val versionPropertiesFile = file("$projectDir/src/main/resources/versions.properties")
        val currentDate = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"))
        versionPropertiesFile.writeText("spring.cloud.nacos.discovery.metadata.version=$currentDate")
    }
}

tasks.named("compileKotlin") {
    dependsOn("updateVersionProperties")
}
