package tech.tiangong.pop.sdk.client

import org.springframework.cloud.openfeign.FeignClient
import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import team.aikero.blade.core.annotation.feign.InnerFeign
import team.aikero.blade.core.constant.UrlVersionConstant
import team.aikero.blade.core.protocol.DataResponse
import tech.tiangong.pop.common.constant.Constant
import tech.tiangong.pop.common.req.BarcodeListReq
import tech.tiangong.pop.common.req.BarcodeQuery
import tech.tiangong.pop.common.req.BatchGenerateBarCodeReq
import tech.tiangong.pop.common.resp.BarCodeListResp
import tech.tiangong.pop.common.resp.BarCodeResp

@FeignClient(
    value = Constant.APPLICATION_NAME,
    path = "${Constant.CONTEXT_PATH}${UrlVersionConstant.INNER}/v1/barcode",
    contextId = "barcodeClient"
)
@InnerFeign
interface BarcodeClient {
    /**
     * 条码查询
     *
     * @param req 入参
     * @return List<BarCodeListResp>
    </BarCodeListResp> */
    @PostMapping("/list")
    fun barcodeList(@RequestBody @Validated req: BarcodeQuery): DataResponse<List<BarCodeListResp>?>

    /**
     * 条码查询
     *
     * @param req 入参
     * @return List<BarCodeListResp>
    </BarCodeListResp> */
    @PostMapping("/list-by-code")
    fun barcodeListBy(@RequestBody @Validated req: BarcodeListReq): DataResponse<List<BarCodeListResp>?>

    /**
     * 批量生成条码
     *
     * @param req 入参
     * @return List<BarCodeListResp>
    </BarCodeListResp> */
    @PostMapping("/batch-generate")
    fun batchGenerateBarCode(@RequestBody @Validated req: BatchGenerateBarCodeReq): DataResponse<List<BarCodeResp>?>
}
