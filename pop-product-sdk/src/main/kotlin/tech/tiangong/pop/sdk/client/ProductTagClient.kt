package tech.tiangong.pop.sdk.client

import org.springframework.cloud.openfeign.FeignClient
import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import team.aikero.blade.core.annotation.feign.InnerFeign
import team.aikero.blade.core.constant.UrlVersionConstant
import team.aikero.blade.core.protocol.DataResponse
import tech.tiangong.pop.common.constant.Constant
import tech.tiangong.pop.common.req.ProductTagAddRegClearReq

@FeignClient(
    value = Constant.APPLICATION_NAME,
    path = "${Constant.CONTEXT_PATH}${UrlVersionConstant.INNER}/v1/product/tag",
    contextId = "ProductTagClient"
)
@InnerFeign
interface ProductTagClient {
    /**
     * 增加清仓标签
     * @param req
     * @return
     */
    @PostMapping("/add/reg/clear")
    fun addRegClear(@Validated @RequestBody req: ProductTagAddRegClearReq): DataResponse<Unit>
}
