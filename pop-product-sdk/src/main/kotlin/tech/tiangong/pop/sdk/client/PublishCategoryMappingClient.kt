package tech.tiangong.pop.sdk.client

import org.springframework.cloud.openfeign.FeignClient
import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestParam
import team.aikero.blade.core.annotation.InnerApi
import team.aikero.blade.core.constant.UrlVersionConstant
import team.aikero.blade.core.protocol.DataResponse
import tech.tiangong.pop.common.constant.Constant
import tech.tiangong.pop.common.req.PlatformCategoryQueryReq
import tech.tiangong.pop.common.resp.AlibabaCategoryResp
import tech.tiangong.pop.common.resp.CategoryAttributeResp

@FeignClient(
    value = Constant.APPLICATION_NAME,
    path = "${Constant.CONTEXT_PATH}${UrlVersionConstant.INNER}/v1/publish-category-mapping",
    contextId = "publishCategoryMappingClient",
    url = "\${domain.ola-api}"
)
@InnerApi
interface PublishCategoryMappingClient {

    /**
     * 根据1688类目ID查询第三方平台品类
     *
     * @param req
     */
    @PostMapping("/query-alibaba-category-by-platform-category-id")
    fun queryAlibabaCategoryByPlatformCategoryId(@RequestBody @Validated req: PlatformCategoryQueryReq): DataResponse<List<AlibabaCategoryResp>>

    /**
     * 根据品类名称（多级-分割）查询品类属性
     */
    @PostMapping("/list-attribute-by-category-name")
    fun listAttributeByCategoryName(@RequestParam("categoryName") categoryName:String):DataResponse<List<CategoryAttributeResp>>
}