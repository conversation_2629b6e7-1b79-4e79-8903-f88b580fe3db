package tech.tiangong.pop.sdk.client

import org.springframework.cloud.openfeign.FeignClient
import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import team.aikero.blade.core.annotation.InnerApi
import team.aikero.blade.core.constant.UrlVersionConstant
import team.aikero.blade.core.protocol.DataResponse
import team.aikero.blade.core.protocol.PageVo
import tech.tiangong.pop.common.constant.Constant
import tech.tiangong.pop.common.dto.ProductImageChangeStateDto
import tech.tiangong.pop.common.req.*
import tech.tiangong.pop.common.resp.*

@FeignClient(
    value = Constant.APPLICATION_NAME,
    path = "${Constant.CONTEXT_PATH}${UrlVersionConstant.INNER}/v1/product",
    contextId = "productClient"
)
@InnerApi
interface ProductClient {

    /**
     * 分页查询商品
     *
     * @param req 入参
     * @return
     */
    @PostMapping("/page")
    fun page(@RequestBody @Validated req: SaleGoodsPageQuery): DataResponse<PageVo<SaleGoddsPageResp>?>

    /**
     * 批量查询产品图片信息
     *
     * @param req SPU批量查询请求
     * @return 产品图片信息
     */
    @PostMapping("/images/batch")
    fun batchQueryImages(@Validated @RequestBody req: ProductImageBatchQueryReq): DataResponse<ProductImageChangeStateDto?>

    /**
     * skc 是否存在
     *
     * @param req
     * @return
     */
    @PostMapping("/skc-exsit")
    fun skcIsExsit(@Validated @RequestBody req: List<SpuSkcSearchReq>): DataResponse<List<SkcRecordResp>?>

    /**
     * 查询上下架的商品SKC信息
     *
     * @param req
     */
    @PostMapping("/skc/info/get")
    fun getProductSkcInfo(@Validated @RequestBody req: GetProductSkcInfoReq): DataResponse<PageVo<GetProductSkcInfoResp>>

    /**
     * 根据条件获取SKU数据
     *
     * @param req
     */
    @PostMapping("/sku/info/page")
    fun pageProductSkuInfo(@Validated @RequestBody req: PageProductSkuReq): DataResponse<PageVo<PageProductSkuResp>>

    /**
     * ETA更新通知(发货期/商品状态: 现货/期货/海外仓)
     *
     * @param req
     */
    @PostMapping("/eta/update/notice")
    fun etaUpdateNotice(@Validated @RequestBody req: List<ETAUpdateReq>): DataResponse<Unit>

    /**
     * 商品查询ETA信息
     *
     * @param req
     * @return ETAGetResp
     */
    @PostMapping("/eta/get")
    fun getETA(@Validated @RequestBody req: List<ETAGetReq>): DataResponse<List<ETAGetResp>>

    /**
     * 查询条码信息(快照)
     *
     * @param req
     * @return
     */
    @PostMapping("/barcode/info/get")
    fun getBarcodeInfo(@Validated @RequestBody req: BarcodeInfoReq): DataResponse<List<BarcodeInfoResp>>

    /**
     * SKC查询上过架的尺码
     *
     * @param req
     * @return
     */
    @PostMapping("/skc/by/size/get")
    fun getSizeBySkc(@Validated @RequestBody req: SizeBySkcReq): DataResponse<List<SizeBySkcResp>>

    /**
     * 上传产品生产资料
     *
     * @param req 包含SPU编码和生产资料图片
     */
    @PostMapping("/material/update")
    fun updateProductMaterial(@Validated @RequestBody req: ProductMaterialReq): DataResponse<Unit>

    /**
     * 批量更新产品生产资料
     *
     * @param req 包含多个SPU编码和生产资料图片的列表
     */
    @PostMapping("/material/batch-update")
    fun batchUpdateProductMaterial(@Validated @RequestBody req: ProductMaterialBatchReq): DataResponse<Unit>
}