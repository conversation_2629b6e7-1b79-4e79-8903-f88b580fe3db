package tech.tiangong.pop.sdk.client

import org.springframework.cloud.openfeign.FeignClient
import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import team.aikero.blade.core.annotation.feign.InnerFeign
import team.aikero.blade.core.constant.UrlVersionConstant
import team.aikero.blade.core.protocol.DataResponse
import tech.tiangong.pop.common.constant.Constant
import tech.tiangong.pop.common.req.PlanningSupplyQuantityReq
import tech.tiangong.pop.common.resp.PlanningSupplyQuantityResp

/**
 * <AUTHOR>
 * @date 2024/12/6 18:05
 */
@FeignClient(
    value = Constant.APPLICATION_NAME,
    path = "${Constant.CONTEXT_PATH}${UrlVersionConstant.INNER}/v1/planning",
    contextId = "planningClient"
)
@InnerFeign
interface PlanningClient {
    /**
     * 查询-企划品类供给数统计
     * @param req 请求对象
     * @return
     */
    @PostMapping("/supply/quantity")
    fun supplyQuantityCategory(@Validated @RequestBody req: PlanningSupplyQuantityReq): DataResponse<PlanningSupplyQuantityResp?>
}
