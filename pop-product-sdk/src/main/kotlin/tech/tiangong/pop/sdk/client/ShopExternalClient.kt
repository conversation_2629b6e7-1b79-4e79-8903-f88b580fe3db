package tech.tiangong.pop.sdk.client

import org.springframework.cloud.openfeign.FeignClient
import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import team.aikero.blade.core.annotation.InnerApi
import team.aikero.blade.core.constant.UrlVersionConstant
import team.aikero.blade.core.protocol.DataResponse
import tech.tiangong.pop.common.constant.Constant
import tech.tiangong.pop.common.req.ShopBrandReq
import tech.tiangong.pop.common.req.ShopReq
import tech.tiangong.pop.common.resp.ShopBrandResp
import tech.tiangong.pop.common.resp.ShopResp

@FeignClient(
    value = Constant.APPLICATION_NAME,
    path = "${Constant.CONTEXT_PATH}${UrlVersionConstant.INNER}/v1/shop",
    contextId = "shopExternalClient",
    url = "\${domain.ola-api}"
)
@InnerApi
interface ShopExternalClient {

    /**
     * 获取店铺列表
     *
     * @param req 查询条件
     * @return 店铺列表
     */
    @PostMapping("/list")
    fun getShopList(@RequestBody @Validated req: ShopReq): DataResponse<List<ShopResp>?>

    /**
     * 获取品牌列表
     *
     * @param req 查询条件
     * @return 品牌列表
     */
    @PostMapping("/brands")
    fun getBrandList(@RequestBody @Validated req: ShopBrandReq): DataResponse<List<ShopBrandResp>?>
}