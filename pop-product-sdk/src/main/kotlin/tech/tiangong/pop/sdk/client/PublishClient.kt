package tech.tiangong.pop.sdk.client

import org.springframework.cloud.openfeign.FeignClient
import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import team.aikero.blade.core.annotation.InnerApi
import team.aikero.blade.core.constant.UrlVersionConstant
import team.aikero.blade.core.protocol.DataResponse
import tech.tiangong.pop.common.constant.Constant
import tech.tiangong.pop.common.req.PublishChannelReq
import tech.tiangong.pop.common.req.PublishPlatformReq
import tech.tiangong.pop.common.resp.PublishChannelResp
import tech.tiangong.pop.common.resp.PublishPlatformResp

@FeignClient(
    value = Constant.APPLICATION_NAME,
    path = "${Constant.CONTEXT_PATH}${UrlVersionConstant.INNER}/v1/publish",
    contextId = "publishClient"
)
@InnerApi
interface PublishClient {
    /**
     * 获取渠道下拉列表
     *
     * @param req 查询条件
     * @return 渠道列表
     */
    @PostMapping("/channel/list")
    fun getChannelList(@RequestBody @Validated req: PublishChannelReq): DataResponse<List<PublishChannelResp>?>

    /**
     * 获取发布平台信息列表
     *
     * @param req 请求参数对象，包含平台名和关联渠道ID等信息
     * @return 包含发布平台信息列表的响应实体
     */
    @PostMapping("/platform/list")
    fun getPlatformList(req: PublishPlatformReq): DataResponse<List<PublishPlatformResp>?>
}