package tech.tiangong.pop.sdk.client

import org.springframework.cloud.openfeign.FeignClient
import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import team.aikero.blade.core.annotation.InnerApi
import team.aikero.blade.core.constant.UrlVersionConstant
import team.aikero.blade.core.protocol.DataResponse
import tech.tiangong.pop.common.constant.Constant
import tech.tiangong.pop.common.req.ExchangeRateQueryReq
import tech.tiangong.pop.common.resp.ExchangeRateResp

@FeignClient(
    value = Constant.APPLICATION_NAME,
    path = "${Constant.CONTEXT_PATH}${UrlVersionConstant.INNER}/v1/exchange-rates",
    contextId = "currencyExchangeRateExternalClient",
    url = "\${domain.ola-api}"
)
@InnerApi
interface CurrencyExchangeRateExternalClient {
    /**
     * 查询汇率列表
     */
    @PostMapping("/query")
    fun queryExchangeRates(@RequestBody @Validated req: ExchangeRateQueryReq): DataResponse<List<ExchangeRateResp>>
}