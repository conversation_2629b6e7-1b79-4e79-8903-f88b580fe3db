package tech.tiangong.pop.sdk.client

import org.springframework.cloud.openfeign.FeignClient
import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import team.aikero.blade.core.annotation.InnerApi
import team.aikero.blade.core.constant.UrlVersionConstant
import team.aikero.blade.core.protocol.DataResponse
import tech.tiangong.pop.common.constant.Constant
import tech.tiangong.pop.common.dto.CreateProductDto

@FeignClient(
    value = Constant.APPLICATION_NAME,
    path = "${Constant.CONTEXT_PATH}${UrlVersionConstant.INNER}/v1/product",
    contextId = "productCreateClient"
)
@InnerApi
interface ProductCreateClient {

    /**
     * 创建商品
     *
     * @param req 入参
     * @return Void
     */
    @PostMapping("/create-product")
    fun createProduct(@Validated @RequestBody req: CreateProductDto): DataResponse<Unit>

    /**
     * 批量创建商品
     *
     * @param req 入参
     * @return Void
     */
    @PostMapping("/batch-create-product")
    fun batchCreateProduct(@Validated @RequestBody req: MutableList<CreateProductDto>): DataResponse<Unit>
}