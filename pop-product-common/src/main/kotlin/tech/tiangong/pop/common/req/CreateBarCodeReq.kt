package tech.tiangong.pop.common.req


/**
 * 条码创建请求
 */
data class CreateBarCodeReq(
    /** SPU编码 */
    var spuCode: String? = null,

    /** 尺码组名称 */
    var groupName: String? = null,

    /** 供给方式 */
    var supplyMode: String? = null,

    /** 商家SKU */
    var sellerSku: String? = null,

    /** 尺码组编号 */
    var sourceGroupCode: String? = null,

    /** 颜色 */
    var color: String? = null,

    /** 设计图url链接 */
    var designImgUrl: String? = null,

    /** 主图url链接 */
    var mainImgUrl: String? = null,

    /** 品类编码（多级-分割） */
    var categoryCode: String? = null,

    /** 品类名称（多级-分割） */
    var categoryName: String? = null,

    /** SKC编码 */
    var skcCode: String? = null,

    /** 尺码值（如 X M L） */
    var sizeValues: List<String>? = null
) {
    /**
     * 校验必要字段，不合法时一次性输出所有错误信息
     */
    fun validate() {
        val errors = mutableListOf<String>()
        if (spuCode.isNullOrBlank()) errors.add("spuCode（SPU编码）不能为空")
        if (skcCode.isNullOrBlank()) errors.add("skcCode（SKC编码）不能为空")
        if (groupName.isNullOrBlank()) errors.add("groupName（尺码组名称）不能为空")
        if (sourceGroupCode.isNullOrBlank()) errors.add("sourceGroupCode（尺码组编号）不能为空")
        if (sizeValues.isNullOrEmpty()) errors.add("sizeValues（尺码值）不能为空")
        // 这些是业务流程必用字段，缺失必定导致NPE或异常
        if (errors.isNotEmpty()) throw IllegalArgumentException(errors.joinToString("；"))
    }
}
