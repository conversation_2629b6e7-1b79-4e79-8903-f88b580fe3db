package tech.tiangong.pop.common.enums

import java.util.*

/**
 * <AUTHOR>
 * @create 2022/3/30 15:46
 */
enum class YesOrNoEnum(val code: Int, val desc: String) {
    NO(0, "否"),
    YES(1, "是"),
    ;


    companion object {
        /**
         * 根据code获取枚举
         *
         * @param desc
         * @return
         */
        @JvmStatic
        fun getByCode(code: Int): YesOrNoEnum? {
            return entries.firstOrNull { e: YesOrNoEnum -> Objects.equals(e.code, code) }
        }

        /**
         * 根据desc获取枚举
         *
         * @param desc
         * @return
         */
        @JvmStatic
        fun getByDesc(desc: String): YesOrNoEnum? {
            return entries.firstOrNull { e: YesOrNoEnum -> Objects.equals(e.desc, desc) }
        }
    }
}
