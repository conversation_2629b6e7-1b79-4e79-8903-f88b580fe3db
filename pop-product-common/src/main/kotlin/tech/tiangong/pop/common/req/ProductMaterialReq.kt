package tech.tiangong.pop.common.req

import jakarta.validation.Valid
import jakarta.validation.constraints.NotEmpty
import jakarta.validation.constraints.Size
import tech.tiangong.pop.common.dto.Image

/**
 * 生产资料请求DTO
 */
data class ProductMaterialReq(
    /**
     * SPU编码
     */
    @field:NotEmpty(message = "SPU编码不能为空")
    val spuCode: String? = null,

    /**
     * 生产资料列表（包含图片、CAD文件等）
     */
    @field:Valid
    val materialImageList: List<Image>? = null
)

/**
 * 批量生产资料请求DTO
 */
data class ProductMaterialBatchReq(
    /**
     * 生产资料请求列表
     */
    @field:Valid
    @field:Size(min = 1, max = 100, message = "生产资料请求列表不能为空，不能超过100个")
    val productMaterials: List<ProductMaterialReq>
)