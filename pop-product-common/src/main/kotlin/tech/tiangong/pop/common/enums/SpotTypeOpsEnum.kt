package tech.tiangong.pop.common.enums

/**
 * 字典枚举-现货类型
 * @date 2024-12-9 23:50:59
 */
enum class SpotTypeOpsEnum(val dictCode: String, val desc: String) {
    A1("A1", "反季现货"),
    A2("A2", "ifashion"),
    A3("A3", "1688"),
    A4("A4", "搜款网"),
    A5("A5", "JIT"),
    A6("A6", "非JIT");

    companion object {
        @JvmStatic
        fun getByCode(dictCode: String?): SpotTypeOpsEnum? = SpotTypeOpsEnum.entries.firstOrNull { it.dictCode == dictCode }

        @JvmStatic
        fun getByDesc(desc: String?): SpotTypeOpsEnum? = SpotTypeOpsEnum.entries.firstOrNull { it.desc == desc }

    }

}