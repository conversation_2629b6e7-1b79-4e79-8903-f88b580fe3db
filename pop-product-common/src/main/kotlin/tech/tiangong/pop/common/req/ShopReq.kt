package tech.tiangong.pop.common.req

/**
 * 店铺信息
 */
class ShopReq {
    /**
     * 【模糊查询】店铺名
     */
    var shopName: String? = null

    /**
     * 批量查询，根据店铺名称
     */
    var shopNames: List<String>? = null

    /**
     * 是否授权【1已授权；0未授权】
     */
    private var isAuth: Int? = null

    /**
     * 平台ID
     */
    var platformId: Long? = null


    fun getIsAuth(): Int? {
        return isAuth
    }

    fun setIsAuth(isAuth: Int?) {
        this.isAuth = isAuth
    }

}