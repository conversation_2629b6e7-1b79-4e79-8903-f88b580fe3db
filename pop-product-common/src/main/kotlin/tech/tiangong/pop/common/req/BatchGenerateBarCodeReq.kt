package tech.tiangong.pop.common.req


import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.NotEmpty

/**
 * 批量生成条码请求 DTO
 */
data class BatchGenerateBarCodeReq(
    /** 条码生成的商品列表 */
    @field:NotEmpty(message = "商品列表不能为空")
    var datas: List<SpuInfo>? = null,

    /** 是否强制创建 */
    var forceCreate: Boolean? = false
) {
    /**
     * 校验所有必填项，不合法时一次性输出所有错误信息
     */
    fun validate() {
        val errors = mutableListOf<String>()
        if (datas.isNullOrEmpty()) errors.add("商品列表(datas)不能为空")
        else {
            datas!!.forEachIndexed { idx, spu ->
                try {
                    spu.validate(idx)
                } catch (e: IllegalArgumentException) {
                    errors.add(e.message ?: "第${idx + 1}个Spu校验失败")
                }
            }
        }
        if (errors.isNotEmpty()) {
            throw IllegalArgumentException(errors.joinToString("\n"))
        }
    }

    /**
     * 商品 SPU 明细 DTO
     */
    data class SpuInfo(
        @field:NotBlank(message = "SPU编码不能为空")
        var spuCode: String? = null,

        /** 尺码组名称 */
        @field:NotBlank(message = "尺码组名称不能为空")
        var groupName: String? = null,

        var sellerSku: String? = null,

        /** 颜色 */
        @field:NotBlank(message = "颜色不能为空")
        var color: String? = null,

        /** 设计师 */
        var designName: String? = null,

        /** 设计组 */
        var designGroup: String? = null,

        /** 供给方式编码 */
        var supplyMode: String? = null,

        /** 尺码组编号 */
        @field:NotBlank(message = "尺码组编号不能为空")
        var sourceGroupCode: String? = null,

        /** 设计图url链接 */
        var designImgUrl: String? = null,

        /** 主图url链接 */
        var mainImgUrl: String? = null,

        /** 品类编码（多级-分割） */
        @field:NotBlank(message = "品类编码不能为空")
        var categoryCode: String? = null,

        /** 品类名称（多级-分割） */
        @field:NotBlank(message = "品类名称不能为空")
        var categoryName: String? = null,

        /** skc编码 */
        @field:NotBlank(message = "skc编码不能为空")
        var skcCode: String? = null,

        /** 尺码值（如 X M L） */
        @field:NotEmpty(message = "尺码值不能为空")
        var sizeValues: List<String>? = null
    ) {
        /**
         * 校验必要字段，不合法时抛出异常（收集全部错误）
         */
        fun validate(idx: Int? = null) {
            val errors = mutableListOf<String>()
            if (spuCode.isNullOrBlank()) errors.add("spuCode（SPU编码）不能为空")
            if (skcCode.isNullOrBlank()) errors.add("skcCode（SKC编码）不能为空")
            if (groupName.isNullOrBlank()) errors.add("groupName（尺码组名称）不能为空")
            if (sourceGroupCode.isNullOrBlank()) errors.add("sourceGroupCode（尺码组编号）不能为空")
            if (sizeValues.isNullOrEmpty()) errors.add("sizeValues（尺码值）不能为空")
            if (errors.isNotEmpty()) {
                val prefix = if (idx != null) "第${idx + 1}项：" else ""
                throw IllegalArgumentException(prefix + errors.joinToString("；"))
            }
        }
    }
}
