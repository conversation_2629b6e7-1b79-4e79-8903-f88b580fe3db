package tech.tiangong.pop.common.dto

import java.math.BigDecimal

/**
 * 更新SKC 品类 颜色 DTO
 * <AUTHOR>
 * @since 2024/11/19 11:19
 */

class ProductUpdateDto {
    /**
     * 参数数据
     */
    var dataList: List<Data>? = null

    class Data {
        /**
         * spu编码
         */
        var spuCode: String? = null

        /**
         * skc编码
         */
        var skc: String? = null

        /**
         * 操作类型
         * @see tech.tiangong.pop.common.enums.ProductUpdateTypeEnum
         */
        var opType: Int? = null

        /**
         * 颜色
         */
        var color: String? = null

        /**
         * 颜色编码（如White）
         */
        var colorCode: String? = null

        /**
         * 颜色缩写编码（如 "WH" ）
         */
        var colorAbbrCode: String? = null

        /**
         * 品类编码（多级-分割）
         */
        var categoryCode: String? = null

        /**
         * 品类名称（多级-分割）
         */
        var categoryName: String? = null

        /**
         * 核价类型：0:预估核价|1：款式核价
         */
        var checkPriceType: Int? = null

        /**
         * 核价师
         */
        var pricerId: Long? = null

        /**
         * 核价师
         */
        var pricerName: String? = null

        /**
         * 成本供货价，核价
         */
        var price: BigDecimal? = null

        /**
         * 采购价
         */
        var purchasePrice: BigDecimal? = null

        /**
         * 定价类型【1按返单规则；2不返单规则 】
         */
        var pricingType: Int? = null

        /**
         * 尺码信息
         */
        var sizeNameList: List<String>? = null

        /**
         * 特殊尺码信息
         */
        var specialSizeNameList: List<String>? = null
        /**
         * 款式风格名称
         */
        var clothingStyleName: String? = null

        /**
         * 款式风格编码
         */
        var clothingStyleCode: String? = null
        /**
         * 企划类型(1:企划内/2:企划外)
         */
        var planningType: Int? = null

        /**
         * 市场编码
         */
        var marketCode: String? = null

        /**
         * 市场系列编码
         */
        var marketSeriesCode: String? = null


        /**
         * 商品属性
         */
        var attributeList: List<ProductAttributesV2Dto>? = null
    }
}
