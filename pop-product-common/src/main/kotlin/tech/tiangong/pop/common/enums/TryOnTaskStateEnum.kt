package tech.tiangong.pop.common.enums

import java.util.*

/**
 * TryOn任务状态1创建中2进行中3已完成4失败5已取消
 */
enum class TryOnTaskStateEnum(
    /**
     * 状态编码
     */
    val code: Int,
    /**
     * 描述
     */
    val desc: String,
) {
    UNKNOWN(-999, "未知状态"),
    /**
     * 创建中
     */
    CREATING(1, "创建中"),

    /**
     * 进行中
     */
    PROCESSING(2, "进行中"),
    /**
     * 已完成
     */
    FINISH(3, "已完成"),
    /**
     * 已失败
     */
    FAIL(4, "已失败"),

    /**
     * 失败
     */
    CANCEL(5, "已取消"),
    ;

    companion object {

        /**
         * 根据code获取枚举
         */
        fun getByCode(code: Int?): TryOnTaskStateEnum? {
            if (code == null) {
                return UNKNOWN
            }
            return entries.find { Objects.equals(it.code, code) }?:UNKNOWN
        }

        /**
         * 根据code获取枚举
         */
        fun getByCode(code: String?): TryOnTaskStateEnum? {
            if (code.isNullOrBlank()) {
                return UNKNOWN
            }
            return entries.find { Objects.equals(it.code.toString(), code) }?:UNKNOWN
        }
    }
}
