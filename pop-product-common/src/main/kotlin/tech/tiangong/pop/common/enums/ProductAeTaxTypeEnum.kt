package tech.tiangong.pop.common.enums

/**
 * AE 速卖通税务报价类型枚举
 */
enum class ProductAeTaxTypeEnum(val value: Int, val description: String) {
    /**
     * 不含税报价
     */
    EXCLUDING_TAX(1, "不含关税报价"),

    /**
     * 含税报价
     */
    INCLUDING_TAX(2, "含关税报价"),

    /**
     * 未知类型
     */
    UNKNOWN(0, "未知类型");

    companion object {
        /**
         * 根据数值获取对应的枚举
         *
         * @param value 税务报价类型值
         * @return 对应的枚举，如果没有匹配则返回UNKNOWN
         */
        fun getByValue(value: Int): ProductAeTaxTypeEnum {
            return entries.find { it.value == value } ?: UNKNOWN
        }

        /**
         * 根据描述获取对应的枚举
         *
         * @param description 税务报价类型描述
         * @return 对应的枚举，如果没有匹配则返回UNKNOWN
         */
        fun getByDescription(description: String): ProductAeTaxTypeEnum {
            return entries.find { it.description == description } ?: UNKNOWN
        }

        /**
         * 判断给定值是否为有效的税务报价类型
         *
         * @param value 要检查的值
         * @return 是否有效
         */
        fun isValid(value: Int): Boolean {
            return entries.any { it.value == value && it != UNKNOWN }
        }
    }
}