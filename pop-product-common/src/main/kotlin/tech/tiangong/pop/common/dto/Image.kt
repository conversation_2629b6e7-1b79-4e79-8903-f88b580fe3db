package tech.tiangong.pop.common.dto

import jakarta.validation.constraints.NotBlank
import java.io.Serializable

data class Image(
    /**
     * 文件名(xxx.jpg)
     * 暂不校验文件名是否为空，为空的需要POP这边手动拼接
     */
    var orgImgName: String? = null,

    /**
     * 图片URL
     */
    @field:NotBlank(message = "图片URL不能为空")
    var ossImageUrl: String? = null,
) : Serializable {
    companion object {
        private const val serialVersionUID = 1L

        @JvmStatic
        fun of(orgImgName: String?, ossImageUrl: String): Image {
            return Image(orgImgName = orgImgName, ossImageUrl = ossImageUrl)
        }
    }
}
