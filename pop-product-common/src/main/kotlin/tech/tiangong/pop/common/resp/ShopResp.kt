package tech.tiangong.pop.common.resp

import java.time.LocalDateTime

/**
 * 店铺信息
 */
class ShopResp {
    /**
     * 主键ID
     */
    var shopId: Long? = null

    /**
     * 平台店铺唯一标识
     */
    var platformSellerId: String? = null

    /**
     * 平台ID
     */
    var platformId: Long? = null

    /**
     * 平台名称
     */
    var platformName: String? = null

    /**
     * 渠道ID
     */
    var channelId: Long? = null

    /**
     * 渠道名称
     */
    var channelName: String? = null

    /**
     * 店铺名
     */
    var shopName: String? = null

    /**
     * lazada short code
     */
    var shortCode: String? = null

    /**
     * 品牌ID
     */
    var brandId: String? = null

    /**
     * 品牌名
     */
    var brandName: String? = null

    /**
     * 授权链接
     */
    var authUrl: String? = null

    /**
     * 备注
     */
    var remark: String? = null

    /**
     * token
     */
    var token: String? = null

    /**
     * 是否授权【1已授权；0未授权】
     */
    private var isAuth: Int? = null

    /**
     * 站点
     */
    var country: String? = null

    /**
     * lzd店铺类型【cb-跨境店;asc-单站点】
     */
    var countryType: String? = null

    /**
     * 运营模式
     */
    var businessType: Int? = null

    /**
     * 是否外部商品(0-否,1-是)
     */
    var external: Int? = null

    /**
     * 店铺所属主体字典值编码
     */
    var entityCode: String? = null


    /**
     * 店铺绑定的TEMU站点
     * @see tech.tiangong.eis.temu.enums.TemuSiteEnum
     */
    var temuSite: String? = null

    /**
     * 店铺卖家映射列表
     */
    var shopSellerMappingList: List<ShopSellerMappingResp>? = null

    /**
     * 店铺别名
     */
    var shopAlias: String? = null

    /**
     * 操作人名称
     */
    var operatorName: String? = null

    /**
     * 操作时间
     */
    var operatedTime: LocalDateTime? = null

    /**
     * 创建人名称
     */
    var creatorName: String? = null

    /**
     * 创建时间
     */
    var createdTime: LocalDateTime? = null


    fun getIsAuth(): Int? {
        return isAuth
    }

    fun setIsAuth(isAuth: Int?) {
        this.isAuth = isAuth
    }
}