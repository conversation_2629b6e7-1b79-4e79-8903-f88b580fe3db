package tech.tiangong.pop.common.enums

/**
 * 字典枚举-店铺实体
 */
enum class ShopEntityOpsEnum(val dictCode: String, val desc: String) {
    HKSD("pop_HKSD", "香港杉缔智衣科技有限公司"),
    GZSD("pop_GZSD", "广州杉缔智衣科技有限公司"),
    ;

    companion object {
        @JvmStatic
        fun getByCode(dictCode: String?): ShopEntityOpsEnum? = ShopEntityOpsEnum.entries.firstOrNull { it.dictCode == dictCode }

        @JvmStatic
        fun getByDesc(desc: String?): ShopEntityOpsEnum? = ShopEntityOpsEnum.entries.firstOrNull { it.desc == desc }

    }

}