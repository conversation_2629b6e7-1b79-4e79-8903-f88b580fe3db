package tech.tiangong.pop.common.resp


import java.math.BigDecimal
import java.time.LocalDateTime

/**
 * 商品分页列表
 *
 * <AUTHOR>
 * @since 2024-11-16 19:07:02
 */

class SaleGoddsPageResp {
    /**
     * 主键
     */
    var productId: Long? = null

    /**
     * 销售商品ID
     */
    var saleGoodsId: Long? = null

    /**
     * 尺码组名称
     */
    var sizeGroupName: String? = null

    /**
     * 平台商品ID
     */
    var platformProductId: String? = null

    /**
     * 供给方式
     */
    var supplyMode: String? = null

    /**
     * 尺码组编号
     */
    var sizeGroupCode: String? = null

    /**
     * 平台ID
     */
    var platformId: Long? = null

    /**
     * 渠道ID
     */
    var channelId: Long? = null

    /**
     * 站点
     */
    var country: String? = null

    /**
     * 发布天数（售龄）
     */
    var publishDay: Int? = null

    /**
     * 站点发布天数（站点售龄）
     */
    var ascPublishDay: Int? = null

    /**
     * 前台链接
     */
    var frontUrl: String? = null

    /**
     * 主图url链接
     */
    var mainImgUrl: String? = null

    /**
     * spu编码
     */
    var spuCode: String? = null

    /**
     * 商品名称（商品标题）
     */
    var productTitle: String? = null

    /**
     * 品类编码（多级-分割）
     */
    var categoryCode: String? = null

    /**
     * 品类名称（多级-分割）
     */
    var categoryName: String? = null

    /**
     * 销售价格（对应订单服务的到手价）
     */
    var salePrice: BigDecimal? = null

    /**
     * 划线价格（对应订单服务的销售价））
     */
    var retailPrice: BigDecimal? = null

    /**
     * 店铺ID
     */
    var shopId: Long? = null

    /**
     * 店铺Code
     */
    var shopCode: String? = null

    /**
     * 店铺名称(在售店铺)
     */
    var shopName: String? = null

    /**
     * 上架时间
     */
    var publishTime: LocalDateTime? = null

    /**
     * 首次上架时间
     */
    var firstPublishTime: LocalDateTime? = null


    /**
     * SKC
     */
    var skc: String? = null

    /**
     * 内部颜色
     */
    var color: String? = null

    /**
     * 平台颜色
     */
    var platformColor: String? = null

    /**
     * skc图片（多个用英文逗号分割）
     */
    var pictures: String? = null

    /**
     * 跨境价
     */
    var cbPrice: BigDecimal? = null

    /**
     * 本土价（供货价）
     */
    var localPrice: BigDecimal? = null

    /**
     * sku编码
     */
    var skuCode: String? = null

    /**
     * 库存数量
     */
    var stockQuantity: Long? = null

    /**
     * sellerSku
     */
    var sellerSku: String? = null

    /**
     * shopSku
     */
    var shopSku: String? = null

    /**
     * 尺码名
     */
    var sizeName: String? = null

    /**
     * 平台SKU ID
     */
    var platformSkuId: String? = null

    /**
     * 是否所有站点都下架的状态【1上架；2下架】
     */
    var publisheState: Int? = null

    /**
     * 站点SPU上下架状态【1上架；2下架】
     */
    var ascPublisheState: Int? = null

    /**
     * sku上架状态【1上架；2下架】
     */
    var skuPublishState: Int? = null

    /**
     * 是否组合商品(1是,0否)
     */
    var combination: Int = 0

    /**
     * 商品条码
     */
    var barcode: String? = null

    /**
     * 商品条码集合
     */
    var barcodeList: List<SaleGoodsPageBarcodeResp>? = null

    /**
     * 商品条码列表
     */
    class SaleGoodsPageBarcodeResp {
        /**
         * 条码编码
         */
        var barcode: String? = null

        /**
         * spu编码
         */
        var spuCode: String? = null

        /**
         * skc
         */
        var skc: String? = null

        /**
         * 颜色
         */
        var color: String? = null

        /**
         * 尺码名
         */
        var sizeName: String? = null

        /**
         * 是否组合商品(1是,0否)
         */
        var combo: Int? = null
    }
}

