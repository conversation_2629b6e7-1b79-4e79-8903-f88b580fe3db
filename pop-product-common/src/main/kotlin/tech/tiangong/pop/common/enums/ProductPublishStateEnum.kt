package tech.tiangong.pop.common.enums

import java.util.*

/**
 * 商品上下架状态
 */
enum class ProductPublishStateEnum(
    /**
     * 状态编码
     */
    val code: Int,
    /**
     * 描述
     */
    val desc: String,
) {
    /**
     * 待上架
     */
    WAIT(0, "待上架"),

    /**
     * 上架
     */
    ACTIVE(1, "上架"),

    /**
     * 下架
     */
    IN_ACTIVE(2, "下架"),

    /**
     * 删除
     */
    DELETED(3, "删除"),

    ;

    companion object {

        /**
         * 根据code获取枚举
         */
        fun getByCode(code: Int?): ProductPublishStateEnum? {
            if (code == null) {
                return null
            }
            return entries.find { Objects.equals(it.code, code) }
        }

        /**
         * 根据code获取枚举
         */
        fun getByCode(code: String?): ProductPublishStateEnum? {
            if (code.isNullOrBlank()) {
                return null
            }
            return entries.find { Objects.equals(it.code.toString(), code) }
        }

        /**
         * 在线状态: 上下架
         */
        fun getOnlineState(): List<ProductPublishStateEnum> {
            return listOf(ACTIVE, IN_ACTIVE)
        }

        /**
         * 判断是否上架
         */
        fun isActive(state: Int?): Boolean {
            return state != null && state == ACTIVE.code
        }
    }
}
