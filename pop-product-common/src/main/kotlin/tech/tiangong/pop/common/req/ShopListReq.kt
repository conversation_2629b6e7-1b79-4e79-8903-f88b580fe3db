package tech.tiangong.pop.common.req

import tech.tiangong.pop.common.enums.PlatformEnum

/**
 * 店铺信息
 */
class ShopListReq {
    /**
     * 平台ID
     */
    var platformId: Long? = null

    /**
     * 店铺的平台
     */
    val platform: PlatformEnum? = null

    /**
     * 是否授权【1已授权；0未授权】
     */
    private var isAuth: Int? = null


    fun getIsAuth(): Int? {
        return isAuth
    }

    fun setIsAuth(isAuth: Int?) {
        this.isAuth = isAuth
    }
}