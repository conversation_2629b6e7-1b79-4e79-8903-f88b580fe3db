package tech.tiangong.pop.common.enums

import java.util.*

/**
 * TryOn任务规则属性
 */
enum class TryOnTaskRuleEnum(
    /**
     * 状态编码
     */
    val code: String,
    /**
     * 描述
     */
    val desc: String,
) {
    UNKNOWN("UNKNOWN", "未知属性"),
    /**
     * 风格定义
     */
    moodboard_id("moodboard_id", "风格定义"),

    /**
     * 参考图组url
     */
    image_url("image_url", "参考图组url"),
    /**
     * 不同tryon模型
     */
    tryon_model_name("tryon_model_name", "不同tryon模型"),
    /**
     * 不同tryon模型跑图数
     */
    tryon_model_num("tryon_model_num", "不同tryon模型跑图数"),

    /**
     * 不同裂变模型
     */
    change_model_name("change_model_name", "不同裂变模型"),
    /**
     * 不同裂变模型跑图数
     */
    change_model_num("change_model_num", "不同裂变模型"),

    /**
     * 姿势范围
     */
    change_model_range("change_model_range","姿势范围"),
    ;

    companion object {

        /**
         * 根据code获取枚举
         */
        fun getByCode(code: Int?): TryOnTaskRuleEnum? {
            if (code == null) {
                return UNKNOWN
            }
            return entries.find { Objects.equals(it.code, code) }?:UNKNOWN
        }

        /**
         * 根据code获取枚举
         */
        fun getByCode(code: String?): TryOnTaskRuleEnum? {
            if (code.isNullOrBlank()) {
                return UNKNOWN
            }
            return entries.find { Objects.equals(it.code.toString(), code) }?:UNKNOWN
        }
    }
}
