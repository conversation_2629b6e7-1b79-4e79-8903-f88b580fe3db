package tech.tiangong.pop.common.enums

import java.math.BigDecimal
import java.math.RoundingMode

/**
 * 货币枚举
 * 数据来源：https://www.exchangerate-api.com
 */
enum class CurrencyEnum(
    /**
     * 货币代码 (ISO 4217)
     */
    val code: String,
    /**
     * 货币名称
     */
    val currencyName: String,
    /**
     * 货币符号
     */
    val symbol: String,
    /**
     * 小数位数
     */
    val decimalPlaces: Int,
    /**
     * 是否主要货币
     */
    val isMajor: Boolean = false
) {
    /**
     * 人民币
     */
    CNY("CNY", "人民币", "¥", 2, true),

    /**
     * 美元
     */
    USD("USD", "美元", "$", 2, true),

    /**
     * 欧元
     */
    EUR("EUR", "欧元", "€", 2, true),

    /**
     * 日元
     */
    JPY("JPY", "日元", "¥", 0, true),

    /**
     * 港币
     */
    HKD("HKD", "港币", "HK$", 2, true),

    /**
     * 英镑
     */
    GBP("GBP", "英镑", "£", 2, true),

    /**
     * 新加坡元
     */
    SGD("SGD", "新加坡元", "S$", 2),

    /**
     * 马来西亚林吉特
     */
    MYR("MYR", "马来西亚林吉特", "RM", 2),

    /**
     * 泰铢
     */
    THB("THB", "泰铢", "฿", 2),

    /**
     * 印尼盾
     */
    IDR("IDR", "印尼盾", "Rp", 2),

    /**
     * 菲律宾比索
     */
    PHP("PHP", "菲律宾比索", "₱", 2),

    /**
     * 越南盾
     */
    VND("VND", "越南盾", "₫", 0),

    /**
     * 澳大利亚元
     */
    AUD("AUD", "澳大利亚元", "A$", 2),

    /**
     * 韩元
     */
    KRW("KRW", "韩元", "₩", 0),

    /**
     * 印度卢比
     */
    INR("INR", "印度卢比", "₹", 2),

    /**
     * 俄罗斯卢布
     */
    RUB("RUB", "俄罗斯卢布", "₽", 2),

    /**
     * 阿联酋迪拉姆
     */
    AED("AED", "阿联酋迪拉姆", "د.إ", 2),

    /**
     * 沙特里亚尔
     */
    SAR("SAR", "沙特里亚尔", "﷼", 2),

    /**
     * 新台币
     */
    TWD("TWD", "新台币", "NT$", 2);

    companion object {
        /**
         * 根据货币代码获取枚举实例
         */
        @JvmStatic
        fun getByCode(code: String?): CurrencyEnum? =
            entries.firstOrNull { it.code == code }

        /**
         * 根据货币名称获取枚举实例
         */
        @JvmStatic
        fun getByName(name: String?): CurrencyEnum? =
            entries.firstOrNull { it.currencyName == name }

        /**
         * 检查货币代码是否有效
         */
        @JvmStatic
        fun isValidCode(code: String?): Boolean =
            entries.any { it.code == code }

        /**
         * 获取所有主要货币
         */
        @JvmStatic
        fun getMajorCurrencies(): List<CurrencyEnum> =
            entries.filter { it.isMajor }

        /**
         * 格式化货币金额
         */
        @JvmStatic
        fun formatAmount(currency: CurrencyEnum, amount: BigDecimal): String {
            return "${currency.symbol}${amount.setScale(currency.decimalPlaces, RoundingMode.HALF_UP)}"
        }

        /**
         * 获取支持的货币代码列表
         */
        @JvmStatic
        fun getSupportedCurrencyCodes(): List<String> =
            entries.map { it.code }

        /**
         * 检查两个货币是否相同
         */
        @JvmStatic
        fun isSameCurrency(code1: String?, code2: String?): Boolean {
            return code1 != null && code2 != null && code1.uppercase() == code2.uppercase()
        }
    }

    /**
     * 格式化指定金额
     */
    fun formatAmount(amount: BigDecimal): String {
        return formatAmount(this, amount)
    }
}