package tech.tiangong.pop.common.enums

import team.aikero.blade.core.toolkit.isBlank
import java.util.*

/**
 * 商品状态-AE
 */
enum class ProductAePublishStateEnum(
    /**
     * 状态编码
     */
    val code: Int,
    /**
     * 平台的状态值
     */
    val platformStateValue: String,
    /**
     * 描述
     */
    val desc: String,
) {
    /**
     * 上架
     */
    ACTIVE(1, "onSelling", "上架"),

    /**
     * 下架
     */
    IN_ACTIVE(2, "offline", "下架"),

    /**
     * 审核中
     */
    AUDITING(3, "auditing", "审核中"),

    /**
     * 审核不通过
     */
    REFUSE(4, "editingRequired", "审核不通过");

    companion object {

        /**
         * 根据code获取枚举
         */
        fun getByCode(code: Int?): ProductAePublishStateEnum? {
            if (code == null) {
                return null
            }
            return entries.find { Objects.equals(it.code, code) }
        }

        /**
         * 根据code获取枚举
         */
        fun getByCode(code: String?): ProductAePublishStateEnum? {
            if (code.isNullOrBlank()) {
                return null
            }
            return entries.find { Objects.equals(it.code.toString(), code) }
        }

        /**
         * 在线状态: 上下架
         */
        fun getOnlineState(): List<ProductAePublishStateEnum> {
            return listOf(ACTIVE, IN_ACTIVE)
        }

        /**
         * 判断是否上架
         */
        fun isActive(state: Int?): Boolean {
            return state != null && state == ACTIVE.code
        }

        /**
         * 根据平台的状态值获取枚举
         */
        fun getByPlatformStateValue(platformStateValue: String?): ProductAePublishStateEnum? {
            if (platformStateValue.isBlank()) {
                return null
            }
            return entries.find { Objects.equals(it.platformStateValue, platformStateValue) }
        }
    }
}
