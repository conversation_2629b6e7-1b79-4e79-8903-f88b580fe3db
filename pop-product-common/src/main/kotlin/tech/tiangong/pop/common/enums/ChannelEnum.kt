package tech.tiangong.pop.common.enums

/**
 * 渠道枚举
 */
enum class ChannelEnum(
    val channelId: Long,
    val channelName: String,
) {
    ALIBABA(1, "阿里"),
    BYTEDANCE(3, "字节"),

    OTHER(2, "其他"),
    ;

    companion object {
        fun getByChannelId(channelId: Long): ChannelEnum? {
            return entries.find { it.channelId == channelId }
        }

        fun getByChannelName(channelName: String): ChannelEnum? {
            return entries.find { it.channelName == channelName }
        }
    }
}
