package tech.tiangong.pop.common.enums

import java.util.*

/**
 * TryOn质检任务状态0待质检1已质检2已取消
 */
enum class TryOnTaskQcStateEnum(
    /**
     * 状态编码
     */
    val code: Int,
    /**
     * 描述
     */
    val desc: String,
) {
    UNKNOWN(-999, "未知状态"),
    /**
     * 待质检
     */
    WAITING(0, "待质检"),

    /**
     * 已质检
     */
    FINISH(1, "已质检"),
    /**
     * 已取消
     */
    CANCEL(2, "已取消"),

    ;

    companion object {

        /**
         * 根据code获取枚举
         */
        fun getByCode(code: Int?): TryOnTaskQcStateEnum? {
            if (code == null) {
                return UNKNOWN
            }
            return entries.find { Objects.equals(it.code, code) }?:UNKNOWN
        }

        /**
         * 根据code获取枚举
         */
        fun getByCode(code: String?): TryOnTaskQcStateEnum? {
            if (code.isNullOrBlank()) {
                return UNKNOWN
            }
            return entries.find { Objects.equals(it.code.toString(), code) }?:UNKNOWN
        }
    }
}
