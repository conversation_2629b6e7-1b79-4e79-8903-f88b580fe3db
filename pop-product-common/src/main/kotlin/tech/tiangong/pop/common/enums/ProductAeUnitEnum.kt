package tech.tiangong.pop.common.enums

/**
 * AE 速卖通商品单位枚举
 */
enum class ProductAeUnitEnum(val code: String, val value: String, val description: String) {
    BAG("100000000", "bag/bags", "袋"),
    BARREL("100000001", "barrel/barrels", "桶"),
    BUSHEL("100000002", "bushel/bushels", "蒲式耳"),
    CARTON("100078580", "carton", "箱"),
    CENTIMETER("100078581", "centimeter", "厘米"),
    CUBIC_METER("100000003", "cubic meter", "立方米"),
    DOZEN("100000004", "dozen", "打"),
    FEET("100078584", "feet", "英尺"),
    GALLON("100000005", "gallon", "加仑"),
    GRAM("100000006", "gram", "克"),
    INCH("100078587", "inch", "英寸"),
    KILOGRAM("100000007", "kilogram", "千克"),
    KILOLITER("100078589", "kiloliter", "千升"),
    KILOMETER("100000008", "kilometer", "千米"),
    LITER("100078559", "liter/liters", "升"),
    LONG_TON("100000009", "long ton", "英吨"),
    METER("100000010", "meter", "米"),
    METRIC_TON("100000011", "metric ton", "公吨"),
    MILLIGRAM("100078560", "milligram", "毫克"),
    MILLILITER("100078596", "milliliter", "毫升"),
    MILLIMETER("100078597", "millimeter", "毫米"),
    OUNCE("100000012", "ounce", "盎司"),
    PACK("100000014", "pack/packs", "包"),
    PAIR("100000013", "pair", "双"),
    PIECE("100000015", "piece/pieces", "件/个"),
    POUND("100000016", "pound", "磅"),
    QUART("100078603", "quart", "夸脱"),
    SET("100000017", "set/sets", "套"),
    SHORT_TON("100000018", "short ton", "美吨"),
    SQUARE_FEET("100078606", "square feet", "平方英尺"),
    SQUARE_INCH("100078607", "square inch", "平方英寸"),
    SQUARE_METER("100000019", "square meter", "平方米"),
    SQUARE_YARD("100078609", "square yard", "平方码"),
    TON("100000020", "ton", "吨"),
    YARD("100078558", "yard/yards", "码"),
    UNKNOWN("0", "unknown", "未知单位");

    companion object {
        /**
         * 根据单位编码获取对应的枚举
         * @param code 单位编码
         * @return ProductAeUnitEnum 对应的枚举，如果没有匹配则返回UNKNOWN
         */
        fun getByCode(code: String): ProductAeUnitEnum {
            return entries.find { it.code == code } ?: UNKNOWN
        }

        /**
         * 根据单位英文名称获取对应的枚举
         * @param value 英文单位名称
         * @return ProductAeUnitEnum 对应的枚举，如果没有匹配则返回UNKNOWN
         */
        fun getByValue(value: String): ProductAeUnitEnum {
            return entries.find {
                it.value.equals(value, ignoreCase = true) ||
                        it.value.split("/").any { part -> part.equals(value, ignoreCase = true) }
            } ?: UNKNOWN
        }

        /**
         * 根据单位中文描述获取对应的枚举
         * @param description 中文描述
         * @return ProductAeUnitEnum 对应的枚举，如果没有匹配则返回UNKNOWN
         */
        fun getByDescription(description: String): ProductAeUnitEnum {
            return entries.find { it.description == description } ?: UNKNOWN
        }
    }
}