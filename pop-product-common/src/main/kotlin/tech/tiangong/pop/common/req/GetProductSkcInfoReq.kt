package tech.tiangong.pop.common.req

import team.aikero.blade.core.protocol.PageReq
import java.time.LocalDateTime

class GetProductSkcInfoReq : PageReq() {

    /**
     * 更新时间
     */
    var revisedStartTime: LocalDateTime? = null

    /**
     * 更新时间
     */
    var revisedEndTime: LocalDateTime? = null

    /**
     * 平台ID
     */
    var platformId: Long? = null

    /**
     * 上架状态: 1 上架 , 2 下架 , 3 审核中 , 4 审核不通过
     * 默认上下架
     */
    var publishStateList: List<Int>? = listOf()

}