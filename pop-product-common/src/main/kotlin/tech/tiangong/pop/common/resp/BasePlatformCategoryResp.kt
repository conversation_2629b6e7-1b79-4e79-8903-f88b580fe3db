package tech.tiangong.pop.common.resp

import java.io.Serializable

/**
 * 第三方平台类目基础对象
 */
open class BasePlatformCategoryResp : Serializable{

    /**
     * 第三方平台类目ID
     */
    var categoryId: Long? = null

    /**
     * 第三方平台类目名称
     */
    var categoryName: String? = null

    /**
     * 平台ID,参考PlatformEnum
     */
    var plantformId: Long? = null

    /**
     * 父节点ID
     */
    var parentId: Long? = null

    /**
     * 层级
     */
    var level: Int? = null

    /**
     * 是否叶子节点
     */

    var leaf: Boolean? = null
    companion object {
        private const val serialVersionUID = 1L
    }
}