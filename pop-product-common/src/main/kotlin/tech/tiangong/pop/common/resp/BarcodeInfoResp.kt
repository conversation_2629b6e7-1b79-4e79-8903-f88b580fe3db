package tech.tiangong.pop.common.resp

class BarcodeInfoResp {
    /**
     * 平台ID
     */
    var platformId: Long? = null

    /**
     * 店铺编码(shortCode)
     */
    var shopCode: String? = null

    /**
     * SellerSku
     */
    var sellerSku: String? = null

    /**
     * 是否组合商品(0:否,1:是)
     */
    var combo: Int? = null

    /**
     * 条码信息
     */
    var barcodeList: List<BarcodeUnitResp> = listOf()

    class BarcodeUnitResp {
        /**
         * 商品编码
         */
        var spuCode: String? = null

        /**
         * SKC编码
         */
        var skc: String? = null

        /**
         * 颜色
         */
        var color: String? = null

        /**
         * 尺码
         */
        var size: String? = null

        /**
         * 条码
         */
        var barcode: String? = null

        /**
         * 件数
         */
        var unit: Int? = null
    }
}