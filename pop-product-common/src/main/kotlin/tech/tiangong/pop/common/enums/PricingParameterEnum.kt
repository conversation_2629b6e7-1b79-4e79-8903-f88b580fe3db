package tech.tiangong.pop.common.enums

/**
 * 定价参数枚举
 * 包含11个核心定价参数
 */
enum class PricingParameterEnum(
    /** 参数编码（用作Map的key） */
    val code: String,
    /** 参数描述 */
    val desc: String,
) {
    
    /** 物流成本 */
    LOGISTICS_COST("logisticsCost", "物流成本"),
    
    /** 仓储成本 */
    STORAGE_COST("storageCost", "仓储成本"),
    
    /** 综合税率 */
    TAX_RATE("taxRate", "综合税率"),
    
    /** 退货率 */
    RETURN_RATE("returnRate", "退货率"),
    
    /** 广告成本 */
    AD_COST("adCost", "广告成本"),
    
    /** 交易佣金 */
    COMMISSION("commission", "交易佣金"),
    
    /** 提现手续费 */
    WITHDRAWAL_FEE("withdrawalFee", "提现手续费"),
    
    /** 目标毛利率 */
    TARGET_MARGIN("targetMargin", "目标毛利率"),
    
    /** 营销费用 */
    MARKETING_COST("marketingCost", "营销费用"),
    
    /** 物流支出 */
    SHIPPING_RATE("shippingRate", "物流支出"),
    
    /** 折扣率 */
    DISCOUNT_RATE("discountRate", "折扣率");
    
    companion object {
        
        /** 通用来源描述常量 */
        const val SOURCE_OVERRIDE = "前端覆盖"
        const val SOURCE_MARGIN_PRODUCT = "产品设置"
        const val SOURCE_ERROR = "获取失败"
        const val FALLBACK_SOURCE = "兜底值"
        
        /**
         * 根据参数编码获取枚举
         */
        fun fromCode(code: String): PricingParameterEnum? {
            return entries.find { it.code == code }
        }
        
        /**
         * 获取所有参数编码列表
         */
        fun getAllCodes(): List<String> {
            return entries.map { it.code }
        }

        /**
         * 获取所有参数描述映射
         */
        fun getCodeDescriptionMap(): Map<String, String> {
            return entries.associate { it.code to it.desc }
        }
    }
}