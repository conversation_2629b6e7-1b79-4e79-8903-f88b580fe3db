package tech.tiangong.pop.common.enums

/**
 * AE 速卖通库存扣减策略枚举
 */
enum class ProductAeInventoryDeductStrategyEnum(val code: String, val description: String, val invDeduction: Int?) {
    /**
     * 下单减库存 - 买家下单时立即扣减库存
     */
    ORDER_PLACED("place_order_withhold", "下单减库存", 1),

    /**
     * 支付减库存 - 买家支付成功后才扣减库存
     */
    PAYMENT_SUCCESS("payment_success_deduct", "支付减库存", 2),

    /**
     * 未知策略
     */
    UNKNOWN("unknown", "未知策略", null);

    companion object {
        /**
         * 根据策略代码获取对应的枚举
         *
         * @param code 策略代码
         * @return 对应的枚举，如果没有匹配则返回UNKNOWN
         */
        fun getByCode(code: String): ProductAeInventoryDeductStrategyEnum {
            return entries.find { it.code == code } ?: UNKNOWN
        }

        /**
         * 根据描述获取对应的枚举
         *
         * @param description 策略描述
         * @return 对应的枚举，如果没有匹配则返回UNKNOWN
         */
        fun getByDescription(description: String): ProductAeInventoryDeductStrategyEnum {
            return entries.find { it.description == description } ?: UNKNOWN
        }

        /**
         * 判断给定代码是否为有效的库存扣减策略
         *
         * @param code 要检查的代码
         * @return 是否有效
         */
        fun isValid(code: String): Boolean {
            return entries.any { it.code == code && it != UNKNOWN }
        }

        /**
         * 获取所有有效的策略列表（不含UNKNOWN）
         *
         * @return 有效策略枚举列表
         */
        fun getValidStrategies(): List<ProductAeInventoryDeductStrategyEnum> {
            return entries.filter { it != UNKNOWN }
        }

        fun getByInvDeduction(invDeduction: Int?): ProductAeInventoryDeductStrategyEnum? {
            return entries.firstOrNull { it.invDeduction == invDeduction }
        }
    }
}