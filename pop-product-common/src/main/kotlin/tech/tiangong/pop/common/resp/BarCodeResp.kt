package tech.tiangong.pop.common.resp



/**
 * 商品条码列表
 *
 * <AUTHOR>
 * @since 2024-11-16 19:07:02
 */

class BarCodeResp {
    /**
     * 主键
     */
    var productBarcodeId: Long? = null
    var sellerSku: String? = null

    /**
     * 条码编码
     */
    var barcode: String? = null


    /**
     * 灵感图URL
     */
    var inspiraImgUrl: String? = null


    /**
     * 设计图url链接
     */
    var designImgUrl: String? = null


    /**
     * 主图url链接
     */
    var mainImgUrl: String? = null


    /**
     * 品类编码（多级-分割）
     */
    var categoryCode: String? = null


    /**
     * 品类名称（多级-分割）
     */
    var categoryName: String? = null

    /**
     * spu编码
     */
    var spuCode: String? = null


    /**
     * 供给方式编码
     */
    var supplyMode: String? = null

    /**
     * skc
     */
    var skc: String? = null

    /**
     * 颜色
     */
    var color: String? = null

    /**
     * 尺码组名称
     */
    var groupName: String? = null

    /**
     * 尺码组编号
     */
    var sourceGroupCode: String? = null

    /**
     * 尺码名
     */
    var sizeName: String? = null

    /**
     * 平台shopSKU
     */
    var shopSku: String? = null

    /**
     * 平台SKUID
     */
    var skuId: String? = null

    /**
     * 件数
     */
    var unit: Int? = null


    companion object {
        private const val serialVersionUID = -64669407410271182L
    }
}

