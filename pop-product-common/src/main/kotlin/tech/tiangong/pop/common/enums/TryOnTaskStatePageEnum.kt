package tech.tiangong.pop.common.enums

/**
 * TryOn任务状态-AE精选商品列表筛选用
 */
enum class TryOnTaskStatePageEnum(
    /**
     * 状态编码
     */
    val code: Int,
    /**
     * 描述
     */
    val desc: String,
) {
    /**
     * 待创建 TryOnTaskStateEnum: 0
     */
    WAITING(0, "待创建"),

    /**
     * 创建中 TryOnTaskStateEnum: 1, 2
     */
    CREATING(1, "创建中"),

    /**
     * 已创建 TryOnTaskStateEnum: 3, 5
     */
    FINISH(3, "已完成"),

    /**
     * 已失败 TryOnTaskStateEnum: 4
     */
    FAIL(4, "已失败"),
    ;
}
