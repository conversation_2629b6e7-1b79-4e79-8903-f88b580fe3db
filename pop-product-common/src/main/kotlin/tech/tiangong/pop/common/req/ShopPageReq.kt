package tech.tiangong.pop.common.req


import team.aikero.blade.core.protocol.PageReq

/**
 * 店铺信息
 */

class ShopPageReq : PageReq() {
    /**
     * 店铺名 模糊查询
     */
    var shopName: String? = null

    /**
     * 是否授权【1已授权；0未授权】
     */
    private var isAuth: Int? = null

    fun getIsAuth(): Int? {
        return isAuth
    }

    fun setIsAuth(isAuth: Int?) {
        this.isAuth = isAuth
    }
}
