package tech.tiangong.pop.common.enums

/**
 * 国家站点枚举
 */
enum class CountryEnum(
    /**
     * 站点代码
     */
    val code: String,
    /**
     * 站点名称
     */
    val desc: String,
    /**
     * 对应的货币代码
     */
    val currencyCode: String
) {
    /**
     * 中国
     */
    CN("CN", "中国", CurrencyEnum.CNY.code),
    /**
     * 菲律宾
     */
    PH("PH", "菲律宾", CurrencyEnum.PHP.code),

    /**
     * 泰国
     */
    TH("TH", "泰国", CurrencyEnum.THB.code),

    /**
     * 马来西亚
     */
    MY("MY", "马来西亚", CurrencyEnum.MYR.code),

    /**
     * 越南
     */
    VN("VN", "越南", CurrencyEnum.VND.code),

    /**
     * 新加坡
     */
    SG("SG", "新加坡", CurrencyEnum.SGD.code),

    /**
     * 印度尼西亚
     */
    ID("ID", "印度尼西亚", CurrencyEnum.IDR.code),

    /**
     * 美国
     */
    US("US", "美国", CurrencyEnum.USD.code);

    companion object {
        /**
         * 根据代码获取枚举实例
         */
        @JvmStatic
        fun getByCode(code: String?): CountryEnum? =
            entries.firstOrNull { it.code == code }

        /**
         * 根据货币代码获取枚举实例
         */
        @JvmStatic
        fun getByCurrencyCode(currencyCode: String?): CountryEnum? =
            entries.firstOrNull { it.currencyCode == currencyCode }

        /**
         * 获取所有支持的国家代码
         */
        @JvmStatic
        fun getSupportedCountryCodes(): List<String> =
            entries.map { it.code }

        /**
         * 获取指定国家的货币信息
         */
        @JvmStatic
        fun getCountryCurrency(countryCode: String?): CurrencyEnum? =
            getByCode(countryCode)?.let { CurrencyEnum.getByCode(it.currencyCode) }
    }

    /**
     * 获取当前国家的货币枚举
     */
    fun getCurrency(): CurrencyEnum? = CurrencyEnum.getByCode(currencyCode)
}
