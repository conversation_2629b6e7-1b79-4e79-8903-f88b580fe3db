package tech.tiangong.pop.common.enums

import java.util.*

/**
 * AI任务状态10-生成中；20-已中止；30-已完成；40-失败；50-排队中
 */
enum class AiTaskStateEnum(
    /**
     * 状态编码
     */
    val code: Int,
    /**
     * 描述
     */
    val desc: String,
) {
    UNKNOWN(-999, "未知状态"),
    /**
     * 生成中
     */
    GENERATING(10, "生成中"),
    /**
     * 已中止
     */
    ABORTED(20, "已中止"),
    /**
     * 已完成
     */
    COMPLETED(30, "已完成"),
    /**
     * 失败
     */
    FAILED(40, "失败"),

    /**
     * 创排队中
     */
    QUEUEING(50, "排队中"),
    ;

    companion object {

        /**
         * 根据code获取枚举
         */
        fun getByCode(code: Int?): AiTaskStateEnum? {
            if (code == null) {
                return UNKNOWN
            }
            return entries.find { Objects.equals(it.code, code) }?:UNKNOWN
        }

        /**
         * 根据code获取枚举
         */
        fun getByCode(code: String?): AiTaskStateEnum? {
            if (code.isNullOrBlank()) {
                return UNKNOWN
            }
            return entries.find { Objects.equals(it.code.toString(), code) }?:UNKNOWN
        }
    }
}
