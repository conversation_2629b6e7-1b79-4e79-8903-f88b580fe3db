package tech.tiangong.pop.common.enums

/**
 * 印花类型枚举
 */
enum class PrintTypeEnum(val code: String, val desc: String) {
    /**
     * LOGO印
     */
    LOGO_PRINT("logo_print", "LOGO印"),

    /**
     * 满印
     */
    FULL_PRINT("full_print", "满印");

    companion object {
        /**
         * 根据编码获取对应的枚举
         *
         * @param code 印花类型编码
         * @return 对应的印花类型枚举，如果不存在则返回null
         */
        fun fromCode(code: String?): PrintTypeEnum? {
            if (code == null) return null
            return entries.find { it.code == code }
        }
        /**
         * 根据编码获取对应的枚举
         *
         * @param code 印花类型编码
         * @return 对应的印花类型枚举，如果不存在则返回null
         */
        fun fromDesc(desc: String?): PrintTypeEnum? {
            if (desc == null) return null
            return entries.find { it.desc == desc }
        }

        /**
         * 获取所有印花类型描述，用于前端展示
         *
         * @return 印花类型描述列表
         */
        fun getAllDescriptions(): List<String> {
            return entries.map { it.desc }
        }
    }
}