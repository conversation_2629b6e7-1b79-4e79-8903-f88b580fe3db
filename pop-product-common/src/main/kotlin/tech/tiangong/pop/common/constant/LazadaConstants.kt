package tech.tiangong.pop.common.constant

import tech.tiangong.pop.common.enums.CountryEnum
import tech.tiangong.pop.common.enums.LazadaCountryEnum
import java.util.regex.Pattern

/**
 * Lazada常量类
 * @date 2024/7/17
 */
object LazadaConstants {
    /** 正则匹配图片名称，主图，301.jpg等等 */
    @JvmField val PATTERN_301_IMAGE: Pattern = Pattern.compile(".*301\\.(jpg|png|jpeg)$", Pattern.CASE_INSENSITIVE)

    // ---------- 其他 ----------
    /** Lazada API成功返回码 */
    const val LAZADA_API_SUCCESS_CODE = "0"
    /** 回调地址配置路径 */
    const val LAZADA_AUTH_CALLBACK_URL_KEY = "lazada.callbackUrl"

    /** Lazada 前台 URL 模板 */
    private val countryUrlTemplates = mapOf(
        LazadaCountryEnum.MY to "https://www.lazada.com.%s/products/i%s.html",
        LazadaCountryEnum.PH to "https://www.lazada.com.%s/products/i%s.html",
        LazadaCountryEnum.TH to "https://www.lazada.co.%s/products/i%s.html",
        LazadaCountryEnum.ID to "https://www.lazada.co.%s/products/i%s.html",
        LazadaCountryEnum.VN to "https://www.lazada.%s/products/i%s.html",
        LazadaCountryEnum.SG to "https://www.lazada.%s/products/i%s.html",
    )

    /**
     * 获取前台链接
     *
     * @param country 国家站点
     * @param itemId 商品ID
     * @return 前台URL
     */
    @JvmStatic
    fun getFrontUrl(country: String, itemId: String): String {
        val countryEnum = LazadaCountryEnum.getByCode(country)
        val template = countryUrlTemplates[countryEnum] ?: throw IllegalArgumentException("不支持的前台链接国家 $country")
        return String.format(template, country.lowercase(), itemId)
    }

    /** Lazada默认站点 */
    val LAZADA_DEFAULT_COUNTRY = listOf(
        CountryEnum.ID,
        CountryEnum.TH,
        CountryEnum.PH,
        CountryEnum.SG,
        CountryEnum.MY,
        CountryEnum.VN,
    )

    /** 平台错误码常量 */
    object ErrorCode {
        /** 商品不存在 */
        const val ITEM_NOT_FOUND = "208"
        /** SKU不存在 */
        const val SKU_NOT_FOUND = "207"
        /** 失效的token */
        const val ILLEGAL_ACCESS_TOKEN = "IllegalAccessToken"
    }
}
