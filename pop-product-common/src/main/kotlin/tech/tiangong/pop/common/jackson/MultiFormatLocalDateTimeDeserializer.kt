package tech.tiangong.pop.common.jackson

import com.fasterxml.jackson.core.JsonParser
import com.fasterxml.jackson.core.JsonToken
import com.fasterxml.jackson.databind.DeserializationContext
import com.fasterxml.jackson.databind.JsonDeserializer
import com.fasterxml.jackson.databind.JsonMappingException
import team.aikero.blade.core.constant.DatePatternConstants.ASIA_SHANGHAI
import java.time.Instant
import java.time.LocalDateTime
import java.time.ZoneId

/**
 * Jackson 反序列化 LocalDateTime 的自定义反序列化器
 */
class MultiFormatLocalDateTimeDeserializer : JsonDeserializer<LocalDateTime>() {
    override fun deserialize(p: JsonParser, ctxt: DeserializationContext): LocalDateTime {
        return when (p.currentToken) {
            JsonToken.VALUE_NUMBER_INT -> {
                Instant.ofEpochMilli(p.longValue)
                    .atZone(ZoneId.of(ASIA_SHANGHAI)).toLocalDateTime()
            }
            JsonToken.VALUE_STRING -> {
                LocalDateTime.parse(p.text)
            }
            else -> throw JsonMappingException(p, "无法解析为 LocalDateTime: ${p.text}")
        }
    }
}
