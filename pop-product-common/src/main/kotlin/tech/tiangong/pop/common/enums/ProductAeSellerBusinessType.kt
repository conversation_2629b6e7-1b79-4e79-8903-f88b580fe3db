package tech.tiangong.pop.common.enums

/**
 * AE速卖通卖家业务类型枚举
 */
enum class ProductAeSellerBusinessType(val value: String, val description: String) {
    ONE_STOP_SERVICE("ONE_STOP_SERVICE", "全托管店铺"),
    POP_CHOICE("POP_CHOICE", "POP与半托管店铺"),
    LOCAL_SERVICE("LOCAL_SERVICE", "海外托管店铺"),
    UNKNOWN("UNKNOWN", "未知业务类型");

    companion object {
        /**
         * 根据值获取对应的枚举
         */
        fun fromValue(value: String?): ProductAeSellerBusinessType {
            if (value == null) return UNKNOWN
            return entries.find { it.value == value } ?: UNKNOWN
        }
    }
}