package tech.tiangong.pop.common.enums

/**
 * 产品下架原因枚举
 * 定义了速卖通产品可能的下架原因
 * @see com.aliexpress.open.domain.AliexpressOfferProductQueryAeopFindProductResultDTO.wsDisplay`
 */
enum class ProductAeOfflineReasonEnum(val code: String, val description: String) {
    /**
     * 手动下架
     */
    USER_OFFLINE("user_offline", "手动下架"),

    /**
     * 到期下架
     */
    EXPIRE_OFFLINE("expire_offline", "到期下架"),

    /**
     * 网规处罚下架
     */
    PUNISH_OFFLINE("punish_offline", "网规处罚下架"),

    /**
     * 交易违规下架
     */
    VIOLATE_OFFLINE("violate_offline", "交易违规下架"),

    /**
     * 降级下架
     */
    DEGRADE_OFFLINE("degrade_offline", "降级下架"),

    /**
     * 未续约下架
     */
    INDUSTRY_OFFLINE("industry_offline", "未续约下架");

    companion object {
        /**
         * 根据编码获取枚举值
         *
         * @param code 编码
         * @return 对应的枚举值，如果不存在则返回null
         */
        fun getByCode(code: String?): ProductAeOfflineReasonEnum? {
            if (code == null) return null
            return entries.find { it.code == code }
        }
    }
}