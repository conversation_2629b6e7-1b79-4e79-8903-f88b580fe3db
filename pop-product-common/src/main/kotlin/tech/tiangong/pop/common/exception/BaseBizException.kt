package tech.tiangong.pop.common.exception

import tech.tiangong.pop.common.enums.BaseErrCodeEnum


/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/5/9 10:12
 * @description: 基础业务异常封装，自定义的异常最好都继承这个异常类，统一拦截异常通过这个异常进行匹配
 */
class BaseBizException : RuntimeException {
    /**
     * 异常码
     */
    var code: String? = null
        private set

    constructor(errCodeEnum: BaseErrCodeEnum, vararg param: Any?) : super(errCodeEnum.msg(*param)) {
        this.code = errCodeEnum.code()
    }

    constructor(errCodeEnum: BaseErrCodeEnum, cause: Throwable?, vararg param: Any?) : super(errCodeEnum.msg(*param), cause) {
        this.code = errCodeEnum.code()
    }

    constructor(code: String?, msg: String, vararg param: Any?) : super(String.format(msg, *param)) {
        this.code = code
    }

    constructor(code: String?, message: String?, cause: Throwable?) : super(message, cause) {
        this.code = code
    }

    constructor(code: String?, cause: Throwable?) : super(cause) {
        this.code = code
    }

    constructor(cause: Throwable?) : super(cause)

    constructor(cause: Throwable?, msg: String?) : super(msg, cause)

    constructor(msg: String?) : super(msg)

    companion object {
        private const val serialVersionUID = 6854854927350714883L
    }
}
