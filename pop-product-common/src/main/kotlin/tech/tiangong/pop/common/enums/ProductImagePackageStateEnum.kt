package tech.tiangong.pop.common.enums

import java.util.*

/**
 * 商品对应图包状态1未完成2已完成3更新中4已更新
 * @create 2025/07/03 15:46
 */
enum class ProductImagePackageStateEnum(val code: Int, val desc: String) {
    UNKNOWN(-1, "未知状态"),
    INCOMPLETE(1, "未完成"),
    COMPLETED(2, "已完成"),
    UPDATING(3, "更新中"),
    UPDATED(4, "已更新"),
    ;


    companion object {
        /**
         * 根据code获取枚举
         *
         * @param desc
         * @return
         */
        @JvmStatic
        fun getByCode(code: Int): ProductImagePackageStateEnum? {
            return entries.firstOrNull { e: ProductImagePackageStateEnum -> Objects.equals(e.code, code) }
        }

        /**
         * 根据desc获取枚举
         *
         * @param desc
         * @return
         */
        @JvmStatic
        fun getByDesc(desc: String): ProductImagePackageStateEnum? {
            return entries.firstOrNull { e: ProductImagePackageStateEnum -> Objects.equals(e.desc, desc) }
        }
    }
}
