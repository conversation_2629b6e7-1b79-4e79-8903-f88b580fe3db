package tech.tiangong.pop.common.resp


import java.math.BigDecimal

/**
 * 商品条码列表
 *
 * <AUTHOR>
 * @since 2024-11-16 19:07:02
 */

class BarCodeListResp {
    /**
     * sku编码
     */
    var sku: String? = null

    /**
     * 条码
     */
    var barcode: String? = null

    /**
     * spu编码
     */
    var spuCode: String? = null

    /**
     * skc
     */
    var skc: String? = null

    /**
     * 颜色
     */
    var color: String? = null

    /**
     * 尺码组名称
     */
    var groupName: String? = null

    /**
     * 尺码组编号
     */
    var sourceGroupCode: String? = null

    /**
     * 尺码名
     */
    var sizeName: String? = null

    /**
     * 平台shopSKU
     */
    var shopSku: String? = null

    /**
     * 平台SKUID
     */
    var skuId: String? = null

    var unit: Int? = null


    /**
     * 供给方式
     */
    var supplyMode: String? = null


    /**
     * 灵感图URL
     */
    var inspiraImgUrl: String? = null


    /**
     * 设计图url链接
     */
    var designImgUrl: String? = null


    /**
     * 主图url链接
     */
    var mainImgUrl: String? = null


    /**
     * 品类编码（多级-分割）
     */
    var categoryCode: String? = null


    /**
     * 品类名称（多级-分割）
     */
    var categoryName: String? = null


    /**
     * 供货价
     */
    var localPrice: BigDecimal? = null


    /**
     * sellerSku
     */
    var sellerSku: String? = null

    companion object {
        private const val serialVersionUID = -93471747013529979L
    }
}

