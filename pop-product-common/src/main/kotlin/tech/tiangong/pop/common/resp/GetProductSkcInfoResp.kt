package tech.tiangong.pop.common.resp

import java.time.LocalDateTime

class GetProductSkcInfoResp {
    /**
     * 平台ID
     */
    var platformId: Long? = null
    /**
     * 国家站点
     */
    var country: String? = null
    /**
     * 店铺code(short_code)
     */
    var shopCode: String? = null
    /**
     * 平台产品ID (PID)
     */
    var platformProductId: String? = null
    /**
     * SPU编码
     */
    var spuCode: String? = null
    /**
     * SKC编码
     */
    var skcCode: String? = null
    /**
     * 是否组合商品(0:否,1:是)
     */
    var combo: Int? = null
    /**
     * 首次上架时间
     */
    var firstPublishTime: LocalDateTime? = null
    /**
     * 最新上架时间
     */
    var latestPublishTime: LocalDateTime? = null
    /**
     * 主图url
     */
    var mainImgUrl: String? = null
    /**
     * 品类编码（多级-分割）
     */
    var categoryCode: String? = null
    /**
     * 品类名称（多级-分割）
     */
    var categoryName: String? = null
    /**
     * 供给方式
     */
    var supplyMode: String? = null
    /**
     * SKU集合
     */
    var skuList:List<SkcInfoResp>? = null


    class SkcInfoResp {
        /**
         * 尺码
         */
        var sizeName:String? = null
        /**
         * 上架状态【1上架；2下架】
         */
        var publishState: Int? = null
    }
}