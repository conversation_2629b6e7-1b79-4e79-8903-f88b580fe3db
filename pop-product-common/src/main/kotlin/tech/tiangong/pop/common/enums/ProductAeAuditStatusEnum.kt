package tech.tiangong.pop.common.enums

/**
 * AE速卖通商品状态枚举
 */
enum class ProductAeAuditStatusEnum(val value: String, val description: String) {
    APPROVED("approved", "审核通过"),
    AUDITING("auditing", "审核中"),
    REFUSE("refuse", "审核不通过"),
    UNKNOWN("unknown", "未知状态");

    companion object {
        /**
         * 根据状态值获取对应的枚举
         * @param value 状态值
         * @return ProductStatus 对应的枚举，如果没有匹配则返回UNKNOWN
         */
        fun getByValue(value: String): ProductAeAuditStatusEnum {
            return entries.find { it.value == value } ?: UNKNOWN
        }
    }
}