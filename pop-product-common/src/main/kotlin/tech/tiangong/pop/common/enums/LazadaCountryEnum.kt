package tech.tiangong.pop.common.enums

import java.util.*

/**
 * lazada站点
 * @date 2024/7/16
 */
enum class LazadaCountryEnum(
    /**
     * 站点代码
     */
    val code: String,
    /**
     * 站点名称
     */
    val desc: String,
    /**
     * 站点服务器地址
     */
    val serverUrl: String,
) {
    /**
     * 菲律宾
     */
    PH("PH", "菲律宾", "https://api.lazada.com.ph/rest"),

    /**
     * 泰国
     */
    TH("TH", "泰国", "https://api.lazada.co.th/rest"),

    /**
     * 马来西亚
     */
    MY("MY", "马来西亚", "https://api.lazada.com.my/rest"),

    /**
     * 越南
     */
    VN("VN", "越南", "https://api.lazada.vn/rest"),

    /**
     * 新加坡
     */
    SG("SG", "新加坡", "https://api.lazada.sg/rest"),

    /**
     * 印度尼西亚
     */
    ID("ID", "印度尼西亚", "https://api.lazada.co.id/rest");

    companion object {

        @JvmStatic
        fun getServerUrlByCode(code: String): String {
            for (country in entries) {
                if (country.code.equals(code, ignoreCase = true)) {
                    return country.serverUrl
                }
            }
            throw IllegalArgumentException("No matching country for code: $code")
        }

        @JvmStatic
        fun getDescByCode(code: String): String {
            for (country in entries) {
                if (country.code.equals(code, ignoreCase = true)) {
                    return country.desc
                }
            }
            throw IllegalArgumentException("No matching desc for code: $code")
        }

        /**
         * 国家代码到顺序索引的映射，用于排序
         */
        private val COUNTRY_ORDER_MAP: Map<String, Int> by lazy {
            val map = HashMap<String, Int>()
            entries.forEachIndexed { index, country ->
                map[country.code] = index
            }
            map
        }

        /**
         * 国家代码列表
         */
        private val COUNTRY_CODE_LIST: List<String> by lazy {
            entries.map { it.code }
        }

        /**
         * 获取国家代码对应的顺序索引
         * @param countryCode 国家代码
         * @return 顺序索引，不存在时返回Int.MAX_VALUE
         */
        @JvmStatic
        fun getOrderIndex(countryCode: String): Int {
            return COUNTRY_ORDER_MAP[countryCode.uppercase(Locale.ENGLISH)] ?: Int.MAX_VALUE
        }

        /**
         * 获取国家代码列表
         */
        @JvmStatic
        fun getCountryCodeList(): List<String> {
            return COUNTRY_CODE_LIST
        }

        @JvmStatic
        fun getCountryList(): List<String> {
            return entries.map { it.code }
        }

        fun getByCode(code: String): LazadaCountryEnum? {
            return entries.find { it.code.equals(code, ignoreCase = true) }
        }

    }
}