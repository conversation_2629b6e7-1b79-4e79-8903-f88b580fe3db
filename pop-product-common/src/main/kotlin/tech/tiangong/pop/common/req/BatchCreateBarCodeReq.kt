package tech.tiangong.pop.common.req


import java.math.BigDecimal

/**
 * 批量创建条码请求
 */
data class BatchCreateBarCodeReq(
    /** SPU编码 */
    var spuCode: String? = null,

    /** 尺码组名称 */
    var groupName: String? = null,

    /** 商家SKU */
    var sellerSku: String? = null,

    /** 尺码组编号 */
    var sourceGroupCode: String? = null,

    /** 颜色 */
    var color: String? = null,

    /** 灵感图URL */
    var inspiraImgUrl: String? = null,

    /** 供货价 */
    var localPrice: BigDecimal? = null,

    /** 供给方式编码 */
    var supplyMode: String? = null,

    /** 设计图url链接 */
    var designImgUrl: String? = null,

    /** 主图url链接 */
    var mainImgUrl: String? = null,

    /** 品类编码（多级-分割） */
    var categoryCode: String? = null,

    /** 品类名称（多级-分割） */
    var categoryName: String? = null,

    /** 设计师 */
    var designName: String? = null,

    /** 设计组 */
    var designGroup: String? = null,

    /** SKC编码 */
    var skcCode: String? = null,

    /** 尺码值（如 X M L） */
    var sizeValues: List<String>? = null
) {
    /**
     * 校验必要字段，不合法时抛出异常（收集全部错误）
     */
    fun validate(idx: Int? = null) {
        val errors = mutableListOf<String>()
        if (spuCode.isNullOrBlank()) errors.add("spuCode（SPU编码）不能为空")
        if (skcCode.isNullOrBlank()) errors.add("skcCode（SKC编码）不能为空")
        if (groupName.isNullOrBlank()) errors.add("groupName（尺码组名称）不能为空")
        if (sourceGroupCode.isNullOrBlank()) errors.add("sourceGroupCode（尺码组编号）不能为空")
        if (sizeValues.isNullOrEmpty()) errors.add("sizeValues（尺码值）不能为空")
        if (errors.isNotEmpty()) {
            val prefix = if (idx != null) "第${idx + 1}项：" else ""
            throw IllegalArgumentException(prefix + errors.joinToString("；"))
        }
    }
}
