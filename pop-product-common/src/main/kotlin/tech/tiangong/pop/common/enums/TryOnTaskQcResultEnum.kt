package tech.tiangong.pop.common.enums

import java.util.*

/**
 * TryOn质检结果0待质检1通过2不通过
 */
enum class TryOnTaskQcResultEnum(
    /**
     * 状态编码
     */
    val code: Int,
    /**
     * 描述
     */
    val desc: String,
) {
    UNKNOWN(-999, "未知状态"),
    /**
     * 待质检
     */
    WAITING(0, "待质检"),

    /**
     * 通过
     */
    PASS(1, "通过"),
    /**
     * 不通过
     */
    NO_PASS(2, "不通过"),

    ;

    companion object {

        /**
         * 根据code获取枚举
         */
        fun getByCode(code: Int?): TryOnTaskQcResultEnum? {
            if (code == null) {
                return UNKNOWN
            }
            return entries.find { Objects.equals(it.code, code) }?:UNKNOWN
        }

        /**
         * 根据code获取枚举
         */
        fun getByCode(code: String?): TryOnTaskQcResultEnum? {
            if (code.isNullOrBlank()) {
                return UNKNOWN
            }
            return entries.find { Objects.equals(it.code.toString(), code) }?:UNKNOWN
        }
    }
}
