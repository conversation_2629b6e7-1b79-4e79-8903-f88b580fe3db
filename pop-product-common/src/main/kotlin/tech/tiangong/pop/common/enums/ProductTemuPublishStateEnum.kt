package tech.tiangong.pop.common.enums

import java.util.*

/**
 * 商品状态-TEMU
 */
enum class ProductTemuPublishStateEnum(
    /**
     * 状态编码
     */
    val code: Int,
    /**
     * 平台的状态值
     */
    val platformStateValue: String,
    /**
     * 描述
     */
    val desc: String,
) {
    /**
     * 在售中
     */
    SELLING(1, "onSale", "在售中"),

    /**
     * 待创建首单
     */
    WAIT_FIRST_ORDER(2, "pendingFirstOrder", "待创建首单"),

    /**
     * 已创建首单
     */
    CREATED_FIRST_ORDER(3, "firstOrderCreated", "已创建首单"),

    /**
     * 待寄样
     */
    WAIT_SAMPLE(4, "samplePending", "待寄样"),

    /**
     * 审版中
     */
    SAMPLE_REVIEWING(5, "sampleReviewing", "审版中"),

    /**
     * 价格申报中
     */
    PRICE_DECLARING(6, "priceDeclaring", "价格申报中"),

    /**
     * 未发布到站点
     */
    UNPUBLISHED(7, "unpublished", "未发布到站点"),

    /**
     * 已下架/已终止
     */
    TERMINATED(8, "terminated", "已下架/已终止");

    companion object {

        /**
         * 根据code获取枚举
         */
        fun getByCode(code: Int?): ProductTemuPublishStateEnum? {
            if (code == null) {
                return null
            }
            return entries.find { Objects.equals(it.code, code) }
        }

        /**
         * 根据code获取枚举
         */
        fun getByCode(code: String?): ProductTemuPublishStateEnum? {
            if (code.isNullOrBlank()) {
                return null
            }
            return entries.find { Objects.equals(it.code.toString(), code) }
        }

//        /**
//         * 在线状态: 上下架
//         */
//        fun getOnlineState(): List<ProductTemuPublishStateEnum> {
//            return listOf(ACTIVE, IN_ACTIVE)
//        }

//        /**
//         * 判断是否上架
//         */
//        fun isActive(state: Int?): Boolean {
//            return state != null && listOf(ACTIVE, IN_ACTIVE)
//        }

        /**
         * 根据平台的状态值获取枚举
         */
        fun getByPlatformStateValue(platformStateValue: String): ProductTemuPublishStateEnum? {
            return entries.find { Objects.equals(it.platformStateValue, platformStateValue) }
        }
    }
}
