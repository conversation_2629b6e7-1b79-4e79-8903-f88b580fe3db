package tech.tiangong.pop.common.constant

import tech.tiangong.pop.common.enums.ProductPublishStateEnum
import tech.tiangong.pop.common.enums.SpotTypeOpsEnum

/**
 * 商品常量
 * <AUTHOR>
 * @date 2025-2-21 14:45:44
 */
object ProductConstant {
    /** 标准尺码 */
    @JvmField
    val STANDARD_SIZE: List<String> = listOf(
        "2XS", "XS", "S", "M", "L", "XL", "2XL", "3XL", "4XL", "5XL", "6XL", "F"
    )

    /**
     * 获取标准尺码顺序
     */
    fun getSizeSort(): Map<String, Int> {
        return STANDARD_SIZE.mapIndexed { index, size ->
            size to index
        }.toMap()
    }

    /**
     * 传入size集合自动排序
     */
    fun sortSize(sizeList: List<String>): List<String> {
        return sizeList.sortedWith { size1, size2 ->
            val sort = getSizeSort()
            sort[size1]!!.compareTo(sort[size2]!!)
        }
    }

    /** 默认尺码 */
    @JvmField
    val DEFAULT_SIZE: List<String> = listOf(
        "S", "M", "L", "XL"
    )

    /** 标准组编码: 字母码(天工尺码) */
    const val SOURCE_GROUP_CODE: String = "tiangong_code_standard"

    /** 条码与标准尺码的映射 */
    val BAR_CODE_SIZE_MAP = mapOf(
        "XXS" to "2XS",
        "XXL" to "2XL",
        "XXXL" to "3XL",
        "XXXXL" to "4XL",
        "XXXXXL" to "5XL",
        "XXXXXXL" to "6XL",
        "one size" to "F",
        "One size" to "F",
        "One Size" to "F"
    )

    /**
     * AE 尺码映射
     */
    val AE_SIZE_MAP = mapOf(
        "XXS" to "2XS",
        "L" to "0XL",
        "XL" to "1XL",
        "XXL" to "2XL",
        "XXXL" to "3XL",
        "XXXXL" to "4XL",
        "XXXXXL" to "5XL",
        "XXXXXXL" to "6XL",
        "one size" to "F",
        "One size" to "F",
        "One Size" to "F"
    )

    /**
     * AE 尺码反向映射 例如有些是XXL，有些是2XL
     */
    val AE_REVERSE_SIZE_MAP = AE_SIZE_MAP
        .entries
        .groupBy({ it.value }, { it.key })
        .mapValues { (_, v) ->
            // 选出标准写法作为反向映射，如 "One Size"
            v.find { it.equals("One Size", ignoreCase = false) }
                ?: v.first()
        }

    /** 上架过状态集合 */
    @JvmField
    val PREVIOUSLY_PUBLISHED_STATES: Set<Int> = setOf(
        ProductPublishStateEnum.ACTIVE.code,
        ProductPublishStateEnum.IN_ACTIVE.code
    )

    /** 衣服尺寸超集 */
    @JvmField
    val CLOTHING_SIZES: List<String> = listOf(
        "2XS", "XXS", "XS", "S", "M", "L", "XL",
        "2XL", "XXL", "3XL", "XXXL", "4XL", "XXXXL",
        "6XL", "XXXXXL", "F"
    )

    /** TRY ON 会计算价格的类型集合 */
    val TRY_ON_HANDLE_SPOT_TYPE_SET = setOf(
        SpotTypeOpsEnum.A1.dictCode,
        SpotTypeOpsEnum.A2.dictCode
    )

}