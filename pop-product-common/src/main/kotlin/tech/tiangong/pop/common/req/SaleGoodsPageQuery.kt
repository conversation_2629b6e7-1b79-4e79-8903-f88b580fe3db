package tech.tiangong.pop.common.req

import java.time.LocalDateTime


/**
 * 商品分页req
 * @Author: lhj
 * @DateTime: 2023/6/14 15:05
 * @Description:
 */
class SaleGoodsPageQuery {

    /**
     * 当前查询的页码
     */
    var pageNum: Int = 1

    /**
     * 当前查询单页的数据量
     */
    var pageSize: Int = 20

    /**
     * 平台id
     */
    var platformId: Long? = null

    /**
     * 上架日期start
     */
    var publishTimeStart: LocalDateTime? = null

    /**
     * 上架日期end
     */
    var publishTimeEnd: LocalDateTime? = null

    /**
     * SKC集合
     */
    var skcList: List<String>? = null

    /**
     * shopSKU 集合
     */
    var shopSkuList: List<String>? = null

    /**
     * sellerSku 集合
     */
    var sellerSkuList: List<String>? = null

    /**
     * 商品条码
     */
    var barcodeList: List<String>? = null

    /**
     * sku状态【1上架；2下架】
     */
    var skuState: Int? = null
}
