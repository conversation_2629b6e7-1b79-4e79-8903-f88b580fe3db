package tech.tiangong.pop.common.enums

/**
 * 平台枚举
 */
enum class PlatformEnum(
    val platformId: Long,
    val platformName: String,
) {
    LAZADA(1, "<PERSON><PERSON><PERSON>"),
    AE(3, "AE"),
    <PERSON><PERSON><PERSON>_TOK(4, "TikTok"),
    T_<PERSON>LL(5, "天猫"),
    TA<PERSON>_BAO(6, "淘宝"),
    YI_LIU_BA_BA(7, "1688"),
    TEMU(8, "Temu"),
    SHOP<PERSON>(9, "Shopee"),

    <PERSON>THER(2, "其他"),
    ;


    companion object {
        fun getByPlatformId(platformId: Long): PlatformEnum? {
            return entries.find { it.platformId == platformId }
        }

        fun getByPlatformName(platformName: String): PlatformEnum? {
            return entries.find { it.platformName == platformName }
        }

        fun getByName(name: String): PlatformEnum? {
            return entries.find { it.name == name }
        }
    }
}
