package tech.tiangong.pop.common.req

import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.NotEmpty
import jakarta.validation.constraints.NotNull
import java.time.LocalDateTime

class BarcodeInfoReq {

    /**
     * 平台ID
     */
    @field:NotNull(message = "平台不能为空")
    var platformId: Long? = null

    /**
     * 店铺编码(shortCode)
     */
    @field:NotBlank(message = "店铺不能为空")
    var shopCode: String? = null

    /**
     * 快照时间
     */
    @field:NotNull(message = "快照时间不能为空")
    var snapshotTime: LocalDateTime? = null

    /**
     * SellerSku
     */
    @field:NotEmpty(message = "SellerSku不能为空")
    var sellerSkuList: List<String>? = null
}