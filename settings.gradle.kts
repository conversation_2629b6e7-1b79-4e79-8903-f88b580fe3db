import team.aikero.gradle.plugin.version.catalog.versionCatalogConf

pluginManagement {
    repositories {
        mavenLocal()
        maven {
            url = uri(providers.gradleProperty("companyNexusRepositoryUrl").get())
            isAllowInsecureProtocol = true
            credentials {
                username = providers.gradleProperty("companyNexusUsername").get()
                password = providers.gradleProperty("companyNexusPassword").get()
            }
        }
        gradlePluginPortal()
    }
}
plugins {
    id("team.aikero.gradle.plugin.version-catalog") version ("3.0.1")
    id("org.gradle.toolchains.foojay-resolver-convention") version "0.8.0"
}
rootProject.name = "pop-product"
val frameworkVersion: String by settings

include(
    "pop-product-common",
    "pop-product-sdk",
    "pop-product-service",
)

versionCatalogConf {
    artifactVersion = frameworkVersion
}



