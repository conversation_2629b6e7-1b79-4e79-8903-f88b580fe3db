-- 基础设置
CREATE TABLE `ref_exchange_rate_market` (
                                        `exchange_rate_market_id` bigint NOT NULL COMMENT '汇率市场数据主键ID',
                                        `base_currency` varchar(10) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '基准货币代码',
                                        `target_currency` varchar(10) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '目标货币代码',
                                        `rate` decimal(20,6) DEFAULT NULL COMMENT '汇率值',
                                        `update_time_utc` datetime DEFAULT NULL COMMENT '数据更新时间（UTC）',
                                        `next_update_time_utc` datetime DEFAULT NULL COMMENT '下次更新时间（UTC）',
                                        `creator_id` bigint NOT NULL COMMENT '创建人ID',
                                        `creator_name` varchar(50) COLLATE utf8mb4_general_ci NOT NULL COMMENT '创建人名称',
                                        `created_time` datetime NOT NULL COMMENT '创建时间',
                                        `reviser_id` bigint DEFAULT NULL COMMENT '更新人ID',
                                        `reviser_name` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '更新人名称',
                                        `revised_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最近更新时间',
                                        `is_deleted` tinyint NOT NULL DEFAULT '0' COMMENT '是否已删除',
                                        PRIMARY KEY (`exchange_rate_market_id`),
                                        KEY `idx_base_currency` (`base_currency`),
                                        KEY `idx_update_time` (`update_time_utc`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='参考汇率市场数据表';

-- 币种汇率配置表
CREATE TABLE `currency_exchange_rate` (
                                          `currency_exchange_rate_id` bigint unsigned NOT NULL COMMENT '主键',
                                          `currency_type` varchar(32) NOT NULL COMMENT '币种类型：RMB-人民币汇率，USD-美元汇率',
                                          `country_code` varchar(16) NOT NULL COMMENT '国家代码：TH-泰国，PH-菲律宾等',
                                          `exchange_rate` decimal(10,6) NOT NULL COMMENT '汇率值',
                                          `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态：0-禁用 1-启用',
                                          `effect_time` datetime NOT NULL COMMENT '生效时间',
                                          `expire_time` datetime NOT NULL COMMENT '失效时间',
                                          `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除，0-未删除，1-已删除',
                                          `creator_id` bigint DEFAULT NULL COMMENT '创建者ID',
                                          `created_time` datetime DEFAULT NULL COMMENT '创建时间',
                                          `reviser_id` bigint DEFAULT NULL COMMENT '修改者ID',
                                          `revised_time` datetime DEFAULT NULL COMMENT '修改时间',
                                          `creator_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建人名称',
                                          `reviser_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '修改人名称',
                                          PRIMARY KEY (`currency_exchange_rate_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='币种汇率配置表';

-- 产品基础价格计算规则主表
CREATE TABLE `product_basic_price_rule` (
                                            `product_basic_price_rule_id` bigint unsigned NOT NULL COMMENT '主键',
                                            `rule_type` varchar(32) COLLATE utf8mb4_general_ci NOT NULL COMMENT '规则类型：ISP等',
                                            `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态：0-禁用 1-启用',
                                            `country_codes` varchar(1000) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '适用国家代码集合，JSON数组格式：["US","CN","JP"]',
                                            `supply_type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '供给方式：1-OBM仿款, 2-AIGC, 3-数码印花',
                                            `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除，0-未删除，1-已删除',
                                            `creator_id` bigint DEFAULT NULL COMMENT '创建者ID',
                                            `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                            `reviser_id` bigint DEFAULT NULL COMMENT '修改者ID',
                                            `revised_time` datetime DEFAULT NULL COMMENT '修改时间',
                                            `creator_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建人名称',
                                            `reviser_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '修改人名称',
                                            PRIMARY KEY (`product_basic_price_rule_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='产品基础价格计算规则主表';

CREATE TABLE `product_basic_price_rule_detail` (
                                                   `product_basic_price_rule_detail_id` bigint unsigned NOT NULL COMMENT '主键',
                                                   `product_basic_price_rule_id` bigint unsigned NOT NULL COMMENT '规则主表ID',
                                                   `price_type` varchar(32) COLLATE utf8mb4_general_ci NOT NULL COMMENT '价格类型：竞品价/供货价等',
                                                   `operator` varchar(32) COLLATE utf8mb4_general_ci NOT NULL COMMENT '运算符：+,-,*,/,=',
                                                   `operand_type` varchar(32) COLLATE utf8mb4_general_ci NOT NULL COMMENT '运算数类型：FIXED-固定值，PERCENT-百分比',
                                                   `operand_value` decimal(10,4) DEFAULT NULL COMMENT '运算数值',
                                                   `round_decimal` int NOT NULL DEFAULT '2' COMMENT '保留小数位数',
                                                   `sequence` int NOT NULL DEFAULT '0' COMMENT '计算顺序',
                                                   `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除，0-未删除，1-已删除',
                                                   `creator_id` bigint DEFAULT NULL COMMENT '创建者ID',
                                                   `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                                   `reviser_id` bigint DEFAULT NULL COMMENT '修改者ID',
                                                   `revised_time` datetime DEFAULT NULL COMMENT '修改时间',
                                                   `creator_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建人名称',
                                                   `reviser_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '修改人名称',
                                                   PRIMARY KEY (`product_basic_price_rule_detail_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='产品基础价格计算规则明细表';

-- 定价规则组
CREATE TABLE `product_price_rule_group` (
                                            `product_price_rule_group_id` bigint NOT NULL COMMENT '主键',
                                            `group_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '规则组名称',
                                            `supply_type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '供给方式：1-OBM仿款, 2-AIGC, 3-数码印花',
                                            `creator_id` bigint DEFAULT NULL COMMENT '创建人id',
                                            `creator_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建人名称',
                                            `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                            `reviser_id` bigint DEFAULT NULL COMMENT '最近修改者ID',
                                            `reviser_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '最近修改者',
                                            `revised_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最近更新时间',
                                            `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '逻辑删除 0 否 1是',
                                            PRIMARY KEY (`product_price_rule_group_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='产品定价规则组表';

CREATE TABLE `product_price_rule` (
                                      `product_price_rule_id` bigint NOT NULL COMMENT '主键',
                                      `product_price_rule_group_id` bigint NOT NULL COMMENT '产品定价规则组表主键',
                                      `priority` int NOT NULL DEFAULT '0' COMMENT '优先级',
                                      `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否激活，0-禁用，1-启用',
                                      `disable_publish` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否禁止上架：1-允许上架，0-禁止上架',
                                      `creator_id` bigint DEFAULT NULL COMMENT '创建人id',
                                      `creator_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建人名称',
                                      `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                      `reviser_id` bigint DEFAULT NULL COMMENT '最近修改者ID',
                                      `reviser_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '最近修改者',
                                      `revised_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最近更新时间',
                                      `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '逻辑删除 0 否 1是',
                                      PRIMARY KEY (`product_price_rule_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='产品定价规则主表';

CREATE TABLE `product_price_condition` (
                                           `product_price_condition_id` bigint NOT NULL COMMENT '主键',
                                           `product_price_rule_id` bigint NOT NULL COMMENT '定价规则ID',
                                           `condition_field` varchar(32) COLLATE utf8mb4_general_ci NOT NULL COMMENT '条件字段：1-ISP倍率，2-SP倍率，3-供货价等',
                                           `condition_operator` varchar(32) COLLATE utf8mb4_general_ci NOT NULL COMMENT '条件运算符：1-等于，2-不等于，3-大于，4-小于，5-区间等',
                                           `condition_value` varchar(32) COLLATE utf8mb4_general_ci NOT NULL COMMENT '条件值',
                                           `range_min` decimal(10,2) DEFAULT NULL COMMENT '区间最小值',
                                           `range_max` decimal(10,2) DEFAULT NULL COMMENT '区间最大值',
                                           `logic_operator` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '逻辑运算符：1-AND, 2-OR',
                                           `sequence` int NOT NULL DEFAULT '0' COMMENT '条件顺序',
                                           `creator_id` bigint DEFAULT NULL COMMENT '创建人id',
                                           `creator_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建人名称',
                                           `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                           `reviser_id` bigint DEFAULT NULL COMMENT '最近修改者ID',
                                           `reviser_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '最近修改者',
                                           `revised_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最近更新时间',
                                           `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '逻辑删除 0 否 1是',
                                           PRIMARY KEY (`product_price_condition_id`) USING BTREE,
                                           KEY `idx_product_price_rule_id` (`product_price_rule_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='产品定价条件表';

CREATE TABLE `product_price_formula` (
                                         `product_price_formula_id` bigint NOT NULL COMMENT '主键',
                                         `product_price_rule_id` bigint NOT NULL COMMENT '定价规则ID',
                                         `target_field` varchar(32) COLLATE utf8mb4_general_ci NOT NULL COMMENT '目标字段：1-划线价，2-售价',
                                         `formula_type` varchar(32) COLLATE utf8mb4_general_ci NOT NULL COMMENT '公式类型：1-固定值，2-计算公式',
                                         `fixed_value` decimal(10,2) DEFAULT NULL COMMENT '固定值',
                                         `rounding_precision` int NOT NULL DEFAULT '-1' COMMENT '保留结果小数位数',
                                         `creator_id` bigint COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建人id',
                                         `creator_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建人名称',
                                         `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                         `reviser_id` bigint DEFAULT NULL COMMENT '最近修改者ID',
                                         `reviser_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '最近修改者',
                                         `revised_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最近更新时间',
                                         `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '逻辑删除 0 否 1是',
                                         PRIMARY KEY (`product_price_formula_id`) USING BTREE,
                                         KEY `idx_product_price_rule_id` (`product_price_rule_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='产品定价计算公式表';

CREATE TABLE `product_price_formula_detail` (
                                                `product_price_formula_detail_id` bigint NOT NULL COMMENT '主键',
                                                `product_price_formula_id` bigint NOT NULL COMMENT '定价公式ID',
                                                `operand_field` varchar(32) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '变量字段：1-供货价，2-ISP倍率，3-SP倍率等',
                                                `operand_value` decimal(10,2) DEFAULT NULL COMMENT '固定值',
                                                `operator` varchar(32) COLLATE utf8mb4_general_ci NOT NULL COMMENT '运算符：1-加，2-减，3-乘，4-除',
                                                `sequence` int NOT NULL DEFAULT '0' COMMENT '计算顺序',
                                                `creator_id` bigint DEFAULT NULL COMMENT '创建人id',
                                                `creator_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建人名称',
                                                `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                                `reviser_id` bigint DEFAULT NULL COMMENT '最近修改者ID',
                                                `reviser_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '最近修改者',
                                                `revised_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最近更新时间',
                                                `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '逻辑删除 0 否 1是',
                                                PRIMARY KEY (`product_price_formula_detail_id`) USING BTREE,
                                                KEY `idx_product_price_formula_id` (`product_price_formula_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='产品定价计算公式明细表';
