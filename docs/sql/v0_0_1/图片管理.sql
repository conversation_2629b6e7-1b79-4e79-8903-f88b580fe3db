-- 图片QC管理

CREATE TABLE `image_qc_record` (
                                   `image_qc_record_id` bigint NOT NULL COMMENT '主键ID',
                                   `main_image_url` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '主图URL',
                                   `spu_code` varchar(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'SPU编号',
                                   `qc_result` tinyint(1) NOT NULL COMMENT 'QC结果：0-不通过，1-通过',
                                   `problem_categories` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '问题分类，ops字典值，value，中文，数组多个',
                                   `problem_images` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '问题图片URL，JSON数组',
                                   `qc_uploaded_images` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT 'QC上传图片，JSON数组',
                                   `remark` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注',
                                   `repair_status` tinyint(1) DEFAULT NULL COMMENT '返修状态：0-未返修，1-已返修',
                                   `repair_complete_time` datetime DEFAULT NULL COMMENT '返修完成时间',
                                   `creator_id` bigint NOT NULL COMMENT '创建人id/QC人',
                                   `creator_name` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '创建人/QC人名称',
                                   `created_time` datetime NOT NULL COMMENT '创建/QC时间',
                                   `reviser_id` bigint DEFAULT NULL COMMENT '最近修改者ID',
                                   `reviser_name` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '最近修改者',
                                   `revised_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最近更新时间',
                                   `company_id` bigint DEFAULT NULL COMMENT '租户ID',
                                   `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '逻辑删除 0 否 1是',
                                   `image_repo_creator_id` bigint DEFAULT NULL COMMENT '图片仓库创建人ID',
                                   `image_repo_creator_name` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '图片仓库创建人名称',
                                   `image_repo_created_time` datetime DEFAULT NULL COMMENT '图片仓库创建时间',
                                   PRIMARY KEY (`image_qc_record_id`),
                                   KEY `idx_spu_code` (`spu_code`),
                                   KEY `idx_created_time` (`created_time`),
                                   KEY `idx_repair_status` (`repair_status`),
                                   KEY `idx_creator_name` (`creator_name`) USING BTREE,
                                   KEY `idx_image_repo_creator_name` (`image_repo_creator_name`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='图片QC管理表';

-- 图片管理

CREATE TABLE `image_repository` (
                                    `image_repository_id` bigint NOT NULL COMMENT '主键ID',
                                    `spu_code` varchar(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'spu编码',
                                    `main_url` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '主图',
                                    `image_urls` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '图片集合',
                                    `is_update` tinyint(1) DEFAULT '0' COMMENT '是否更新【1更新；0或者空为无更新】',
                                    `lazada_image_url` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT 'lazada图片地址',
                                    `qc_status` tinyint(1) DEFAULT '0' COMMENT 'QC状态【0未质检；1已质检】',
                                    `creator_id` bigint DEFAULT NULL COMMENT '创建人id',
                                    `creator_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建人名称',
                                    `created_time` datetime DEFAULT NULL COMMENT '创建时间',
                                    `reviser_id` bigint DEFAULT NULL COMMENT '最近修改者ID',
                                    `reviser_name` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '最近修改者',
                                    `revised_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最近更新时间;新增时取创建时间',
                                    `company_id` bigint DEFAULT NULL COMMENT '租户ID',
                                    `is_deleted` tinyint(1) DEFAULT NULL COMMENT '逻辑删除 0 否 1是',
                                    PRIMARY KEY (`image_repository_id`),
                                    UNIQUE KEY `spu_uniq_index` (`spu_code`) USING BTREE,
                                    KEY `idx_qc_status` (`qc_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='图片库管理';
