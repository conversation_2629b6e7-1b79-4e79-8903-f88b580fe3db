CREATE TABLE `lazada_category`
(
    `lazada_category_id` bigint       not null comment '主键ID',
    `language`           varchar(100) null comment 'language',
    `category_data`      mediumtext   null comment 'lzd category',
    `country`            varchar(20)  null comment '国家简码',
    `company_id`         bigint       null comment '租户id',
    `creator_id`         bigint       NOT NULL COMMENT '创建人id',
    `creator_name`       varchar(32)  NOT NULL COMMENT '创建人名称',
    `created_time`       datetime     NOT NULL COMMENT '创建时间',
    `reviser_id`         bigint                DEFAULT NULL COMMENT '最近修改人id',
    `reviser_name`       varchar(32)           DEFAULT NULL COMMENT '最近修改人名称',
    `revised_time`       datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最近更新时间',
    `is_deleted`         tinyint(1)   NOT NULL COMMENT '是否已删除;0未删除，1已删除',
    PRIMARY KEY (`lazada_category_id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='Lazada品类表';