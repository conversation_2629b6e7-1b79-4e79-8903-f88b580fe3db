# show create table planning_info;
# show create table planning_summary;
# show create table planning_develop_rhythm;
# show create table planning_publish_rhythm;
# show create table planning_category_price;
# show create table planning_update_log;
# show create table cargo_tray_info;


CREATE TABLE `planning_info`
(
    `planning_id`      bigint      NOT NULL COMMENT '企划id',
    `planning_name`    varchar(64) NOT NULL COMMENT '企划名称',
    `channel_id`       bigint      NOT NULL COMMENT '渠道id',
    `channel_name`     varchar(64) NOT NULL COMMENT '渠道name',
    `platform_id`      bigint      NOT NULL COMMENT '平台id',
    `platform_name`    varchar(64) NOT NULL COMMENT '平台name',
    `applicable_month` varchar(64) NOT NULL COMMENT '适用月份:2024-11',
    `creator_id`       bigint      NOT NULL COMMENT '创建人id',
    `creator_name`     varchar(32) NOT NULL COMMENT '创建人名称',
    `created_time`     datetime    NOT NULL COMMENT '创建时间',
    `reviser_id`       bigint               DEFAULT NULL COMMENT '最近修改人id',
    `reviser_name`     varchar(32)          DEFAULT NULL COMMENT '最近修改人名称',
    `revised_time`     datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最近更新时间',
    `is_deleted`       tinyint(1)  NOT NULL COMMENT '是否已删除;0未删除，1已删除',
    PRIMARY KEY (`planning_id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='企划信息表';

CREATE TABLE `planning_summary`
(
    `summary_id`        bigint       NOT NULL COMMENT '企划汇总id',
    `planning_id`       bigint       NOT NULL COMMENT '企划id',
    `shop_id`           bigint       NOT NULL COMMENT '店铺id',
    `shop_name`         varchar(255) NOT NULL COMMENT '店铺name',
    `country_site_code` varchar(255) NOT NULL COMMENT '国家站点code',
    `country_site_name` varchar(255) NOT NULL COMMENT '国家站点name',
    `supply_mode_code`  varchar(255) NOT NULL COMMENT '供给方式code',
    `supply_mode_name`  varchar(255) NOT NULL COMMENT '供给方式name',
    `supply_quantity`   int          NOT NULL COMMENT '供给数量',
    `creator_id`        bigint       NOT NULL COMMENT '创建人id',
    `creator_name`      varchar(32)  NOT NULL COMMENT '创建人名称',
    `created_time`      datetime     NOT NULL COMMENT '创建时间',
    `reviser_id`        bigint                DEFAULT NULL COMMENT '最近修改人id',
    `reviser_name`      varchar(32)           DEFAULT NULL COMMENT '最近修改人名称',
    `revised_time`      datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最近更新时间',
    `is_deleted`        tinyint(1)   NOT NULL COMMENT '是否已删除;0未删除，1已删除',
    PRIMARY KEY (`summary_id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='企划汇总表';

CREATE TABLE `planning_develop_rhythm`
(
    `develop_rhythm_id` bigint       NOT NULL COMMENT '企划开发节奏id',
    `planning_id`       bigint       NOT NULL COMMENT '企划id',
    `shop_id`           bigint       NOT NULL COMMENT '店铺id',
    `shop_name`         varchar(255) NOT NULL COMMENT '店铺name',
    `country_site_code` varchar(255) NOT NULL COMMENT '国家站点code',
    `country_site_name` varchar(255) NOT NULL COMMENT '国家站点name',
    `supply_mode_code`  varchar(255) NOT NULL COMMENT '供给方式code',
    `supply_mode_name`  varchar(255) NOT NULL COMMENT '供给方式name',
    `publish_date`      date         NOT NULL COMMENT '上架日期',
    `wave_batch_code`   varchar(255) NOT NULL COMMENT '波次编号',
    `supply_quantity`   int          NOT NULL COMMENT '供给数量',
    `creator_id`        bigint       NOT NULL COMMENT '创建人id',
    `creator_name`      varchar(32)  NOT NULL COMMENT '创建人名称',
    `created_time`      datetime     NOT NULL COMMENT '创建时间',
    `reviser_id`        bigint                DEFAULT NULL COMMENT '最近修改人id',
    `reviser_name`      varchar(32)           DEFAULT NULL COMMENT '最近修改人名称',
    `revised_time`      datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最近更新时间',
    `is_deleted`        tinyint(1)   NOT NULL COMMENT '是否已删除;0未删除，1已删除',
    PRIMARY KEY (`develop_rhythm_id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='企划开发节奏表';

CREATE TABLE `planning_publish_rhythm`
(
    `publish_rhythm_id` bigint       NOT NULL COMMENT '企划上架节奏id',
    `planning_id`       bigint       NOT NULL COMMENT '企划id',
    `shop_id`           bigint       NOT NULL COMMENT '店铺id',
    `shop_name`         varchar(255) NOT NULL COMMENT '店铺name',
    `country_site_code` varchar(255) NOT NULL COMMENT '国家站点code',
    `country_site_name` varchar(255) NOT NULL COMMENT '国家站点name',
    `supply_mode_code`  varchar(255) NOT NULL COMMENT '供给方式code',
    `supply_mode_name`  varchar(255) NOT NULL COMMENT '供给方式name',
    `publish_date`      date         NOT NULL COMMENT '上架日期',
    `wave_batch_code`   varchar(255) NOT NULL COMMENT '波次编号',
    `supply_quantity`   int          NOT NULL COMMENT '供给数量',
    `creator_id`        bigint       NOT NULL COMMENT '创建人id',
    `creator_name`      varchar(32)  NOT NULL COMMENT '创建人名称',
    `created_time`      datetime     NOT NULL COMMENT '创建时间',
    `reviser_id`        bigint                DEFAULT NULL COMMENT '最近修改人id',
    `reviser_name`      varchar(32)           DEFAULT NULL COMMENT '最近修改人名称',
    `revised_time`      datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最近更新时间',
    `is_deleted`        tinyint(1)   NOT NULL COMMENT '是否已删除;0未删除，1已删除',
    PRIMARY KEY (`publish_rhythm_id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='企划上架节奏表';

CREATE TABLE `planning_category_price`
(
    `category_price_id`          bigint       NOT NULL COMMENT '品类价格id',
    `planning_id`                bigint       NOT NULL COMMENT '企划id',
    `shop_id`                    bigint       NOT NULL COMMENT '店铺id',
    `shop_name`                  varchar(255) NOT NULL COMMENT '店铺name',
    `country_site_code`          varchar(255) NOT NULL COMMENT '国家站点code',
    `country_site_name`          varchar(255) NOT NULL COMMENT '国家站点name',
    `supply_mode_code`           varchar(255) NOT NULL COMMENT '供给方式code',
    `supply_mode_name`           varchar(255) NOT NULL COMMENT '供给方式name',
    `first_level_category_code`  varchar(255) NOT NULL COMMENT '一级品类code',
    `first_level_category_name`  varchar(255) NOT NULL COMMENT '一级品类name',
    `second_level_category_code` varchar(255) NOT NULL COMMENT '二级品类icode',
    `second_level_category_name` varchar(255) NOT NULL COMMENT '二级品类name',
    `last_level_category_code`   varchar(255) NOT NULL COMMENT '末级品类code',
    `last_level_category_name`   varchar(255) NOT NULL COMMENT '末级品类name',
    `price_band_code`            varchar(255) NOT NULL COMMENT '价格带code',
    `price_band_name`            varchar(255) NOT NULL COMMENT '价格带name',
    `supply_quantity`            int          NOT NULL COMMENT '供给数量',
    `creator_id`                 bigint       NOT NULL COMMENT '创建人id',
    `creator_name`               varchar(32)  NOT NULL COMMENT '创建人名称',
    `created_time`               datetime     NOT NULL COMMENT '创建时间',
    `reviser_id`                 bigint                DEFAULT NULL COMMENT '最近修改人id',
    `reviser_name`               varchar(32)           DEFAULT NULL COMMENT '最近修改人名称',
    `revised_time`               datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最近更新时间',
    `is_deleted`                 tinyint(1)   NOT NULL COMMENT '是否已删除;0未删除，1已删除',
    PRIMARY KEY (`category_price_id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='企划品类价格表';

CREATE TABLE `planning_update_log`
(
    `log_id`         bigint       NOT NULL COMMENT '日志id',
    `planning_id`    bigint       NOT NULL COMMENT '企划id',
    `update_page`    varchar(255) NOT NULL COMMENT '更新页面',
    `update_message` varchar(255) NOT NULL COMMENT '更新说明',
    `creator_id`     bigint       NOT NULL COMMENT '创建人id',
    `creator_name`   varchar(32)  NOT NULL COMMENT '创建人名称',
    `created_time`   datetime     NOT NULL COMMENT '创建时间',
    `reviser_id`     bigint                DEFAULT NULL COMMENT '最近修改人id',
    `reviser_name`   varchar(32)           DEFAULT NULL COMMENT '最近修改人名称',
    `revised_time`   datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最近更新时间',
    `is_deleted`     tinyint(1)   NOT NULL COMMENT '是否已删除;0未删除，1已删除',
    PRIMARY KEY (`log_id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='企划更新日志';

CREATE TABLE `cargo_tray_info`
(
    `cargo_tray_id`   bigint       NOT NULL COMMENT '货盘id',
    `planning_id`     bigint       NOT NULL COMMENT '企划id',
    `cargo_tray_code` varchar(255) NOT NULL COMMENT '货盘类型',
    `quantity`        int          NOT NULL COMMENT '货盘数量',
    `creator_id`      bigint       NOT NULL COMMENT '创建人id',
    `creator_name`    varchar(32)  NOT NULL COMMENT '创建人名称',
    `created_time`    datetime     NOT NULL COMMENT '创建时间',
    `reviser_id`      bigint                DEFAULT NULL COMMENT '最近修改人id',
    `reviser_name`    varchar(32)           DEFAULT NULL COMMENT '最近修改人名称',
    `revised_time`    datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最近更新时间',
    `is_deleted`      tinyint(1)   NOT NULL COMMENT '是否已删除;0未删除，1已删除',
    PRIMARY KEY (`cargo_tray_id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='货盘信息';