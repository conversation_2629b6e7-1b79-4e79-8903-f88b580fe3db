ALTER TABLE `pop_product`.`product`
    ADD COLUMN `pricing_type` int(1) NULL COMMENT '定价类型【1按返单规则；2不返单规则 】' AFTER `shop_id`,
    ADD COLUMN `spot_type` int(1) NULL COMMENT '现货类型【1现货try on ；2ifashion】' AFTER `pricing_type`;

ALTER TABLE `pop_product`.`product_skc`
    ADD COLUMN `purchase_price` decimal(10, 2) NULL COMMENT '采购价格' AFTER `local_price`,
    ADD COLUMN `cost_price` decimal(10, 2) NULL COMMENT '定价成本' AFTER `purchase_price`;
ALTER TABLE `pop_product`.`product`
    ADD COLUMN `spot_type_code` varchar(80) NULL COMMENT '现货类型OPS编码' AFTER `fabric_name`;

ALTER TABLE `pop_product`.`product_skc`
    ADD COLUMN `import_flag` int(1) NULL COMMENT '导入标识【1为导入】' AFTER `cost_price`;

alter table product
    add column tag_available_inv INT(1) default 0 COMMENT '标签(1是 0否):可用库存' after category_id,
    add column tag_reg_clear     INT(1) default 0 COMMENT '标签(1是 0否):常规清仓' after tag_available_inv;


CREATE TABLE `product_stock_info`
(
    `id`                    BIGINT(20)   NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `spu_code`              VARCHAR(255) NOT NULL COMMENT 'SPU编码',
    `skc_code`              VARCHAR(255) NOT NULL COMMENT 'SKC编码',
    `size_group`            VARCHAR(255) NOT NULL COMMENT '尺码组',
    `size`                  VARCHAR(255) NOT NULL COMMENT '尺码',
    `color`                 VARCHAR(255) NOT NULL COMMENT '颜色',
    `barcode`               VARCHAR(255) NOT NULL COMMENT '条码/商品编码',
    `stock_quantity`        INT(9)       NOT NULL default 0 COMMENT '库存数量',
    `on_way_stock_quantity` INT(9)       NOT NULL default 0 COMMENT '在途库存',
    `usable_stock_quantity` INT(9)       NOT NULL default 0 COMMENT '可用库存',
    `occupy_stock_quantity` INT(9)       NOT NULL default 0 COMMENT '占用库存(订单占用-销售出库数量)',
    `creator_id`            bigint       NOT NULL COMMENT '创建人id',
    `creator_name`          varchar(32)  NOT NULL COMMENT '创建人名称',
    `created_time`          datetime     NOT NULL COMMENT '创建时间',
    `reviser_id`            bigint                DEFAULT NULL COMMENT '最近修改人id',
    `reviser_name`          varchar(32)           DEFAULT NULL COMMENT '最近修改人名称',
    `revised_time`          datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最近更新时间',
    `is_deleted`            tinyint(1)   NOT NULL COMMENT '是否已删除;0未删除，1已删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uniq_uk_barcode` (`barcode`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='商品库存信息表';
ALTER TABLE pop_product.platform_product_pull_task ADD spu_code varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT 'spu编码' after product_id;

ALTER TABLE `sale_sku`
    ADD COLUMN `last_sale_price` decimal(13,2) DEFAULT NULL COMMENT '上次销售价' AFTER `sale_price`,
    ADD COLUMN `last_retail_price` decimal(13,2) DEFAULT NULL COMMENT '上次常规划线价' AFTER `retail_price`,
    ADD COLUMN `purchase_sale_price` decimal(13,2) DEFAULT NULL COMMENT '采购销售价' AFTER `last_retail_price`,
    ADD COLUMN `purchase_retail_price` decimal(13,2) DEFAULT NULL COMMENT '采购划线价' AFTER `purchase_sale_price`,
    ADD COLUMN `regular_sale_price` decimal(13,2) DEFAULT NULL COMMENT '常规销售价' AFTER `purchase_retail_price`,
    ADD COLUMN `regular_retail_price` decimal(13,2) DEFAULT NULL COMMENT '常规划线价' AFTER `regular_sale_price`;

CREATE TABLE `fee_configuration` (
                                     `fee_config_id` bigint unsigned NOT NULL COMMENT '主键',
                                     `country_code` varchar(16) NOT NULL COMMENT '国家代码：TH-泰国，PH-菲律宾等',
                                     `fee_type` varchar(32) NOT NULL COMMENT '费用类型：LOGISTICS-物流费用等',
                                     `fee_amount` decimal(16,6) NOT NULL COMMENT '费用金额',
                                     `currency_type` varchar(32) NOT NULL COMMENT '币种类型：CNY-人民币，USD-美元等',
                                     `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除，0-未删除，1-已删除',
                                     `creator_id` bigint DEFAULT NULL COMMENT '创建者ID',
                                     `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                     `reviser_id` bigint DEFAULT NULL COMMENT '修改者ID',
                                     `revised_time` datetime DEFAULT NULL COMMENT '修改时间',
                                     `creator_name` varchar(50) DEFAULT NULL COMMENT '创建人名称',
                                     `reviser_name` varchar(50) DEFAULT NULL COMMENT '修改人名称',
                                     PRIMARY KEY (`fee_config_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='费用配置表';

CREATE TABLE `category_price_range` (
                                        `category_price_range_id` bigint unsigned NOT NULL COMMENT '主键',
                                        `category_code` varchar(100) COLLATE utf8mb4_general_ci NOT NULL COMMENT '品类编码（多级-分割）',
                                        `category_name` varchar(120) COLLATE utf8mb4_general_ci NOT NULL COMMENT '品类名称（多级-分割）',
                                        `cost_amount` decimal(16,6) NOT NULL COMMENT '成本金额',
                                        `condition_operator` varchar(32) COLLATE utf8mb4_general_ci NOT NULL COMMENT '条件运算符：EQ-等于，GT-大于，GTE-大于等于，LT-小于，LTE-小于等于，BETWEEN-区间等',

                                        `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除，0-未删除，1-已删除',
                                        `creator_id` bigint DEFAULT NULL COMMENT '创建者ID',
                                        `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                        `reviser_id` bigint DEFAULT NULL COMMENT '修改者ID',
                                        `revised_time` datetime DEFAULT NULL COMMENT '修改时间',
                                        `creator_name` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建人名称',
                                        `reviser_name` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '修改人名称',
                                        PRIMARY KEY (`category_price_range_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='品类价格计算区间表';



alter table pop_product.base_size add column `tenant_id` bigint NULL COMMENT '租户id' ;
alter table pop_product.base_size_group add column `tenant_id` bigint NULL COMMENT '租户id' ;
alter table pop_product.base_sku_info add column `tenant_id` bigint NULL COMMENT '租户id' ;
alter table pop_product.base_sku_info_copy1 add column `tenant_id` bigint NULL COMMENT '租户id' ;
alter table pop_product.basic_product add column `tenant_id` bigint NULL COMMENT '租户id' ;
alter table pop_product.cargo_tray_info add column `tenant_id` bigint NULL COMMENT '租户id' ;
alter table pop_product.category_price_range add column `tenant_id` bigint NULL COMMENT '租户id' ;
alter table pop_product.currency_exchange_rate add column `tenant_id` bigint NULL COMMENT '租户id' ;
alter table pop_product.download_task add column `tenant_id` bigint NULL COMMENT '租户id' ;
alter table pop_product.error_order_info add column `tenant_id` bigint NULL COMMENT '租户id' ;
alter table pop_product.error_order_spu_info add column `tenant_id` bigint NULL COMMENT '租户id' ;
alter table pop_product.fee_configuration add column `tenant_id` bigint NULL COMMENT '租户id' ;
alter table pop_product.fetch_product_parmas add column `tenant_id` bigint NULL COMMENT '租户id' ;
alter table pop_product.history_handle_result add column `tenant_id` bigint NULL COMMENT '租户id' ;
alter table pop_product.history_publish_record add column `tenant_id` bigint NULL COMMENT '租户id' ;
alter table pop_product.image_qc_record add column `tenant_id` bigint NULL COMMENT '租户id' ;
alter table pop_product.image_repository add column `tenant_id` bigint NULL COMMENT '租户id' ;
alter table pop_product.jst_barcode add column `tenant_id` bigint NULL COMMENT '租户id' ;
alter table pop_product.lazada_brand add column `tenant_id` bigint NULL COMMENT '租户id' ;
alter table pop_product.lazada_category add column `tenant_id` bigint NULL COMMENT '租户id' ;
alter table pop_product.lazada_original_product_sku add column `tenant_id` bigint NULL COMMENT '租户id' ;
alter table pop_product.lazada_original_product_spu add column `tenant_id` bigint NULL COMMENT '租户id' ;
alter table pop_product.message_consumer_record add column `tenant_id` bigint NULL COMMENT '租户id' ;
alter table pop_product.message_record add column `tenant_id` bigint NULL COMMENT '租户id' ;
alter table pop_product.planning_category_price add column `tenant_id` bigint NULL COMMENT '租户id' ;
alter table pop_product.planning_develop_rhythm add column `tenant_id` bigint NULL COMMENT '租户id' ;
alter table pop_product.planning_info add column `tenant_id` bigint NULL COMMENT '租户id' ;
alter table pop_product.planning_preset_category add column `tenant_id` bigint NULL COMMENT '租户id' ;
alter table pop_product.planning_publish_rhythm add column `tenant_id` bigint NULL COMMENT '租户id' ;
alter table pop_product.planning_summary add column `tenant_id` bigint NULL COMMENT '租户id' ;
alter table pop_product.planning_update_log add column `tenant_id` bigint NULL COMMENT '租户id' ;
alter table pop_product.platform_product_pull_task add column `tenant_id` bigint NULL COMMENT '租户id' ;
alter table pop_product.product_attributes add column `tenant_id` bigint NULL COMMENT '租户id' ;
alter table pop_product.product_bar_code add column `tenant_id` bigint NULL COMMENT '租户id' ;
alter table pop_product.product_barcode add column `tenant_id` bigint NULL COMMENT '租户id' ;
alter table pop_product.product_basic_price_rule add column `tenant_id` bigint NULL COMMENT '租户id' ;
alter table pop_product.product_basic_price_rule_detail add column `tenant_id` bigint NULL COMMENT '租户id' ;
alter table pop_product.product_change_failure add column `tenant_id` bigint NULL COMMENT '租户id' ;
alter table pop_product.product_operate_log add column `tenant_id` bigint NULL COMMENT '租户id' ;
alter table pop_product.product_picture add column `tenant_id` bigint NULL COMMENT '租户id' ;
alter table pop_product.product_price_condition add column `tenant_id` bigint NULL COMMENT '租户id' ;
alter table pop_product.product_price_formula add column `tenant_id` bigint NULL COMMENT '租户id' ;
alter table pop_product.product_price_formula_detail add column `tenant_id` bigint NULL COMMENT '租户id' ;
alter table pop_product.product_price_rule add column `tenant_id` bigint NULL COMMENT '租户id' ;
alter table pop_product.product_price_rule_group add column `tenant_id` bigint NULL COMMENT '租户id' ;
alter table pop_product.product_skc add column `tenant_id` bigint NULL COMMENT '租户id' ;
alter table pop_product.product_source_data add column `tenant_id` bigint NULL COMMENT '租户id' ;
alter table pop_product.product_stock_info add column `tenant_id` bigint NULL COMMENT '租户id' ;
alter table pop_product.product_sync_log add column `tenant_id` bigint NULL COMMENT '租户id' ;
alter table pop_product.prototype_basic add column `tenant_id` bigint NULL COMMENT '租户id' ;
alter table pop_product.publish_attribute add column `tenant_id` bigint NULL COMMENT '租户id' ;
alter table pop_product.publish_attribute_group add column `tenant_id` bigint NULL COMMENT '租户id' ;
alter table pop_product.publish_attribute_value add column `tenant_id` bigint NULL COMMENT '租户id' ;
alter table pop_product.publish_category add column `tenant_id` bigint NULL COMMENT '租户id' ;
alter table pop_product.publish_category_attr add column `tenant_id` bigint NULL COMMENT '租户id' ;
alter table pop_product.publish_category_mapping add column `tenant_id` bigint NULL COMMENT '租户id' ;
alter table pop_product.publish_channel add column `tenant_id` bigint NULL COMMENT '租户id' ;
alter table pop_product.publish_platform add column `tenant_id` bigint NULL COMMENT '租户id' ;
alter table pop_product.publish_platform_attr add column `tenant_id` bigint NULL COMMENT '租户id' ;
alter table pop_product.publish_platform_attr_value add column `tenant_id` bigint NULL COMMENT '租户id' ;
alter table pop_product.ref_exchange_rate_market add column `tenant_id` bigint NULL COMMENT '租户id' ;
alter table pop_product.sale_goods add column `tenant_id` bigint NULL COMMENT '租户id' ;
alter table pop_product.sale_sku add column `tenant_id` bigint NULL COMMENT '租户id' ;
alter table pop_product.shop add column `tenant_id` bigint NULL COMMENT '租户id' ;
alter table pop_product.shop_seller_mapping add column `tenant_id` bigint NULL COMMENT '租户id' ;
alter table pop_product.size_map add column `tenant_id` bigint NULL COMMENT '租户id' ;
alter table pop_product.size_template add column `tenant_id` bigint NULL COMMENT '租户id' ;
alter table pop_product.sku_code_rule add column `tenant_id` bigint NULL COMMENT '租户id' ;
alter table pop_product.sku_rule_element add column `tenant_id` bigint NULL COMMENT '租户id' ;


CREATE TABLE `task_info`
(
    `task_id`      bigint       NOT NULL COMMENT '业务id',
    `task_name`    varchar(255) NOT NULL COMMENT '任务名称',
    `task_type`    tinyint(1)   NOT NULL COMMENT '任务类型,1-拉取Lazada商品,2-同步Lazada商品,3-更新商品标题,4-更新商品库存/价格',
    `task_status`  tinyint(1)   NOT NULL COMMENT '任务状态,1-排队中,2-进行中,3-已完成,-1-失败,0-已取消',
    `retry_count`  int                   DEFAULT '0' COMMENT '重试次数',
    `error_reason` text COMMENT '错误原因',
    `creator_id`   bigint       NOT NULL COMMENT '创建人id',
    `creator_name` varchar(32)  NOT NULL COMMENT '创建人名称',
    `created_time` datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `reviser_id`   bigint                DEFAULT NULL COMMENT '最近修改人id',
    `reviser_name` varchar(32)           DEFAULT NULL COMMENT '最近修改人名称',
    `revised_time` datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最近更新时间',
    `is_deleted`   tinyint(1)   NOT NULL DEFAULT '0' COMMENT '是否已删除;0未删除，1已删除',
    PRIMARY KEY (`task_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='通用任务表';

CREATE TABLE `product_batch_update_price_inventory_task`
(
    `task_id`                bigint      NOT NULL AUTO_INCREMENT COMMENT '主键',
    `shop_name`              varchar(255)         DEFAULT NULL COMMENT '店铺',
    `country`                varchar(255)         DEFAULT NULL COMMENT '国家站点',
    `spu_code`               varchar(255)         DEFAULT NULL COMMENT 'SPU',
    `skc_code`               varchar(255)         DEFAULT NULL COMMENT 'SKC',
    `color`                  varchar(255)         DEFAULT NULL COMMENT '颜色',
    `size`                   varchar(255)         DEFAULT NULL COMMENT '尺码',
    `publish_state`          varchar(255)         DEFAULT NULL COMMENT '状态',
    `tags`                   varchar(255)         DEFAULT NULL COMMENT '标签',
    `supply_price`           decimal(19, 4)       DEFAULT NULL COMMENT '供货价',
    `purchase_price`         decimal(19, 4)       DEFAULT NULL COMMENT '采购价',
    `pricing_cost`           decimal(19, 4)       DEFAULT NULL COMMENT '定价成本',
    `retail_price`           decimal(19, 4)       DEFAULT NULL COMMENT '划线价',
    `sale_price`             decimal(19, 4)       DEFAULT NULL COMMENT '售价',
    `regular_strike_price`   decimal(19, 4)       DEFAULT NULL COMMENT '常规划线价',
    `regular_selling_price`  decimal(19, 4)       DEFAULT NULL COMMENT '常规售价',
    `purchase_strike_price`  decimal(19, 4)       DEFAULT NULL COMMENT '采购价划线价',
    `purchase_selling_price` decimal(19, 4)       DEFAULT NULL COMMENT '采购价售价',
    `stock`                  int                  DEFAULT NULL COMMENT '库存',
    `physical_stock`         int                  DEFAULT NULL COMMENT '实物可用库存',
    `creator_id`             bigint      NOT NULL COMMENT '创建人id',
    `creator_name`           varchar(32) NOT NULL COMMENT '创建人名称',
    `created_time`           datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `reviser_id`             bigint               DEFAULT NULL COMMENT '最近修改人id',
    `reviser_name`           varchar(32)          DEFAULT NULL COMMENT '最近修改人名称',
    `revised_time`           datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最近更新时间',
    `is_deleted`             tinyint(1)  NOT NULL DEFAULT '0' COMMENT '是否已删除;0未删除，1已删除',
    PRIMARY KEY (`task_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='商品批量更新-价格/库存任务';

CREATE TABLE `product_batch_update_title_task`
(
    `task_id`      bigint      NOT NULL AUTO_INCREMENT COMMENT '主键',
    `shop_name`    varchar(255)         DEFAULT NULL COMMENT '店铺',
    `country`      varchar(255)         DEFAULT NULL COMMENT '国家站点',
    `spu_code`     varchar(255)         DEFAULT NULL COMMENT 'SPU',
    `title`        varchar(255)         DEFAULT NULL COMMENT '标题',
    `creator_id`   bigint      NOT NULL COMMENT '创建人id',
    `creator_name` varchar(32) NOT NULL COMMENT '创建人名称',
    `created_time` datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `reviser_id`   bigint               DEFAULT NULL COMMENT '最近修改人id',
    `reviser_name` varchar(32)          DEFAULT NULL COMMENT '最近修改人名称',
    `revised_time` datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最近更新时间',
    `is_deleted`   tinyint(1)  NOT NULL DEFAULT '0' COMMENT '是否已删除;0未删除，1已删除',
    PRIMARY KEY (`task_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='商品批量更新-标题任务';

CREATE TABLE `import_product_record` (
                                         `import_product_record_id` bigint NOT NULL COMMENT '主键',
                                         `supply_mode` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '供给方式',
                                         `skc` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'skc',
                                         `spu_code` varchar(200) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'spu编码',
                                         `process_state` int DEFAULT '1' COMMENT '处理进度【1排队中；2处理中；3处理成功；4处理失败】',
                                         `import_data` text COLLATE utf8mb4_general_ci COMMENT '导入源数据',
                                         `error_msg` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '错误信息',
                                         `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否已删除;0未删除，1已删除',
                                         `tenant_id` bigint DEFAULT NULL COMMENT '租户ID',
                                         `creator_id` bigint NOT NULL COMMENT '创建人id',
                                         `creator_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '创建人',
                                         `created_time` datetime NOT NULL COMMENT '创建时间',
                                         `reviser_id` bigint DEFAULT NULL COMMENT '最近修改人id',
                                         `reviser_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '最近修改人名称',
                                         `revised_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                         PRIMARY KEY (`import_product_record_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='商品导入记录';

INSERT INTO pop_product.category_price_range (category_price_range_id,category_code,category_name,cost_amount,condition_operator,is_deleted,creator_id,created_time,reviser_id,revised_time,creator_name,reviser_name,tenant_id) VALUES
                                                                                                                                                                                                                                     (7293798845074472967,'01-0101-010101','女装-上装类-衬衫',26.000000,'GT',0,154441261,'2025-02-28 10:03:39',154613988,'2025-03-03 15:47:05','潘希赛','黄珊',2),
                                                                                                                                                                                                                                     (7293807890556616705,'01-0101-010102','女装-上装类-T恤',16.000000,'GT',0,190677529,'2025-02-28 10:39:36',154613988,'2025-03-03 15:47:05','颜佳明','黄珊',2),
                                                                                                                                                                                                                                     (7293807891294814210,'01-0103-010301','女装-裙装类-连衣裙',32.000000,'GT',1,190677529,'2025-02-28 10:39:36',600093064,'2025-03-01 09:48:34','颜佳明','张振东',2),
                                                                                                                                                                                                                                     (7294260153992515589,'01-0102-010202','女装-下装类-牛仔裤',66.000000,'GT',1,154441261,'2025-03-01 16:36:44',154441261,'2025-03-01 16:36:44','潘希赛','潘希赛',2),
                                                                                                                                                                                                                                     (7294260153996709894,'01-0103-010301','女装-裙装类-连衣裙',66.000000,'LT',1,154441261,'2025-03-01 16:36:44',154441261,'2025-03-01 16:38:02','潘希赛','潘希赛',2),
                                                                                                                                                                                                                                     (7294260230500647094,'01-0103-010301','女装-裙装类-连衣裙',66.000000,'GT',0,154441261,'2025-03-01 16:37:02',154613988,'2025-03-03 15:47:05','潘希赛','黄珊',2),
                                                                                                                                                                                                                                     (7294972435018948811,'01-0102-010202','女装-下装类-牛仔裤',1.000000,'GT',0,154613988,'2025-03-03 15:47:05',154613988,'2025-03-03 15:47:05','黄珊','黄珊',2);

INSERT INTO pop_product.fee_configuration (fee_config_id,country_code,fee_type,fee_amount,currency_type,is_deleted,creator_id,created_time,reviser_id,revised_time,creator_name,reviser_name,tenant_id) VALUES
                                                                                                                                                                                                            (7293798726958678017,'TH','LOGISTICS',1.200000,'USD',0,600093064,'2025-02-28 10:03:11',600093064,'2025-02-28 10:03:11','张振东','张振东',2),
                                                                                                                                                                                                            (7293798726971260930,'PH','LOGISTICS',1.600000,'USD',0,600093064,'2025-02-28 10:03:11',600093064,'2025-02-28 10:03:11','张振东','张振东',2),
                                                                                                                                                                                                            (7293798726975455235,'SG','LOGISTICS',2.300000,'USD',0,600093064,'2025-02-28 10:03:11',600093064,'2025-02-28 10:03:11','张振东','张振东',2),
                                                                                                                                                                                                            (7293798726975455236,'VN','LOGISTICS',1.000000,'USD',0,600093064,'2025-02-28 10:03:11',600093064,'2025-02-28 10:03:11','张振东','张振东',2),
                                                                                                                                                                                                            (7293798726975455237,'MY','LOGISTICS',1.500000,'USD',0,600093064,'2025-02-28 10:03:11',600093064,'2025-02-28 10:03:11','张振东','张振东',2),
                                                                                                                                                                                                            (7293798726975455238,'ID','LOGISTICS',1.110000,'USD',0,600093064,'2025-02-28 10:03:11',600093064,'2025-02-28 10:03:11','张振东','张振东',2);

INSERT INTO pop_product.product_basic_price_rule (product_basic_price_rule_id,rule_type,status,country_codes,supply_type,is_deleted,creator_id,created_time,reviser_id,revised_time,creator_name,reviser_name,tenant_id) VALUES
                                                                                                                                                                                                                             (7293872924161904652,'ISP_RATE',1,'["VN"]','try_on',0,600093064,'2025-02-28 14:58:01',600093064,'2025-03-04 09:37:22','张振东','张振东',2),
                                                                                                                                                                                                                             (7293872924208041998,'ISP_RATE',1,'["PH"]','try_on',0,600093064,'2025-02-28 14:58:01',600093064,'2025-03-04 09:37:22','张振东','张振东',2),
                                                                                                                                                                                                                             (7293872924245790736,'ISP_RATE',1,'["TH","SG","MY","ID"]','try_on',0,600093064,'2025-02-28 14:58:01',600093064,'2025-03-04 09:37:22','张振东','张振东',2),
                                                                                                                                                                                                                             (7294961404775371001,'ISP_RATE',1,'["VN"]','Manufacturer',0,600093064,'2025-03-03 15:03:15',600093064,'2025-03-04 09:37:22','张振东','张振东',2),
                                                                                                                                                                                                                             (7294961404813119739,'ISP_RATE',1,'["PH"]','Manufacturer',0,600093064,'2025-03-03 15:03:15',600093064,'2025-03-04 09:37:22','张振东','张振东',2),
                                                                                                                                                                                                                             (7294961404850868477,'ISP_RATE',1,'["TH","SG","MY","ID"]','Manufacturer',0,600093064,'2025-03-03 15:03:15',600093064,'2025-03-04 09:37:22','张振东','张振东',2);

INSERT INTO pop_product.product_basic_price_rule_detail (product_basic_price_rule_detail_id,product_basic_price_rule_id,price_type,operator,operand_value,round_decimal,`sequence`,is_deleted,creator_id,created_time,reviser_id,revised_time,creator_name,reviser_name,tenant_id) VALUES
                                                                                                                                                                                                                                                                                       (7295241783423934853,7294961404775371001,'FIXED_VALUE','',3.1000,2,0,0,600093064,'2025-03-04 09:37:22',600093064,'2025-03-04 09:37:22','张振东','张振东',2),
                                                                                                                                                                                                                                                                                       (7295241783474266502,7294961404813119739,'FIXED_VALUE','',3.2000,2,0,0,600093064,'2025-03-04 09:37:22',600093064,'2025-03-04 09:37:22','张振东','张振东',2),
                                                                                                                                                                                                                                                                                       (7295241783516209543,7294961404850868477,'FIXED_VALUE','',3.3000,2,0,0,600093064,'2025-03-04 09:37:22',600093064,'2025-03-04 09:37:22','张振东','张振东',2),
                                                                                                                                                                                                                                                                                       (7295241783558152584,7293872924161904652,'FIXED_VALUE','',3.1000,2,0,0,600093064,'2025-03-04 09:37:22',600093064,'2025-03-04 09:37:22','张振东','张振东',2),
                                                                                                                                                                                                                                                                                       (7295241783595901321,7293872924208041998,'FIXED_VALUE','',3.2000,2,0,0,600093064,'2025-03-04 09:37:22',600093064,'2025-03-04 09:37:22','张振东','张振东',2),
                                                                                                                                                                                                                                                                                       (7295241783633650058,7293872924245790736,'FIXED_VALUE','',3.3000,2,0,0,600093064,'2025-03-04 09:37:22',600093064,'2025-03-04 09:37:22','张振东','张振东',2),
                                                                                                                                                                                                                                                                                       (7295241783675593099,7268824428367639469,'COMPETITOR_RETAIL_PRICE','',NULL,2,0,0,600093064,'2025-03-04 09:37:22',600093064,'2025-03-04 09:37:22','张振东','张振东',2),
                                                                                                                                                                                                                                                                                       (7295241783675593100,7268824428367639469,'FIXED_VALUE','*',0.9600,2,1,0,600093064,'2025-03-04 09:37:22',600093064,'2025-03-04 09:37:22','张振东','张振东',2),
                                                                                                                                                                                                                                                                                       (7295241783675593101,7268824428367639469,'SUPPLY_PRICE','/',NULL,2,2,0,600093064,'2025-03-04 09:37:22',600093064,'2025-03-04 09:37:22','张振东','张振东',2),
                                                                                                                                                                                                                                                                                       (7295241783759479182,7268170063185228088,'FIXED_VALUE','',3.2000,2,0,0,600093064,'2025-03-04 09:37:22',600093064,'2025-03-04 09:37:22','张振东','张振东',2);
INSERT INTO pop_product.product_basic_price_rule_detail (product_basic_price_rule_detail_id,product_basic_price_rule_id,price_type,operator,operand_value,round_decimal,`sequence`,is_deleted,creator_id,created_time,reviser_id,revised_time,creator_name,reviser_name,tenant_id) VALUES
                                                                                                                                                                                                                                                                                       (7295241783797227919,7265134485904547850,'FIXED_VALUE','',3.1000,2,0,0,600093064,'2025-03-04 09:37:22',600093064,'2025-03-04 09:37:22','张振东','张振东',2),
                                                                                                                                                                                                                                                                                       (7295241783839170960,7265134485929713676,'FIXED_VALUE','',3.2000,2,0,0,600093064,'2025-03-04 09:37:22',600093064,'2025-03-04 09:37:22','张振东','张振东',2),
                                                                                                                                                                                                                                                                                       (7295241783881114001,7265134485954879502,'FIXED_VALUE','',3.3000,2,0,0,600093064,'2025-03-04 09:37:22',600093064,'2025-03-04 09:37:22','张振东','张振东',2),
                                                                                                                                                                                                                                                                                       (7295241783923057042,7264849065031372807,'FIXED_VALUE','',3.1000,2,0,0,600093064,'2025-03-04 09:37:22',600093064,'2025-03-04 09:37:22','张振东','张振东',2),
                                                                                                                                                                                                                                                                                       (7295241783965000083,7264848776526172163,'FIXED_VALUE','',3.2000,2,0,0,600093064,'2025-03-04 09:37:22',600093064,'2025-03-04 09:37:22','张振东','张振东',2),
                                                                                                                                                                                                                                                                                       (7295241784006943124,7264847852198031361,'FIXED_VALUE','',3.3000,2,0,0,600093064,'2025-03-04 09:37:22',600093064,'2025-03-04 09:37:22','张振东','张振东',2);

INSERT INTO pop_product.product_price_rule_group (product_price_rule_group_id,group_name,supply_type,creator_id,creator_name,created_time,reviser_id,reviser_name,revised_time,is_deleted,tenant_id) VALUES
                                                                                                                                                                                                         (7294960611098624237,'ODM','Manufacturer',600093064,'张振东','2025-03-03 15:00:06',600093064,'张振东','2025-03-03 15:00:06',0,2),
                                                                                                                                                                                                         (7294969325276889089,'现货 try on','try_on',600093064,'张振东','2025-03-03 15:34:43',600093064,'张振东','2025-03-04 16:50:19',0,2);

INSERT INTO pop_product.product_price_rule (product_price_rule_id,product_price_rule_group_id,priority,status,creator_id,creator_name,created_time,reviser_id,reviser_name,revised_time,is_deleted,tenant_id) VALUES
                                                                                                                                                                                                                  (7294960611123790062,7294960611098624237,1,1,600093064,'张振东','2025-03-03 15:00:06',600093064,'张振东','2025-03-03 15:00:06',0,2),
                                                                                                                                                                                                                  (7295347859943207024,7294969325276889089,1,1,600093064,'张振东','2025-03-04 16:38:53',600093064,'张振东','2025-03-04 16:50:19',0,2),
                                                                                                                                                                                                                  (7295350680801407949,7294969325276889089,2,1,600093064,'张振东','2025-03-04 16:50:05',600093064,'张振东','2025-03-04 16:50:19',0,2);

INSERT INTO pop_product.product_price_condition (product_price_condition_id,product_price_rule_id,condition_field,condition_operator,condition_value,range_min,range_max,logic_operator,`sequence`,creator_id,creator_name,created_time,reviser_id,reviser_name,revised_time,is_deleted,tenant_id) VALUES
                                                                                                                                                                                                                                                                                                       (7295347859964178545,7295347859943207024,'SPOT_TYPE','EQ','A1',NULL,NULL,'AND',0,600093064,'张振东','2025-03-04 16:38:53',600093064,'张振东','2025-03-04 16:50:19',0,2),
                                                                                                                                                                                                                                                                                                       (7295350680805602254,7295350680801407949,'SPOT_TYPE','EQ','A2',NULL,NULL,'AND',0,600093064,'张振东','2025-03-04 16:50:05',600093064,'张振东','2025-03-04 16:50:19',0,2);

INSERT INTO pop_product.product_price_formula (product_price_formula_id,product_price_rule_id,target_field,formula_type,fixed_value,rounding_precision,rounding_precision_type,country_rounding_precisions,creator_id,creator_name,created_time,reviser_id,reviser_name,revised_time,is_deleted,tenant_id) VALUES
                                                                                                                                                                                                                                                                                                               (7294960611148955887,7294960611123790062,'RETAIL_PRICE','DYNAMIC',NULL,0,'FIXED','[]',600093064,'张振东','2025-03-03 15:00:06',600093064,'张振东','2025-03-03 15:00:06',0,2),
                                                                                                                                                                                                                                                                                                               (7294960611148955888,7294960611123790062,'SALE_PRICE','DYNAMIC',NULL,0,'FIXED','[]',600093064,'张振东','2025-03-03 15:00:06',600093064,'张振东','2025-03-03 15:00:06',0,2),
                                                                                                                                                                                                                                                                                                               (7295347859989344370,7295347859943207024,'RETAIL_PRICE','DYNAMIC',0.00,0,'FIXED','[]',600093064,'张振东','2025-03-04 16:38:53',600093064,'张振东','2025-03-04 16:50:19',0,2),
                                                                                                                                                                                                                                                                                                               (7295347859989344371,7295347859943207024,'SALE_PRICE','DYNAMIC',0.00,0,'FIXED','[]',600093064,'张振东','2025-03-04 16:38:53',600093064,'张振东','2025-03-04 16:50:19',0,2),
                                                                                                                                                                                                                                                                                                               (7295347859989344372,7295347859943207024,'PURCHASE_RETAIL_PRICE','DYNAMIC',0.00,0,'FIXED','[]',600093064,'张振东','2025-03-04 16:38:53',600093064,'张振东','2025-03-04 16:50:19',0,2),
                                                                                                                                                                                                                                                                                                               (7295347859993538677,7295347859943207024,'PURCHASE_SALE_PRICE','DYNAMIC',0.00,0,'FIXED','[]',600093064,'张振东','2025-03-04 16:38:53',600093064,'张振东','2025-03-04 16:50:19',0,2),
                                                                                                                                                                                                                                                                                                               (7295350680813990863,7295350680801407949,'RETAIL_PRICE','DYNAMIC',0.00,0,'FIXED','[]',600093064,'张振东','2025-03-04 16:50:05',600093064,'张振东','2025-03-04 16:50:19',0,2),
                                                                                                                                                                                                                                                                                                               (7295350680813990864,7295350680801407949,'SALE_PRICE','DYNAMIC',0.00,0,'FIXED','[]',600093064,'张振东','2025-03-04 16:50:05',600093064,'张振东','2025-03-04 16:50:19',0,2),
                                                                                                                                                                                                                                                                                                               (7295350680813990865,7295350680801407949,'PURCHASE_RETAIL_PRICE','DYNAMIC',0.00,0,'FIXED','[]',600093064,'张振东','2025-03-04 16:50:05',600093064,'张振东','2025-03-04 16:50:19',0,2),
                                                                                                                                                                                                                                                                                                               (7295350680813990866,7295350680801407949,'PURCHASE_SALE_PRICE','DYNAMIC',0.00,0,'FIXED','[]',600093064,'张振东','2025-03-04 16:50:05',600093064,'张振东','2025-03-04 16:50:19',0,2);

INSERT INTO pop_product.product_price_formula_detail (product_price_formula_detail_id,product_price_formula_id,operand_field,operand_value,operator,`sequence`,creator_id,creator_name,created_time,reviser_id,reviser_name,revised_time,is_deleted,tenant_id) VALUES
                                                                                                                                                                                                                                                                   (7293800579518861313,7269237963778277749,'EXCHANGE_RATE_CNY',NULL,'*',2,600093064,'张振东','2025-02-28 10:10:32',600093064,'张振东','2025-02-28 10:10:32',0,2),
                                                                                                                                                                                                                                                                   (7293800579590164482,7269237963778277750,'ISP_RATE',NULL,'*',1,600093064,'张振东','2025-02-28 10:10:33',600093064,'张振东','2025-02-28 10:10:33',0,2),
                                                                                                                                                                                                                                                                   (7294960611169927409,7294960611148955887,'PURCHASE_PRICE',NULL,'',0,600093064,'张振东','2025-03-03 15:00:06',600093064,'张振东','2025-03-03 15:00:06',0,2),
                                                                                                                                                                                                                                                                   (7294960611174121714,7294960611148955887,'FIXED_VALUE',1.10,'+',1,600093064,'张振东','2025-03-03 15:00:06',600093064,'张振东','2025-03-03 15:00:06',0,2),
                                                                                                                                                                                                                                                                   (7294960611174121715,7294960611148955887,'ISP_RATE',NULL,'*',2,600093064,'张振东','2025-03-03 15:00:06',600093064,'张振东','2025-03-03 15:00:06',0,2),
                                                                                                                                                                                                                                                                   (7294960611174121716,7294960611148955887,'EXCHANGE_RATE_CNY',NULL,'*',3,600093064,'张振东','2025-03-03 15:00:06',600093064,'张振东','2025-03-03 15:00:06',0,2),
                                                                                                                                                                                                                                                                   (7294960611174121717,7294960611148955888,'RETAIL_PRICE',NULL,'',0,600093064,'张振东','2025-03-03 15:00:06',600093064,'张振东','2025-03-03 15:00:06',0,2),
                                                                                                                                                                                                                                                                   (7295347860014510198,7295347859989344370,'SUPPLY_PRICE',0.00,'',0,600093064,'张振东','2025-03-04 16:38:53',600093064,'张振东','2025-03-04 16:50:19',0,2),
                                                                                                                                                                                                                                                                   (7295347860014510199,7295347859989344370,'ISP_RATE',0.00,'*',1,600093064,'张振东','2025-03-04 16:38:53',600093064,'张振东','2025-03-04 16:50:19',0,2),
                                                                                                                                                                                                                                                                   (7295347860014510200,7295347859989344370,'EXCHANGE_RATE_CNY',0.00,'*',2,600093064,'张振东','2025-03-04 16:38:53',600093064,'张振东','2025-03-04 16:50:19',0,2);
INSERT INTO pop_product.product_price_formula_detail (product_price_formula_detail_id,product_price_formula_id,operand_field,operand_value,operator,`sequence`,creator_id,creator_name,created_time,reviser_id,reviser_name,revised_time,is_deleted,tenant_id) VALUES
                                                                                                                                                                                                                                                                   (7295347860018704505,7295347859989344371,'RETAIL_PRICE',0.00,'',0,600093064,'张振东','2025-03-04 16:38:53',600093064,'张振东','2025-03-04 16:50:19',0,2),
                                                                                                                                                                                                                                                                   (7295347860018704506,7295347859989344372,'PURCHASE_PRICE',0.00,'',0,600093064,'张振东','2025-03-04 16:38:53',600093064,'张振东','2025-03-04 16:50:19',0,2),
                                                                                                                                                                                                                                                                   (7295347860018704507,7295347859989344372,'ISP_RATE',0.00,'*',1,600093064,'张振东','2025-03-04 16:38:53',600093064,'张振东','2025-03-04 16:50:19',0,2),
                                                                                                                                                                                                                                                                   (7295347860018704508,7295347859989344372,'EXCHANGE_RATE_CNY',0.00,'*',2,600093064,'张振东','2025-03-04 16:38:53',600093064,'张振东','2025-03-04 16:50:19',0,2),
                                                                                                                                                                                                                                                                   (7295347860018704509,7295347859993538677,'PURCHASE_PRICE',0.00,'',0,600093064,'张振东','2025-03-04 16:38:53',600093064,'张振东','2025-03-04 16:50:19',0,2),
                                                                                                                                                                                                                                                                   (7295350680793019339,7295347859993538677,'ISP_RATE',0.00,'*',1,600093064,'张振东','2025-03-04 16:50:05',600093064,'张振东','2025-03-04 16:50:19',0,2),
                                                                                                                                                                                                                                                                   (7295350680793019340,7295347859993538677,'EXCHANGE_RATE_CNY',0.00,'*',2,600093064,'张振东','2025-03-04 16:50:05',600093064,'张振东','2025-03-04 16:50:19',0,2),
                                                                                                                                                                                                                                                                   (7295350680818185171,7295350680813990863,'LOGISTICS',0.00,'',0,600093064,'张振东','2025-03-04 16:50:05',600093064,'张振东','2025-03-04 16:50:19',0,2),
                                                                                                                                                                                                                                                                   (7295350680818185172,7295350680813990863,'SUPPLY_PRICE',0.00,'+',1,600093064,'张振东','2025-03-04 16:50:05',600093064,'张振东','2025-03-04 16:50:19',0,2),
                                                                                                                                                                                                                                                                   (7295350680818185173,7295350680813990863,'FIXED_VALUE',1.30,'*',2,600093064,'张振东','2025-03-04 16:50:05',600093064,'张振东','2025-03-04 16:50:19',0,2);
INSERT INTO pop_product.product_price_formula_detail (product_price_formula_detail_id,product_price_formula_id,operand_field,operand_value,operator,`sequence`,creator_id,creator_name,created_time,reviser_id,reviser_name,revised_time,is_deleted,tenant_id) VALUES
                                                                                                                                                                                                                                                                   (7295350680818185174,7295350680813990863,'EXCHANGE_RATE_CNY',0.00,'*',3,600093064,'张振东','2025-03-04 16:50:05',600093064,'张振东','2025-03-04 16:50:19',0,2),
                                                                                                                                                                                                                                                                   (7295350680818185175,7295350680813990864,'RETAIL_PRICE',0.00,'',0,600093064,'张振东','2025-03-04 16:50:05',600093064,'张振东','2025-03-04 16:50:19',0,2),
                                                                                                                                                                                                                                                                   (7295350680818185176,7295350680813990865,'LOGISTICS',0.00,'',0,600093064,'张振东','2025-03-04 16:50:05',600093064,'张振东','2025-03-04 16:50:19',0,2),
                                                                                                                                                                                                                                                                   (7295350680818185177,7295350680813990865,'PURCHASE_PRICE',0.00,'+',1,600093064,'张振东','2025-03-04 16:50:05',600093064,'张振东','2025-03-04 16:50:19',0,2),
                                                                                                                                                                                                                                                                   (7295350680818185178,7295350680813990865,'FIXED_VALUE',1.30,'*',2,600093064,'张振东','2025-03-04 16:50:05',600093064,'张振东','2025-03-04 16:50:19',0,2),
                                                                                                                                                                                                                                                                   (7295350680818185179,7295350680813990865,'EXCHANGE_RATE_CNY',0.00,'*',3,600093064,'张振东','2025-03-04 16:50:05',600093064,'张振东','2025-03-04 16:50:19',0,2),
                                                                                                                                                                                                                                                                   (7295350680818185180,7295350680813990866,'LOGISTICS',0.00,'',0,600093064,'张振东','2025-03-04 16:50:05',600093064,'张振东','2025-03-04 16:50:19',0,2),
                                                                                                                                                                                                                                                                   (7295350680818185181,7295350680813990866,'PURCHASE_PRICE',0.00,'+',1,600093064,'张振东','2025-03-04 16:50:05',600093064,'张振东','2025-03-04 16:50:19',0,2),
                                                                                                                                                                                                                                                                   (7295350680818185182,7295350680813990866,'FIXED_VALUE',1.30,'*',2,600093064,'张振东','2025-03-04 16:50:05',600093064,'张振东','2025-03-04 16:50:19',0,2),
                                                                                                                                                                                                                                                                   (7295350680822379487,7295350680813990866,'EXCHANGE_RATE_CNY',0.00,'*',3,600093064,'张振东','2025-03-04 16:50:05',600093064,'张振东','2025-03-04 16:50:19',0,2);
