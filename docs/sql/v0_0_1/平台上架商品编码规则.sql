CREATE TABLE `sku_code_rule` (
                                 `sku_code_rule_id` BIGINT  NOT NULL COMMENT 'SKU编码规则主键ID',
                                 `rule_name` VARCHAR(100) NOT NULL COMMENT '规则名称',
                                 `channel_id` bigint NOT NULL COMMENT '上架渠道，阿里，字节',
                                 `channel_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '渠道名称',
                                 `platform_id` bigint NOT NULL COMMENT '上架平台，lazada，AE',
                                 `platform_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '平台名称',
                                 `supply_path` VARCHAR(255) DEFAULT NULL COMMENT '供给方式路径，拼接',
                                 `rule_type` TINYINT  NOT NULL COMMENT '规则类型【0: 自定义规则；1: 平台默认规则】',
                                 `separator` VARCHAR(10) DEFAULT '-' COMMENT '连接符（如 空格、-、_）',
                                 `enabled` TINYINT  NOT NULL DEFAULT 1 COMMENT '是否启用【1: 启用；0: 禁用】',
                                 `remark` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注',

                                 `creator_id` BIGINT  NOT NULL COMMENT '创建人ID',
                                 `creator_name` VARCHAR(50) NOT NULL COMMENT '创建人名称',
                                 `created_time` DATETIME NOT NULL COMMENT '创建时间',
                                 `reviser_id` BIGINT  DEFAULT NULL COMMENT '更新人ID',
                                 `reviser_name` VARCHAR(50) DEFAULT NULL COMMENT '更新人名称',
                                 `revised_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最近更新时间',
                                 `is_deleted` TINYINT  NOT NULL DEFAULT '0' COMMENT '是否已删除',

                                 PRIMARY KEY (`sku_code_rule_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='SKU编码规则表';


CREATE TABLE `sku_rule_element` (
                                    `sku_rule_element_id` BIGINT  NOT NULL COMMENT 'SKU编码规则元素主键ID',
                                    `sku_code_rule_id` BIGINT  NOT NULL COMMENT '关联SKU编码规则ID',
                                    `element_name` VARCHAR(50) NOT NULL COMMENT '元素名称（如 SKU, 颜色, 尺码等）',
                                    `element_type` VARCHAR(20) NOT NULL COMMENT '元素类型【固定值, 动态值, 系统变量】',
                                    `element_value` VARCHAR(255) DEFAULT NULL COMMENT '元素值（如果为固定值）',
                                    `dynamic_field` VARCHAR(50) DEFAULT NULL COMMENT '动态字段（如 sku_code, color_code 等）',
                                    `sort_order` INT NOT NULL COMMENT '元素顺序，用于编码生成时的排序',

                                    `creator_id` BIGINT  NOT NULL COMMENT '创建人ID',
                                    `creator_name` VARCHAR(50) NOT NULL COMMENT '创建人名称',
                                    `created_time` DATETIME NOT NULL COMMENT '创建时间',
                                    `reviser_id` BIGINT  DEFAULT NULL COMMENT '更新人ID',
                                    `reviser_name` VARCHAR(50) DEFAULT NULL COMMENT '更新人名称',
                                    `revised_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最近更新时间',
                                    `is_deleted`   TINYINT  NOT NULL DEFAULT '0' COMMENT '是否已删除',

                                    PRIMARY KEY (`sku_rule_element_id`),
                                    KEY `idx_sku_code_rule_id` (`sku_code_rule_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='SKU编码规则元素表';
