-- 发布渠道表 publish_channel
CREATE TABLE `publish_channel`
(
    `channel_id`   bigint       NOT NULL COMMENT '渠道ID',
    `channel_name` varchar(100) NOT NULL COMMENT '渠道名称',
    `creator_id`   bigint       NOT NULL COMMENT '创建人id',
    `creator_name` varchar(32)  NOT NULL COMMENT '创建人名称',
    `created_time` datetime     NOT NULL COMMENT '创建时间',
    `reviser_id`   bigint                DEFAULT NULL COMMENT '最近修改人id',
    `reviser_name` varchar(32)           DEFAULT NULL COMMENT '最近修改人名称',
    `revised_time` datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最近更新时间',
    `is_deleted`   tinyint(1)   NOT NULL COMMENT '是否已删除;0未删除，1已删除',
    PRIMARY KEY (`channel_id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='发布渠道表';

-- 发布平台表 publish_platform
CREATE TABLE `publish_platform`
(
    `platform_id`   bigint       NOT NULL COMMENT '平台ID',
    `channel_id`    bigint       NOT NULL COMMENT '关联渠道ID',
    `platform_name` varchar(100) NOT NULL COMMENT '平台名称',
    `creator_id`    bigint       NOT NULL COMMENT '创建人id',
    `creator_name`  varchar(32)  NOT NULL COMMENT '创建人名称',
    `created_time`  datetime     NOT NULL COMMENT '创建时间',
    `reviser_id`    bigint                DEFAULT NULL COMMENT '最近修改人id',
    `reviser_name`  varchar(32)           DEFAULT NULL COMMENT '最近修改人名称',
    `revised_time`  datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最近更新时间',
    `is_deleted`    tinyint(1)   NOT NULL COMMENT '是否已删除;0未删除，1已删除',
    PRIMARY KEY (`platform_id`) USING BTREE,
    KEY `index_platform_name` (`platform_name`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='发布平台表';

-- 业务属性表 publish_attribute
CREATE TABLE `publish_attribute`
(
    `attribute_id`       bigint       NOT NULL COMMENT '属性ID',
    `attribute_group_id` bigint       NOT NULL COMMENT '属性组ID',
    `attribute_name`     varchar(255) NOT NULL COMMENT '属性名称',
    `attribute_code`     varchar(255)          DEFAULT NULL COMMENT '属性编码',
    `creator_id`         bigint       NOT NULL COMMENT '创建人ID',
    `creator_name`       varchar(32)  NOT NULL COMMENT '创建人名称',
    `created_time`       datetime     NOT NULL COMMENT '创建时间',
    `reviser_id`         bigint                DEFAULT NULL COMMENT '最近修改人ID',
    `reviser_name`       varchar(32)           DEFAULT NULL COMMENT '最近修改人名称',
    `revised_time`       datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最近更新时间',
    `is_deleted`         tinyint(1)   NOT NULL COMMENT '逻辑删除 0 未删除 1 已删除',
    PRIMARY KEY (`attribute_id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='业务属性表';

-- 属性组表 publish_attribute_group
CREATE TABLE `publish_attribute_group`
(
    `attribute_group_id` bigint       NOT NULL COMMENT '属性组ID',
    `group_name`         varchar(255) NOT NULL COMMENT '属性组名称',
    `attr_amount`        int                   DEFAULT '0' COMMENT '属性数量',
    `creator_id`         bigint       NOT NULL COMMENT '创建人ID',
    `creator_name`       varchar(32)  NOT NULL COMMENT '创建人名称',
    `created_time`       datetime     NOT NULL COMMENT '创建时间',
    `reviser_id`         bigint                DEFAULT NULL COMMENT '最近修改人ID',
    `reviser_name`       varchar(32)           DEFAULT NULL COMMENT '最近修改人名称',
    `revised_time`       datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最近更新时间',
    `is_deleted`         tinyint(1)   NOT NULL COMMENT '逻辑删除 0 未删除 1 已删除',
    PRIMARY KEY (`attribute_group_id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='属性组表';

-- 业务属性值表 publish_attribute_value
CREATE TABLE `publish_attribute_value`
(
    `attribute_value_id` bigint       NOT NULL COMMENT '属性值ID',
    `attribute_id`       bigint       NOT NULL COMMENT '属性ID',
    `attribute_value`    varchar(255) NOT NULL COMMENT '属性值',
    `state`              tinyint               DEFAULT '1' COMMENT '启用状态1启用0禁用',
    `creator_id`         bigint       NOT NULL COMMENT '创建人ID',
    `creator_name`       varchar(32)  NOT NULL COMMENT '创建人名称',
    `created_time`       datetime     NOT NULL COMMENT '创建时间',
    `reviser_id`         bigint                DEFAULT NULL COMMENT '最近修改人ID',
    `reviser_name`       varchar(32)           DEFAULT NULL COMMENT '最近修改人名称',
    `revised_time`       datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最近更新时间',
    `is_deleted`         tinyint(1)   NOT NULL COMMENT '逻辑删除 0 未删除 1 已删除',
    PRIMARY KEY (`attribute_value_id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='业务属性值表';

-- 业务属性与平台属性的映射 publish_platform_attr
CREATE TABLE `publish_platform_attr`
(
    `platform_attr_id`              bigint      NOT NULL COMMENT '平台属性ID',
    `category_id`                   bigint      NOT NULL COMMENT '业务品类id',
    `platform_id`                   bigint      NOT NULL COMMENT '平台ID',
    `category_mapping_id`           bigint               DEFAULT NULL COMMENT '平台品类映射ID',
    `platform_attribute_label_name` varchar(255)         DEFAULT NULL COMMENT '平台上的属性名称(展示用)',
    `platform_attribute_key_name`   varchar(255)         DEFAULT NULL COMMENT '平台上的属性名称(用于上架API)',
    `attribute_id`                  bigint      NOT NULL COMMENT '属性ID',
    `country`                       varchar(20) NOT NULL COMMENT '国家/地区',
    `creator_id`                    bigint      NOT NULL COMMENT '创建人ID',
    `creator_name`                  varchar(32) NOT NULL COMMENT '创建人名称',
    `created_time`                  datetime    NOT NULL COMMENT '创建时间',
    `reviser_id`                    bigint               DEFAULT NULL COMMENT '修改人ID',
    `reviser_name`                  varchar(16)          DEFAULT NULL COMMENT '修改人名称',
    `revised_time`                  datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    `is_deleted`                    tinyint(1)  NOT NULL DEFAULT '0' COMMENT '是否已删除',
    PRIMARY KEY (`platform_attr_id`) USING BTREE,
    KEY `platform_id` (`platform_id`) USING BTREE,
    KEY `attribute_id` (`attribute_id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='业务属性与平台属性的映射';

-- 业务属性与平台属性的映射 publish_platform_attr_value
CREATE TABLE `publish_platform_attr_value`
(
    `platform_attr_value_id`   bigint      NOT NULL COMMENT '主键ID',
    `publish_platform_attr_id` bigint               DEFAULT NULL COMMENT '平台属性ID',
    `platform_attribute_value` varchar(255)         DEFAULT NULL COMMENT '平台上的属性值',
    `platform_attribute_code`  varchar(255)         DEFAULT NULL COMMENT '平台属性编码',
    `attribute_value_id`       bigint               DEFAULT NULL COMMENT '业务属性值ID',
    `attribute_value`          varchar(255)         DEFAULT NULL COMMENT '属性值',
    `attribute_id`             bigint               DEFAULT NULL COMMENT '业务属性ID',
    `creator_id`               bigint      NOT NULL COMMENT '创建人ID',
    `creator_name`             varchar(32) NOT NULL COMMENT '创建人名称',
    `created_time`             datetime    NOT NULL COMMENT '创建时间',
    `reviser_id`               bigint               DEFAULT NULL COMMENT '修改人ID',
    `reviser_name`             varchar(16)          DEFAULT NULL COMMENT '修改人名称',
    `revised_time`             datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    `is_deleted`               tinyint(1)  NOT NULL DEFAULT '0' COMMENT '是否已删除',
    PRIMARY KEY (`platform_attr_value_id`) USING BTREE,
    KEY `index_publish_platform_attr_id` (`publish_platform_attr_id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='业务属性与平台属性的映射';

-- 上架品类表 publish_category
CREATE TABLE `publish_category`
(
    `publish_category_id` bigint      NOT NULL COMMENT '品类id',
    `parent_category_id`  bigint      NOT NULL COMMENT '父品类ID',
    `parent_paths`        text COMMENT '父节点ID，多个用英文逗号隔开',
    `level`               int                  DEFAULT '1' COMMENT '节点所在层级',
    `category_name`       varchar(64) NOT NULL COMMENT '品类名称',
    `sort_num`            int         NOT NULL DEFAULT '0' COMMENT '序号;排序',
    `creator_id`          bigint      NOT NULL COMMENT '创建人id',
    `creator_name`        varchar(32) NOT NULL COMMENT '创建人名称',
    `created_time`        datetime    NOT NULL COMMENT '创建时间',
    `reviser_id`          bigint               DEFAULT NULL COMMENT '最近修改人id',
    `reviser_name`        varchar(16) NOT NULL COMMENT '最近修改人名称',
    `revised_time`        datetime             DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '最近更新时间',
    `is_deleted`          tinyint(1)  NOT NULL COMMENT '是否已删除;0未删除，1已删除',
    PRIMARY KEY (`publish_category_id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='上架品类表';

-- 业务品类和业务属性映射表 publish_category_attr
CREATE TABLE `publish_category_attr`
(
    `category_attr_id` bigint      NOT NULL COMMENT '主键ID',
    `category_id`      bigint      NOT NULL COMMENT '业务品类id',
    `attribute_id`     bigint      NOT NULL COMMENT '属性ID',
    `creator_id`       bigint      NOT NULL COMMENT '创建人ID',
    `creator_name`     varchar(32) NOT NULL COMMENT '创建人名称',
    `created_time`     datetime    NOT NULL COMMENT '创建时间',
    `reviser_id`       bigint               DEFAULT NULL COMMENT '修改人ID',
    `reviser_name`     varchar(16)          DEFAULT NULL COMMENT '修改人名称',
    `revised_time`     datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    `is_deleted`       tinyint(1)  NOT NULL DEFAULT '0' COMMENT '是否已删除',
    PRIMARY KEY (`category_attr_id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='业务品类和业务属性映射表';

-- 品类映射表 publish_category_mapping
CREATE TABLE `publish_category_mapping`
(
    `category_mapping_id`    bigint       NOT NULL COMMENT '映射ID',
    `publish_category_id`    bigint       NOT NULL COMMENT '品类id',
    `platform_id`            bigint       NOT NULL COMMENT '关联平台ID',
    `platform_category_id`   varchar(100) NOT NULL COMMENT '关联平台品类ID',
    `platform_category_name` varchar(255) NOT NULL COMMENT '关联平台品类名称',
    `country`                varchar(20)  NOT NULL COMMENT '国家',
    `channel_name`           varchar(100) NOT NULL COMMENT '渠道名称',
    `platform_name`          varchar(100) NOT NULL COMMENT '平台名称',
    `channel_id`             bigint       NOT NULL COMMENT '渠道ID',
    `remark`                 varchar(100) NOT NULL COMMENT '备注',
    `creator_id`             bigint       NOT NULL COMMENT '创建人id',
    `creator_name`           varchar(32)  NOT NULL COMMENT '创建人名称',
    `created_time`           datetime     NOT NULL COMMENT '创建时间',
    `reviser_id`             bigint                DEFAULT NULL COMMENT '最近修改人id',
    `reviser_name`           varchar(32)           DEFAULT NULL COMMENT '最近修改人名称',
    `revised_time`           datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最近更新时间',
    `is_deleted`             tinyint(1)   NOT NULL COMMENT '是否已删除;0未删除，1已删除',
    PRIMARY KEY (`category_mapping_id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='品类映射表';