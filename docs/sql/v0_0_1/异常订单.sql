# 异常订单 error_order_info
# drop table error_order_info;
CREATE TABLE `error_order_info`
(
    `id`                    bigint       NOT NULL COMMENT 'id',
    `order_code`            varchar(255) NOT NULL COMMENT '订单号',
    `shop_id`               bigint       NOT NULL COMMENT '店铺id',
    `shop_name`             varchar(255) NOT NULL COMMENT '店铺name',
    `country_site_code`     varchar(255) NOT NULL COMMENT '国家站点code',
    `country_site_name`     varchar(255) NOT NULL COMMENT '国家站点name',
    `order_created_time`    datetime     NOT NULL COMMENT '异常订单创建时间',
    `cancel_total_quantity` int(5)       NOT NULL COMMENT '取消件数（合计）',
    `order_cancel_time`     datetime     NOT NULL COMMENT '订单取消时间',
    `order_cancel_reason`   varchar(255) NOT NULL COMMENT '订单取消原因',
    `order_canceller`       varchar(255) NOT NULL COMMENT '订单取消人',
    `op_processing_status`  int(2)       NULL COMMENT '运营处理状态 0待处理,1已处理',
    `op_processor_id`       bigint       NULL COMMENT '运营处理人id',
    `op_processor_name`     varchar(255) NULL COMMENT '运营处理人name',
    `op_contact_time`       datetime     NULL COMMENT '运营触达时间',
    `cs_processing_status`  int(2)       NULL COMMENT '客服处理状态 0待处理,1已处理',
    `cs_processor_id`       bigint       NULL COMMENT '客服处理人id',
    `cs_processor_name`     varchar(255) NULL COMMENT '客服处理人name',
    `cs_contact_time`       datetime     NULL COMMENT '客服触达时间',
    `creator_id`            bigint       NOT NULL COMMENT '创建人id',
    `creator_name`          varchar(32)  NOT NULL COMMENT '创建人名称',
    `created_time`          datetime     NOT NULL COMMENT '创建时间',
    `reviser_id`            bigint                DEFAULT NULL COMMENT '最近修改人id',
    `reviser_name`          varchar(32)           DEFAULT NULL COMMENT '最近修改人名称',
    `revised_time`          datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最近更新时间',
    `is_deleted`            tinyint(1)   NOT NULL COMMENT '是否已删除;0未删除，1已删除',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='异常订单表';

# 异常订单商品 error_order_spu_info
CREATE TABLE `error_order_spu_info`
(
    `id`              bigint       NOT NULL COMMENT 'id',
    `order_code`      varchar(255) NOT NULL COMMENT '订单号',
    `spu_code`        varchar(255) NOT NULL COMMENT 'SPU编号',
    `cancel_quantity` int(5)       NOT NULL COMMENT '取消件数',
    `creator_id`      bigint       NOT NULL COMMENT '创建人id',
    `creator_name`    varchar(32)  NOT NULL COMMENT '创建人名称',
    `created_time`    datetime     NOT NULL COMMENT '创建时间',
    `reviser_id`      bigint                DEFAULT NULL COMMENT '最近修改人id',
    `reviser_name`    varchar(32)           DEFAULT NULL COMMENT '最近修改人名称',
    `revised_time`    datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最近更新时间',
    `is_deleted`      tinyint(1)   NOT NULL COMMENT '是否已删除;0未删除，1已删除',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='异常订单商品表';