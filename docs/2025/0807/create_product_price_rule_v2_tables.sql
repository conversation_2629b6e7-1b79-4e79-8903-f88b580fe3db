-- =====================================================================================
-- 产品定价规则 V2 数据库表结构
-- 版本: V2.0.0
-- 创建时间: 2025-07-30 18:00:00
-- 说明: 支持括号嵌套运算和新增定价变量的销售定价规则系统
-- =====================================================================================

-- 1. 产品定价规则组表 V2
-- =====================================================================================
CREATE TABLE `product_price_rule_group_v2` (
                                               `product_price_rule_group_v2_id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键',
                                               `group_name` VARCHAR(100) NOT NULL COMMENT '规则组名称',
                                               `supply_mode` VARCHAR(50) DEFAULT NULL COMMENT '供给方式',
                                               `description` VARCHAR(500) DEFAULT NULL COMMENT '规则组描述',
                                               `deleted`            tinyint(1)            DEFAULT '0' COMMENT '是否已删除:0未删除,1已删除',
                                               `creator_id`         bigint       NOT NULL COMMENT '创建人ID',
                                               `creator_name`       varchar(32)  NOT NULL COMMENT '创建人',
                                               `created_time`       datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                               `reviser_id`         bigint                DEFAULT NULL COMMENT '最近修改人ID',
                                               `reviser_name`       varchar(32)           DEFAULT NULL COMMENT '最近修改人名称',
                                               `revised_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
                                               `tenant_id`          bigint                DEFAULT NULL COMMENT '租户ID',
                                               PRIMARY KEY (`product_price_rule_group_v2_id`),
                                               KEY `idx_supply_mode` (`supply_mode`),
                                               KEY `idx_created_time` (`created_time`),
                                               KEY `idx_deleted` (`deleted`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='产品定价规则组表V2';

-- 2. 产品定价规则主表 V2
-- =====================================================================================
CREATE TABLE `product_price_rule_v2` (
                                         `product_price_rule_v2_id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键',
                                         `product_price_rule_group_v2_id` BIGINT NOT NULL COMMENT '产品定价规则组表主键',
                                         `rule_name` VARCHAR(100) NOT NULL COMMENT '规则名称',
                                         `priority` INT NOT NULL DEFAULT 0 COMMENT '优先级，数值越小优先级越高',
                                         `enabled` TINYINT NOT NULL DEFAULT 1 COMMENT '是否启用，0-禁用，1-启用',
                                         `description` VARCHAR(500) DEFAULT NULL COMMENT '规则描述',
                                         `deleted`            tinyint(1)            DEFAULT '0' COMMENT '是否已删除:0未删除,1已删除',
                                         `creator_id`         bigint       NOT NULL COMMENT '创建人ID',
                                         `creator_name`       varchar(32)  NOT NULL COMMENT '创建人',
                                         `created_time`       datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                         `reviser_id`         bigint                DEFAULT NULL COMMENT '最近修改人ID',
                                         `reviser_name`       varchar(32)           DEFAULT NULL COMMENT '最近修改人名称',
                                         `revised_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
                                         `tenant_id`          bigint                DEFAULT NULL COMMENT '租户ID',
                                         PRIMARY KEY (`product_price_rule_v2_id`),
                                         KEY `idx_rule_group_priority` (`product_price_rule_group_v2_id`, `priority`),
                                         KEY `idx_created_time` (`created_time`),
                                         KEY `idx_deleted` (`deleted`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='产品定价规则主表V2';

-- 3. 产品定价条件表 V2
-- =====================================================================================
CREATE TABLE `product_price_condition_v2` (
                                              `product_price_condition_v2_id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键',
                                              `product_price_rule_v2_id` BIGINT NOT NULL COMMENT '定价规则ID',
                                              `condition_field` VARCHAR(100) NOT NULL COMMENT '条件字段，使用新的变量枚举',
                                              `condition_operator` VARCHAR(50) NOT NULL COMMENT '条件运算符，使用新的运算符枚举',
                                              `condition_value` VARCHAR(200) DEFAULT NULL COMMENT '条件值',
                                              `range_min` DECIMAL(20,4) DEFAULT NULL COMMENT '区间最小值',
                                              `range_max` DECIMAL(20,4) DEFAULT NULL COMMENT '区间最大值',
                                              `logic_operator` VARCHAR(20) DEFAULT NULL COMMENT '逻辑运算符：AND, OR',
                                              `sequence` INT NOT NULL DEFAULT 0 COMMENT '条件顺序',
                                              `deleted`            tinyint(1)            DEFAULT '0' COMMENT '是否已删除:0未删除,1已删除',
                                              `creator_id`         bigint       NOT NULL COMMENT '创建人ID',
                                              `creator_name`       varchar(32)  NOT NULL COMMENT '创建人',
                                              `created_time`       datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                              `reviser_id`         bigint                DEFAULT NULL COMMENT '最近修改人ID',
                                              `reviser_name`       varchar(32)           DEFAULT NULL COMMENT '最近修改人名称',
                                              `revised_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
                                              `tenant_id`          bigint                DEFAULT NULL COMMENT '租户ID',
                                              PRIMARY KEY (`product_price_condition_v2_id`),
                                              KEY `idx_rule_sequence` (`product_price_rule_v2_id`, `sequence`),
                                              KEY `idx_condition_field` (`condition_field`),
                                              KEY `idx_created_time` (`created_time`),
                                              KEY `idx_deleted` (`deleted`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='产品定价条件表V2';

-- 4. 产品定价计算公式表 V2
-- =====================================================================================
CREATE TABLE `product_price_formula_v2` (
                                            `product_price_formula_v2_id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键',
                                            `product_price_rule_v2_id` BIGINT NOT NULL COMMENT '定价规则ID',
                                            `target_field` VARCHAR(50) NOT NULL COMMENT '目标字段：RETAIL_PRICE-划线价，SALE_PRICE-售价',
                                            `rounding_precision` INT NOT NULL DEFAULT 2 COMMENT '保留结果小数位数',
                                            `rounding_precision_type` VARCHAR(20) NOT NULL DEFAULT 'FIXED' COMMENT '小数位数配置类型：FIXED-固定值，BY_COUNTRY-按国家',
                                            `country_rounding_precisions` JSON NULL COMMENT '各国家小数位数配置，JSON格式存储',
                                            `formula_details` JSON NOT NULL COMMENT '公式明细配置，JSON格式存储',
                                            `deleted`            tinyint(1)            DEFAULT '0' COMMENT '是否已删除:0未删除,1已删除',
                                            `creator_id`         bigint       NOT NULL COMMENT '创建人ID',
                                            `creator_name`       varchar(32)  NOT NULL COMMENT '创建人',
                                            `created_time`       datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                            `reviser_id`         bigint                DEFAULT NULL COMMENT '最近修改人ID',
                                            `reviser_name`       varchar(32)           DEFAULT NULL COMMENT '最近修改人名称',
                                            `revised_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
                                            `tenant_id`          bigint                DEFAULT NULL COMMENT '租户ID',
                                            PRIMARY KEY (`product_price_formula_v2_id`),
                                            KEY `idx_target_field` (`target_field`),
                                            KEY `idx_created_time` (`created_time`),
                                            KEY `idx_deleted` (`deleted`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='产品定价计算公式表V2';