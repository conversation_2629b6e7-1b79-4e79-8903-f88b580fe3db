-- ----------------------------
-- 区域定价-广告成本配置
-- ----------------------------
CREATE TABLE `region_price_rule_ad`  (
                                         `ad_rule_id` bigint NOT NULL COMMENT '广告成本规则配置ID',
                                         `channel_id` bigint NULL DEFAULT NULL COMMENT '渠道ID',
                                         `platform_id` bigint NULL DEFAULT NULL COMMENT '关联平台ID',
                                         `shop_ids` json NULL COMMENT '店铺ID JSON数组',
                                         `rate` decimal(10, 2) NULL DEFAULT NULL COMMENT '广告成本(%)',
                                         `default_rule` tinyint NULL DEFAULT 0 COMMENT '是否默认1是0否',
                                         `creator_id` bigint NULL DEFAULT NULL COMMENT '创建人id',
                                         `creator_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人名称',
                                         `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                         `reviser_id` bigint NULL DEFAULT NULL COMMENT '最近修改者ID',
                                         `reviser_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '最近修改者',
                                         `revised_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最近更新时间',
                                         `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否已删除:0未删除,1已删除',
                                         `tenant_id` bigint NULL DEFAULT NULL COMMENT '租户id',
                                         PRIMARY KEY (`ad_rule_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '区域定价-广告成本配置' ROW_FORMAT = Dynamic;

-- ----------------------------
-- 区域定价-佣金配置
-- ----------------------------
CREATE TABLE `region_price_rule_commission`  (
                                                 `commission_rule_id` bigint NOT NULL COMMENT '佣金规则配置ID',
                                                 `channel_id` bigint NULL DEFAULT NULL COMMENT '渠道ID',
                                                 `platform_id` bigint NULL DEFAULT NULL COMMENT '关联平台ID',
                                                 `business_type` int NULL DEFAULT NULL COMMENT '店铺运营模式:2半托 3全托',
                                                 `shop_ids` json NULL COMMENT '店铺ID JSON数组',
                                                 `rate` decimal(10, 2) NULL DEFAULT NULL COMMENT '交易佣金(%)',
                                                 `default_rule` tinyint NULL DEFAULT 0 COMMENT '是否默认1是0否',
                                                 `creator_id` bigint NULL DEFAULT NULL COMMENT '创建人id',
                                                 `creator_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人名称',
                                                 `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                                 `reviser_id` bigint NULL DEFAULT NULL COMMENT '最近修改者ID',
                                                 `reviser_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '最近修改者',
                                                 `revised_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最近更新时间',
                                                 `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否已删除:0未删除,1已删除',
                                                 `tenant_id` bigint NULL DEFAULT NULL COMMENT '租户id',
                                                 PRIMARY KEY (`commission_rule_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '区域定价-佣金配置' ROW_FORMAT = Dynamic;

-- ----------------------------
-- 区域定价-折扣率配置
-- ----------------------------
CREATE TABLE `region_price_rule_discount_rate`  (
                                                    `discount_rate_rule_id` bigint NOT NULL COMMENT '折扣率规则配置ID',
                                                    `channel_id` bigint NULL DEFAULT NULL COMMENT '渠道ID',
                                                    `platform_id` bigint NULL DEFAULT NULL COMMENT '关联平台ID',
                                                    `shop_ids` json NULL COMMENT '店铺ID JSON数组',
                                                    `business_type` int NULL DEFAULT NULL COMMENT '店铺运营模式: 2半托 3全托',
                                                    `supply_type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '供给方式',
                                                    `rate` decimal(10, 2) NULL DEFAULT NULL COMMENT '折扣率(%)',
                                                    `default_rule` tinyint NULL DEFAULT 0 COMMENT '是否默认1是0否',
                                                    `creator_id` bigint NULL DEFAULT NULL COMMENT '创建人id',
                                                    `creator_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人名称',
                                                    `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                                    `reviser_id` bigint NULL DEFAULT NULL COMMENT '最近修改者ID',
                                                    `reviser_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '最近修改者',
                                                    `revised_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最近更新时间',
                                                    `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否已删除:0未删除,1已删除',
                                                    `tenant_id` bigint NULL DEFAULT NULL COMMENT '租户id',
                                                    PRIMARY KEY (`discount_rate_rule_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '区域定价-折扣率配置' ROW_FORMAT = Dynamic;

-- ----------------------------
-- 区域定价-物流支出比例配置
-- ----------------------------
CREATE TABLE `region_price_rule_freight_rate`  (
                                                   `freight_rate_rule_id` bigint NOT NULL COMMENT '物流支出比例规则配置ID',
                                                   `platform_id` bigint NULL DEFAULT NULL COMMENT '关联平台ID',
                                                   `rule_config` json NULL COMMENT '配置参数JSON',
                                                   `creator_id` bigint NULL DEFAULT NULL COMMENT '创建人id',
                                                   `creator_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人名称',
                                                   `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                                   `reviser_id` bigint NULL DEFAULT NULL COMMENT '最近修改者ID',
                                                   `reviser_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '最近修改者',
                                                   `revised_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最近更新时间',
                                                   `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否已删除:0未删除,1已删除',
                                                   `tenant_id` bigint NULL DEFAULT NULL COMMENT '租户id',
                                                   PRIMARY KEY (`freight_rate_rule_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '区域定价-物流支出比例配置' ROW_FORMAT = Dynamic;

-- ----------------------------
-- 区域定价-目标毛利率配置
-- ----------------------------
CREATE TABLE `region_price_rule_gross_margin`  (
                                                   `gross_margin_rule_id` bigint NOT NULL COMMENT '目标毛利率规则配置ID',
                                                   `channel_id` bigint NULL DEFAULT NULL COMMENT '渠道ID',
                                                   `platform_id` bigint NULL DEFAULT NULL COMMENT '关联平台ID',
                                                   `supply_type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '供给方式',
                                                   `shop_ids` json NULL COMMENT '店铺ID JSON数组',
                                                   `rate` decimal(10, 2) NULL DEFAULT NULL COMMENT '目标毛利率(%)',
                                                   `default_rule` tinyint NULL DEFAULT 0 COMMENT '是否默认1是0否',
                                                   `creator_id` bigint NULL DEFAULT NULL COMMENT '创建人id',
                                                   `creator_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人名称',
                                                   `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                                   `reviser_id` bigint NULL DEFAULT NULL COMMENT '最近修改者ID',
                                                   `reviser_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '最近修改者',
                                                   `revised_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最近更新时间',
                                                   `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否已删除:0未删除,1已删除',
                                                   `tenant_id` bigint NULL DEFAULT NULL COMMENT '租户id',
                                                   PRIMARY KEY (`gross_margin_rule_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '区域定价-目标毛利率配置' ROW_FORMAT = Dynamic;

-- ----------------------------
-- 区域定价-物流费配置
-- ----------------------------
CREATE TABLE `region_price_rule_logistics`  (
                                                `logistics_rule_id` bigint NOT NULL COMMENT '物流费规则配置ID',
                                                `platform_id` bigint NULL DEFAULT NULL COMMENT '关联平台ID',
                                                `rule_config` json NULL COMMENT '配置参数JSON',
                                                `creator_id` bigint NULL DEFAULT NULL COMMENT '创建人id',
                                                `creator_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人名称',
                                                `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                                `reviser_id` bigint NULL DEFAULT NULL COMMENT '最近修改者ID',
                                                `reviser_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '最近修改者',
                                                `revised_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最近更新时间',
                                                `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否已删除:0未删除,1已删除',
                                                `tenant_id` bigint NULL DEFAULT NULL COMMENT '租户id',
                                                PRIMARY KEY (`logistics_rule_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '区域定价-物流费配置' ROW_FORMAT = Dynamic;

-- ----------------------------
-- 区域定价-营销费用配置
-- ----------------------------
CREATE TABLE `region_price_rule_marketing`  (
                                                `marketing_rule_id` bigint NOT NULL COMMENT '营销费用规则配置ID',
                                                `channel_id` bigint NULL DEFAULT NULL COMMENT '渠道ID',
                                                `platform_id` bigint NULL DEFAULT NULL COMMENT '关联平台ID',
                                                `business_type` int NULL DEFAULT NULL COMMENT '店铺运营模式: 2半托 3全托',
                                                `shop_ids` json NULL COMMENT '店铺ID JSON数组',
                                                `rate` decimal(10, 2) NULL DEFAULT NULL COMMENT '营销费用(%)',
                                                `default_rule` tinyint NULL DEFAULT 0 COMMENT '是否默认1是0否',
                                                `creator_id` bigint NULL DEFAULT NULL COMMENT '创建人id',
                                                `creator_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人名称',
                                                `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                                `reviser_id` bigint NULL DEFAULT NULL COMMENT '最近修改者ID',
                                                `reviser_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '最近修改者',
                                                `revised_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最近更新时间',
                                                `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否已删除:0未删除,1已删除',
                                                `tenant_id` bigint NULL DEFAULT NULL COMMENT '租户id',
                                                PRIMARY KEY (`marketing_rule_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '区域定价-营销费用配置' ROW_FORMAT = Dynamic;

-- ----------------------------
-- 区域定价-退货率配置
-- ----------------------------
CREATE TABLE `region_price_rule_reject_rate`  (
                                                  `reject_rate_rule_id` bigint NOT NULL COMMENT '退货率规则配置ID',
                                                  `channel_id` bigint NULL DEFAULT NULL COMMENT '渠道ID',
                                                  `platform_id` bigint NULL DEFAULT NULL COMMENT '关联平台ID',
                                                  `shop_ids` json NULL COMMENT '店铺ID JSON数组',
                                                  `rate` decimal(10, 2) NULL DEFAULT NULL COMMENT '退货率(%)',
                                                  `default_rule` tinyint NULL DEFAULT 0 COMMENT '是否默认1是0否',
                                                  `creator_id` bigint NULL DEFAULT NULL COMMENT '创建人id',
                                                  `creator_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人名称',
                                                  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                                  `reviser_id` bigint NULL DEFAULT NULL COMMENT '最近修改者ID',
                                                  `reviser_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '最近修改者',
                                                  `revised_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最近更新时间',
                                                  `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否已删除:0未删除,1已删除',
                                                  `tenant_id` bigint NULL DEFAULT NULL COMMENT '租户id',
                                                  PRIMARY KEY (`reject_rate_rule_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '区域定价-退货率配置' ROW_FORMAT = Dynamic;

-- ----------------------------
-- 区域定价-仓储成本配置
-- ----------------------------
CREATE TABLE `region_price_rule_storage`  (
                                              `storage_rule_id` bigint NOT NULL COMMENT '仓储成本规则配置ID',
                                              `platform_id` bigint NULL DEFAULT NULL COMMENT '关联平台ID',
                                              `rule_config` json NULL COMMENT '配置参数',
                                              `creator_id` bigint NULL DEFAULT NULL COMMENT '创建人id',
                                              `creator_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人名称',
                                              `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                              `reviser_id` bigint NULL DEFAULT NULL COMMENT '最近修改者ID',
                                              `reviser_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '最近修改者',
                                              `revised_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最近更新时间',
                                              `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否已删除:0未删除,1已删除',
                                              `tenant_id` bigint NULL DEFAULT NULL COMMENT '租户id',
                                              PRIMARY KEY (`storage_rule_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '区域定价-仓储成本配置' ROW_FORMAT = Dynamic;

-- ----------------------------
-- 区域定价-综合税率配置
-- ----------------------------
CREATE TABLE `region_price_rule_tax_rate`  (
                                               `tax_rate_rule_id` bigint NOT NULL COMMENT '综合税率规则配置ID',
                                               `rule_type` tinyint NULL DEFAULT NULL COMMENT '配置维度1主体2店铺3供给方式4主体+供给方式5店铺+供给方式',
                                               `rule_config` json NULL COMMENT '参数配置JSON',
                                               `apply_rule` tinyint NULL DEFAULT 0 COMMENT '是否应用1是0否',
                                               `creator_id` bigint NULL DEFAULT NULL COMMENT '创建人id',
                                               `creator_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人名称',
                                               `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                               `reviser_id` bigint NULL DEFAULT NULL COMMENT '最近修改者ID',
                                               `reviser_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '最近修改者',
                                               `revised_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最近更新时间',
                                               `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否已删除:0未删除,1已删除',
                                               `tenant_id` bigint NULL DEFAULT NULL COMMENT '租户id',
                                               PRIMARY KEY (`tax_rate_rule_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '区域定价-综合税率配置' ROW_FORMAT = Dynamic;

-- ----------------------------
-- 区域定价-提现手续费配置
-- ----------------------------
CREATE TABLE `region_price_rule_withdraw`  (
                                               `withdraw_rule_id` bigint NOT NULL COMMENT '提现手续费规则配置ID',
                                               `channel_id` bigint NULL DEFAULT NULL COMMENT '渠道ID',
                                               `platform_id` bigint NULL DEFAULT NULL COMMENT '关联平台ID',
                                               `business_type` int NULL DEFAULT NULL COMMENT '店铺运营模式: 2半托 3全托',
                                               `shop_ids` json NULL COMMENT '店铺ID JSON数组',
                                               `rate` decimal(10, 2) NULL DEFAULT NULL COMMENT '提现手续费(%)',
                                               `default_rule` tinyint NULL DEFAULT 0 COMMENT '是否默认1是0否',
                                               `creator_id` bigint NULL DEFAULT NULL COMMENT '创建人id',
                                               `creator_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人名称',
                                               `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                               `reviser_id` bigint NULL DEFAULT NULL COMMENT '最近修改者ID',
                                               `reviser_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '最近修改者',
                                               `revised_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最近更新时间',
                                               `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否已删除:0未删除,1已删除',
                                               `tenant_id` bigint NULL DEFAULT NULL COMMENT '租户id',
                                               PRIMARY KEY (`withdraw_rule_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '区域定价-提现手续费配置' ROW_FORMAT = Dynamic;




ALTER TABLE `pop_product`.`product`
    ADD COLUMN `plan_audit_state` tinyint NULL DEFAULT 0 COMMENT '企划审核状态0待审核1通过2不通过' AFTER `image_version_num`,
    ADD COLUMN `plan_auditor_id` bigint NULL COMMENT '企划审核人ID' AFTER `plan_audit_state`,
    ADD COLUMN `plan_auditor_name` varchar(32) NULL COMMENT '企划审核人' AFTER `plan_auditor_id`,
    ADD COLUMN `plan_audit_time` datetime NULL COMMENT '企划审核时间' AFTER `plan_auditor_name`,
    ADD COLUMN `gross_margin` decimal(10,2) NULL COMMENT '选款店铺毛利率' AFTER `plan_audit_time`,
    ADD COLUMN `gross_margin_use_all_shop` tinyint NULL DEFAULT 0 COMMENT '毛利率是否应用到所有店铺，1是0否' AFTER `gross_margin`,
    ADD COLUMN `shop_gross_margin` json NULL COMMENT '商品所在店铺毛利率配置 JSON对象' AFTER `gross_margin_use_all_shop`;

ALTER TABLE `pop_product`.`ae_sale_goods`
    ADD COLUMN `price_calculate_rule` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '价格计算规则' AFTER `product_not_found`;
-- ----------------------------
-- 用户列表页面展示列配置
-- ----------------------------
CREATE TABLE `user_page_column_config`  (
            `id` bigint NOT NULL COMMENT 'ID',
            `user_id` bigint NULL DEFAULT NULL COMMENT '所属用户ID',
            `user_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '所属用户名',
            `page_code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '页面编码',
            `show_column_config` json NULL COMMENT '列展示配置JSON数组',
            `creator_id` bigint NULL DEFAULT NULL COMMENT '创建人id',
            `creator_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人名称',
            `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            `reviser_id` bigint NULL DEFAULT NULL COMMENT '最近修改者ID',
            `reviser_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '最近修改者',
            `revised_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最近更新时间',
            `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否已删除:0未删除,1已删除',
            `tenant_id` bigint NULL DEFAULT NULL COMMENT '租户id',
            PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户列表页面展示列配置' ROW_FORMAT = Dynamic;


-- ----------------------------
-- AE SKU增加店铺维度
-- ----------------------------
# 待上架-店铺价格表
alter table product_template_ae_sku
    add column shop_id               bigint COMMENT '店铺id' after ae_spu_id,
    add column national_quote_config JSON COMMENT '区域价格配置JSON' after last_retail_price;
# 已上架-SKU表增加区域定价JSON字段
alter table ae_sale_sku
    add column national_quote_config JSON COMMENT '区域价格配置JSON' after last_retail_price;