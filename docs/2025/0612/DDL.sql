-- 自动化标题，新增非数码印花需要使用的规则字段
ALTER TABLE `product`
    ADD COLUMN `style_element_name` varchar(32) NULL COMMENT '元素名称' AFTER print_type,
    ADD COLUMN `style_element_code` varchar(32) NULL COMMENT '元素编码' AFTER style_element_name,
    ADD COLUMN `style_season` JSON NULL COMMENT '季节json, 多选' AFTER style_element_code,
    ADD COLUMN `fit_code` varchar(32) DEFAULT NULL COMMENT '合身编码(版型)-OPS' AFTER style_season,
    ADD COLUMN `fit_name` varchar(32) DEFAULT NULL COMMENT '合身名称(版型)' AFTER fit_code;

-- 添加标题字段缺失信息字段
ALTER TABLE `product_template_ae_spu`
    ADD COLUMN `generated_title_missing_fields` json NULL COMMENT '自动化标题字段缺失信息，存储为JSON数组，例如：["HOT_WORD","PRINT_ATTRIBUTE_EXTEND1"]' AFTER `size_group_code`,
    ADD COLUMN `generated_title_over_length` tinyint(1) DEFAULT '0' COMMENT '自动化标题是否过长：0-否，1-是' AFTER `generated_title_missing_fields`;

ALTER TABLE `product_template_lazada_spu`
    ADD COLUMN `generated_title_missing_fields` json NULL COMMENT '自动化标题字段缺失信息，存储为JSON数组，例如：["HOT_WORD","PRINT_ATTRIBUTE_EXTEND1"]' AFTER `size_group_code`,
    ADD COLUMN `generated_title_over_length` tinyint(1) DEFAULT '0' COMMENT '自动化标题是否过长：0-否，1-是' AFTER `generated_title_missing_fields`;

-- 标题规则主表
CREATE TABLE `product_title_rule` (
                                      `product_title_rule_id` bigint NOT NULL COMMENT '规则ID',
                                      `rule_name` varchar(100) NOT NULL COMMENT '规则名称',
                                      `platform_shops` json NULL COMMENT '使用平台-店铺,JSON格式:[{id:1,name:"lazada-店铺1"},{id:2,name:"lazada-店铺2"}]',
                                      `supply_modes` json NULL COMMENT '使用供应方式,JSON格式:["Artificial","Equipment"]',
                                      `title_over_remove_fields` json NULL COMMENT '标题过长去除字段,JSON格式:["字段1","字段2"]',
                                      `remark` varchar(500) DEFAULT NULL COMMENT '备注',
                                      `enabled` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态:1-启用,0-禁用',
                                      `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否已删除;0未删除，1已删除',
                                      `creator_id` bigint NOT NULL COMMENT '创建人id',
                                      `creator_name` varchar(32) NOT NULL COMMENT '创建人',
                                      `created_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                      `reviser_id` bigint DEFAULT NULL COMMENT '最近修改人id',
                                      `reviser_name` varchar(32) DEFAULT NULL COMMENT '最近修改人名称',
                                      `revised_time` datetime DEFAULT NULL COMMENT '更新时间',
                                      `tenant_id` bigint DEFAULT NULL COMMENT '租户id',
                                      PRIMARY KEY (`product_title_rule_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='商品标题规则表';

-- 标题规则顺序表
CREATE TABLE `product_title_rule_detail` (
                                             `product_title_rule_detail_id` bigint NOT NULL AUTO_INCREMENT COMMENT '顺序ID',
                                             `product_title_rule_id` bigint NOT NULL COMMENT '规则ID',
                                             `rule_field` varchar(32) NOT NULL COMMENT '规则类型:CATEGORY-品类,CATEGORY_EXTEND1-品类拓展1,CATEGORY_EXTEND2-品类拓展2,HOT_WORD-热词,PRINT_ELEMENT-印花元素,FIXED_WORD-固定词',
                                             `rule_value` varchar(128) DEFAULT NULL COMMENT '规则值(固定词时存储文本)',
                                             `sequence` int NOT NULL DEFAULT '0' COMMENT '排序号',
                                             `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否已删除;0未删除，1已删除',
                                             `creator_id` bigint NOT NULL COMMENT '创建人id',
                                             `creator_name` varchar(32) NOT NULL COMMENT '创建人',
                                             `created_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                             `reviser_id` bigint DEFAULT NULL COMMENT '最近修改人id',
                                             `reviser_name` varchar(32) DEFAULT NULL COMMENT '最近修改人名称',
                                             `revised_time` datetime DEFAULT NULL COMMENT '更新时间',
                                             `tenant_id` bigint DEFAULT NULL COMMENT '租户id',
                                             PRIMARY KEY (`product_title_rule_detail_id`),
                                             KEY `idx_product_title_rule_id` (`product_title_rule_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='商品标题规则详情表';

-- 商品标题词库字段表
CREATE TABLE `product_title_dictionary_field` (
                                                  `product_title_dictionary_field_id` bigint NOT NULL COMMENT '字段ID',
                                                  `field_name` varchar(32) NOT NULL COMMENT '字段名称',
                                                  `sys_field` varchar(32) NOT NULL COMMENT '关联系统字段:CATEGORY-品类,STYLE-风格,PRINT_ATTRIBUTE-印花属性,PATTERN-版型,SEASON-季节,COLLAR-领型,SLEEVE-袖型,STYLE_WORD-风格词',
                                                  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
                                                  `enabled` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态:1-启用,0-禁用',
                                                  `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否已删除;0未删除，1已删除',
                                                  `creator_id` bigint NOT NULL COMMENT '创建人id',
                                                  `creator_name` varchar(32) NOT NULL COMMENT '创建人',
                                                  `created_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                                  `reviser_id` bigint DEFAULT NULL COMMENT '最近修改人id',
                                                  `reviser_name` varchar(32) DEFAULT NULL COMMENT '最近修改人名称',
                                                  `revised_time` datetime DEFAULT NULL COMMENT '更新时间',
                                                  `tenant_id` bigint DEFAULT NULL COMMENT '租户id',
                                                  PRIMARY KEY (`product_title_dictionary_field_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='商品标题词库字段表';

-- 商品标题词库字段值表
CREATE TABLE `product_title_dictionary_field_value` (
                                                        `product_title_dictionary_field_value_id` bigint NOT NULL AUTO_INCREMENT COMMENT '值ID',
                                                        `product_title_dictionary_field_id` bigint NOT NULL COMMENT '字段ID',
                                                        `field_value_id` varchar(32) DEFAULT NULL COMMENT '字段值ID标识',
                                                        `field_value` varchar(120) NOT NULL COMMENT '字段值',
                                                        `extend1` varchar(50) DEFAULT NULL COMMENT '拓展1',
                                                        `extend2` varchar(50) DEFAULT NULL COMMENT '拓展2',
                                                        `extend3` varchar(50) DEFAULT NULL COMMENT '拓展3',
                                                        `enabled` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态:1-启用,0-禁用',
                                                        `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否已删除;0未删除，1已删除',
                                                        `creator_id` bigint NOT NULL COMMENT '创建人id',
                                                        `creator_name` varchar(32) COLLATE utf8mb4_general_ci NOT NULL COMMENT '创建人',
                                                        `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                                        `reviser_id` bigint DEFAULT NULL COMMENT '最近修改人id',
                                                        `reviser_name` varchar(32) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '最近修改人名称',
                                                        `revised_time` datetime DEFAULT NULL COMMENT '更新时间',
                                                        `tenant_id` bigint DEFAULT NULL COMMENT '租户id',
                                                        PRIMARY KEY (`product_title_dictionary_field_value_id`),
                                                        KEY `idx_field_id` (`product_title_dictionary_field_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='商品标题词库字段值表';

-- 商品标题条件词表
CREATE TABLE `product_title_condition_word` (
                                                `product_title_condition_word_id` bigint NOT NULL AUTO_INCREMENT COMMENT '条件ID',
                                                `condition_field` varchar(32) NOT NULL COMMENT '条件类型:CROWD-人群,YEAR-年份',
                                                `condition_value` varchar(32) NOT NULL COMMENT '条件值',
                                                `field_value_id` varchar(32) DEFAULT NULL COMMENT '字段值ID',
                                                `field_value` varchar(120) DEFAULT NULL COMMENT '字段值',
                                                `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否已删除;0未删除，1已删除',
                                                `creator_id` bigint NOT NULL COMMENT '创建人id',
                                                `creator_name` varchar(32) NOT NULL COMMENT '创建人',
                                                `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                                `reviser_id` bigint DEFAULT NULL COMMENT '最近修改人id',
                                                `reviser_name` varchar(32) DEFAULT NULL COMMENT '最近修改人名称',
                                                `revised_time` datetime DEFAULT NULL COMMENT '更新时间',
                                                `tenant_id` bigint DEFAULT NULL COMMENT '租户id',
                                                PRIMARY KEY (`product_title_condition_word_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='商品标题条件词表';

-- 商品标题热词导入记录表
CREATE TABLE `product_title_hot_word_import` (
                                                 `product_title_hot_word_import_id` bigint NOT NULL AUTO_INCREMENT COMMENT '导入ID',
                                                 `file_name` varchar(255) NOT NULL COMMENT '文件名称',
                                                 `category_path` text COMMENT '关联品类，多个，、分隔',
                                                 `import_count` int NOT NULL DEFAULT '0' COMMENT '导入数量',
                                                 `import_status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '导入状态:0-导入中,1-导入成功,2-导入失败',
                                                 `import_file_url` VARCHAR(512) NULL COMMENT '源文件URL',
                                                 `error_msg` text COMMENT '错误信息',
                                                 `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否已删除;0未删除，1已删除',
                                                 `creator_id` bigint NOT NULL COMMENT '创建人id',
                                                 `creator_name` varchar(32) NOT NULL COMMENT '创建人',
                                                 `created_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                                 `reviser_id` bigint DEFAULT NULL COMMENT '最近修改人id',
                                                 `reviser_name` varchar(32) DEFAULT NULL COMMENT '最近修改人名称',
                                                 `revised_time` datetime DEFAULT NULL COMMENT '更新时间',
                                                 `tenant_id` bigint DEFAULT NULL COMMENT '租户id',
                                                 PRIMARY KEY (`product_title_hot_word_import_id`),
                                                 KEY `idx_created_time` (`created_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='商品标题热词导入记录表';

-- 商品标题热词表
CREATE TABLE `product_title_hot_word` (
                                          `product_title_hot_word_id` bigint NOT NULL AUTO_INCREMENT COMMENT '热词ID',
                                          `category_id` bigint DEFAULT NULL COMMENT '品类ID',
                                          `category_path` varchar(120) NOT NULL COMMENT '品类路径',
                                          `hot_word` varchar(2048) NOT NULL COMMENT '热词，多个，超过10个，用英文逗号分隔',
                                          `product_title_hot_word_import_id` bigint DEFAULT NULL COMMENT '导入ID',
                                          `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否已删除;0未删除，1已删除',
                                          `creator_id` bigint NOT NULL COMMENT '创建人id',
                                          `creator_name` varchar(32) NOT NULL COMMENT '创建人',
                                          `created_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                          `reviser_id` bigint DEFAULT NULL COMMENT '最近修改人id',
                                          `reviser_name` varchar(32) DEFAULT NULL COMMENT '最近修改人名称',
                                          `revised_time` datetime DEFAULT NULL COMMENT '更新时间',
                                          `tenant_id` bigint DEFAULT NULL COMMENT '租户id',
                                          PRIMARY KEY (`product_title_hot_word_id`),
                                          KEY `idx_category_id` (`category_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='商品标题热词表';


alter table ae_original_product_sku
    add color_property_value_name varchar(255) null comment '颜色属性值Name' after color_property_value_id;

CREATE TABLE `product_title_generation_record` (
                                                   `record_id` bigint unsigned NOT NULL auto_increment COMMENT '主键',
                                                   `product_id` bigint NOT NULL COMMENT '商品ID',
                                                   `shop_id` bigint DEFAULT NULL COMMENT '店铺ID',
                                                   `spu_code` varchar(250) DEFAULT NULL COMMENT 'SPU编码',
                                                   `generated_title` varchar(1000) DEFAULT NULL COMMENT '生成的标题',
                                                   `title_length` int DEFAULT NULL COMMENT '标题长度',
                                                   `over_length` tinyint(1) DEFAULT NULL COMMENT '是否超长',
                                                   `rule_id` bigint DEFAULT NULL COMMENT '使用的规则ID',
                                                   `rule_name` varchar(64) DEFAULT NULL COMMENT '规则名称',
                                                   `missing_fields_json` text COMMENT '缺失的字段(JSON格式)',
                                                   `remove_fields_json` text COMMENT '标题过长移除的字段(JSON格式)',
                                                   `field_values_json` text COMMENT '使用的字段及对应的值(JSON格式)',
                                                   `supply_mode` varchar(60) DEFAULT NULL COMMENT '供给模式',
                                                   `platform_id` bigint DEFAULT NULL COMMENT '平台ID',
                                                   `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除，0-未删除，1-已删除',
                                                   `creator_id` bigint DEFAULT NULL COMMENT '创建者ID',
                                                   `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                                   `reviser_id` bigint DEFAULT NULL COMMENT '修改者ID',
                                                   `revised_time` datetime DEFAULT NULL COMMENT '修改时间',
                                                   `creator_name` varchar(50) DEFAULT NULL COMMENT '创建人名称',
                                                   `reviser_name` varchar(50) DEFAULT NULL COMMENT '修改人名称',
                                                   `tenant_id` bigint DEFAULT NULL COMMENT '租户id',
                                                   PRIMARY KEY (`record_id`),
                                                   INDEX `idx_product_id` (`product_id`),
                                                   INDEX `idx_shop_id` (`shop_id`),
                                                   INDEX `idx_created_time` (`created_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='商品标题生成记录表';
