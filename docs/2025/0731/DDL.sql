ALTER TABLE shop
    ADD COLUMN external TINYINT(1) DEFAULT 0 COMMENT '是否外部商品(0-否,1-是)' after business_type;

CREATE TABLE `ae_job_time`
(
    `id`                  bigint       NOT NULL AUTO_INCREMENT,
    `shop_id`             bigint       NOT NULL COMMENT '店铺id',
    `product_status_type` varchar(255) NOT NULL COMMENT '商品业务状态：上架:onSelling ；下架:offline ；审核中:auditing ；审核不通过:editingRequired；客服删除:service_delete ; 所有删除商品：deleted',
    `task_start_time`     datetime     NOT NULL COMMENT '任务入参开始时间',
    `task_end_time`       datetime     NOT NULL COMMENT '任务入参结束时间',
    `tenant_id`           bigint                DEFAULT NULL COMMENT '租户ID',
    `creator_id`          bigint                DEFAULT NULL COMMENT '创建人ID',
    `creator_name`        varchar(50)           DEFAULT NULL COMMENT '创建人姓名',
    `created_time`        datetime              DEFAULT NULL COMMENT '创建时间',
    `reviser_id`          bigint                DEFAULT NULL COMMENT '更新人ID',
    `reviser_name`        varchar(50)           DEFAULT NULL COMMENT '更新人姓名',
    `revised_time`        datetime              DEFAULT NULL COMMENT '更新时间',
    `deleted`             tinyint      NOT NULL DEFAULT '0' COMMENT '逻辑删除：0-否；1-是',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='AE定时任务游标时间';


CREATE TABLE `ae_platform_product_spu`
(
    `product_id`                        bigint  NOT NULL AUTO_INCREMENT COMMENT '商品id(AE平台的product_id)',
    `shop_id`                           bigint  NOT NULL COMMENT 'POP字段-店铺id',
    `product_type`                      varchar(255)     DEFAULT NULL COMMENT '商品类型， POP:普通商品，POPCHOICE:半托管商品',
    `audit_failure_reason`              text COMMENT '审核不通过原因',
    `group_id`                          varchar(255)     DEFAULT NULL COMMENT '商品分组id',
    `owner_member_id`                   varchar(255)     DEFAULT NULL COMMENT '商品所属人loginId',
    `owner_member_seq`                  varchar(255)     DEFAULT NULL COMMENT '商品所属人Seq',
    `product_max_price`                 varchar(255)     DEFAULT NULL COMMENT '最大价格。',
    `product_min_price`                 varchar(255)     DEFAULT NULL COMMENT '最小价格。',
    `src`                               varchar(255)     DEFAULT NULL COMMENT '产品来源。tdx为淘宝代销产品，isv为通过API发布的商品。其他字符或空为普通产品。',
    `subject`                           varchar(255)     DEFAULT NULL COMMENT '商品标题',
    `coupon_end_date`                   varchar(255)     DEFAULT NULL COMMENT '优惠券结束日期',
    `coupon_start_date`                 varchar(255)     DEFAULT NULL COMMENT '优惠券开始日期',
    `aeop_qualification_struct_list`    json COMMENT '资质信息',
    `msr_eu_id`                         varchar(255)     DEFAULT NULL COMMENT '欧盟责任人id',
    `manufacturer_id`                   varchar(255)     DEFAULT NULL COMMENT '制造商id',
    `hscode`                            text COMMENT '税号',
    `tax_type`                          varchar(255)     DEFAULT NULL COMMENT '税种: 1：不含关税报价 2：含关税报价',
    `add_unit`                          varchar(255)     DEFAULT NULL COMMENT '每增加件数.取值范围1-1000',
    `add_weight`                        varchar(255)     DEFAULT NULL COMMENT '对应增加的重量.取值范围:0.001-500.000,保留三位小数,采用进位制,单位:公斤',
    `aeop_a_e_multimedia`               json COMMENT '商品多媒体信息，该属性主要包含商品的视频列表',
    `aeop_ae_product_propertys`         json COMMENT '类目属性',
    `aeop_national_quote_configuration` json COMMENT '商品分国家报价的配置',
    `base_unit`                         varchar(255)     DEFAULT NULL COMMENT '自定义计重的基本产品件数',
    `bulk_discount`                     varchar(255)     DEFAULT NULL COMMENT '产品的批发折扣',
    `bulk_order`                        varchar(255)     DEFAULT NULL COMMENT '享受批发价的产品数',
    `category_id`                       varchar(255)     DEFAULT NULL COMMENT '产品所在类目的ID',
    `currency_code`                     varchar(255)     DEFAULT NULL COMMENT '产品的货币单位。美元: USD,卢布: RUB',
    `delivery_time`                     varchar(255)     DEFAULT NULL COMMENT '商品的备货期',
    `gross_weight`                      varchar(255)     DEFAULT NULL COMMENT '产品的毛重',
    `group_ids`                         json COMMENT '产品所在的产品分组列表',
    `image_urls`                        varchar(1000)    DEFAULT NULL COMMENT '产品的主图列表',
    `is_pack_sell`                      varchar(255)     DEFAULT NULL COMMENT '是否支持是自定义计重',
    `keyword`                           varchar(255)     DEFAULT NULL COMMENT '关键字',
    `lot_num`                           varchar(255)     DEFAULT NULL COMMENT '每包的数量',
    `package_height`                    varchar(255)     DEFAULT NULL COMMENT '产品的高度',
    `package_length`                    varchar(255)     DEFAULT NULL COMMENT '产品的长度',
    `package_width`                     varchar(255)     DEFAULT NULL COMMENT '产品的宽度',
    `product_price`                     varchar(255)     DEFAULT NULL COMMENT '单品产品的价格',
    `product_status_type`               varchar(255)     DEFAULT NULL COMMENT '产品的状态，包括onselling(正在销售)，offine(已下架)，auditing(审核中)，editingRequired(审核不通过)',
    `product_unit`                      varchar(255)     DEFAULT NULL COMMENT '产品的单位',
    `promise_template_id`               varchar(255)     DEFAULT NULL COMMENT '产品所关联的服务模版',
    `reduce_strategy`                   varchar(255)     DEFAULT NULL COMMENT '库存的扣减策略',
    `sizechart_id`                      varchar(255)     DEFAULT NULL COMMENT '产品所关联的尺码模版ID',
    `ws_display`                        varchar(255)     DEFAULT NULL COMMENT '产品的下架原因，包括user_offline：手动下架，expire_offline：到期下架，punish_offline：网规处罚下架，violate_offline：交易违规下架，degrade_offline：降级下架，industry_offline：未续约下架',
    `ws_offline_date`                   datetime         DEFAULT NULL COMMENT '产品的下架日期',
    `ws_valid_num`                      int              DEFAULT NULL COMMENT '产品的有效期',
    `package_type`                      tinyint(1)       DEFAULT NULL COMMENT '打包销售: true 非打包销售:false',
    `gmt_modified`                      datetime         DEFAULT NULL COMMENT '修改日期',
    `gmt_create`                        datetime         DEFAULT NULL COMMENT '创建日期',
    `detail_source_list`                json COMMENT '多语言详描',
    `subject_list`                      json COMMENT '多语言标题',
    `locale`                            varchar(255)     DEFAULT NULL COMMENT '商品初始发布locale',
    `market_images`                     json COMMENT '营销图列表',
    `freight_template_id`               json COMMENT '产品关联的运费模版ID',
    `ext_param`                         varchar(255)     DEFAULT NULL COMMENT 'json格式的扩展参数 new_standard_size_chart 是否是新版尺码商品',
    `mode_size_chart_list`              json COMMENT '模特尺码数据 单位 CM',
    `tenant_id`                         bigint           DEFAULT NULL COMMENT '租户ID',
    `creator_id`                        bigint           DEFAULT NULL COMMENT '创建人ID',
    `creator_name`                      varchar(50)      DEFAULT NULL COMMENT '创建人姓名',
    `created_time`                      datetime         DEFAULT NULL COMMENT '创建时间',
    `reviser_id`                        bigint           DEFAULT NULL COMMENT '更新人ID',
    `reviser_name`                      varchar(50)      DEFAULT NULL COMMENT '更新人姓名',
    `revised_time`                      datetime         DEFAULT NULL COMMENT '更新时间',
    `deleted`                           tinyint NOT NULL DEFAULT '0' COMMENT '逻辑删除：0-否；1-是',
    PRIMARY KEY (`product_id`),
    KEY `idx_shop_id` (`shop_id`) COMMENT '店铺ID索引，优化店铺商品查询',
    KEY `idx_product_status` (`product_status_type`) COMMENT '商品状态索引，优化状态筛选',
    KEY `idx_gmt_create` (`gmt_create`) COMMENT '创建时间索引，优化时间范围查询',
    KEY `idx_gmt_modified` (`gmt_modified`) COMMENT '修改时间索引，优化时间范围查询'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='AE平台商品-SPU';


CREATE TABLE `ae_platform_product_sku`
(
    `sku_id`                                  bigint  NOT NULL AUTO_INCREMENT COMMENT '商品id(AE平台的sku_id)',
    `product_id`                              bigint  NOT NULL COMMENT '商品id(AE平台的product_id)',
    `mobile_model_sku_image`                  varchar(1000)    DEFAULT NULL COMMENT '手机机型sku图，仅对选择了手机机型的sku生效',
    `net_weight`                              varchar(255)     DEFAULT NULL COMMENT '假发净重，单位g',
    `aeop_s_k_u_property_list`                json COMMENT 'Sku属性对象list，允许1-3个sku属性对象，按sku属性顺序排放。注意，sku属性是有顺序的，必须按照顺序存放。',
    `currency_code`                           varchar(255)     DEFAULT NULL COMMENT '产品的货币单位。美元: USD, 卢布: RUB',
    `id`                                      varchar(255)     DEFAULT NULL COMMENT 'SKU ID，格式：“sku_property_id:property_value_id”',
    `ipm_sku_stock`                           varchar(255)     DEFAULT NULL COMMENT 'SKU实际可售库存属性ipmSkuStock',
    `sku_code`                                varchar(255)     DEFAULT NULL COMMENT 'Sku商家编码。 格式:半角英数字,长度20,不包含空格大于号和小于号',
    `sku_price`                               varchar(255)     DEFAULT NULL COMMENT 'Sku价格',
    `sku_stock`                               tinyint(1)       DEFAULT NULL COMMENT 'Sku库存,数据格式有货true，无货false；至少有一条sku记录是有货的。',
    `barcode`                                 tinyint(1)       DEFAULT NULL COMMENT '条码，所有的仓发商品（尖货，自营，假发）会返回这个参数',
    `aeop_s_k_u_national_discount_price_list` json COMMENT 'sku分国家的日常促销价',
    `sku_discount_price`                      varchar(255)     DEFAULT NULL COMMENT 'sku日常促销价',
    `gross_weight`                            varchar(255)     DEFAULT NULL COMMENT 'sku重量，单位公斤',
    `package_height`                          varchar(255)     DEFAULT NULL COMMENT 'sku物流尺寸-高，单位cm',
    `package_width`                           varchar(255)     DEFAULT NULL COMMENT 'sku物流尺寸-宽，单位cm',
    `package_length`                          varchar(255)     DEFAULT NULL COMMENT 'sku物流尺寸-长，单位cm',
    `ext_param`                               varchar(255)     DEFAULT NULL COMMENT '扩展参数 json格式',
    `ean_code`                                varchar(255)     DEFAULT NULL COMMENT 'sku维度eanCode',
    `enable_status`                           int              DEFAULT NULL COMMENT 'POP可用状态: 1可用, 0不可用',
    `tenant_id`                               bigint           DEFAULT NULL COMMENT '租户ID',
    `creator_id`                              bigint           DEFAULT NULL COMMENT '创建人ID',
    `creator_name`                            varchar(50)      DEFAULT NULL COMMENT '创建人姓名',
    `created_time`                            datetime         DEFAULT NULL COMMENT '创建时间',
    `reviser_id`                              bigint           DEFAULT NULL COMMENT '更新人ID',
    `reviser_name`                            varchar(50)      DEFAULT NULL COMMENT '更新人姓名',
    `revised_time`                            datetime         DEFAULT NULL COMMENT '更新时间',
    `deleted`                                 tinyint NOT NULL DEFAULT '0' COMMENT '逻辑删除：0-否；1-是',
    PRIMARY KEY (`sku_id`),
    KEY `idx_product_id` (`product_id`) COMMENT '商品ID索引，优化关联查询',
    KEY `idx_sku_code` (`sku_code`) COMMENT '商家编码索引，优化编码查询'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='AE平台商品-SKU';

CREATE TABLE `ae_selected_product_spu`
(
    `product_id`          bigint     NOT NULL COMMENT '商品id(AE平台的product_id)',
    `shop_id`             bigint     NOT NULL COMMENT '店铺id',
    `version_watermark`   varchar(300)        DEFAULT NULL COMMENT '版本水印，用于数据版本控制',
    `version_num`         int                 DEFAULT '1' COMMENT '版本号',
    `pop_category_id`     bigint              DEFAULT NULL COMMENT 'POP品类ID，关联品类表',
    `pop_category_code`   varchar(255)        DEFAULT NULL COMMENT 'POP品类CODE',
    `ae_category_id`      bigint              DEFAULT NULL COMMENT 'AE品类ID',
    `alibaba_id`          varchar(255)        DEFAULT NULL COMMENT '1688 id',
    `style_code`          varchar(255)        DEFAULT NULL COMMENT '商品风格code',
    `style_name`          varchar(255)        DEFAULT NULL COMMENT '商品风格name',
    `brand_name`          varchar(255)        DEFAULT NULL COMMENT '品牌',
    `market_code`         varchar(255)        DEFAULT NULL COMMENT '市场code（字典Market_Style第一层)',
    `market_name`         varchar(255)        DEFAULT NULL COMMENT '市场name（字典Market_Style第一层)',
    `try_on_rule_code`    varchar(255)        DEFAULT NULL COMMENT 'TryOn规则code',
    `try_on_rule_name`    varchar(255)        DEFAULT NULL COMMENT 'TryOn规则name',
    `latest_sync_time`    datetime            DEFAULT NULL COMMENT '最新同步数据时间',
    `is_platform_updated` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否更新到平台：0-否，1-是',
    `is_main_push`        tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否主推：0-否，1-是',
    `is_active_sales`     tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否动销：0-否，1-是',
    `ae_image_urls`       text COMMENT 'AE图片URL地址(多个)',
    `product_title`       text COMMENT '商品标题',
    `product_url`         varchar(255)        DEFAULT NULL COMMENT 'AE商品链接',
    `ae_product_status`   int                 DEFAULT NULL COMMENT 'AE商品状态',
    `product_image`       varchar(1000)       DEFAULT NULL COMMENT '商品图(单张:算法选中/手动选)',
    `result_images`       varchar(1000)       DEFAULT NULL COMMENT '结果图(多张,质检后,+到6张)',
    `recognition_task_id` bigint              DEFAULT NULL COMMENT '识别任务ID（模型接口返回）',
    `try_on_task_id`      bigint              DEFAULT NULL COMMENT 'TryOn任务id',
    `update_task_id`      bigint              DEFAULT NULL COMMENT '更新AE任务id',
    `tenant_id`           bigint              DEFAULT NULL COMMENT '租户ID',
    `creator_id`          bigint              DEFAULT NULL COMMENT '创建人ID',
    `creator_name`        varchar(50)         DEFAULT NULL COMMENT '创建人姓名',
    `created_time`        datetime            DEFAULT NULL COMMENT '创建时间',
    `reviser_id`          bigint              DEFAULT NULL COMMENT '更新人ID',
    `reviser_name`        varchar(50)         DEFAULT NULL COMMENT '更新人姓名',
    `revised_time`        datetime            DEFAULT NULL COMMENT '更新时间',
    `deleted`             tinyint    NOT NULL DEFAULT '0' COMMENT '逻辑删除：0-否；1-是',
    PRIMARY KEY (`product_id`),
    KEY `idx_shop_id` (`shop_id`) COMMENT '店铺ID索引，优化店铺商品查询',
    KEY `idx_platform_updated` (`is_platform_updated`) COMMENT '平台更新状态索引，优化更新任务查询'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='AE精选商品-SPU';


CREATE TABLE `recognition_task`
(
    `id`                     bigint        NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `product_id`             bigint        NOT NULL COMMENT '商品ID（关联商品表）',
    `version_watermark`      varchar(300)           DEFAULT NULL COMMENT 'POP字段-版本水印，用于数据版本控制',
    `image_type`             tinyint       NOT NULL COMMENT '图片类型：1-商品主图',
    `ae_image_urls`          varchar(1000) NOT NULL COMMENT 'AE图片URL地址(多个)',
    `recognition_task_id`    bigint                 DEFAULT NULL COMMENT '识别任务ID（模型接口返回）',
    `recognition_start_time` datetime               DEFAULT NULL COMMENT '识别开始时间',
    `recognition_end_time`   datetime               DEFAULT NULL COMMENT '识别结束时间',
    `recognition_duration`   int                    DEFAULT NULL COMMENT '识别耗时（单位：ms）',
    `recognition_param`      json COMMENT '识别参数',
    `recognition_result`     json COMMENT '识别结果',
    `recognition_status`     int           NOT NULL DEFAULT '0' COMMENT '识别状态：0-未识别；1-识别中；2-识别完成；3-识别失败',
    `recognition_error_msg`  text COMMENT '识别失败原因',
    `try_on_image_url`       varchar(1000)          DEFAULT NULL COMMENT '适合TryOn的图片(单个url)',
    `tenant_id`              bigint                 DEFAULT NULL COMMENT '租户ID',
    `creator_id`             bigint                 DEFAULT NULL COMMENT '创建人ID',
    `creator_name`           varchar(50)            DEFAULT NULL COMMENT '创建人姓名',
    `created_time`           datetime               DEFAULT NULL COMMENT '创建时间',
    `reviser_id`             bigint                 DEFAULT NULL COMMENT '更新人ID',
    `reviser_name`           varchar(50)            DEFAULT NULL COMMENT '更新人姓名',
    `revised_time`           datetime               DEFAULT NULL COMMENT '更新时间',
    `deleted`                tinyint       NOT NULL DEFAULT '0' COMMENT '逻辑删除：0-否；1-是',
    PRIMARY KEY (`id`),
    KEY `idx_product_id` (`product_id`) COMMENT '商品ID索引，用于快速查询商品关联图片',
    KEY `idx_recognition_task_id` (`recognition_task_id`) COMMENT '识别任务ID索引，用于关联识别任务'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='识别任务表';


CREATE TABLE `recognition_style_task`
(
    `id`                     bigint        NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `product_id`             bigint        NOT NULL COMMENT '商品ID（关联商品表）',
    `version_watermark`      varchar(300)           DEFAULT NULL COMMENT 'POP字段-版本水印，用于数据版本控制',
    `ae_image_urls`          varchar(1000) NOT NULL COMMENT 'AE图片URL地址(多个)',
    `recognition_task_id`    bigint                 DEFAULT NULL COMMENT '识别任务ID（模型接口返回）',
    `recognition_start_time` datetime               DEFAULT NULL COMMENT '识别开始时间',
    `recognition_end_time`   datetime               DEFAULT NULL COMMENT '识别结束时间',
    `recognition_duration`   int                    DEFAULT NULL COMMENT '识别耗时（单位：ms）',
    `recognition_param`      json COMMENT '识别参数',
    `recognition_result`     json COMMENT '识别结果',
    `recognition_status`     int           NOT NULL DEFAULT '0' COMMENT '识别状态：0-未识别；1-识别中；2-识别完成；3-识别失败',
    `recognition_error_msg`  text COMMENT '识别失败原因',
    `tenant_id`              bigint                 DEFAULT NULL COMMENT '租户ID',
    `creator_id`             bigint                 DEFAULT NULL COMMENT '创建人ID',
    `creator_name`           varchar(50)            DEFAULT NULL COMMENT '创建人姓名',
    `created_time`           datetime               DEFAULT NULL COMMENT '创建时间',
    `reviser_id`             bigint                 DEFAULT NULL COMMENT '更新人ID',
    `reviser_name`           varchar(50)            DEFAULT NULL COMMENT '更新人姓名',
    `revised_time`           datetime               DEFAULT NULL COMMENT '更新时间',
    `deleted`                tinyint       NOT NULL DEFAULT '0' COMMENT '逻辑删除：0-否；1-是',
    PRIMARY KEY (`id`),
    KEY `idx_product_id` (`product_id`) COMMENT '商品ID索引，用于快速查询商品关联图片',
    KEY `idx_recognition_task_id` (`recognition_task_id`) COMMENT '识别任务ID索引，用于关联识别任务'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='识别风格任务表';


CREATE TABLE `ae_update_platform_task`
(
    `id`                 bigint  NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    `shop_id`            bigint  NOT NULL COMMENT '店铺ID',
    `product_id`         bigint  NOT NULL COMMENT '商品ID，关联SPU表',
    `try_on_task_id`     bigint  NOT NULL COMMENT 'TryOn任务ID，关联TryOn任务表',
    `task_status`        tinyint NOT NULL DEFAULT '0' COMMENT '任务状态：0-待更新，1-更新中，2-更新成功，3-更新失败',
    `version_num`        int              DEFAULT NULL COMMENT '任务版本号(同一个商品每发起一次任务则自增1)',
    `request_image_urls` text COMMENT '请求更新图片URL地址(多个)',
    `request_params`     text COMMENT '请求参数',
    `response_params`    text COMMENT '响应参数',
    `exception_info`     text COMMENT '异常信息',
    `complete_time`      datetime         DEFAULT NULL COMMENT '调用成功完成时间',
    `tenant_id`          bigint           DEFAULT NULL COMMENT '租户ID',
    `creator_id`         bigint           DEFAULT NULL COMMENT '创建人ID',
    `creator_name`       varchar(50)      DEFAULT NULL COMMENT '创建人姓名',
    `created_time`       datetime         DEFAULT NULL COMMENT '创建时间',
    `reviser_id`         bigint           DEFAULT NULL COMMENT '更新人ID',
    `reviser_name`       varchar(50)      DEFAULT NULL COMMENT '更新人姓名',
    `revised_time`       datetime         DEFAULT NULL COMMENT '更新时间',
    `deleted`            tinyint NOT NULL DEFAULT '0' COMMENT '逻辑删除：0-否；1-是',
    PRIMARY KEY (`id`),
    KEY `idx_shop_id` (`shop_id`) COMMENT '店铺ID索引，优化查询',
    KEY `idx_product_id` (`product_id`) COMMENT '商品ID索引，优化查询',
    KEY `idx_try_on_task_id` (`try_on_task_id`) COMMENT 'TryOn任务ID索引，优化查询',
    KEY `idx_update_status` (`task_status`) COMMENT '任务状态索引，优化状态筛选'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='AE平台商品更新任务表';


CREATE TABLE `try_on_task`
(
    `task_id`           bigint      NOT NULL COMMENT '任务ID',
    `task_code`         varchar(50) NULL     DEFAULT NULL COMMENT '任务编号',
    `product_id`        bigint      NULL     DEFAULT NULL COMMENT '商品ID',
    `product_snapshot`  JSON COMMENT '商品快照',
    `version_num`       int         NULL     DEFAULT NULL COMMENT '任务版本号(同一个商品每发起一次任务则自增1)',
    `task_param`        JSON COMMENT '发起任务参数配置',
    `task_param_rule`   JSON COMMENT '发起任务参数获取规则',
    `state`             int         NULL     DEFAULT NULL COMMENT '任务状态1创建中2进行中3已完成4失败5已取消',
    `push_time`         datetime    NULL     DEFAULT NULL COMMENT '推送AI时间',
    `finish_time`       datetime    NULL     DEFAULT NULL COMMENT '完成时间',
    `qc_result`         int         NULL     DEFAULT NULL COMMENT '质检结果0待质检1通过2未通过',
    `task_creator_id`   bigint      NULL     DEFAULT NULL COMMENT '任务发起人ID',
    `task_creator_name` varchar(32) NULL     DEFAULT NULL COMMENT '任务发起人',
    `task_created_time` datetime    NULL     DEFAULT NULL COMMENT '任务发起时间',
    `is_latest`         tinyint     NULL     DEFAULT 1 COMMENT '是否最新(同一商品下最新的一条) 0-否、1-是',
    `fail_reason`       text        NULL COMMENT '任务失败原因',
    `deleted`           tinyint(1)  NOT NULL DEFAULT 0 COMMENT '是否已删除;0未删除，1已删除',
    `tenant_id`         bigint      NULL     DEFAULT NULL COMMENT '租户ID',
    `creator_id`        bigint      NOT NULL COMMENT '创建人id',
    `creator_name`      varchar(32) NOT NULL COMMENT '创建人',
    `created_time`      datetime    NOT NULL COMMENT '创建时间',
    `reviser_id`        bigint      NULL     DEFAULT NULL COMMENT '最近修改人id;新增时取创建人id',
    `reviser_name`      varchar(32) NULL     DEFAULT NULL COMMENT '最近修改人名称;新增时取创建人名称',
    `revised_time`      datetime    NULL     DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`task_id`) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = 'TryOn任务'
  ROW_FORMAT = Dynamic;


CREATE TABLE `try_on_task_qc`
(
    `qc_id`          bigint      NOT NULL COMMENT '质检任务ID',
    `qc_code`        varchar(50) NULL     DEFAULT NULL COMMENT '质检任务编码',
    `product_id`     bigint      NULL     DEFAULT NULL COMMENT '商品ID',
    `task_id`        bigint      NULL     DEFAULT NULL COMMENT '任务ID',
    `state`          int         NULL     DEFAULT NULL COMMENT '任务状态0待质检1已质检2已取消',
    `qc_result`      int         NULL     DEFAULT NULL COMMENT '质检结果0待质检1通过2不通过',
    `qc_time`        datetime    NULL     DEFAULT NULL COMMENT '质检时间',
    `qc_record`      json COMMENT '质检记录JSON（记录图片,通过状态）',
    `inspector_id`   bigint      NULL     DEFAULT NULL COMMENT '质检人ID',
    `inspector_name` varchar(16) NULL     DEFAULT NULL COMMENT '质检人名称',
    `version_num`    int         NULL     DEFAULT NULL COMMENT '质检任务版本号（暂时跟任务版本号保持一致）',
    `stop_retry`     tinyint     NULL     DEFAULT NULL COMMENT '不通过时是否停止发起AI任务0否1是',
    `deleted`        tinyint(1)  NOT NULL DEFAULT 0 COMMENT '是否已删除;0未删除，1已删除',
    `tenant_id`      bigint      NULL     DEFAULT NULL COMMENT '租户ID',
    `creator_id`     bigint      NOT NULL COMMENT '创建人id',
    `creator_name`   varchar(32) NOT NULL COMMENT '创建人',
    `created_time`   datetime    NOT NULL COMMENT '创建时间',
    `reviser_id`     bigint      NULL     DEFAULT NULL COMMENT '最近修改人id;新增时取创建人id',
    `reviser_name`   varchar(32) NULL     DEFAULT NULL COMMENT '最近修改人名称;新增时取创建人名称',
    `revised_time`   datetime    NULL     DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`qc_id`) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = 'TryOn质检任务'
  ROW_FORMAT = Dynamic;


CREATE TABLE `try_on_task_sub`
(
    `sub_task_id`         bigint      NOT NULL COMMENT '子任务ID',
    `task_id`             bigint      NULL     DEFAULT NULL COMMENT '任务ID',
    `state`               int         NULL     DEFAULT NULL COMMENT 'AI任务状态10-生成中；20-已中止；30-已完成；40-失败；50-排队中',
    `ai_task_id`          bigint      NULL     DEFAULT NULL COMMENT 'AI返回任务ID',
    `ai_generated_images` JSON COMMENT 'AI返回结果',
    `ai_generated_time`   datetime    NULL     DEFAULT NULL COMMENT 'AI结果生成时间',
    `is_timeout`          tinyint     NULL     DEFAULT 0 COMMENT '是否超时0否1是',
    `deleted`             tinyint     NOT NULL DEFAULT 0 COMMENT '是否已删除;0未删除，1已删除',
    `tenant_id`           bigint      NULL     DEFAULT NULL COMMENT '租户ID',
    `creator_id`          bigint      NOT NULL COMMENT '创建人id',
    `creator_name`        varchar(32) NOT NULL COMMENT '创建人',
    `created_time`        datetime    NOT NULL COMMENT '创建时间',
    `reviser_id`          bigint      NULL     DEFAULT NULL COMMENT '最近修改人id;新增时取创建人id',
    `reviser_name`        varchar(32) NULL     DEFAULT NULL COMMENT '最近修改人名称;新增时取创建人名称',
    `revised_time`        datetime    NULL     DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`sub_task_id`) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = 'TryOn子任务（对应灵感中心任务）'
  ROW_FORMAT = Dynamic;