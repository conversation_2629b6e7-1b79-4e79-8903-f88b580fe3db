update publish_attribute set show_type = 'singleSelect' where deleted!=5;

update publish_category c
    left join (select count(1) as attributeAmount,attr.category_id from publish_category_attr attr where attr.deleted=0 group by attr.category_id) as t1 on c.publish_category_id = t1.category_id
    left join (select count(1) as platformAmount,cmapping.publish_category_id as category_id from publish_category_mapping cmapping where cmapping.deleted=0 group by cmapping.publish_category_id) as t2 on c.publish_category_id = t2.category_id
    left join (select count(1) as platformAttributeAmount,pamapping.category_id from publish_platform_attr pamapping where pamapping.deleted=0 group by pamapping.category_id) as t3 on c.publish_category_id = t3.category_id
    set c.attribute_associate_state = (CASE WHEN t1.attributeAmount is not null THEN 1 ELSE 0 END),
        c.platform_associate_state = (CASE WHEN t3.platformAttributeAmount is not null THEN 2 WHEN t2.platformAmount THEN 1 ELSE 0 END)
where c.deleted=0