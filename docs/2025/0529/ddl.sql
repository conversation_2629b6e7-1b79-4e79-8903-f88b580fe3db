CREATE TABLE `product_tag`
(
    `tag_id`       bigint auto_increment NOT NULL COMMENT '企划id',
    `target_id`    bigint                NOT NULL COMMENT '目标id',
    `target_type`  varchar(255)          NOT NULL COMMENT '目标类型',
    `tag_key`      varchar(255)          NOT NULL COMMENT '标签key',
    `tag_value`    varchar(255)          NOT NULL COMMENT '标签value',
    `creator_id`   bigint                NOT NULL COMMENT '创建人id',
    `creator_name` varchar(32)           NOT NULL COMMENT '创建人名称',
    `created_time` datetime              NOT NULL COMMENT '创建时间',
    `reviser_id`   bigint                         DEFAULT NULL COMMENT '最近修改人id',
    `reviser_name` varchar(32)                    DEFAULT NULL COMMENT '最近修改人名称',
    `revised_time` datetime              NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最近更新时间',
    `is_deleted`   tinyint(1)            NOT NULL COMMENT '是否已删除;0未删除，1已删除',
    `tenant_id`    bigint                         DEFAULT NULL COMMENT '租户id',
    PRIMARY KEY (`tag_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='商品标签表';


alter table product
    add designer_id bigint null comment '设计师id' after main_img_url;

alter table product_skc
    add check_price_type int          null comment '核价类型(核价环节)：1.精准核价 2.预估核价' after cost_price,
    add pricer_id        bigint       null comment '核价师id' after check_price_type,
    add pricer_name      varchar(255) null comment '核价师name' after pricer_id;

alter table ae_sale_goods
    add cost_price_update_state     int default 0 null comment '供货价是否更新【1有更新；0没有】' after update_state,
    add system_update_platform_fail int default 0 null comment '系统更新平台失败(1是 0否)' after cost_price_update_state;

alter table sale_goods
    add cost_price_update_state     int default 0 null comment '供货价是否更新【1有更新；0没有】' after update_state,
    add system_update_platform_fail int default 0 null comment '系统更新平台失败(1是 0否)' after cost_price_update_state;

alter table sale_skc
    add update_local_price      decimal(10, 2) null comment '更新供货价(新的价格)' after cost_price,
    add update_purchase_price   decimal(10, 2) null comment '更新采购价(新的价格)' after update_local_price,
    add update_cost_price       decimal(10, 2) null comment '更新定价成本(新的价格)' after update_purchase_price,
    add cost_price_update_state int default 0  null comment '成本价是否更新(1有更新；0没有)' after update_cost_price
;

alter table ae_sale_skc
    add update_local_price      decimal(10, 2) null comment '更新供货价(新的价格)' after cost_price,
    add update_purchase_price   decimal(10, 2) null comment '更新采购价(新的价格)' after update_local_price,
    add update_cost_price       decimal(10, 2) null comment '更新定价成本(新的价格)' after update_purchase_price,
    add cost_price_update_state int default 0  null comment '成本价是否更新(1有更新；0没有)' after update_cost_price
;

CREATE TABLE `ding_talk_approval_price_overrange`
(
    `id`                                     bigint       NOT NULL COMMENT 'id' primary key auto_increment,
    `instance_id`                            varchar(255) NULL COMMENT '审批实例id(钉钉返回)',
    `approver_id`                            varchar(255) NULL COMMENT '审批人id',
    `approver_name`                          varchar(255) NULL COMMENT '审批人name',
    `approval_content_spu_code`              varchar(255) null COMMENT '审批内容-SPU编号',
    `approval_content_skc_code`              varchar(255) null COMMENT '审批内容-SKC编号',
    `approval_content_color`                 varchar(255) null COMMENT '审批内容-颜色',
    `approval_content_category`              varchar(255) null COMMENT '审批内容-品类',
    `approval_content_store`                 varchar(255) null COMMENT '审批内容-店铺',
    `approval_content_cost_change_stage`     varchar(255) null COMMENT '审批内容-成本变动环节(预估核价/生产核价)',
    `approval_content_current_cost`          varchar(255) null COMMENT '审批内容-当前成本(指SKC供货价)',
    `approval_content_original_cost`         varchar(255) null COMMENT '审批内容-原成本(指SKC现供货价)',
    `approval_content_current_pricing_cost`  varchar(255) null COMMENT '审批内容-当前定价成本(指SKC现需应用的定价成本)',
    `approval_content_original_pricing_cost` varchar(255) null COMMENT '审批内容-原定价成本(指SKC原需应用的定价成本)',
    `approval_content_cost_change_threshold` varchar(255) null COMMENT '审批内容-成本变动阈值(如:10%)',
    `approval_content_pricer_id`             varchar(255) null COMMENT '审批内容-核价师id',
    `approval_content_pricer`                varchar(255) null COMMENT '审批内容-核价师',
    `approval_content_excess_amount`         varchar(255) null COMMENT '审批内容-超出部分(当前定价成本 - 原定价成本)',
    `approval_result`                        varchar(255) null COMMENT '审批结果',
    `approval_reason`                        text         null COMMENT '审批结果原因',
    original_local_price                     varchar(255) null comment '原供货价',
    current_local_price                      varchar(255) null comment '新供货价',
    original_purchase_price                  varchar(255) null comment '原采购价',
    current_purchase_price                   varchar(255) null comment '新采购价',
    `creator_id`                             bigint       NOT NULL COMMENT '创建人id',
    `creator_name`                           varchar(32)  NOT NULL COMMENT '创建人名称',
    `created_time`                           datetime     NOT NULL COMMENT '创建时间',
    `reviser_id`                             bigint                DEFAULT NULL COMMENT '最近修改人id',
    `reviser_name`                           varchar(32)           DEFAULT NULL COMMENT '最近修改人名称',
    `revised_time`                           datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最近更新时间',
    `is_deleted`                             tinyint(1)   NOT NULL COMMENT '是否已删除;0未删除，1已删除',
    `tenant_id`                              bigint                DEFAULT NULL COMMENT '租户id'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='钉钉审批表-超价';


-- 价格预警配置主表
CREATE TABLE `price_alert_configuration`
(
    `price_alert_configuration_id` bigint unsigned NOT NULL COMMENT '配置ID主键',
    `platform_id`                  bigint          NOT NULL COMMENT '平台ID：LAZADA, AE的等',
    `publish_category_id`          bigint          NOT NULL COMMENT '内部品类ID',
    `publish_category_code`        varchar(60)              DEFAULT NULL COMMENT '内部品类编码',
    `publish_category_path`        varchar(120)             DEFAULT NULL COMMENT '内部品类路径名称（多级用/分隔）',
    `cost_price_min`               decimal(16, 6)           DEFAULT NULL COMMENT '定价成本最小值',
    `cost_price_max`               decimal(16, 6)           DEFAULT NULL COMMENT '定价成本最大值',
    `cost_currency_type`           varchar(16)     NOT NULL DEFAULT 'CNY' COMMENT '成本币种：CNY-人民币等',
    `is_deleted`                   tinyint(1)      NOT NULL DEFAULT '0' COMMENT '是否删除，0-未删除，1-已删除',
    `creator_id`                   bigint                   DEFAULT NULL COMMENT '创建者ID',
    `creator_name`                 varchar(50)              DEFAULT NULL COMMENT '创建人名称',
    `created_time`                 datetime        NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `reviser_id`                   bigint                   DEFAULT NULL COMMENT '修改者ID',
    `reviser_name`                 varchar(50)              DEFAULT NULL COMMENT '修改人名称',
    `revised_time`                 datetime                 DEFAULT NULL COMMENT '修改时间',
    `tenant_id`                    bigint                   DEFAULT NULL COMMENT '租户id',
    PRIMARY KEY (`price_alert_configuration_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT ='价格预警配置主表';

-- 价格预警配置站点明细表
CREATE TABLE `price_alert_configuration_detail`
(
    `price_alert_configuration_detail_id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '明细ID主键',
    `price_alert_configuration_id`        bigint unsigned NOT NULL COMMENT '关联的价格预警配置ID',
    `country_code`                        varchar(16)     NOT NULL COMMENT '国家站点代码：TH-泰国，PH-菲律宾，SG-新加坡等',
    `retail_price_min`                    decimal(16, 6)           DEFAULT NULL COMMENT '划线价最小值',
    `retail_price_max`                    decimal(16, 6)           DEFAULT NULL COMMENT '划线价最大值',
    `sale_price_min`                      decimal(16, 6)           DEFAULT NULL COMMENT '售价最小值',
    `sale_price_max`                      decimal(16, 6)           DEFAULT NULL COMMENT '售价最大值',
    `currency_type`                       varchar(16)              DEFAULT 'CNY' NOT NULL COMMENT '币种类型，CNY，USD',
    `is_deleted`                          tinyint(1)      NOT NULL DEFAULT '0' COMMENT '是否删除，0-未删除，1-已删除',
    `creator_id`                          bigint                   DEFAULT NULL COMMENT '创建者ID',
    `creator_name`                        varchar(50)              DEFAULT NULL COMMENT '创建人名称',
    `created_time`                        datetime        NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `reviser_id`                          bigint                   DEFAULT NULL COMMENT '修改者ID',
    `reviser_name`                        varchar(50)              DEFAULT NULL COMMENT '修改人名称',
    `revised_time`                        datetime                 DEFAULT NULL COMMENT '修改时间',
    `tenant_id`                           bigint                   DEFAULT NULL COMMENT '租户id',
    PRIMARY KEY (`price_alert_configuration_detail_id`),
    KEY `idx_price_alert_configuration_id` (`price_alert_configuration_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT ='价格预警配置站点明细表';

-- 价格预警记录表（用于记录预警触发历史）
CREATE TABLE `price_alert_record`
(
    `price_alert_record_id`        bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '记录ID主键',
    `price_alert_configuration_id` bigint unsigned          DEFAULT NULL COMMENT '关联的价格预警配置ID',
    `product_id`                   bigint                   DEFAULT NULL COMMENT '触发预警的商品ID',
    `sale_goods_id`                bigint                   DEFAULT NULL COMMENT '触发预警的销售商品ID',
    `country_code`                 varchar(16)              DEFAULT NULL COMMENT '国家站点代码',
    `alert_type`                   varchar(32)     NOT NULL COMMENT '预警类型：COST_PRICE-成本价，LIST_PRICE-划线价，SALE_PRICE-售价',
    `current_value`                decimal(16, 6)  NOT NULL COMMENT '当前触发预警的值',
    `threshold_min`                decimal(16, 6)           DEFAULT NULL COMMENT '预警阈值最小值',
    `threshold_max`                decimal(16, 6)           DEFAULT NULL COMMENT '预警阈值最大值',
    `alert_message`                VARCHAR(2048) COMMENT '预警消息',
    `alert_message_template_code`  varchar(64)              DEFAULT NULL COMMENT '消息模板编码，如TPL_PRICE_FLOOR_NOTIFY',
    `is_deleted`                   tinyint(1)      NOT NULL DEFAULT '0' COMMENT '是否删除，0-未删除，1-已删除',
    `creator_id`                   bigint                   DEFAULT NULL COMMENT '创建者ID',
    `creator_name`                 varchar(50)              DEFAULT NULL COMMENT '创建人名称',
    `created_time`                 datetime        NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `reviser_id`                   bigint                   DEFAULT NULL COMMENT '修改者ID',
    `reviser_name`                 varchar(50)              DEFAULT NULL COMMENT '修改人名称',
    `revised_time`                 datetime                 DEFAULT NULL COMMENT '修改时间',
    `tenant_id`                    bigint                   DEFAULT NULL COMMENT '租户id',
    PRIMARY KEY (`price_alert_record_id`),
    KEY `idx_price_alert_configuration_id` (`price_alert_configuration_id`),
    KEY `idx_product_id` (`product_id`),
    KEY `idx_sale_goods_id` (`sale_goods_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT ='价格预警记录表';

-- 移除版型表
DROP TABLE pop_product.prototype_basic;
