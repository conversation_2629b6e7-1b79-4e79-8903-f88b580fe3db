update product set image_version_num=1 where publish_state!=0;

update product_skc skc
left join product p on skc.product_id=p.product_id
set skc.cost_price = IFNULL(skc.purchase_price,0)
where p.style_type=2 and p.supply_mode='imitation' and (skc.cost_price is null or skc.cost_price=0);

update product_template_ae_skc skc
left join product_template_ae_spu spu on skc.ae_spu_id=spu.ae_spu_id
left join product p on p.product_id=spu.product_id
set skc.cost_price = IFNULL(skc.purchase_price,0)
where p.style_type=2 and p.supply_mode='imitation' and (skc.cost_price is null or skc.cost_price=0);

update product_template_lazada_skc skc
left join product_template_lazada_spu spu on skc.lazada_spu_id=spu.lazada_spu_id
left join product p on p.product_id=spu.product_id
set skc.cost_price = IFNULL(skc.purchase_price,0)
where p.style_type=2 and p.supply_mode='imitation' and (skc.cost_price is null or skc.cost_price=0);

update product_template_temu_skc skc
left join product_template_temu_spu spu on skc.temu_spu_id=spu.temu_spu_id
left join product p on p.product_id=spu.product_id
set skc.cost_price = IFNULL(skc.purchase_price,0)
where p.style_type=2 and p.supply_mode='imitation' and (skc.cost_price is null or skc.cost_price=0);