ALTER TABLE `pop_product`.`product`
    ADD COLUMN `style_type`          tinyint NULL DEFAULT NULL COMMENT '款式类型1设计款2现货款3数据印花款' AFTER `fit_name`,
    ADD COLUMN `image_package_state` tinyint NULL DEFAULT NULL COMMENT '图包状态1未完成2已完成3更新中4已更新' AFTER `style_type`,
    ADD COLUMN `image_version_num`   int     NULL COMMENT '上架时使用的图包版本号' AFTER `image_package_state`;

ALTER TABLE `pop_product`.`image_repository`
    ADD COLUMN `version_num` int NULL DEFAULT 1 COMMENT '图包版本号' AFTER `qc_status`;

ALTER TABLE `pop_product`.`product_system_update_fail_log`
    ADD COLUMN `update_state` tinyint(1) NULL DEFAULT 3 COMMENT '更新状态1更新中2成功3失败' AFTER `process_state`,
    COMMENT = '商品-系统更新日志表';

-- 图包规则
CREATE TABLE `image_pack_rule`
(
    `image_pack_rule_id` bigint       NOT NULL COMMENT '规则ID',
    `rule_code`          varchar(10)  NOT NULL COMMENT '规则序号，R+3位流水号，如R001',
    `rule_name`          varchar(100) NOT NULL COMMENT '规则名称',
    `channel_id`         bigint       NOT NULL COMMENT '渠道ID',
    `channel_name`       varchar(50)  NOT NULL COMMENT '渠道名称',
    `platform_id`        bigint       NOT NULL COMMENT '平台ID',
    `platform_name`      varchar(50)  NOT NULL COMMENT '平台名称',
    `version`            varchar(20)  NOT NULL COMMENT '规则版本号，时间戳格式，如20250605155500123456',
    `remark`             varchar(100)          DEFAULT NULL COMMENT '备注',
    `enabled`            tinyint(1)            DEFAULT '1' COMMENT '是否启用:0-禁用,1-启用',
    `deleted`            tinyint(1)            DEFAULT '0' COMMENT '是否已删除:0未删除,1已删除',
    `creator_id`         bigint       NOT NULL COMMENT '创建人ID',
    `creator_name`       varchar(32)  NOT NULL COMMENT '创建人',
    `created_time`       datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `reviser_id`         bigint                DEFAULT NULL COMMENT '最近修改人ID',
    `reviser_name`       varchar(32)           DEFAULT NULL COMMENT '最近修改人名称',
    `revised_time`       datetime              DEFAULT NULL COMMENT '更新时间',
    `tenant_id`          bigint                DEFAULT NULL COMMENT '租户ID',
    PRIMARY KEY (`image_pack_rule_id`),
    UNIQUE KEY `uniq_rule_code` (`rule_code`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='图包规则表';

CREATE TABLE `image_pack_rule_detail`
(
    `image_pack_rule_detail_id` bigint      NOT NULL AUTO_INCREMENT COMMENT '详情ID',
    `image_pack_rule_id`        bigint      NOT NULL COMMENT '规则ID',
    `image_type`                int         NOT NULL COMMENT '图片类型:1-版型表,2-类目表,3-尺码表,4-商品首图,5-商品详情图,6-面料图,7-卖点细节,8-面料颜色,9-买家秀',
    `image_count`               int         NOT NULL DEFAULT '1' COMMENT '图片数量',
    `display_order`             int         NOT NULL DEFAULT '1' COMMENT '显示顺序:1-顺序显示,2-倒序显示,3-随机显示',
    `sequence`                  int         NOT NULL DEFAULT '0' COMMENT '排序号',
    `detail_type`               tinyint(1)  NOT NULL COMMENT '详情类型:1-商品图片,2-商品详情图',
    `is_main_image`             tinyint(1)           DEFAULT '0' COMMENT '是否首图:0-否,1-是',
    `main_image_type`           int                  DEFAULT NULL COMMENT '首图类型:1-固定标识,2-拼图',
    `main_image_identifier`     varchar(50)          DEFAULT NULL COMMENT '首图标识符',
    `deleted`                   tinyint(1)           DEFAULT '0' COMMENT '是否已删除:0未删除,1已删除',
    `creator_id`                bigint      NOT NULL COMMENT '创建人ID',
    `creator_name`              varchar(32) NOT NULL COMMENT '创建人',
    `created_time`              datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `reviser_id`                bigint               DEFAULT NULL COMMENT '最近修改人ID',
    `reviser_name`              varchar(32)          DEFAULT NULL COMMENT '最近修改人名称',
    `revised_time`              datetime             DEFAULT NULL COMMENT '更新时间',
    `tenant_id`                 bigint               DEFAULT NULL COMMENT '租户ID',
    PRIMARY KEY (`image_pack_rule_detail_id`),
    KEY `idx_image_pack_rule_id` (`image_pack_rule_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='图包规则详情表';

CREATE TABLE `image_pack_rule_shop`
(
    `image_pack_rule_shop_id` bigint      NOT NULL AUTO_INCREMENT COMMENT '关联ID',
    `image_pack_rule_id`      bigint      NOT NULL COMMENT '规则ID',
    `shop_id`                 bigint      NOT NULL COMMENT '店铺ID',
    `deleted`                 tinyint(1)           DEFAULT '0' COMMENT '是否已删除:0未删除,1已删除',
    `creator_id`              bigint      NOT NULL COMMENT '创建人ID',
    `creator_name`            varchar(32) NOT NULL COMMENT '创建人',
    `created_time`            datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `reviser_id`              bigint               DEFAULT NULL COMMENT '最近修改人ID',
    `reviser_name`            varchar(32)          DEFAULT NULL COMMENT '最近修改人名称',
    `revised_time`            datetime             DEFAULT NULL COMMENT '更新时间',
    `tenant_id`               bigint               DEFAULT NULL COMMENT '租户ID',
    PRIMARY KEY (`image_pack_rule_shop_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='图包规则店铺关联表';

ALTER TABLE `ae_sale_goods`
    ADD COLUMN `image_pack_rule_id`      bigint      NULL COMMENT '图包规则ID',
    ADD COLUMN `image_pack_rule_version` varchar(20) NULL COMMENT '使用时的规则版本号，时间戳格式';

-- 标题配置
CREATE TABLE product_title_config
(
    product_title_config_id BIGINT                               NOT NULL COMMENT '配置ID' PRIMARY KEY,
    channel_id              BIGINT                               NOT NULL COMMENT '渠道ID',
    platform_id             BIGINT                               NOT NULL COMMENT '平台ID',
    title_count             INT        DEFAULT 1                 NOT NULL COMMENT '标题数量(1-100)',
    deleted                 TINYINT(1) DEFAULT 0                 NOT NULL COMMENT '是否已删除:0未删除,1已删除',
    creator_id              BIGINT                               NOT NULL COMMENT '创建人id',
    creator_name            VARCHAR(32)                          NOT NULL COMMENT '创建人',
    created_time            DATETIME   DEFAULT CURRENT_TIMESTAMP NOT NULL COMMENT '创建时间',
    reviser_id              BIGINT                               NULL COMMENT '最近修改人id',
    reviser_name            VARCHAR(32)                          NULL COMMENT '最近修改人名称',
    revised_time            DATETIME                             NULL COMMENT '更新时间',
    tenant_id               BIGINT                               NULL COMMENT '租户id'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT '商品标题配置表';

CREATE TABLE product_title_config_rule
(
    product_title_config_rule_id BIGINT                               NOT NULL AUTO_INCREMENT COMMENT '关联ID' PRIMARY KEY,
    product_title_config_id      BIGINT                               NOT NULL COMMENT '配置ID',
    title_index                  INT                                  NOT NULL COMMENT '标题序号(0,1,2...)',
    product_title_rule_id        BIGINT                               NOT NULL COMMENT '标题规则ID',
    deleted                      TINYINT(1) DEFAULT 0                 NOT NULL COMMENT '是否已删除:0未删除,1已删除',
    creator_id                   BIGINT                               NOT NULL COMMENT '创建人id',
    creator_name                 VARCHAR(32)                          NOT NULL COMMENT '创建人',
    created_time                 DATETIME   DEFAULT CURRENT_TIMESTAMP NOT NULL COMMENT '创建时间',
    reviser_id                   BIGINT                               NULL COMMENT '最近修改人id',
    reviser_name                 VARCHAR(32)                          NULL COMMENT '最近修改人名称',
    revised_time                 DATETIME                             NULL COMMENT '更新时间',
    tenant_id                    BIGINT                               NULL COMMENT '租户id'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT '商品标题配置规则关联表';

CREATE TABLE product_title_config_shop
(
    product_title_config_shop_id BIGINT                               NOT NULL AUTO_INCREMENT COMMENT '关联ID' PRIMARY KEY,
    product_title_config_id      BIGINT                               NOT NULL COMMENT '配置ID',
    title_index                  INT                                  NOT NULL COMMENT '标题序号(0,1,2...)',
    shop_id                      BIGINT                               NOT NULL COMMENT '店铺ID',
    deleted                      TINYINT(1) DEFAULT 0                 NOT NULL COMMENT '是否已删除:0未删除,1已删除',
    creator_id                   BIGINT                               NOT NULL COMMENT '创建人id',
    creator_name                 VARCHAR(32)                          NOT NULL COMMENT '创建人',
    created_time                 DATETIME   DEFAULT CURRENT_TIMESTAMP NOT NULL COMMENT '创建时间',
    reviser_id                   BIGINT                               NULL COMMENT '最近修改人id',
    reviser_name                 VARCHAR(32)                          NULL COMMENT '最近修改人名称',
    revised_time                 DATETIME                             NULL COMMENT '更新时间',
    tenant_id                    BIGINT                               NULL COMMENT '租户id'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT '商品标题配置店铺关联表';

-- 创建索引
CREATE INDEX idx_product_title_config_rule_config_id ON product_title_config_rule (product_title_config_id);
CREATE INDEX idx_product_title_config_shop_config_id ON product_title_config_shop (product_title_config_id);

-- 发货地
alter table product_template_ae_sku
    add `ships_from_attribute_id`         bigint       NULL COMMENT '发货地-属性ID' after enable_state,
    add `ships_from_attribute_name`       varchar(255) NULL COMMENT '发货地-属性名称' after ships_from_attribute_id,
    add `ships_from_attribute_value_id`   bigint       NULL COMMENT '发货地-属性值ID' after ships_from_attribute_name,
    add `ships_from_attribute_value_name` varchar(255) NULL COMMENT '发货地-属性值名称' after ships_from_attribute_value_id;

alter table ae_sale_sku
    add `ships_from_attribute_id`         bigint       NULL COMMENT '发货地-属性ID' after size_property_value_id,
    add `ships_from_attribute_name`       varchar(255) NULL COMMENT '发货地-属性名称' after ships_from_attribute_id,
    add `ships_from_attribute_value_id`   bigint       NULL COMMENT '发货地-属性值ID' after ships_from_attribute_name,
    add `ships_from_attribute_value_name` varchar(255) NULL COMMENT '发货地-属性值名称' after ships_from_attribute_value_id;

alter table ae_original_product_sku
    add `ships_from_attribute_id`         bigint       NULL COMMENT '发货地-属性ID' after size,
    add `ships_from_attribute_name`       varchar(255) NULL COMMENT '发货地-属性名称' after ships_from_attribute_id,
    add `ships_from_attribute_value_id`   bigint       NULL COMMENT '发货地-属性值ID' after ships_from_attribute_name,
    add `ships_from_attribute_value_name` varchar(255) NULL COMMENT '发货地-属性值名称' after ships_from_attribute_value_id;


-- 标题规则适配：新增多标题生成JSON字段
-- 为商品模板SPU表添加多标题生成数据存储字段

-- AE SPU表
ALTER TABLE `product_template_ae_spu`
    ADD COLUMN `generated_titles_json` json NULL COMMENT '多标题生成数据，存储为JSON格式，包含titleCount、titles数组等信息' AFTER `generated_title_over_length`;

-- Lazada SPU表
ALTER TABLE `product_template_lazada_spu`
    ADD COLUMN `generated_titles_json` json NULL COMMENT '多标题生成数据，存储为JSON格式，包含titleCount、titles数组等信息' AFTER `generated_title_over_length`;

-- Temu SPU表
ALTER TABLE `product_template_temu_spu`
    ADD COLUMN `generated_titles_json` json NULL COMMENT '多标题生成数据，存储为JSON格式，包含titleCount、titles数组等信息' AFTER `generated_title_over_length`;

-- 图包规则表增加默认规则字段
ALTER TABLE `image_pack_rule`
    ADD COLUMN `default_rule` tinyint(1) DEFAULT '0' COMMENT '是否默认规则:0-否,1-是' AFTER `enabled`;


alter table product_batch_update_price_inventory_task
    add ships_from varchar(255) null comment '发货地' after pricing_cost;