CREATE TABLE `product_asp_configuration`
(
    `product_asp_configuration_id` bigint unsigned NOT NULL COMMENT '主键',
    `platform_id`                  bigint          NOT NULL COMMENT '平台',
    `country_type`                 varchar(32)     NOT NULL COMMENT '店铺类型,cb,local',
    `shop_business_type`           tinyint(1)      NOT NULL COMMENT '运营模式',
    `print_type`                   varchar(32)     NOT NULL COMMENT '印花类型',
    `prototype_num`                varchar(32)     NOT NULL COMMENT '版型号',
    `asp_amount`                   decimal(16, 6)  NOT NULL COMMENT '商品ASP金额(USD)',
    `is_deleted`                   tinyint(1)      NOT NULL DEFAULT '0' COMMENT '是否删除，0-未删除，1-已删除',
    `creator_id`                   bigint                   DEFAULT NULL COMMENT '创建者ID',
    `created_time`                 datetime        NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `reviser_id`                   bigint                   DEFAULT NULL COMMENT '修改者ID',
    `revised_time`                 datetime                 DEFAULT NULL COMMENT '修改时间',
    `creator_name`                 varchar(50)              DEFAULT NULL COMMENT '创建人名称',
    `reviser_name`                 varchar(50)              DEFAULT NULL COMMENT '修改人名称',
    `tenant_id`                    bigint                   DEFAULT NULL COMMENT '租户id',
    PRIMARY KEY (`product_asp_configuration_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='商品ASP配置表';

-- 新增字段
ALTER TABLE `product`
    ADD COLUMN `image_label_info_list` JSON NULL COMMENT '图片标签信息集合' after error_info,
    ADD COLUMN `print_type`            VARCHAR(32) DEFAULT NULL COMMENT '印花类型' after image_label_info_list;


-- 为product_id字段添加索引（用于表关联查询）
ALTER TABLE `product_picture`
    ADD INDEX `idx_product_picture_product_id` (`product_id`);

-- 为spu_code字段添加索引（用于按SPU查询）
ALTER TABLE `product_picture`
    ADD INDEX `idx_product_picture_spu_code` (`spu_code`);

ALTER TABLE `product_picture`
    ADD COLUMN `material_images` JSON COMMENT '生产资料图片JSON数据，包含图片名称和URL' after flower_url,
    ADD COLUMN `picture_kit_list_json` json COMMENT '图套信息JSON数据，包含营销图套相关数据' after material_images;

-- shop表 增加运营模式字段
ALTER TABLE shop ADD business_type tinyint(1) COMMENT '运营模式' after country_type;

-- ae_sale_goods表 增加两个属性值
ALTER TABLE ae_sale_goods
    ADD COLUMN origin_property_value_items json COMMENT '原产地属性值JSON' after hs_extend_info,
    ADD COLUMN ships_from_property_value_item json COMMENT '发货地属性值JSON' after origin_property_value_items;

-- product_template_ae_spu 增加两个属性值
ALTER TABLE product_template_ae_spu
    ADD COLUMN origin_property_value_items json COMMENT '原产地属性值JSON' after hs_extend_info,
    ADD COLUMN ships_from_property_value_item json COMMENT '发货地属性值JSON' after origin_property_value_items;

-- 批量更新任务增加平台id
alter table product_batch_update_image_task add platform_id bigint null comment '平台id' after task_id;
alter table product_batch_update_title_task add platform_id bigint null comment '平台id' after task_id;
alter table product_batch_update_price_inventory_task add platform_id bigint null comment '平台id' after task_id;