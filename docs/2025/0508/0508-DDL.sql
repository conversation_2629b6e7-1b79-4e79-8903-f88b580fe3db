CREATE TABLE `alibaba_category`  (
                                     `alibaba_category_id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                     `category_id` bigint NOT NULL COMMENT '1688类目ID',
                                     `parent_id` bigint NULL DEFAULT NULL COMMENT '父类目ID',
                                     `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '类目名称',
                                     `level` int NULL DEFAULT NULL COMMENT '类目层级',
                                     `leaf` tinyint(1) NULL DEFAULT NULL COMMENT '是否叶子类目（只有叶子类目才能发布商品）',
                                     `features` json NULL COMMENT '类目特性',
                                     `category_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '类目的类型，1为1688大市场类目，2为1688工业品专业化类目，3为1688主流商品类目',
                                     `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否已删除;0未删除，1已删除',
                                     `creator_id` bigint NULL DEFAULT NULL COMMENT '创建人id',
                                     `creator_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
                                     `created_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
                                     `reviser_id` bigint NULL DEFAULT NULL COMMENT '最近修改人id;新增时取创建人id',
                                     `reviser_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '最近修改人名称;新增时取创建人名称',
                                     `revised_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
                                     `tenant_id` bigint NULL DEFAULT NULL COMMENT '租户id',
                                     PRIMARY KEY (`alibaba_category_id`) USING BTREE,
                                     UNIQUE INDEX `uk_category_id`(`category_id`) USING BTREE,
                                     INDEX `idx_parent_id`(`parent_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '1688类目表' ROW_FORMAT = DYNAMIC;


CREATE TABLE `alibaba_category_tree_cache`  (
                                                `alibaba_category_tree_cache_id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                                `tree_data` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '完整类目树JSON',
                                                `platform_tree_data` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT 'POP平台通用类目树JSON',
                                                `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否已删除;0未删除，1已删除',
                                                `creator_id` bigint NOT NULL COMMENT '创建人id',
                                                `creator_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '创建人',
                                                `created_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
                                                `reviser_id` bigint NULL DEFAULT NULL COMMENT '最近修改人id;新增时取创建人id',
                                                `reviser_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '最近修改人名称;新增时取创建人名称',
                                                `revised_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
                                                `tenant_id` bigint NULL DEFAULT NULL COMMENT '租户id',
                                                PRIMARY KEY (`alibaba_category_tree_cache_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '1688类目树缓存表' ROW_FORMAT = DYNAMIC;


ALTER TABLE `image_qc_record`
    ADD COLUMN `image_repo_reviser_id` bigint NULL DEFAULT NULL COMMENT '图片仓库更新人ID（质检时的）' AFTER `image_repo_created_time`,
ADD COLUMN `image_repo_reviser_name` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '图片仓库更新人名称（质检时的）' AFTER `image_repo_reviser_id`,
ADD COLUMN `image_repo_revised_time` datetime(0) NULL DEFAULT NULL COMMENT '图片仓库更新时间（质检时的）' AFTER `image_repo_reviser_name`;


ALTER TABLE aliexpress_category
    ADD COLUMN category_path_list json COMMENT '类目路径JSON字符串，表示从根类目到当前类目的完整路径' after features;


-- DDL: 添加三个字段到 product_template_ae_spu 表
ALTER TABLE product_template_ae_spu
    ADD COLUMN hs_code VARCHAR(50) COMMENT '税号' after origin_place_name,
    ADD COLUMN hs_pv_list JSON COMMENT '税号监管属性列表，存储为JSON' after hs_code,
    ADD COLUMN hs_extend_info VARCHAR(1000) COMMENT '税号扩展字段，存储为JSON格式字符串' after hs_pv_list;

-- DDL: 添加三个字段到 ae_sale_goods 表
ALTER TABLE ae_sale_goods
    ADD COLUMN hs_code VARCHAR(50) COMMENT '税号' after product_groups,
    ADD COLUMN hs_pv_list JSON COMMENT '税号监管属性列表，存储为JSON' after hs_code,
    ADD COLUMN hs_extend_info VARCHAR(1000) COMMENT '税号扩展字段，存储为JSON格式字符串' after hs_pv_list;