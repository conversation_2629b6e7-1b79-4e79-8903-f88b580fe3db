CREATE TABLE `ae_original_product_spu`
(
    `product_id`               bigint       NOT NULL COMMENT '平台商品id',
    `title`                    varchar(500) NOT NULL COMMENT '商品标题',
    `shop_id`                  bigint COMMENT '系统店铺id',
    `product_status_type`      varchar(50) COMMENT '商品状态',
    `gmt_create`               datetime COMMENT 'AE创建时间',
    `gmt_modified`             datetime COMMENT 'AE更新时间',
    `delivery_time`            int COMMENT '发货期',
    `category_id`              bigint COMMENT '分类id',
    `mobile_detail_images`     json COMMENT '移动端-商品图片链接（JSON格式）',
    `web_detail_images`        json COMMENT 'WEB端商品图片链接（JSON格式）',
    `aeop_ae_product_property` json COMMENT '商品属性',
    `package_height`           varchar(255) COMMENT '包裹高度',
    `package_length`           varchar(255) COMMENT '包裹长度',
    `package_width`            varchar(255) COMMENT '包裹宽度',
    `package_weight`           varchar(255) COMMENT '包裹重量',
    `tax_type`                 int          NOT NULL DEFAULT '1' COMMENT '是否包含关税(1：不含关税报价 2：含关税报价)',
    `promise_template_id`      varchar(255) COMMENT '产品所关联的服务模版id',
    `freight_template_id`      varchar(255) COMMENT '产品关联的运费模版id',
    `sizechart_id`             varchar(255) COMMENT '产品所关联的尺码模版id',
    `msr_eu_id`                varchar(255) COMMENT '欧盟责任人id',
    `manufacturer_id`          varchar(255) COMMENT '制造商id',
    `source_detail_json`       json COMMENT '商品详情源json',
    `creator_id`               bigint       NOT NULL COMMENT '创建人id',
    `creator_name`             varchar(32)  NOT NULL COMMENT '创建人名称',
    `created_time`             datetime     NOT NULL COMMENT '创建时间',
    `reviser_id`               bigint                DEFAULT NULL COMMENT '最近修改人id',
    `reviser_name`             varchar(32)           DEFAULT NULL COMMENT '最近修改人名称',
    `revised_time`             datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最近更新时间',
    `is_deleted`               tinyint(1)   NOT NULL COMMENT '是否已删除;0未删除，1已删除',
    `tenant_id`                bigint                DEFAULT NULL COMMENT '租户id',
    PRIMARY KEY (`product_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT ='AE原数据-商品spu';

CREATE TABLE `ae_original_product_sku`
(
    `sku_id`              bigint       NOT NULL COMMENT 'SKU唯一标识',
    `id`                  varchar(255) NOT NULL COMMENT 'AE另一个的sku id',
    `product_id`          bigint                DEFAULT NULL COMMENT 'SPU唯一标识',
    `sku_code`            varchar(255)          DEFAULT NULL COMMENT '卖家SKU编号',
    `extract_spu_code`    varchar(255)          DEFAULT NULL COMMENT '解析SPU编号',
    `aeop_ae_product_sku` json                  DEFAULT NULL COMMENT '商品属性',
    `color`               varchar(255)          DEFAULT NULL COMMENT '颜色',
    `size`                varchar(50)           DEFAULT NULL COMMENT '尺码',
    `sku_image`           varchar(500)          DEFAULT NULL COMMENT 'sku图片',
    `currency_code`       varchar(255)          DEFAULT NULL COMMENT '货币代码',
    `sku_price`           decimal(10, 2)        DEFAULT NULL COMMENT '价格',
    `quantity`            int                   DEFAULT NULL COMMENT '库存数量',
    `creator_id`          bigint       NOT NULL COMMENT '创建人id',
    `creator_name`        varchar(32)  NOT NULL COMMENT '创建人名称',
    `created_time`        datetime     NOT NULL COMMENT '创建时间',
    `reviser_id`          bigint                DEFAULT NULL COMMENT '最近修改人id',
    `reviser_name`        varchar(32)           DEFAULT NULL COMMENT '最近修改人名称',
    `revised_time`        datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最近更新时间',
    `is_deleted`          tinyint(1)   NOT NULL COMMENT '是否已删除;0未删除，1已删除',
    `tenant_id`           bigint                DEFAULT NULL COMMENT '租户id',
    PRIMARY KEY (`sku_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT ='AE原数据-商品sku';

alter table product_operate_log
    modify product_id bigint null comment '商品id';

alter table product_operate_log
    add sale_goods_id bigint null comment '销售商品id' after product_id;

alter table task_info
    add platform_id bigint not null comment '平台id' after task_type;

alter table lazada_original_product_sku
    add country varchar(255) null comment '国家站点' after item_id;

alter table lazada_original_product_spu
    modify country varchar(50) not null comment '国家站点';

alter table lazada_original_product_spu
    drop primary key;

alter table lazada_original_product_spu
    add primary key (item_id, country);

alter table sale_goods
    add column error_state int default 0 null comment '是否异常数据(1是0否)' after is_history,
    add column error_info  json          null comment '异常类型json数组[[{"errorType":  1, "errorMsg":  "SKU为空"}]' after error_state;

alter table ae_sale_goods
    add column error_state int default 0 null comment '是否异常数据(1是0否)' after is_history,
    add column error_info  json          null comment '异常类型json数组[[{"errorType":  1, "errorMsg":  "SKU为空"}]' after error_state;

alter table sale_skc
    add column allowed_update_unit int default 1 null comment '是否允许修改unit(1是0否)' after combo;
alter table ae_sale_skc
    add column allowed_update_unit int default 1 null comment '是否允许修改unit(1是0否)' after combo;

alter table product_template_ae_spu
    add tax_type int not null default 1 comment '是否包含关税(1：不含关税报价 2：含关税报价)' after package_dimensions_width;
alter table ae_sale_goods
    add tax_type int not null default 1 comment '是否包含关税(1：不含关税报价 2：含关税报价)' after package_dimensions_width;
