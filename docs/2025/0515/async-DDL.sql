CREATE TABLE `product_create_task` (
                                         `product_create_task_id` bigint NOT NULL AUTO_INCREMENT COMMENT '任务ID',
                                         `spu_code` varchar(250) NOT NULL COMMENT 'SPU编码',
                                         `task_status` tinyint NOT NULL DEFAULT '0' COMMENT '任务状态: 0待执行, 1执行中, 2执行成功, 3执行失败',
                                         `request_data` json COMMENT '请求数据(JSON)',
                                         `result_data` json COMMENT '结果数据(JSON)',
                                         `error_message` text COMMENT '错误信息',
                                         `retry_count` int DEFAULT '0' COMMENT '重试次数',
                                         `callback_status` tinyint DEFAULT '0' COMMENT '回调状态: 0未回调, 1回调成功, 2回调失败',
                                         `execution_time` bigint DEFAULT NULL COMMENT '执行时间(毫秒)',
                                         `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否已删除;0未删除，1已删除',
                                         `creator_id` bigint DEFAULT NULL COMMENT '创建人id',
                                         `creator_name` varchar(32) DEFAULT NULL COMMENT '创建人',
                                         `created_time` datetime NOT NULL COMMENT '创建时间',
                                         `reviser_id` bigint DEFAULT NULL COMMENT '最近修改人id',
                                         `reviser_name` varchar(32) DEFAULT NULL COMMENT '最近修改人名称',
                                         `revised_time` datetime DEFAULT NULL COMMENT '更新时间',
                                         `tenant_id` bigint DEFAULT NULL COMMENT '租户id',
                                         PRIMARY KEY (`product_create_task_id`),
                                         KEY `idx_spu_code` (`spu_code`),
                                         KEY `idx_task_status` (`task_status`),
                                         KEY `idx_created_time` (`created_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='商品创建任务表';