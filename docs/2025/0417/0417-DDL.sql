alter table product
    add submit_platform json null comment '提交平台json数组(platform_id)' after clothing_style_code,
    add init_template   int default 1 comment '是否初始化模板(待上架) 1是0否' after is_update;

alter table import_product_record
    add platform_id bigint default 1 null comment '平台id(默认1: <PERSON><PERSON><PERSON>)' after process_state;

alter table product_source_data
    add shop_id bigint null comment '店铺ID' after product_id;

alter table sale_goods
    add stock_type                int DEFAULT NULL COMMENT '库存类型: 1期货;2现货;3海外仓' after platform_category_name,
    add country_stock_type        int DEFAULT NULL COMMENT '站点库存类型: 1期货;2现货;3海外仓' after stock_type,
    add delay_delivery_days       int           null comment '发货期' after country_stock_type,
    ADD package_weight            varchar(13)   NULL COMMENT '包装规格-重量' after delay_delivery_days,
    ADD package_dimensions_length varchar(13)   NULL COMMENT '包装规格-长度' after package_weight,
    ADD package_dimensions_height varchar(13)   NULL COMMENT '包装规格-高度' after package_dimensions_length,
    ADD package_dimensions_width  varchar(13)   NULL COMMENT '包装规格-宽度' after package_dimensions_height,
    ADD publish_user_id           bigint        NULL COMMENT '首次发布人id' after publish_time,
    ADD publish_user_name         varchar(32)   NULL COMMENT '首次发布人名称' after publish_user_id,
    add update_state              int default 0 null comment '是否更新【1有更新；0没有】' after publish_state;

alter table sale_sku
    add `delay_delivery_days` int    null comment '发货期' after platform_category_name,
    add `sale_skc_id`         bigint null comment '销售skc id' after sale_sku_id,
    add `barcodes`            json   null COMMENT '商品条码(多个)' after barcode,
    add `seller_sku_flat_id`  bigint NULL COMMENT 'seller_sku平铺信息id' after barcodes;


CREATE TABLE `seller_sku_flat_info`
(
    `seller_sku_flat_id` bigint        NOT NULL AUTO_INCREMENT COMMENT 'seller_sku平铺信息id',
    `image`              varchar(1000) not null comment '图片',
    `color`              varchar(255)  not null comment '颜色',
    `color_code`         varchar(255)  not null comment '内部颜色编码',
    `seller_sku`         varchar(255)  not null comment '卖家SKU',
    `spu_code`           varchar(255)  null comment 'SPU编号',
    `combo`              tinyint(1)             default 0 not null comment '是否组合商品(1是,0否)',
    `platform_id`        bigint        null comment '平台id',
    `shop_id`            bigint        null comment '店铺id',
    `is_deleted`         tinyint(1)    NOT NULL DEFAULT '0' COMMENT '是否已删除;0未删除，1已删除',
    `tenant_id`          bigint                 DEFAULT NULL COMMENT '租户ID',
    `creator_id`         bigint        NOT NULL COMMENT '创建人id',
    `creator_name`       varchar(32)   NOT NULL COMMENT '创建人',
    `created_time`       datetime      NOT NULL COMMENT '创建时间',
    `reviser_id`         bigint                 DEFAULT NULL COMMENT '最近修改人id;新增时取创建人id',
    `reviser_name`       varchar(32)            DEFAULT NULL COMMENT '最近修改人名称;新增时取创建人名称',
    `revised_time`       datetime               DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`seller_sku_flat_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='sellerSku平铺信息表';

CREATE TABLE `seller_sku_barcode_ref`
(
    `barcode_ref_id`     bigint       NOT NULL AUTO_INCREMENT COMMENT '关系id',
    `seller_sku_flat_id` bigint       NOT NULL COMMENT 'seller_sku平铺信息id',
    `barcode`            varchar(255) not null comment '商品条码',
    `is_deleted`         tinyint(1)   NOT NULL DEFAULT '0' COMMENT '是否已删除;0未删除，1已删除',
    `tenant_id`          bigint                DEFAULT NULL COMMENT '租户ID',
    `creator_id`         bigint       NOT NULL COMMENT '创建人id',
    `creator_name`       varchar(32)  NOT NULL COMMENT '创建人',
    `created_time`       datetime     NOT NULL COMMENT '创建时间',
    `reviser_id`         bigint                DEFAULT NULL COMMENT '最近修改人id;新增时取创建人id',
    `reviser_name`       varchar(32)           DEFAULT NULL COMMENT '最近修改人名称;新增时取创建人名称',
    `revised_time`       datetime              DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`barcode_ref_id`),
    KEY `idx_seller_sku_flat_id` (`seller_sku_flat_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='sellerSku条码关系表';

CREATE TABLE `barcode_unit_snapshot`
(
    `snapshot_id`    bigint      NOT NULL AUTO_INCREMENT COMMENT '快照id',
    `barcode_ref_id` bigint      NOT NULL COMMENT '关系id',
    `unit`           int                  default 1 not null comment '件数',
    `is_deleted`     tinyint(1)  NOT NULL DEFAULT '0' COMMENT '是否已删除;0未删除，1已删除',
    `tenant_id`      bigint               DEFAULT NULL COMMENT '租户ID',
    `creator_id`     bigint      NOT NULL COMMENT '创建人id',
    `creator_name`   varchar(32) NOT NULL COMMENT '创建人',
    `created_time`   datetime    NOT NULL COMMENT '创建时间',
    `reviser_id`     bigint               DEFAULT NULL COMMENT '最近修改人id;新增时取创建人id',
    `reviser_name`   varchar(32)          DEFAULT NULL COMMENT '最近修改人名称;新增时取创建人名称',
    `revised_time`   datetime             DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`snapshot_id`),
    KEY `idx_barcode_ref_id` (`barcode_ref_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='条码件数快照表';

CREATE TABLE `product_template_lazada_spu`
(
    `lazada_spu_id`             bigint       NOT NULL AUTO_INCREMENT COMMENT 'SPU模板id',
    `product_id`                bigint       NOT NULL COMMENT '商品id',
    `spu_code`                  varchar(250) NOT NULL COMMENT 'spu编码',
    `product_title`             varchar(600)          DEFAULT NULL COMMENT '商品标题',
    `all_country_title`         json         null COMMENT '所有站点标题',
    `brand_id`                  int                   DEFAULT NULL COMMENT '品牌ID',
    `brand_name`                varchar(100)          DEFAULT NULL COMMENT '品牌名称',
    `stock_type`                int                   DEFAULT NULL COMMENT '库存类型: 1期货;2现货;3海外仓',
    `delay_delivery_days`       int                   DEFAULT NULL COMMENT '发货期(一个数字)',
    `package_weight`            varchar(13)           DEFAULT NULL COMMENT '包装规格-重量',
    `package_dimensions_length` varchar(13)           DEFAULT NULL COMMENT '包装规格-长度',
    `package_dimensions_height` varchar(13)           DEFAULT NULL COMMENT '包装规格-高度',
    `package_dimensions_width`  varchar(13)           DEFAULT NULL COMMENT '包装规格-宽度',
    `size_group_name`           varchar(60)           DEFAULT NULL COMMENT '尺码组名称',
    `size_group_code`           varchar(60)           DEFAULT NULL COMMENT '尺码组编码',
    `is_deleted`                tinyint(1)   NOT NULL DEFAULT '0' COMMENT '是否已删除;0未删除，1已删除',
    `tenant_id`                 bigint                DEFAULT NULL COMMENT '租户ID',
    `creator_id`                bigint       NOT NULL COMMENT '创建人id',
    `creator_name`              varchar(32)  NOT NULL COMMENT '创建人',
    `created_time`              datetime     NOT NULL COMMENT '创建时间',
    `reviser_id`                bigint                DEFAULT NULL COMMENT '最近修改人id;新增时取创建人id',
    `reviser_name`              varchar(32)           DEFAULT NULL COMMENT '最近修改人名称;新增时取创建人名称',
    `revised_time`              datetime              DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`lazada_spu_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='商品模板Lazada-SPU';

CREATE TABLE `product_template_lazada_skc`
(
    `lazada_skc_id`   bigint auto_increment primary key NOT NULL COMMENT 'SKC模板id',
    `lazada_spu_id`   bigint                            NOT NULL COMMENT 'SPU模板id',
    `product_skc_id`  bigint                            NULL COMMENT '商品skc id(可空)',
    `skc`             varchar(32)                       NULL COMMENT 'SKC',
    `color`           varchar(250)                      NOT NULL COMMENT '内部颜色',
    `platform_color`  varchar(250)                      NULL COMMENT '平台颜色',
    `color_code`      varchar(250)                      NULL COMMENT '颜色编码',
    `color_abbr_code` varchar(250)                      NULL COMMENT '颜色缩写编码',
    `pictures`        varchar(2000)                     NULL COMMENT 'skc图片（多个用英文逗号分割）',
    `state`           tinyint(1)                                 DEFAULT 1 COMMENT 'SKC状态【1可用；0取消】',
    `combo`           tinyint(1)                                 DEFAULT 0 COMMENT '是否组合商品(1是,0否)',
    `cb_price`        decimal(10, 2)                    NULL COMMENT '跨境价',
    `local_price`     decimal(10, 2)                    NULL COMMENT '本土价（供货价）',
    `purchase_price`  decimal(10, 2)                    NULL COMMENT '采购价格',
    `cost_price`      decimal(10, 2)                    NULL COMMENT '定价成本',
    `is_deleted`      tinyint(1)                        NOT NULL DEFAULT '0' COMMENT '是否已删除;0未删除，1已删除',
    `creator_id`      bigint                            NOT NULL COMMENT '创建人id',
    `creator_name`    varchar(32)                       NOT NULL COMMENT '创建人',
    `created_time`    datetime                          NOT NULL COMMENT '创建时间',
    `reviser_id`      bigint                                     DEFAULT NULL COMMENT '最近修改人id;新增时取创建人id',
    `reviser_name`    varchar(32)                                DEFAULT NULL COMMENT '最近修改人名称;新增时取创建人名称',
    `revised_time`    datetime                                   DEFAULT NULL COMMENT '更新时间',
    `tenant_id`       bigint                                     DEFAULT NULL COMMENT '租户id'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='商品模板Lazada-SKC';

CREATE TABLE `product_template_lazada_sku`
(
    `lazada_sku_id`         bigint auto_increment NOT NULL COMMENT 'SKU模板id',
    `lazada_skc_id`         bigint                NOT NULL COMMENT 'SKC模板id',
    `lazada_spu_id`         bigint                NOT NULL COMMENT 'SPU模板id',
    `seller_sku`            varchar(600)          NULL COMMENT '平台sku',
    `barcode`               varchar(600)          NULL COMMENT '商品条码',
    `barcodes`              json                  null COMMENT '商品条码(多个)',
    `country`               varchar(20)           NULL COMMENT '站点编码',
    `stock_quantity`        bigint                NULL COMMENT '库存数量',
    `size_name`             varchar(50)           NULL COMMENT '尺码名',
    `sale_price`            decimal(13, 2)        NULL COMMENT '销售价',
    `last_sale_price`       decimal(13, 2)        null comment '上次销售价',
    `retail_price`          decimal(13, 2)        NULL COMMENT '划线价',
    `last_retail_price`     decimal(13, 2)        null comment '上次划线价',
    `purchase_sale_price`   decimal(13, 2)        NULL COMMENT '采购销售价',
    `purchase_retail_price` decimal(13, 2)        NULL COMMENT '采购划线价',
    `regular_sale_price`    decimal(13, 2)        NULL COMMENT '常规销售价',
    `regular_retail_price`  decimal(13, 2)        NULL COMMENT '常规划线价',
    `flag_frontend`         tinyint(1)                     DEFAULT 1 COMMENT '是否到前台：1是，0否',
    `enable_state`          tinyint(1)                     DEFAULT 1 COMMENT '是否启用【1启动；0禁用】',
    `is_deleted`            tinyint(1)            NOT NULL DEFAULT 0 COMMENT '是否已删除;0未删除，1已删除',
    `creator_id`            bigint                NOT NULL COMMENT '创建人id',
    `creator_name`          varchar(32)           NOT NULL COMMENT '创建人',
    `created_time`          datetime              NOT NULL COMMENT '创建时间',
    `reviser_id`            bigint                         DEFAULT NULL COMMENT '最近修改人id;新增时取创建人id',
    `reviser_name`          varchar(32)                    DEFAULT NULL COMMENT '最近修改人名称;新增时取创建人名称',
    `revised_time`          datetime                       DEFAULT NULL COMMENT '更新时间',
    `tenant_id`             bigint                         DEFAULT NULL COMMENT '租户id',
    PRIMARY KEY (`lazada_sku_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='商品模板Lazada-SKU';

CREATE TABLE `product_template_ae_spu`
(
    `ae_spu_id`                 bigint       NOT NULL AUTO_INCREMENT COMMENT 'SPU模板id',
    `product_id`                bigint       NOT NULL COMMENT '商品id',
    `spu_code`                  varchar(250) NOT NULL COMMENT 'spu编码',
    `product_title`             varchar(600)          DEFAULT NULL COMMENT '商品标题',
    `brand_id`                  int                   DEFAULT NULL COMMENT '品牌ID',
    `brand_name`                varchar(100)          DEFAULT NULL COMMENT '品牌名称',
    `stock_type`                int                   DEFAULT NULL COMMENT '库存类型: 1期货;2现货;3海外仓',
    `delay_delivery_days`       int                   DEFAULT NULL COMMENT '发货期(一个数字)',
    `origin_place_name`         varchar(255) null comment '产地(国家/地区)',
    `package_weight`            varchar(13)           DEFAULT NULL COMMENT '包装规格-重量',
    `package_dimensions_length` varchar(13)           DEFAULT NULL COMMENT '包装规格-长度',
    `package_dimensions_height` varchar(13)           DEFAULT NULL COMMENT '包装规格-高度',
    `package_dimensions_width`  varchar(13)           DEFAULT NULL COMMENT '包装规格-宽度',
    `size_group_name`           varchar(60)           DEFAULT NULL COMMENT '尺码组名称',
    `size_group_code`           varchar(60)           DEFAULT NULL COMMENT '尺码组编码',
    `inv_deduction`             int                   DEFAULT '2' COMMENT '库存扣减方式(1:下单扣库存, 2:付款扣库存)',
    `is_deleted`                tinyint(1)   NOT NULL DEFAULT '0' COMMENT '是否已删除;0未删除，1已删除',
    `tenant_id`                 bigint                DEFAULT NULL COMMENT '租户ID',
    `creator_id`                bigint       NOT NULL COMMENT '创建人id',
    `creator_name`              varchar(32)  NOT NULL COMMENT '创建人',
    `created_time`              datetime     NOT NULL COMMENT '创建时间',
    `reviser_id`                bigint                DEFAULT NULL COMMENT '最近修改人id;新增时取创建人id',
    `reviser_name`              varchar(32)           DEFAULT NULL COMMENT '最近修改人名称;新增时取创建人名称',
    `revised_time`              datetime              DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`ae_spu_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='商品模板AE-SPU';

CREATE TABLE `product_template_ae_skc`
(
    `ae_skc_id`       bigint auto_increment primary key NOT NULL COMMENT 'SKC模板id',
    `ae_spu_id`       bigint                            NOT NULL COMMENT 'SPU模板id',
    `product_skc_id`  bigint                            NULL COMMENT '商品skc id(可空)',
    `skc`             varchar(32)                       NULL COMMENT 'SKC',
    `color`           varchar(250)                      NOT NULL COMMENT '内部颜色',
    `platform_color`  varchar(250)                      NULL COMMENT '平台颜色',
    `color_code`      varchar(250)                      NULL COMMENT '颜色编码',
    `color_abbr_code` varchar(250)                      NULL COMMENT '颜色缩写编码',
    `pictures`        varchar(2000)                     NULL COMMENT 'skc图片（多个用英文逗号分割）',
    `state`           tinyint(1)                                 DEFAULT 1 COMMENT 'SKC状态【1可用；0取消】',
    `combo`           tinyint(1)                                 DEFAULT 0 COMMENT '是否组合商品(1是,0否)',
    `cb_price`        decimal(10, 2)                    NULL COMMENT '跨境价',
    `local_price`     decimal(10, 2)                    NULL COMMENT '本土价（供货价）',
    `purchase_price`  decimal(10, 2)                    NULL COMMENT '采购价格',
    `cost_price`      decimal(10, 2)                    NULL COMMENT '定价成本',
    `is_deleted`      tinyint(1)                        NOT NULL DEFAULT '0' COMMENT '是否已删除;0未删除，1已删除',
    `creator_id`      bigint                            NOT NULL COMMENT '创建人id',
    `creator_name`    varchar(32)                       NOT NULL COMMENT '创建人',
    `created_time`    datetime                          NOT NULL COMMENT '创建时间',
    `reviser_id`      bigint                                     DEFAULT NULL COMMENT '最近修改人id;新增时取创建人id',
    `reviser_name`    varchar(32)                                DEFAULT NULL COMMENT '最近修改人名称;新增时取创建人名称',
    `revised_time`    datetime                                   DEFAULT NULL COMMENT '更新时间',
    `tenant_id`       bigint                                     DEFAULT NULL COMMENT '租户id'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='商品模板AE-SKC';

CREATE TABLE `product_template_ae_sku`
(
    `ae_sku_id`             bigint auto_increment NOT NULL COMMENT 'SKU模板id',
    `ae_skc_id`             bigint                NOT NULL COMMENT 'SKC模板id',
    `ae_spu_id`             bigint                NOT NULL COMMENT 'SPU模板id',
    `seller_sku`            varchar(600)          NULL COMMENT '平台sku',
    `barcode`               varchar(600)          NULL COMMENT '商品条码',
    `barcodes`              json                  null COMMENT '商品条码(多个)',
    `stock_quantity`        bigint                NULL COMMENT '库存数量',
    `size_name`             varchar(50)           NULL COMMENT '尺码名',
    `sale_price`            decimal(13, 2)        NULL COMMENT '销售价',
    `last_sale_price`       decimal(13, 2)        null comment '上次销售价',
    `retail_price`          decimal(13, 2)        NULL COMMENT '划线价',
    `last_retail_price`     decimal(13, 2)        null comment '上次划线价',
    `purchase_sale_price`   decimal(13, 2)        NULL COMMENT '采购销售价',
    `purchase_retail_price` decimal(13, 2)        NULL COMMENT '采购划线价',
    `regular_sale_price`    decimal(13, 2)        NULL COMMENT '常规销售价',
    `regular_retail_price`  decimal(13, 2)        NULL COMMENT '常规划线价',
    `flag_frontend`         tinyint(1)                     DEFAULT 1 COMMENT '是否到前台：1是，0否',
    `enable_state`          tinyint(1)                     DEFAULT 1 COMMENT '是否启用【1启动；0禁用】',
    `is_deleted`            tinyint(1)            NOT NULL DEFAULT 0 COMMENT '是否已删除;0未删除，1已删除',
    `creator_id`            bigint                NOT NULL COMMENT '创建人id',
    `creator_name`          varchar(32)           NOT NULL COMMENT '创建人',
    `created_time`          datetime              NOT NULL COMMENT '创建时间',
    `reviser_id`            bigint                         DEFAULT NULL COMMENT '最近修改人id;新增时取创建人id',
    `reviser_name`          varchar(32)                    DEFAULT NULL COMMENT '最近修改人名称;新增时取创建人名称',
    `revised_time`          datetime                       DEFAULT NULL COMMENT '更新时间',
    `tenant_id`             bigint                         DEFAULT NULL COMMENT '租户id',
    PRIMARY KEY (`ae_sku_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='商品模板AE-SKU';


CREATE TABLE `sale_skc`
(
    `sale_skc_id`      bigint auto_increment primary key NOT NULL COMMENT '销售skc id',
    `sale_goods_id`    bigint                            NOT NULL COMMENT '销售商品id',
    `product_skc_id`   bigint                            NULL COMMENT '商品skc id',
    `skc`              varchar(32)                       NULL COMMENT 'SKC',
    `color`            varchar(250)                      NOT NULL COMMENT '内部颜色',
    `platform_color`   varchar(250)                      NULL COMMENT '平台颜色',
    `combo_color_code` varchar(250)                      NULL COMMENT '组合颜色编码(生成sellerSku用)',
    `color_code`       varchar(250)                      NULL COMMENT '颜色编码',
    `color_abbr_code`  varchar(250)                      NULL COMMENT '颜色缩写编码',
    `pictures`         varchar(2000)                     NULL COMMENT 'skc图片（多个用英文逗号分割）',
    `state`            tinyint(1)                                 DEFAULT '1' COMMENT 'SKC状态【1可用；0取消】',
    `combo`            tinyint(1)                                 DEFAULT 0 COMMENT '是否组合商品(1是,0否)',
    `cb_price`         decimal(10, 2)                    NULL COMMENT '跨境价',
    `local_price`      decimal(10, 2)                    NULL COMMENT '本土价（供货价）',
    `purchase_price`   decimal(10, 2)                    NULL COMMENT '采购价格',
    `cost_price`       decimal(10, 2)                    NULL COMMENT '定价成本',
    `is_deleted`       tinyint(1)                        NOT NULL DEFAULT '0' COMMENT '是否已删除;0未删除，1已删除',
    `creator_id`       bigint                            NOT NULL COMMENT '创建人id',
    `creator_name`     varchar(32)                       NOT NULL COMMENT '创建人',
    `created_time`     datetime                          NOT NULL COMMENT '创建时间',
    `reviser_id`       bigint                                     DEFAULT NULL COMMENT '最近修改人id;新增时取创建人id',
    `reviser_name`     varchar(32)                                DEFAULT NULL COMMENT '最近修改人名称;新增时取创建人名称',
    `revised_time`     datetime                                   DEFAULT NULL COMMENT '更新时间',
    `tenant_id`        bigint                                     DEFAULT NULL COMMENT '租户id'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='销售SKC';


-- 基础配置，ISP倍率计算相关
ALTER TABLE `product_basic_price_rule_detail`
    ADD COLUMN `product_basic_price_rule_condition_id` bigint DEFAULT NULL COMMENT '产品基础价格计算条件表ID' AFTER `product_basic_price_rule_id`,
    ADD KEY `idx_rule_condition_id` (`product_basic_price_rule_condition_id`) USING BTREE;

CREATE TABLE `product_basic_price_rule_condition`
(
    `product_basic_price_rule_condition_id` bigint          NOT NULL COMMENT '主键',
    `product_basic_price_rule_id`           bigint unsigned NOT NULL COMMENT '关联的基础价格规则ID',
    `priority`                              int             NOT NULL DEFAULT '0' COMMENT '优先级，数字越小优先级越高',
    `rule_description`                      varchar(255)             DEFAULT NULL COMMENT '规则组描述',
    `is_default`                            tinyint(1)      NOT NULL DEFAULT '0' COMMENT '是否默认规则组：0-否，1-是（无条件匹配时使用）',
    `is_deleted`                            tinyint(1)      NOT NULL DEFAULT '0' COMMENT '是否删除，0-未删除，1-已删除',
    `creator_id`                            bigint                   DEFAULT NULL COMMENT '创建者ID',
    `created_time`                          datetime        NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `reviser_id`                            bigint                   DEFAULT NULL COMMENT '修改者ID',
    `revised_time`                          datetime                 DEFAULT NULL COMMENT '修改时间',
    `creator_name`                          varchar(50)              DEFAULT NULL COMMENT '创建人名称',
    `reviser_name`                          varchar(50)              DEFAULT NULL COMMENT '修改人名称',
    `tenant_id`                             bigint                   DEFAULT NULL COMMENT '租户id',
    PRIMARY KEY (`product_basic_price_rule_condition_id`) USING BTREE,
    KEY `idx_product_basic_price_rule_id` (`product_basic_price_rule_id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT ='产品基础价格计算条件表';

CREATE TABLE `product_basic_price_rule_condition_detail`
(
    `product_basic_price_rule_condition_detail_id` bigint      NOT NULL COMMENT '主键',
    `product_basic_price_rule_condition_id`        bigint      NOT NULL COMMENT '规则组ID',
    `condition_field`                              varchar(32) NOT NULL COMMENT '条件字段',
    `condition_operator`                           varchar(32) NOT NULL COMMENT '条件运算符：EQ-等于，NEQ-不等于，GT-大于，LT-小于，BETWEEN-区间等',
    `condition_value`                              varchar(255)         DEFAULT NULL COMMENT '条件值',
    `range_min`                                    decimal(10, 4)       DEFAULT NULL COMMENT '区间最小值',
    `range_max`                                    decimal(10, 4)       DEFAULT NULL COMMENT '区间最大值',
    `logic_operator`                               varchar(32)          DEFAULT 'AND' COMMENT '逻辑运算符：AND, OR',
    `sequence`                                     int         NOT NULL DEFAULT '0' COMMENT '条件顺序',
    `is_deleted`                                   tinyint(1)  NOT NULL DEFAULT '0' COMMENT '是否删除，0-未删除，1-已删除',
    `creator_id`                                   bigint               DEFAULT NULL COMMENT '创建者ID',
    `created_time`                                 datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `reviser_id`                                   bigint               DEFAULT NULL COMMENT '修改者ID',
    `revised_time`                                 datetime             DEFAULT NULL COMMENT '修改时间',
    `creator_name`                                 varchar(50)          DEFAULT NULL COMMENT '创建人名称',
    `reviser_name`                                 varchar(50)          DEFAULT NULL COMMENT '修改人名称',
    `tenant_id`                                    bigint               DEFAULT NULL COMMENT '租户id',
    PRIMARY KEY (`product_basic_price_rule_condition_detail_id`) USING BTREE,
    KEY `idx_rule_condition_id` (`product_basic_price_rule_condition_id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT ='产品基础价格计算条件详情表';

-- ---- aliexpress 类名相关表--------------------------------------------------------------------------------------------
CREATE TABLE `aliexpress_category`
(
    `aliexpress_category_id` bigint                                 NOT NULL auto_increment COMMENT '主键ID',
    `category_id`            bigint                                 NOT NULL COMMENT '速卖通类目ID',
    `parent_category_id`     bigint                                          DEFAULT NULL COMMENT '父类目ID',
    `category_level`         int                                    NOT NULL COMMENT '类目层级',
    `leaf`                   tinyint(1)                             NOT NULL COMMENT '是否叶子节点',
    `category_names`         json                                   NOT NULL COMMENT '多语言名称',
    `features`               json                                   NULL COMMENT '类目特性',
    `is_deleted`             tinyint(1)                             NOT NULL DEFAULT '0' COMMENT '是否已删除;0未删除，1已删除',
    `creator_id`             bigint                                 NOT NULL COMMENT '创建人id',
    `creator_name`           varchar(32) COLLATE utf8mb4_general_ci NOT NULL COMMENT '创建人',
    `created_time`           datetime                                        DEFAULT NULL COMMENT '创建时间',
    `reviser_id`             bigint                                          DEFAULT NULL COMMENT '最近修改人id;新增时取创建人id',
    `reviser_name`           varchar(32) COLLATE utf8mb4_general_ci          DEFAULT NULL COMMENT '最近修改人名称;新增时取创建人名称',
    `revised_time`           datetime                                        DEFAULT NULL COMMENT '更新时间',
    `tenant_id`              bigint                                          DEFAULT NULL COMMENT '租户id',
    PRIMARY KEY (`aliexpress_category_id`),
    UNIQUE KEY `uniq_category_id` (`category_id`),
    KEY `idx_parent_id` (`parent_category_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='速卖通类目表';

CREATE TABLE `aliexpress_category_tree_cache`
(
    `aliexpress_category_tree_cache_id` bigint                                 NOT NULL auto_increment COMMENT '主键ID',
    `tree_data`                         mediumtext COMMENT '完整类目树JSON',
    `platform_tree_data`                mediumtext COMMENT 'POP平台通用类目树JSON',
    `is_deleted`                        tinyint(1)                             NOT NULL DEFAULT '0' COMMENT '是否已删除;0未删除，1已删除',
    `creator_id`                        bigint                                 NOT NULL COMMENT '创建人id',
    `creator_name`                      varchar(32) COLLATE utf8mb4_general_ci NOT NULL COMMENT '创建人',
    `created_time`                      datetime                                        DEFAULT NULL COMMENT '创建时间',
    `reviser_id`                        bigint                                          DEFAULT NULL COMMENT '最近修改人id;新增时取创建人id',
    `reviser_name`                      varchar(32) COLLATE utf8mb4_general_ci          DEFAULT NULL COMMENT '最近修改人名称;新增时取创建人名称',
    `revised_time`                      datetime                                        DEFAULT NULL COMMENT '更新时间',
    `tenant_id`                         bigint                                          DEFAULT NULL COMMENT '租户id',
    PRIMARY KEY (`aliexpress_category_tree_cache_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='速卖通类目树缓存表';

-- ----AE相关表--------------------------------------------------------------------------------------------


CREATE TABLE `ae_sale_goods`
(
    `sale_goods_id`             bigint       NOT NULL COMMENT '主键',
    `product_id`                bigint                DEFAULT NULL COMMENT '商品表ID',
    `platform_product_id`       bigint                DEFAULT NULL COMMENT '平台商品ID',
    `platform_id`               bigint                DEFAULT NULL COMMENT '平台ID',
    `channel_id`                bigint                DEFAULT NULL COMMENT '渠道ID',
    `product_title`             varchar(600)          DEFAULT NULL COMMENT '商品标题',
    `spu_code`                  varchar(500)          DEFAULT NULL COMMENT 'spu编码',
    `shop_id`                   bigint                DEFAULT NULL COMMENT '店铺ID',
    `shop_name`                 varchar(50)           DEFAULT NULL COMMENT '店铺名称',
    `brand_id`                  int                   DEFAULT NULL COMMENT '品牌ID',
    `brand_name`                varchar(100)          DEFAULT NULL COMMENT '品牌名称',
    `category_code`             varchar(50)           DEFAULT NULL COMMENT '品类编码',
    `platform_category_id`      varchar(100)          DEFAULT NULL COMMENT '平台品类唯一标识',
    `platform_category_name`    varchar(120)          DEFAULT NULL COMMENT '平台品类名',
    `stock_type`                int                   DEFAULT NULL COMMENT '库存类型: 1期货;2现货;3海外仓',
    `delay_delivery_days`       int                   DEFAULT NULL COMMENT '发货期',
    `inv_deduction`             int                   DEFAULT '2' COMMENT '库存扣减方式(1:下单扣库存, 2:付款扣库存)',
    `origin_place_name`         varchar(255) null comment '产地(国家/地区)',
    `package_weight`            varchar(13)           DEFAULT NULL COMMENT '包装规格-重量',
    `package_dimensions_length` varchar(13)           DEFAULT NULL COMMENT '包装规格-长度',
    `package_dimensions_height` varchar(13)           DEFAULT NULL COMMENT '包装规格-高度',
    `package_dimensions_width`  varchar(13)           DEFAULT NULL COMMENT '包装规格-宽度',
    `publish_state`             tinyint(1)            DEFAULT '2' COMMENT '商品上架状态【1上架；2下架】',
    `update_state`              int                   DEFAULT '0' COMMENT '是否更新【1有更新；0没有】',
    `platform_sync_state`       tinyint(1)            DEFAULT '0' COMMENT '平台同步状态【0待同步;2同步中；1已同步；-1同步失败】',
    `publish_time`              datetime              DEFAULT NULL COMMENT '首次上架时间',
    `publish_user_id`           bigint                DEFAULT NULL COMMENT '首次发布人id',
    `publish_user_name`         varchar(32)           DEFAULT NULL COMMENT '首次发布人名称',
    `latest_publish_time`       datetime              DEFAULT NULL COMMENT '最近上架时间',
    `latest_offline_user_id`    bigint                DEFAULT NULL COMMENT '最近下架人id',
    `latest_publish_user_id`    bigint                DEFAULT NULL COMMENT '最近上架人id',
    `latest_offline_user_name`  varchar(32)           DEFAULT NULL COMMENT '最近下架人名称',
    `latest_publish_user_name`  varchar(32)           DEFAULT NULL COMMENT '最近上架人名称',
    `latest_offline_time`       datetime              DEFAULT NULL COMMENT '最近下架时间',
    `is_history`                int                   DEFAULT NULL COMMENT '是否历史数据【1：是】',
    `is_deleted`                tinyint(1)   NOT NULL DEFAULT '0' COMMENT '是否已删除;0未删除，1已删除',
    `creator_id`                bigint       NOT NULL COMMENT '创建人id',
    `creator_name`              varchar(32)  NOT NULL COMMENT '创建人',
    `created_time`              datetime              DEFAULT NULL COMMENT '创建时间',
    `reviser_id`                bigint                DEFAULT NULL COMMENT '最近修改人id;新增时取创建人id',
    `reviser_name`              varchar(32)           DEFAULT NULL COMMENT '最近修改人名称;新增时取创建人名称',
    `revised_time`              datetime              DEFAULT NULL COMMENT '更新时间',
    `tenant_id`                 bigint                DEFAULT NULL COMMENT '租户id',
    PRIMARY KEY (`sale_goods_id`),
    UNIQUE KEY `uniq_index` (`product_id`, `spu_code`, `shop_id`, `platform_product_id`) USING BTREE COMMENT '唯一标识索引',
    KEY `idx_sale_goods_product_id` (`product_id`),
    KEY `idx_sale_goods_publish_state` (`publish_state`),
    KEY `idx_sale_goods_filtering` (`product_id`, `product_title`, `publish_state`),
    KEY `idx_sale_goods_publish_time_index` (`publish_time`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='销售商品表-AE';

CREATE TABLE `ae_sale_skc`
(
    `sale_skc_id`      bigint       NOT NULL AUTO_INCREMENT COMMENT '销售skc id',
    `sale_goods_id`    bigint       NOT NULL COMMENT '销售商品id',
    `product_skc_id`   bigint                DEFAULT NULL COMMENT '商品skc id',
    `skc`              varchar(32)           DEFAULT NULL COMMENT 'SKC',
    `color`            varchar(250) NOT NULL COMMENT '内部颜色',
    `platform_color`   varchar(250)          DEFAULT NULL COMMENT '平台颜色',
    `combo_color_code` varchar(250) NULL COMMENT '组合颜色编码(生成sellerSku用)',
    `color_code`       varchar(250)          DEFAULT NULL COMMENT '颜色编码',
    `color_abbr_code`  varchar(250)          DEFAULT NULL COMMENT '颜色缩写编码',
    `pictures`         varchar(2000)         DEFAULT NULL COMMENT 'skc图片（多个用英文逗号分割）',
    `state`            tinyint(1)            DEFAULT '1' COMMENT 'SKC状态【1可用；0取消】',
    `combo`            tinyint(1)            DEFAULT '0' COMMENT '是否组合商品(1是,0否)',
    `cb_price`         decimal(10, 2)        DEFAULT NULL COMMENT '跨境价',
    `local_price`      decimal(10, 2)        DEFAULT NULL COMMENT '本土价（供货价）',
    `purchase_price`   decimal(10, 2)        DEFAULT NULL COMMENT '采购价格',
    `cost_price`       decimal(10, 2)        DEFAULT NULL COMMENT '定价成本',
    `is_deleted`       tinyint(1)   NOT NULL DEFAULT '0' COMMENT '是否已删除;0未删除，1已删除',
    `creator_id`       bigint       NOT NULL COMMENT '创建人id',
    `creator_name`     varchar(32)  NOT NULL COMMENT '创建人',
    `created_time`     datetime     NOT NULL COMMENT '创建时间',
    `reviser_id`       bigint                DEFAULT NULL COMMENT '最近修改人id;新增时取创建人id',
    `reviser_name`     varchar(32)           DEFAULT NULL COMMENT '最近修改人名称;新增时取创建人名称',
    `revised_time`     datetime              DEFAULT NULL COMMENT '更新时间',
    `tenant_id`        bigint                DEFAULT NULL COMMENT '租户id',
    PRIMARY KEY (`sale_skc_id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 7311984820148924495
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='销售SKC-AE';


CREATE TABLE `ae_sale_sku`
(
    `sale_sku_id`            bigint      NOT NULL COMMENT '主键',
    `sale_skc_id`            bigint               DEFAULT NULL COMMENT '销售skc id',
    `sale_goods_id`          bigint               DEFAULT NULL COMMENT '销售商品ID',
    `product_id`             bigint               DEFAULT NULL COMMENT '商品表ID',
    `platform_product_id`    bigint               DEFAULT NULL COMMENT '平台商品ID',
    `seller_sku`             varchar(600)         DEFAULT NULL COMMENT '平台sku',
    `shop_sku`               varchar(600)         DEFAULT NULL COMMENT 'shop sku',
    `product_skc_id`         bigint               DEFAULT NULL COMMENT '商品skcID',
    `barcode`                varchar(600)         DEFAULT NULL COMMENT '商品条码',
    `barcodes`               json        NULL COMMENT '商品条码(多个)',
    `seller_sku_flat_id`     bigint               DEFAULT NULL COMMENT 'seller_sku平铺信息id',
    `sku_code`               varchar(120)         DEFAULT NULL COMMENT 'sku编码',
    `stock_quantity`         bigint               DEFAULT NULL COMMENT '库存数量',
    `size_name`              varchar(50)          DEFAULT NULL COMMENT '尺码名',
    `lzd_size_name`          varchar(100)         DEFAULT NULL COMMENT 'Lazada尺码名',
    `sale_price`             decimal(13, 2)       DEFAULT NULL COMMENT '销售价',
    `last_sale_price`        decimal(13, 2)       DEFAULT NULL COMMENT '上次销售价',
    `retail_price`           decimal(13, 2)       DEFAULT NULL COMMENT '划线价',
    `last_retail_price`      decimal(13, 2)       DEFAULT NULL COMMENT '上次常规划线价',
    `purchase_sale_price`    decimal(13, 2)       DEFAULT NULL COMMENT '采购销售价',
    `purchase_retail_price`  decimal(13, 2)       DEFAULT NULL COMMENT '采购划线价',
    `regular_sale_price`     decimal(13, 2)       DEFAULT NULL COMMENT '常规销售价',
    `regular_retail_price`   decimal(13, 2)       DEFAULT NULL COMMENT '常规划线价',
    `platform_sku_id`        varchar(100)         DEFAULT NULL COMMENT '平台SKU ID',
    `enable_state`           tinyint(1)           DEFAULT '1' COMMENT '是否启用【1启动；0禁用】',
    `shop_name`              varchar(50)          DEFAULT NULL COMMENT '店铺名称',
    `brand_id`               varchar(80)          DEFAULT NULL COMMENT '品牌ID',
    `brand_name`             varchar(100)         DEFAULT NULL COMMENT '品牌名称',
    `platform_category_id`   varchar(100)         DEFAULT NULL COMMENT '平台品类唯一标识',
    `platform_category_name` varchar(120)         DEFAULT NULL COMMENT '平台品类名',
    `delay_delivery_days`    int                  DEFAULT NULL COMMENT '发货期',
    `publish_state`          tinyint(1)           DEFAULT '0' COMMENT 'SKU上架状态【1上架；2下架】',
    `latest_offline_time`    datetime             DEFAULT NULL COMMENT '最近下架时间',
    `is_deleted`             tinyint(1)  NOT NULL DEFAULT '0' COMMENT '是否已删除;0未删除，1已删除',
    `creator_id`             bigint      NOT NULL COMMENT '创建人id',
    `creator_name`           varchar(32) NOT NULL COMMENT '创建人',
    `created_time`           datetime    NOT NULL COMMENT '创建时间',
    `reviser_id`             bigint               DEFAULT NULL COMMENT '最近修改人id;新增时取创建人id',
    `reviser_name`           varchar(32)          DEFAULT NULL COMMENT '最近修改人名称;新增时取创建人名称',
    `revised_time`           datetime             DEFAULT NULL COMMENT '更新时间',
    `tenant_id`              bigint               DEFAULT NULL COMMENT '租户id',
    PRIMARY KEY (`sale_sku_id`),
    KEY `idx_product_id` (`product_id`) USING BTREE,
    KEY `idx_sale_goods_id` (`sale_goods_id`) USING BTREE,
    KEY `idx_seller_sku` (`seller_sku`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='销售SKU表-AE';

-- shop 增加token失效时间
ALTER TABLE shop
    ADD COLUMN `access_token_expire_time`  datetime COMMENT '访问令牌过期时间' after token,
    ADD COLUMN `refresh_token_expire_time` datetime COMMENT '刷新令牌过期时间' after refresh_token;

ALTER TABLE pop_product.image_repository ADD aliexpress_image_url text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT 'AE图片地址' after lazada_image_url;

ALTER TABLE `ae_sale_goods`
-- 服务模板
    ADD COLUMN `promise_template_id` bigint DEFAULT NULL COMMENT 'AE服务模板ID' after latest_offline_time,
    ADD COLUMN `promise_template_name` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'AE服务模板名称' after promise_template_id,

-- 运费模板
    ADD COLUMN `freight_template_id` bigint DEFAULT NULL COMMENT 'AE运费模板ID' after promise_template_name,
    ADD COLUMN `freight_template_name` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'AE运费模板名称' after freight_template_id,

-- 欧盟责任人
    ADD COLUMN `msr_id` bigint DEFAULT NULL COMMENT 'AE欧盟责任人ID' after freight_template_name,
    ADD COLUMN `msr_name` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'AE欧盟责任人名称' after msr_id,

-- 制造商
    ADD COLUMN `manufacture_id` bigint DEFAULT NULL COMMENT 'AE制造商ID' after msr_name,
    ADD COLUMN `manufacture_name` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'AE制造商名称' after manufacture_id,

-- 产品分组(JSON格式支持多选和分层结构)
    ADD COLUMN `product_groups` json COMMENT 'AE产品分组JSON数据,支持多选' after manufacture_name;
