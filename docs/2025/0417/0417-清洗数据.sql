# 逻辑删除旧的初始化数据
# update sale_goods
# set is_deleted = 2
# where platform_product_id is null;
#
# update sale_sku
# set is_deleted = 2
# where seller_sku is null and platform_product_id is null;
-- 店铺数据
# UPDATE shop
# SET
#     platform_seller_id = '6000895335',
#     token = '50000500626roeqJesAnnpYqrbY5FUCJpfWSeb10efb8a5hbTALtwEHIQjst4tM1PNYv',
#     access_token_expire_time = '2026-04-20 12:11:31',
#     brand_id = NULL,
#     brand_name = NULL,
#     is_auth = 1,
#     country = 'US',
#     country_type = 'cb',
#     refresh_token = '50001500426zhraZqMhwkjmrxopg8ZFVcnUDtt1405b57eiXFhQ5nTVHmgGw9e3HcrgF',
#     refresh_token_expire_time = '2026-04-09 15:02:27',
#     revised_time = '2025-04-24 19:00:00'
# WHERE
#     shop_id = 7276480307564389700;
#
# UPDATE shop
# SET
#     platform_seller_id = '6002258770',
#     token = '50000000900t12fa779fZhr5iBQyqUBA6mxfiqCwqhADjv9KTXegmPK2ES7ttIxBIp3v',
#     access_token_expire_time = '2026-04-20 12:10:56',
#     brand_id = NULL,
#     brand_name = NULL,
#     is_auth = 1,
#     country = 'US',
#     country_type = 'cb',
#     refresh_token = '50001001700j112c6c378gioatRfaGCRBrvwdgGOi0HS1FiWkSqbSuZwMOAfLAQd4udp',
#     refresh_token_expire_time = '2026-04-15 16:00:30',
#     revised_time = '2025-04-24 19:00:00'
# WHERE
#     shop_id = 7308304887945629727;
# INSERT INTO shop_seller_mapping (shop_seller_mapping_id,shop_id,platform_seller_id,short_code,country,is_deleted,creator_id,created_time,reviser_id,revised_time,creator_name,reviser_name,tenant_id) VALUES
#       (7301619471135002905,7276480307564389700,'6000895335','cn1091715035cllae','US',0,1,'2025-04-10 19:46:55',1,'2025-04-10 19:46:59','系统','系统',2),
#       (7301619471135002906,7308304887945629727,'6002258770','cn1100881170nhzae','US',0,1,'2025-04-15 16:15:17',1,'2025-04-15 16:15:20','系统','系统',2);

# 清洗product的提交平台字段
# update product a join (
#     select product_id, concat('[',group_concat(distinct p),']') as js
#     from (
#              select a.product_id, 1 as p
#              from product a
#                       join sale_goods b on a.product_id = b.product_id
#              group by a.product_id
#              union
#              select a.product_id, 3 as p
#              from product a
#                       join ae_sale_goods b on a.product_id = b.product_id
#              group by a.product_id
#          ) as t
#     group by product_id
# ) as tt on a.product_id = tt.product_id
# set a.submit_platform = js , platform_sync_state = 1
# where a.submit_platform is null;

-- sellerSku生成规则配置-AE用
# INSERT INTO pop_product.sku_code_rule (sku_code_rule_id, rule_name, channel_id, channel_name, platform_id, platform_name, rule_path, rule_type, supply_mode, supply_mode_name, segment_separator, enabled, remark, creator_id, creator_name, created_time, reviser_id, reviser_name, revised_time, is_deleted, tenant_id) VALUES (7265589072821769311, '数码印花', 1, '阿里', 3, 'ae', 'SPU#尺码#颜色扩展1', 'custom', 'digital_printing', '数码印花', '#', 1, 'SPU#尺码#颜色', 154441261, '潘希赛', '2024-12-11 16:23:59', 154441261, '潘希赛', '2024-12-11 16:23:59', 0, null);
# INSERT INTO pop_product.sku_code_rule (sku_code_rule_id, rule_name, channel_id, channel_name, platform_id, platform_name, rule_path, rule_type, supply_mode, supply_mode_name, segment_separator, enabled, remark, creator_id, creator_name, created_time, reviser_id, reviser_name, revised_time, is_deleted, tenant_id) VALUES (7268125201169654711, 'OEM', 1, '阿里', 3, 'ae', 'SPU-颜色扩展1-尺码', 'custom', 'Equipment', 'OEM', '-', 1, '撒发的', 154441261, '潘希赛', '2024-12-18 16:21:40', 154441261, '潘希赛', '2025-04-16 10:54:31', 0, null);
# INSERT INTO pop_product.sku_code_rule (sku_code_rule_id, rule_name, channel_id, channel_name, platform_id, platform_name, rule_path, rule_type, supply_mode, supply_mode_name, segment_separator, enabled, remark, creator_id, creator_name, created_time, reviser_id, reviser_name, revised_time, is_deleted, tenant_id) VALUES (7268402957983985711, 'ODM', 1, '阿里', 3, 'ae', 'SPU-颜色扩展1-尺码', 'custom', 'Manufacturer', 'ODM', '-', 1, '', 154441261, '潘希赛', '2024-12-19 10:45:22', 600093064, '张振东', '2025-03-19 15:58:35', 0, null);
# INSERT INTO pop_product.sku_code_rule (sku_code_rule_id, rule_name, channel_id, channel_name, platform_id, platform_name, rule_path, rule_type, supply_mode, supply_mode_name, segment_separator, enabled, remark, creator_id, creator_name, created_time, reviser_id, reviser_name, revised_time, is_deleted, tenant_id) VALUES (7268515700409983111, '仿款', 1, '阿里', 3, 'ae', 'SPU-颜色扩展1-尺码', 'custom', 'imitation', '仿款', '-', 1, '', 154441261, '潘希赛', '2024-12-19 18:13:22', 154441261, '潘希赛', '2024-12-19 18:13:22', 0, null);
# INSERT INTO pop_product.sku_code_rule (sku_code_rule_id, rule_name, channel_id, channel_name, platform_id, platform_name, rule_path, rule_type, supply_mode, supply_mode_name, segment_separator, enabled, remark, creator_id, creator_name, created_time, reviser_id, reviser_name, revised_time, is_deleted, tenant_id) VALUES (7268547789536715011, 'try_on', 1, '阿里', 3, 'ae', 'SPU-颜色扩展1-尺码', 'custom', 'try_on', '现货 try on', '-', 1, '', 154441261, '潘希赛', '2024-12-19 20:20:53', 154441261, '潘希赛', '2024-12-19 20:20:53', 0, null);
# INSERT INTO pop_product.sku_code_rule (sku_code_rule_id, rule_name, channel_id, channel_name, platform_id, platform_name, rule_path, rule_type, supply_mode, supply_mode_name, segment_separator, enabled, remark, creator_id, creator_name, created_time, reviser_id, reviser_name, revised_time, is_deleted, tenant_id) VALUES (7301530713593143811, 'AIGC', 1, '阿里', 3, 'ae', 'SPU-颜色扩展1-尺码', 'custom', 'Artificial', 'AIGC', '-', 1, '', 154441261, '潘希赛', '2025-03-21 18:07:20', 154441261, '潘希赛', '2025-03-21 18:07:20', 0, 2);
#
# INSERT INTO pop_product.sku_rule_element (sku_rule_element_id, sku_code_rule_id, element_name, element_type, element_value, dynamic_field, sort_order, creator_id, creator_name, created_time, reviser_id, reviser_name, revised_time, is_deleted, tenant_id) VALUES (7265589072842710001, 7265589072821769311, 'SPU', 'DYNAMIC', null, 'spu', 0, 154441261, '潘希赛', '2024-12-11 16:23:59', 154441261, '潘希赛', '2024-12-11 16:23:59', 0, null);
# INSERT INTO pop_product.sku_rule_element (sku_rule_element_id, sku_code_rule_id, element_name, element_type, element_value, dynamic_field, sort_order, creator_id, creator_name, created_time, reviser_id, reviser_name, revised_time, is_deleted, tenant_id) VALUES (7265589072863710002, 7265589072821769311, '尺码', 'DYNAMIC', null, 'size', 2, 154441261, '潘希赛', '2024-12-11 16:23:59', 154441261, '潘希赛', '2025-04-21 16:23:22', 0, null);
# INSERT INTO pop_product.sku_rule_element (sku_rule_element_id, sku_code_rule_id, element_name, element_type, element_value, dynamic_field, sort_order, creator_id, creator_name, created_time, reviser_id, reviser_name, revised_time, is_deleted, tenant_id) VALUES (7265589072880410003, 7265589072821769311, '颜色扩展1', 'DYNAMIC', null, 'color_1', 1, 154441261, '潘希赛', '2024-12-11 16:23:59', 154441261, '潘希赛', '2025-04-21 16:23:13', 0, null);
# INSERT INTO pop_product.sku_rule_element (sku_rule_element_id, sku_code_rule_id, element_name, element_type, element_value, dynamic_field, sort_order, creator_id, creator_name, created_time, reviser_id, reviser_name, revised_time, is_deleted, tenant_id) VALUES (7265589072842710004, 7268125201169654711, 'SPU', 'DYNAMIC', null, 'spu', 0, 154441261, '潘希赛', '2024-12-11 16:23:59', 154441261, '潘希赛', '2024-12-11 16:23:59', 0, null);
# INSERT INTO pop_product.sku_rule_element (sku_rule_element_id, sku_code_rule_id, element_name, element_type, element_value, dynamic_field, sort_order, creator_id, creator_name, created_time, reviser_id, reviser_name, revised_time, is_deleted, tenant_id) VALUES (7265589072863710005, 7268125201169654711, '尺码', 'DYNAMIC', null, 'size', 2, 154441261, '潘希赛', '2024-12-11 16:23:59', 154441261, '潘希赛', '2025-04-21 16:23:22', 0, null);
# INSERT INTO pop_product.sku_rule_element (sku_rule_element_id, sku_code_rule_id, element_name, element_type, element_value, dynamic_field, sort_order, creator_id, creator_name, created_time, reviser_id, reviser_name, revised_time, is_deleted, tenant_id) VALUES (7265589072880410006, 7268125201169654711, '颜色扩展1', 'DYNAMIC', null, 'color_1', 1, 154441261, '潘希赛', '2024-12-11 16:23:59', 154441261, '潘希赛', '2025-04-21 16:23:13', 0, null);
# INSERT INTO pop_product.sku_rule_element (sku_rule_element_id, sku_code_rule_id, element_name, element_type, element_value, dynamic_field, sort_order, creator_id, creator_name, created_time, reviser_id, reviser_name, revised_time, is_deleted, tenant_id) VALUES (7265589072842710007, 7268402957983985711, 'SPU', 'DYNAMIC', null, 'spu', 0, 154441261, '潘希赛', '2024-12-11 16:23:59', 154441261, '潘希赛', '2024-12-11 16:23:59', 0, null);
# INSERT INTO pop_product.sku_rule_element (sku_rule_element_id, sku_code_rule_id, element_name, element_type, element_value, dynamic_field, sort_order, creator_id, creator_name, created_time, reviser_id, reviser_name, revised_time, is_deleted, tenant_id) VALUES (7265589072863710008, 7268402957983985711, '尺码', 'DYNAMIC', null, 'size', 2, 154441261, '潘希赛', '2024-12-11 16:23:59', 154441261, '潘希赛', '2025-04-21 16:23:22', 0, null);
# INSERT INTO pop_product.sku_rule_element (sku_rule_element_id, sku_code_rule_id, element_name, element_type, element_value, dynamic_field, sort_order, creator_id, creator_name, created_time, reviser_id, reviser_name, revised_time, is_deleted, tenant_id) VALUES (7265589072880410009, 7268402957983985711, '颜色扩展1', 'DYNAMIC', null, 'color_1', 1, 154441261, '潘希赛', '2024-12-11 16:23:59', 154441261, '潘希赛', '2025-04-21 16:23:13', 0, null);
# INSERT INTO pop_product.sku_rule_element (sku_rule_element_id, sku_code_rule_id, element_name, element_type, element_value, dynamic_field, sort_order, creator_id, creator_name, created_time, reviser_id, reviser_name, revised_time, is_deleted, tenant_id) VALUES (7265589072842710010, 7268515700409983111, 'SPU', 'DYNAMIC', null, 'spu', 0, 154441261, '潘希赛', '2024-12-11 16:23:59', 154441261, '潘希赛', '2024-12-11 16:23:59', 0, null);
# INSERT INTO pop_product.sku_rule_element (sku_rule_element_id, sku_code_rule_id, element_name, element_type, element_value, dynamic_field, sort_order, creator_id, creator_name, created_time, reviser_id, reviser_name, revised_time, is_deleted, tenant_id) VALUES (7265589072863710011, 7268515700409983111, '尺码', 'DYNAMIC', null, 'size', 2, 154441261, '潘希赛', '2024-12-11 16:23:59', 154441261, '潘希赛', '2025-04-21 16:23:22', 0, null);
# INSERT INTO pop_product.sku_rule_element (sku_rule_element_id, sku_code_rule_id, element_name, element_type, element_value, dynamic_field, sort_order, creator_id, creator_name, created_time, reviser_id, reviser_name, revised_time, is_deleted, tenant_id) VALUES (7265589072880410012, 7268515700409983111, '颜色扩展1', 'DYNAMIC', null, 'color_1', 1, 154441261, '潘希赛', '2024-12-11 16:23:59', 154441261, '潘希赛', '2025-04-21 16:23:13', 0, null);
# INSERT INTO pop_product.sku_rule_element (sku_rule_element_id, sku_code_rule_id, element_name, element_type, element_value, dynamic_field, sort_order, creator_id, creator_name, created_time, reviser_id, reviser_name, revised_time, is_deleted, tenant_id) VALUES (7265589072842710013, 7268547789536715011, 'SPU', 'DYNAMIC', null, 'spu', 0, 154441261, '潘希赛', '2024-12-11 16:23:59', 154441261, '潘希赛', '2024-12-11 16:23:59', 0, null);
# INSERT INTO pop_product.sku_rule_element (sku_rule_element_id, sku_code_rule_id, element_name, element_type, element_value, dynamic_field, sort_order, creator_id, creator_name, created_time, reviser_id, reviser_name, revised_time, is_deleted, tenant_id) VALUES (7265589072863710014, 7268547789536715011, '尺码', 'DYNAMIC', null, 'size', 2, 154441261, '潘希赛', '2024-12-11 16:23:59', 154441261, '潘希赛', '2025-04-21 16:23:22', 0, null);
# INSERT INTO pop_product.sku_rule_element (sku_rule_element_id, sku_code_rule_id, element_name, element_type, element_value, dynamic_field, sort_order, creator_id, creator_name, created_time, reviser_id, reviser_name, revised_time, is_deleted, tenant_id) VALUES (7265589072880410015, 7268547789536715011, '颜色扩展1', 'DYNAMIC', null, 'color_1', 1, 154441261, '潘希赛', '2024-12-11 16:23:59', 154441261, '潘希赛', '2025-04-21 16:23:13', 0, null);
# INSERT INTO pop_product.sku_rule_element (sku_rule_element_id, sku_code_rule_id, element_name, element_type, element_value, dynamic_field, sort_order, creator_id, creator_name, created_time, reviser_id, reviser_name, revised_time, is_deleted, tenant_id) VALUES (7265589072842710016, 7301530713593143811, 'SPU', 'DYNAMIC', null, 'spu', 0, 154441261, '潘希赛', '2024-12-11 16:23:59', 154441261, '潘希赛', '2024-12-11 16:23:59', 0, null);
# INSERT INTO pop_product.sku_rule_element (sku_rule_element_id, sku_code_rule_id, element_name, element_type, element_value, dynamic_field, sort_order, creator_id, creator_name, created_time, reviser_id, reviser_name, revised_time, is_deleted, tenant_id) VALUES (7265589072863710017, 7301530713593143811, '尺码', 'DYNAMIC', null, 'size', 2, 154441261, '潘希赛', '2024-12-11 16:23:59', 154441261, '潘希赛', '2025-04-21 16:23:22', 0, null);
# INSERT INTO pop_product.sku_rule_element (sku_rule_element_id, sku_code_rule_id, element_name, element_type, element_value, dynamic_field, sort_order, creator_id, creator_name, created_time, reviser_id, reviser_name, revised_time, is_deleted, tenant_id) VALUES (7265589072880410018, 7301530713593143811, '颜色扩展1', 'DYNAMIC', null, 'color_1', 1, 154441261, '潘希赛', '2024-12-11 16:23:59', 154441261, '潘希赛', '2025-04-21 16:23:13', 0, null);



-- 清洗sale_skc数据, 和sale_sku的sale_skc_id关联

# insert into sale_skc (sale_goods_id, product_skc_id, skc, color, platform_color, color_code, color_abbr_code, pictures,
#                       cb_price, local_price, purchase_price, cost_price, creator_id, creator_name, created_time,
#                       reviser_id, reviser_name, revised_time, tenant_id)
# select a.sale_goods_id,
#        b.product_skc_id,
#        b.skc,
#        b.color,
#        b.platform_color,
#        b.color_code,
#        b.color_abbr_code,
#        b.pictures,
#        b.cb_price,
#        b.local_price,
#        b.purchase_price,
#        b.cost_price,
#        b.creator_id,
#        b.creator_name,
#        b.created_time,
#        b.reviser_id,
#        b.reviser_name,
#        b.revised_time,
#        b.tenant_id
# from sale_goods a
#          join product_skc b on a.product_id = b.product_id
#          left join sale_skc c on b.product_skc_id = c.product_skc_id
# where c.sale_skc_id is null and a.is_deleted = 0 and b.is_deleted = 0 and a.platform_product_id is not null;
#
# update sale_sku a join sale_skc b on a.product_skc_id = b.product_skc_id and a.sale_goods_id = b.sale_goods_id
# set a.sale_skc_id = b.sale_skc_id
# where a.sale_skc_id is null;


-- ----------------------------------------------------------------------------

-- 清洗模板数据-Lazada

insert into product_template_lazada_spu
(product_id, spu_code, product_title, all_country_title, brand_id, brand_name, stock_type, delay_delivery_days,
 package_weight, package_dimensions_length, package_dimensions_height, package_dimensions_width, size_group_name,
 size_group_code, is_deleted, tenant_id, creator_id, creator_name, created_time, reviser_id, reviser_name, revised_time)
select a.product_id,
       a.spu_code,
       a.product_title,
       if(a.all_country_title = '', null, a.all_country_title),
       null,
       a.brand_name,
       null,
       null,
       a.package_weight,
       a.package_dimensions_length,
       a.package_dimensions_height,
       a.package_dimensions_width,
       a.size_group_name,
       a.size_group_code,
       a.is_deleted,
       a.tenant_id,
       a.creator_id,
       a.creator_name,
       a.created_time,
       a.reviser_id,
       a.reviser_name,
       a.revised_time
from product a left join product_template_lazada_spu b on a.spu_code = b.spu_code
where a.is_deleted = 0 and a.spu_code is not null and b.lazada_spu_id is null and a.is_sync_platform = 0;

insert into product_template_lazada_skc
    ( lazada_spu_id, product_skc_id, skc, color, platform_color, color_code, color_abbr_code, pictures, state, combo, cb_price, local_price, purchase_price, cost_price, is_deleted, creator_id, creator_name, created_time, reviser_id, reviser_name, revised_time, tenant_id)
select  c.lazada_spu_id, b.product_skc_id, b.skc, b.color, b.platform_color, b.color_code, b.color_abbr_code, b.pictures, b.state, 0, b.cb_price, b.local_price,b. purchase_price,b.cost_price, b.is_deleted, b.creator_id, b.creator_name, b.created_time, b.reviser_id, b.reviser_name, b.revised_time, b.tenant_id
from product a join product_skc b on a.product_id = b.product_id
join product_template_lazada_spu c on a.product_id = c.product_id
left join product_template_lazada_skc d on b.product_skc_id = d.product_skc_id
where a.is_deleted = 0 and b.is_deleted = 0 and a.spu_code is not null and d.lazada_skc_id is null;
;

insert into product_template_lazada_sku
(lazada_sku_id, lazada_skc_id, lazada_spu_id, seller_sku, barcode, barcodes, country, stock_quantity, size_name, sale_price, last_sale_price, retail_price, last_retail_price, purchase_sale_price, purchase_retail_price, regular_sale_price, regular_retail_price, creator_id, creator_name, created_time, reviser_id, reviser_name, revised_time, tenant_id)
select
    sale_sku_id, a.lazada_skc_id, a.lazada_spu_id, b.seller_sku, b.barcode, b.barcodes, b.country, b.stock_quantity, b.size_name, b.sale_price, b.last_sale_price, b.retail_price, b.last_retail_price, b.purchase_sale_price, b.purchase_retail_price, b.regular_sale_price, b.regular_retail_price, b.creator_id, b.creator_name, b.created_time, b.reviser_id, b.reviser_name, b.revised_time, b.tenant_id
from product_template_lazada_skc a join sale_sku b on a.product_skc_id = b.product_skc_id
left join product_template_lazada_sku c on a.lazada_skc_id = c.lazada_skc_id and b.country = c.country and b.size_name = c.size_name
where b.enable = 1 and c.lazada_sku_id is null;

-- 清洗模板数据-AE






# 修复模板空条码
update product_template_ae_spu a
         join product_template_ae_skc b on a.ae_spu_id = b.ae_spu_id
         join product_template_ae_sku c on b.ae_skc_id = c.ae_skc_id
join product_barcode d on d.spu_code = a.spu_code and d.skc = b.skc and d.size_name = c.size_name
set  c.barcode =  d.barcode
where c.barcode is null;