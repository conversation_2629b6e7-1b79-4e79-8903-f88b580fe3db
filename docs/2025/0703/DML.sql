update `ae_original_product_sku` set deleted = is_deleted where is_deleted!=5;
update `ae_original_product_spu` set deleted = is_deleted where is_deleted!=5;
update `ae_sale_goods` set deleted = is_deleted where is_deleted!=5;
update `ae_sale_skc` set deleted = is_deleted where is_deleted!=5;
update `ae_sale_sku` set deleted = is_deleted where is_deleted!=5;
update `alibaba_category` set deleted = is_deleted where is_deleted!=5;
update `alibaba_category_tree_cache` set deleted = is_deleted where is_deleted!=5;
update `aliexpress_category` set deleted = is_deleted where is_deleted!=5;
update `aliexpress_category_tree_cache` set deleted = is_deleted where is_deleted!=5;
update `barcode_unit_snapshot` set deleted = is_deleted where is_deleted!=5;
update `base_size` set deleted = is_deleted where is_deleted!=5;
update `base_size_group` set deleted = is_deleted where is_deleted!=5;
update `base_sku_info` set deleted = is_deleted where is_deleted!=5;
update `base_sku_info_copy1` set deleted = is_deleted where is_deleted!=5;
update `basic_product` set deleted = is_deleted where is_deleted!=5;
update `cargo_tray_info` set deleted = is_deleted where is_deleted!=5;
update `category_price_range` set deleted = is_deleted where is_deleted!=5;
update `currency_exchange_rate` set deleted = is_deleted where is_deleted!=5;
update `ding_talk_approval_price_overrange` set deleted = is_deleted where is_deleted!=5;
update `download_task` set deleted = is_deleted where is_deleted!=5;
update `error_order_info` set deleted = is_deleted where is_deleted!=5;
update `error_order_spu_info` set deleted = is_deleted where is_deleted!=5;
update `external_api_call_log` set deleted = is_deleted where is_deleted!=5;
update `fee_configuration` set deleted = is_deleted where is_deleted!=5;
update `fetch_product_parmas` set deleted = is_deleted where is_deleted!=5;
update `history_handle_result` set deleted = is_deleted where is_deleted!=5;
update `history_publish_record` set deleted = is_deleted where is_deleted!=5;
update `image_qc_record` set deleted = is_deleted where is_deleted!=5;
update `image_repository` set deleted = is_deleted where is_deleted!=5;
update `import_product_record` set deleted = is_deleted where is_deleted!=5;
update `jst_barcode` set deleted = is_deleted where is_deleted!=5;
update `lazada_brand` set deleted = is_deleted where is_deleted!=5;
update `lazada_category` set deleted = is_deleted where is_deleted!=5;
update `lazada_original_product_sku` set deleted = is_deleted where is_deleted!=5;
update `lazada_original_product_spu` set deleted = is_deleted where is_deleted!=5;
update `listing_template` set deleted = is_deleted where is_deleted!=5;
update `listing_template_shop` set deleted = is_deleted where is_deleted!=5;
update `listing_template_skc` set deleted = is_deleted where is_deleted!=5;
update `listing_template_sku` set deleted = is_deleted where is_deleted!=5;
update `planning_category_price` set deleted = is_deleted where is_deleted!=5;
update `planning_develop_rhythm` set deleted = is_deleted where is_deleted!=5;
update `planning_info` set deleted = is_deleted where is_deleted!=5;
update `planning_preset_category` set deleted = is_deleted where is_deleted!=5;
update `planning_publish_rhythm` set deleted = is_deleted where is_deleted!=5;
update `planning_summary` set deleted = is_deleted where is_deleted!=5;
update `planning_update_log` set deleted = is_deleted where is_deleted!=5;
update `platform_product_pull_task` set deleted = is_deleted where is_deleted!=5;
update `price_alert_configuration` set deleted = is_deleted where is_deleted!=5;
update `price_alert_configuration_detail` set deleted = is_deleted where is_deleted!=5;
update `price_alert_record` set deleted = is_deleted where is_deleted!=5;
update `price_overrange_comparison_log` set deleted = is_deleted where is_deleted!=5;
update `product` set deleted = is_deleted where is_deleted!=5;
update `product_asp_configuration` set deleted = is_deleted where is_deleted!=5;
update `product_attributes` set deleted = is_deleted where is_deleted!=5;
update `product_bar_code` set deleted = is_deleted where is_deleted!=5;
update `product_barcode` set deleted = is_deleted where is_deleted!=5;
update `product_basic_price_rule` set deleted = is_deleted where is_deleted!=5;
update `product_basic_price_rule_condition` set deleted = is_deleted where is_deleted!=5;
update `product_basic_price_rule_condition_detail` set deleted = is_deleted where is_deleted!=5;
update `product_basic_price_rule_detail` set deleted = is_deleted where is_deleted!=5;
update `product_batch_update_image_task` set deleted = is_deleted where is_deleted!=5;
update `product_batch_update_price_inventory_task` set deleted = is_deleted where is_deleted!=5;
update `product_batch_update_title_task` set deleted = is_deleted where is_deleted!=5;
update `product_change_failure` set deleted = is_deleted where is_deleted!=5;
update `product_create_task` set deleted = is_deleted where is_deleted!=5;
update `product_operate_log` set deleted = is_deleted where is_deleted!=5;
update `product_picture` set deleted = is_deleted where is_deleted!=5;
update `product_price_condition` set deleted = is_deleted where is_deleted!=5;
update `product_price_formula` set deleted = is_deleted where is_deleted!=5;
update `product_price_formula_detail` set deleted = is_deleted where is_deleted!=5;
update `product_price_rule` set deleted = is_deleted where is_deleted!=5;
update `product_price_rule_group` set deleted = is_deleted where is_deleted!=5;
update `product_publish_task` set deleted = is_deleted where is_deleted!=5;
update `product_skc` set deleted = is_deleted where is_deleted!=5;
update `product_source_data` set deleted = is_deleted where is_deleted!=5;
update `product_stock_info` set deleted = is_deleted where is_deleted!=5;
update `product_sync_log` set deleted = is_deleted where is_deleted!=5;
update `product_system_update_fail_log` set deleted = is_deleted where is_deleted!=5;
update `product_tag` set deleted = is_deleted where is_deleted!=5;
update `product_template_ae_skc` set deleted = is_deleted where is_deleted!=5;
update `product_template_ae_sku` set deleted = is_deleted where is_deleted!=5;
update `product_template_ae_spu` set deleted = is_deleted where is_deleted!=5;
update `product_template_lazada_skc` set deleted = is_deleted where is_deleted!=5;
update `product_template_lazada_sku` set deleted = is_deleted where is_deleted!=5;
update `product_template_lazada_spu` set deleted = is_deleted where is_deleted!=5;
update `product_template_temu_skc` set deleted = is_deleted where is_deleted!=5;
update `product_template_temu_sku` set deleted = is_deleted where is_deleted!=5;
update `product_template_temu_spu` set deleted = is_deleted where is_deleted!=5;
update `product_title_condition_word` set deleted = is_deleted where is_deleted!=5;
update `product_title_dictionary_field` set deleted = is_deleted where is_deleted!=5;
update `product_title_dictionary_field_value` set deleted = is_deleted where is_deleted!=5;
update `product_title_generation_record` set deleted = is_deleted where is_deleted!=5;
update `product_title_hot_word` set deleted = is_deleted where is_deleted!=5;
update `product_title_hot_word_import` set deleted = is_deleted where is_deleted!=5;
update `product_title_rule` set deleted = is_deleted where is_deleted!=5;
update `product_title_rule_detail` set deleted = is_deleted where is_deleted!=5;
update `publish_attribute` set deleted = is_deleted where is_deleted!=5;
update `publish_attribute_group` set deleted = is_deleted where is_deleted!=5;
update `publish_attribute_value` set deleted = is_deleted where is_deleted!=5;
update `publish_category` set deleted = is_deleted where is_deleted!=5;
update `publish_category_attr` set deleted = is_deleted where is_deleted!=5;
update `publish_category_mapping` set deleted = is_deleted where is_deleted!=5;
update `publish_channel` set deleted = is_deleted where is_deleted!=5;
update `publish_platform` set deleted = is_deleted where is_deleted!=5;
update `publish_platform_attr` set deleted = is_deleted where is_deleted!=5;
update `publish_platform_attr_value` set deleted = is_deleted where is_deleted!=5;
update `ref_exchange_rate_market` set deleted = is_deleted where is_deleted!=5;
update `sale_goods` set deleted = is_deleted where is_deleted!=5;
update `sale_skc` set deleted = is_deleted where is_deleted!=5;
update `sale_sku` set deleted = is_deleted where is_deleted!=5;
update `seller_sku_barcode_ref` set deleted = is_deleted where is_deleted!=5;
update `seller_sku_barcode_relation` set deleted = is_deleted where is_deleted!=5;
update `seller_sku_flat_info` set deleted = is_deleted where is_deleted!=5;
update `shop` set deleted = is_deleted where is_deleted!=5;
update `shop_seller_mapping` set deleted = is_deleted where is_deleted!=5;
update `size_map` set deleted = is_deleted where is_deleted!=5;
update `size_template` set deleted = is_deleted where is_deleted!=5;
update `sku_code_rule` set deleted = is_deleted where is_deleted!=5;
update `sku_rule_element` set deleted = is_deleted where is_deleted!=5;
update `task_info` set deleted = is_deleted where is_deleted!=5;
update `temu_category` set deleted = is_deleted where is_deleted!=5;
update `temu_pull_select_status_log` set deleted = is_deleted where is_deleted!=5;
update `temu_sale_goods` set deleted = is_deleted where is_deleted!=5;
update `temu_sale_skc` set deleted = is_deleted where is_deleted!=5;
update `temu_sale_sku` set deleted = is_deleted where is_deleted!=5;
update `temu_skc_detailed_image` set deleted = is_deleted where is_deleted!=5;