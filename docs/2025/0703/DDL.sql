ALTER TABLE `ae_original_product_sku` MODIFY COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是(已作废，用deleted代替)',
    ADD COLUMN `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是' AFTER `is_deleted`;
ALTER TABLE `ae_original_product_spu` MODIFY COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是(已作废，用deleted代替)',
    ADD COLUMN `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是' AFTER `is_deleted`;
ALTER TABLE `ae_sale_goods` MODIFY COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是(已作废，用deleted代替)',
    ADD COLUMN `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是' AFTER `is_deleted`;
ALTER TABLE `ae_sale_skc` MODIFY COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是(已作废，用deleted代替)',
    ADD COLUMN `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是' AFTER `is_deleted`;
ALTER TABLE `ae_sale_sku` MODIFY COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是(已作废，用deleted代替)',
    ADD COLUMN `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是' AFTER `is_deleted`;
ALTER TABLE `alibaba_category` MODIFY COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是(已作废，用deleted代替)',
    ADD COLUMN `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是' AFTER `is_deleted`;
ALTER TABLE `alibaba_category_tree_cache` MODIFY COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是(已作废，用deleted代替)',
    ADD COLUMN `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是' AFTER `is_deleted`;
ALTER TABLE `aliexpress_category` MODIFY COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是(已作废，用deleted代替)',
    ADD COLUMN `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是' AFTER `is_deleted`;
ALTER TABLE `aliexpress_category_tree_cache` MODIFY COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是(已作废，用deleted代替)',
    ADD COLUMN `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是' AFTER `is_deleted`;
ALTER TABLE `barcode_unit_snapshot` MODIFY COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是(已作废，用deleted代替)',
    ADD COLUMN `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是' AFTER `is_deleted`;
ALTER TABLE `base_size` MODIFY COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是(已作废，用deleted代替)',
    ADD COLUMN `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是' AFTER `is_deleted`;
ALTER TABLE `base_size_group` MODIFY COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是(已作废，用deleted代替)',
    ADD COLUMN `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是' AFTER `is_deleted`;
ALTER TABLE `base_sku_info` MODIFY COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是(已作废，用deleted代替)',
    ADD COLUMN `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是' AFTER `is_deleted`;
ALTER TABLE `base_sku_info_copy1` MODIFY COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是(已作废，用deleted代替)',
    ADD COLUMN `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是' AFTER `is_deleted`;
ALTER TABLE `basic_product` MODIFY COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是(已作废，用deleted代替)',
    ADD COLUMN `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是' AFTER `is_deleted`;
ALTER TABLE `cargo_tray_info` MODIFY COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是(已作废，用deleted代替)',
    ADD COLUMN `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是' AFTER `is_deleted`;
ALTER TABLE `category_price_range` MODIFY COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是(已作废，用deleted代替)',
    ADD COLUMN `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是' AFTER `is_deleted`;
ALTER TABLE `currency_exchange_rate` MODIFY COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是(已作废，用deleted代替)',
    ADD COLUMN `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是' AFTER `is_deleted`;
ALTER TABLE `ding_talk_approval_price_overrange` MODIFY COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是(已作废，用deleted代替)',
    ADD COLUMN `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是' AFTER `is_deleted`;
ALTER TABLE `download_task` MODIFY COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是(已作废，用deleted代替)',
    ADD COLUMN `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是' AFTER `is_deleted`;
ALTER TABLE `error_order_info` MODIFY COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是(已作废，用deleted代替)',
    ADD COLUMN `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是' AFTER `is_deleted`;
ALTER TABLE `error_order_spu_info` MODIFY COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是(已作废，用deleted代替)',
    ADD COLUMN `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是' AFTER `is_deleted`;
ALTER TABLE `external_api_call_log` MODIFY COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是(已作废，用deleted代替)',
    ADD COLUMN `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是' AFTER `is_deleted`;
ALTER TABLE `fee_configuration` MODIFY COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是(已作废，用deleted代替)',
    ADD COLUMN `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是' AFTER `is_deleted`;
ALTER TABLE `fetch_product_parmas` MODIFY COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是(已作废，用deleted代替)',
    ADD COLUMN `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是' AFTER `is_deleted`;
ALTER TABLE `history_handle_result` MODIFY COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是(已作废，用deleted代替)',
    ADD COLUMN `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是' AFTER `is_deleted`;
ALTER TABLE `history_publish_record` MODIFY COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是(已作废，用deleted代替)',
    ADD COLUMN `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是' AFTER `is_deleted`;
ALTER TABLE `image_qc_record` MODIFY COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是(已作废，用deleted代替)',
    ADD COLUMN `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是' AFTER `is_deleted`;
ALTER TABLE `image_repository` MODIFY COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是(已作废，用deleted代替)',
    ADD COLUMN `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是' AFTER `is_deleted`;
ALTER TABLE `import_product_record` MODIFY COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是(已作废，用deleted代替)',
    ADD COLUMN `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是' AFTER `is_deleted`;
ALTER TABLE `jst_barcode` MODIFY COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是(已作废，用deleted代替)',
    ADD COLUMN `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是' AFTER `is_deleted`;
ALTER TABLE `lazada_brand` MODIFY COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是(已作废，用deleted代替)',
    ADD COLUMN `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是' AFTER `is_deleted`;
ALTER TABLE `lazada_category` MODIFY COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是(已作废，用deleted代替)',
    ADD COLUMN `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是' AFTER `is_deleted`;
ALTER TABLE `lazada_original_product_sku` MODIFY COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是(已作废，用deleted代替)',
    ADD COLUMN `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是' AFTER `is_deleted`;
ALTER TABLE `lazada_original_product_spu` MODIFY COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是(已作废，用deleted代替)',
    ADD COLUMN `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是' AFTER `is_deleted`;
ALTER TABLE `listing_template` MODIFY COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是(已作废，用deleted代替)',
    ADD COLUMN `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是' AFTER `is_deleted`;
ALTER TABLE `listing_template_shop` MODIFY COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是(已作废，用deleted代替)',
    ADD COLUMN `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是' AFTER `is_deleted`;
ALTER TABLE `listing_template_skc` MODIFY COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是(已作废，用deleted代替)',
    ADD COLUMN `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是' AFTER `is_deleted`;
ALTER TABLE `listing_template_sku` MODIFY COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是(已作废，用deleted代替)',
    ADD COLUMN `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是' AFTER `is_deleted`;
ALTER TABLE `planning_category_price` MODIFY COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是(已作废，用deleted代替)',
    ADD COLUMN `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是' AFTER `is_deleted`;
ALTER TABLE `planning_develop_rhythm` MODIFY COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是(已作废，用deleted代替)',
    ADD COLUMN `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是' AFTER `is_deleted`;
ALTER TABLE `planning_info` MODIFY COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是(已作废，用deleted代替)',
    ADD COLUMN `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是' AFTER `is_deleted`;
ALTER TABLE `planning_preset_category` MODIFY COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是(已作废，用deleted代替)',
    ADD COLUMN `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是' AFTER `is_deleted`;
ALTER TABLE `planning_publish_rhythm` MODIFY COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是(已作废，用deleted代替)',
    ADD COLUMN `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是' AFTER `is_deleted`;
ALTER TABLE `planning_summary` MODIFY COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是(已作废，用deleted代替)',
    ADD COLUMN `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是' AFTER `is_deleted`;
ALTER TABLE `planning_update_log` MODIFY COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是(已作废，用deleted代替)',
    ADD COLUMN `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是' AFTER `is_deleted`;
ALTER TABLE `platform_product_pull_task` MODIFY COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是(已作废，用deleted代替)',
    ADD COLUMN `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是' AFTER `is_deleted`;
ALTER TABLE `price_alert_configuration` MODIFY COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是(已作废，用deleted代替)',
    ADD COLUMN `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是' AFTER `is_deleted`;
ALTER TABLE `price_alert_configuration_detail` MODIFY COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是(已作废，用deleted代替)',
    ADD COLUMN `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是' AFTER `is_deleted`;
ALTER TABLE `price_alert_record` MODIFY COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是(已作废，用deleted代替)',
    ADD COLUMN `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是' AFTER `is_deleted`;
ALTER TABLE `price_overrange_comparison_log` MODIFY COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是(已作废，用deleted代替)',
    ADD COLUMN `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是' AFTER `is_deleted`;
ALTER TABLE `product` MODIFY COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是(已作废，用deleted代替)',
    ADD COLUMN `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是' AFTER `is_deleted`;
ALTER TABLE `product_asp_configuration` MODIFY COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是(已作废，用deleted代替)',
    ADD COLUMN `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是' AFTER `is_deleted`;
ALTER TABLE `product_attributes` MODIFY COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是(已作废，用deleted代替)',
    ADD COLUMN `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是' AFTER `is_deleted`;
ALTER TABLE `product_bar_code` MODIFY COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是(已作废，用deleted代替)',
    ADD COLUMN `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是' AFTER `is_deleted`;
ALTER TABLE `product_barcode` MODIFY COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是(已作废，用deleted代替)',
    ADD COLUMN `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是' AFTER `is_deleted`;
ALTER TABLE `product_basic_price_rule` MODIFY COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是(已作废，用deleted代替)',
    ADD COLUMN `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是' AFTER `is_deleted`;
ALTER TABLE `product_basic_price_rule_condition` MODIFY COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是(已作废，用deleted代替)',
    ADD COLUMN `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是' AFTER `is_deleted`;
ALTER TABLE `product_basic_price_rule_condition_detail` MODIFY COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是(已作废，用deleted代替)',
    ADD COLUMN `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是' AFTER `is_deleted`;
ALTER TABLE `product_basic_price_rule_detail` MODIFY COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是(已作废，用deleted代替)',
    ADD COLUMN `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是' AFTER `is_deleted`;
ALTER TABLE `product_batch_update_image_task` MODIFY COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是(已作废，用deleted代替)',
    ADD COLUMN `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是' AFTER `is_deleted`;
ALTER TABLE `product_batch_update_price_inventory_task` MODIFY COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是(已作废，用deleted代替)',
    ADD COLUMN `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是' AFTER `is_deleted`;
ALTER TABLE `product_batch_update_title_task` MODIFY COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是(已作废，用deleted代替)',
    ADD COLUMN `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是' AFTER `is_deleted`;
ALTER TABLE `product_change_failure` MODIFY COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是(已作废，用deleted代替)',
    ADD COLUMN `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是' AFTER `is_deleted`;
ALTER TABLE `product_create_task` MODIFY COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是(已作废，用deleted代替)',
    ADD COLUMN `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是' AFTER `is_deleted`;
ALTER TABLE `product_operate_log` MODIFY COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是(已作废，用deleted代替)',
    ADD COLUMN `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是' AFTER `is_deleted`;
ALTER TABLE `product_picture` MODIFY COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是(已作废，用deleted代替)',
    ADD COLUMN `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是' AFTER `is_deleted`;
ALTER TABLE `product_price_condition` MODIFY COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是(已作废，用deleted代替)',
    ADD COLUMN `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是' AFTER `is_deleted`;
ALTER TABLE `product_price_formula` MODIFY COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是(已作废，用deleted代替)',
    ADD COLUMN `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是' AFTER `is_deleted`;
ALTER TABLE `product_price_formula_detail` MODIFY COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是(已作废，用deleted代替)',
    ADD COLUMN `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是' AFTER `is_deleted`;
ALTER TABLE `product_price_rule` MODIFY COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是(已作废，用deleted代替)',
    ADD COLUMN `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是' AFTER `is_deleted`;
ALTER TABLE `product_price_rule_group` MODIFY COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是(已作废，用deleted代替)',
    ADD COLUMN `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是' AFTER `is_deleted`;
ALTER TABLE `product_publish_task` MODIFY COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是(已作废，用deleted代替)',
    ADD COLUMN `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是' AFTER `is_deleted`;
ALTER TABLE `product_skc` MODIFY COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是(已作废，用deleted代替)',
    ADD COLUMN `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是' AFTER `is_deleted`;
ALTER TABLE `product_source_data` MODIFY COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是(已作废，用deleted代替)',
    ADD COLUMN `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是' AFTER `is_deleted`;
ALTER TABLE `product_stock_info` MODIFY COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是(已作废，用deleted代替)',
    ADD COLUMN `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是' AFTER `is_deleted`;
ALTER TABLE `product_sync_log` MODIFY COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是(已作废，用deleted代替)',
    ADD COLUMN `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是' AFTER `is_deleted`;
ALTER TABLE `product_system_update_fail_log` MODIFY COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是(已作废，用deleted代替)',
    ADD COLUMN `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是' AFTER `is_deleted`;
ALTER TABLE `product_tag` MODIFY COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是(已作废，用deleted代替)',
    ADD COLUMN `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是' AFTER `is_deleted`;
ALTER TABLE `product_template_ae_skc` MODIFY COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是(已作废，用deleted代替)',
    ADD COLUMN `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是' AFTER `is_deleted`;
ALTER TABLE `product_template_ae_sku` MODIFY COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是(已作废，用deleted代替)',
    ADD COLUMN `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是' AFTER `is_deleted`;
ALTER TABLE `product_template_ae_spu` MODIFY COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是(已作废，用deleted代替)',
    ADD COLUMN `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是' AFTER `is_deleted`;
ALTER TABLE `product_template_lazada_skc` MODIFY COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是(已作废，用deleted代替)',
    ADD COLUMN `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是' AFTER `is_deleted`;
ALTER TABLE `product_template_lazada_sku` MODIFY COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是(已作废，用deleted代替)',
    ADD COLUMN `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是' AFTER `is_deleted`;
ALTER TABLE `product_template_lazada_spu` MODIFY COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是(已作废，用deleted代替)',
    ADD COLUMN `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是' AFTER `is_deleted`;
ALTER TABLE `product_template_temu_skc` MODIFY COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是(已作废，用deleted代替)',
    ADD COLUMN `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是' AFTER `is_deleted`;
ALTER TABLE `product_template_temu_sku` MODIFY COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是(已作废，用deleted代替)',
    ADD COLUMN `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是' AFTER `is_deleted`;
ALTER TABLE `product_template_temu_spu` MODIFY COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是(已作废，用deleted代替)',
    ADD COLUMN `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是' AFTER `is_deleted`;
ALTER TABLE `product_title_condition_word` MODIFY COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是(已作废，用deleted代替)',
    ADD COLUMN `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是' AFTER `is_deleted`;
ALTER TABLE `product_title_dictionary_field` MODIFY COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是(已作废，用deleted代替)',
    ADD COLUMN `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是' AFTER `is_deleted`;
ALTER TABLE `product_title_dictionary_field_value` MODIFY COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是(已作废，用deleted代替)',
    ADD COLUMN `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是' AFTER `is_deleted`;
ALTER TABLE `product_title_generation_record` MODIFY COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是(已作废，用deleted代替)',
    ADD COLUMN `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是' AFTER `is_deleted`;
ALTER TABLE `product_title_hot_word` MODIFY COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是(已作废，用deleted代替)',
    ADD COLUMN `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是' AFTER `is_deleted`;
ALTER TABLE `product_title_hot_word_import` MODIFY COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是(已作废，用deleted代替)',
    ADD COLUMN `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是' AFTER `is_deleted`;
ALTER TABLE `product_title_rule` MODIFY COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是(已作废，用deleted代替)',
    ADD COLUMN `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是' AFTER `is_deleted`;
ALTER TABLE `product_title_rule_detail` MODIFY COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是(已作废，用deleted代替)',
    ADD COLUMN `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是' AFTER `is_deleted`;
ALTER TABLE `publish_attribute` MODIFY COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是(已作废，用deleted代替)',
    ADD COLUMN `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是' AFTER `is_deleted`;
ALTER TABLE `publish_attribute_group` MODIFY COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是(已作废，用deleted代替)',
    ADD COLUMN `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是' AFTER `is_deleted`;
ALTER TABLE `publish_attribute_value` MODIFY COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是(已作废，用deleted代替)',
    ADD COLUMN `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是' AFTER `is_deleted`;
ALTER TABLE `publish_category` MODIFY COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是(已作废，用deleted代替)',
    ADD COLUMN `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是' AFTER `is_deleted`;
ALTER TABLE `publish_category_attr` MODIFY COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是(已作废，用deleted代替)',
    ADD COLUMN `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是' AFTER `is_deleted`;
ALTER TABLE `publish_category_mapping` MODIFY COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是(已作废，用deleted代替)',
    ADD COLUMN `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是' AFTER `is_deleted`;
ALTER TABLE `publish_channel` MODIFY COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是(已作废，用deleted代替)',
    ADD COLUMN `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是' AFTER `is_deleted`;
ALTER TABLE `publish_platform` MODIFY COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是(已作废，用deleted代替)',
    ADD COLUMN `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是' AFTER `is_deleted`;
ALTER TABLE `publish_platform_attr` MODIFY COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是(已作废，用deleted代替)',
    ADD COLUMN `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是' AFTER `is_deleted`;
ALTER TABLE `publish_platform_attr_value` MODIFY COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是(已作废，用deleted代替)',
    ADD COLUMN `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是' AFTER `is_deleted`;
ALTER TABLE `ref_exchange_rate_market` MODIFY COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是(已作废，用deleted代替)',
    ADD COLUMN `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是' AFTER `is_deleted`;
ALTER TABLE `sale_goods` MODIFY COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是(已作废，用deleted代替)',
    ADD COLUMN `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是' AFTER `is_deleted`;
ALTER TABLE `sale_skc` MODIFY COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是(已作废，用deleted代替)',
    ADD COLUMN `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是' AFTER `is_deleted`;
ALTER TABLE `sale_sku` MODIFY COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是(已作废，用deleted代替)',
    ADD COLUMN `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是' AFTER `is_deleted`;
ALTER TABLE `seller_sku_barcode_ref` MODIFY COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是(已作废，用deleted代替)',
    ADD COLUMN `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是' AFTER `is_deleted`;
ALTER TABLE `seller_sku_barcode_relation` MODIFY COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是(已作废，用deleted代替)',
    ADD COLUMN `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是' AFTER `is_deleted`;
ALTER TABLE `seller_sku_flat_info` MODIFY COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是(已作废，用deleted代替)',
    ADD COLUMN `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是' AFTER `is_deleted`;
ALTER TABLE `shop` MODIFY COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是(已作废，用deleted代替)',
    ADD COLUMN `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是' AFTER `is_deleted`;
ALTER TABLE `shop_seller_mapping` MODIFY COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是(已作废，用deleted代替)',
    ADD COLUMN `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是' AFTER `is_deleted`;
ALTER TABLE `size_map` MODIFY COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是(已作废，用deleted代替)',
    ADD COLUMN `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是' AFTER `is_deleted`;
ALTER TABLE `size_template` MODIFY COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是(已作废，用deleted代替)',
    ADD COLUMN `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是' AFTER `is_deleted`;
ALTER TABLE `sku_code_rule` MODIFY COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是(已作废，用deleted代替)',
    ADD COLUMN `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是' AFTER `is_deleted`;
ALTER TABLE `sku_rule_element` MODIFY COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是(已作废，用deleted代替)',
    ADD COLUMN `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是' AFTER `is_deleted`;
ALTER TABLE `task_info` MODIFY COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是(已作废，用deleted代替)',
    ADD COLUMN `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是' AFTER `is_deleted`;
ALTER TABLE `temu_category` MODIFY COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是(已作废，用deleted代替)',
    ADD COLUMN `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是' AFTER `is_deleted`;
ALTER TABLE `temu_pull_select_status_log` MODIFY COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是(已作废，用deleted代替)',
    ADD COLUMN `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是' AFTER `is_deleted`;
ALTER TABLE `temu_sale_goods` MODIFY COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是(已作废，用deleted代替)',
    ADD COLUMN `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是' AFTER `is_deleted`;
ALTER TABLE `temu_sale_skc` MODIFY COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是(已作废，用deleted代替)',
    ADD COLUMN `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是' AFTER `is_deleted`;
ALTER TABLE `temu_sale_sku` MODIFY COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是(已作废，用deleted代替)',
    ADD COLUMN `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是' AFTER `is_deleted`;
ALTER TABLE `temu_skc_detailed_image` MODIFY COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是(已作废，用deleted代替)',
    ADD COLUMN `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是' AFTER `is_deleted`;
