CREATE TABLE `listing_template` (
                                    `listing_template_id` bigint NOT NULL COMMENT '上架模板ID',
                                    `listing_template_code` varchar(55) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '模板序号',
                                    `platform_id` bigint NOT NULL COMMENT '平台ID',
                                    `pattern_code` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '版型号',
                                    `template_image` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '版型图',
                                    `category_code` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '品类Code',
                                    `category_name` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '品类名称',
                                    `category_id` bigint NOT NULL COMMENT '品类ID',
                                    `material_languages` json NULL COMMENT '多语言素材集合',
                                    `size_group_name` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '尺码组名称',
                                    `size_group_code` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '尺码组编码',
                                    `size_specification` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '尺码规格',
                                    `is_customized` tinyint(1) DEFAULT NULL COMMENT '商品类型：0-非定制商品 1-定制商品',
                                    `document_file` varchar(5000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '说明文件',
                                    `document_file_type` tinyint(1) DEFAULT NULL COMMENT '说明文件类型 1.更新说明文件 2.补充或修改[安装/使用说明]和[安全信息说明]',
                                    `state` tinyint(1) NOT NULL DEFAULT '0' COMMENT '模板状态 0停用 1正常',
                                    `submit_state` tinyint(1) NOT NULL DEFAULT '0' COMMENT '提交状态 0 未提交 1 已提交',
                                    `is_deleted` tinyint(1) NOT NULL COMMENT '逻辑删除 0 否 1是',
                                    `creator_id` bigint DEFAULT NULL COMMENT '创建者ID',
                                    `created_time` datetime DEFAULT NULL COMMENT '创建时间',
                                    `reviser_id` bigint DEFAULT NULL COMMENT '修改者ID',
                                    `revised_time` datetime DEFAULT NULL COMMENT '修改时间',
                                    `creator_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建人名称',
                                    `reviser_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '修改人名称',
                                    `tenant_id` bigint DEFAULT NULL COMMENT '租户id',
                                    PRIMARY KEY (`listing_template_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='上架模板表';




CREATE TABLE `listing_template_shop` (
                                         `listing_template_shop_id` bigint NOT NULL COMMENT '主键ID',
                                         `listing_template_id` bigint NOT NULL COMMENT '上架模板ID',
                                         `shop_id` bigint NOT NULL COMMENT '店铺ID',
                                         `pattern_code` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '版型号',
                                         `country_shipping_warehouse` json NULL COMMENT '站点和对应的发货仓',
                                         `currency_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '币种编码',
                                         `currency_symbol` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '币种符号',
                                         `size_template_name` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '尺码表名称',
                                         `size_template_code` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '尺码表编码',
                                         `unit_setting` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '单位设置',
                                         `model_profile` json NULL COMMENT '模特信息',
                                         `delivery_promise` json NULL COMMENT '承诺发货时效',
                                         `freight_template` json NULL COMMENT '运费模版',
                                         `stocking_area` json NULL COMMENT '备货区域',
                                         `package_specs` json NULL COMMENT '包装规格',
                                         `is_deleted` tinyint(1) NOT NULL COMMENT '逻辑删除 0 否 1是',
                                         `creator_id` bigint DEFAULT NULL COMMENT '创建者ID',
                                         `created_time` datetime DEFAULT NULL COMMENT '创建时间',
                                         `reviser_id` bigint DEFAULT NULL COMMENT '修改者ID',
                                         `revised_time` datetime DEFAULT NULL COMMENT '修改时间',
                                         `creator_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建人名称',
                                         `reviser_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '修改人名称',
                                         `tenant_id` bigint DEFAULT NULL COMMENT '租户id',
                                         PRIMARY KEY (`listing_template_shop_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='上架模板店铺表';




CREATE TABLE `listing_template_skc` (
                                        `template_skc_id` bigint NOT NULL COMMENT '上架模板SKC模板id',
                                        `listing_template_id` bigint NOT NULL COMMENT '上架模板ID',
                                        `color` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '内部颜色',
                                        `platform_color` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '平台颜色',
                                        `color_code` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '颜色编码',
                                        `color_abbr_code` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '颜色缩写编码',
                                        `template_image` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '版型图',
                                        `state` tinyint(1) DEFAULT '1' COMMENT 'SKC状态【1可用；0取消】',
                                        `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否已删除;0未删除，1已删除',
                                        `creator_id` bigint NOT NULL COMMENT '创建人id',
                                        `creator_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '创建人',
                                        `created_time` datetime NOT NULL COMMENT '创建时间',
                                        `reviser_id` bigint DEFAULT NULL COMMENT '最近修改人id;新增时取创建人id',
                                        `reviser_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '最近修改人名称;新增时取创建人名称',
                                        `revised_time` datetime DEFAULT NULL COMMENT '更新时间',
                                        `tenant_id` bigint DEFAULT NULL COMMENT '租户id',
                                        PRIMARY KEY (`template_skc_id`),
                                        KEY `idx_listing_template_id` (`listing_template_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='上架模板-SKC';




CREATE TABLE `listing_template_sku` (
                                        `template_sku_id` bigint NOT NULL AUTO_INCREMENT COMMENT '上架模板SKU模板id',
                                        `template_skc_id` bigint NOT NULL COMMENT '上架模板SKC模板id',
                                        `listing_template_id` bigint NOT NULL COMMENT '上架模板ID',
                                        `stock_quantity` bigint DEFAULT NULL COMMENT '库存数量',
                                        `size_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '尺码名',
                                        `classified_attrs` json NULL COMMENT 'SKU分类关联属性组',
                                        `packing_items` json NULL COMMENT '包装清单',
                                        `reference_url` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '参考链接',
                                        `sensitive_config` json NULL COMMENT '敏感属性',
                                        `longest_edge` decimal(13,2) DEFAULT NULL COMMENT '最长边',
                                        `second_edge` decimal(13,2) DEFAULT NULL COMMENT '次长边',
                                        `shortest_edge` decimal(13,2) DEFAULT NULL COMMENT '最短边',
                                        `weight` decimal(13,2) DEFAULT NULL COMMENT '重量',
                                        `enable_state` tinyint(1) DEFAULT '1' COMMENT '是否启用【1启动；0禁用】',
                                        `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否已删除;0未删除，1已删除',
                                        `creator_id` bigint NOT NULL COMMENT '创建人id',
                                        `creator_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '创建人',
                                        `created_time` datetime NOT NULL COMMENT '创建时间',
                                        `reviser_id` bigint DEFAULT NULL COMMENT '最近修改人id;新增时取创建人id',
                                        `reviser_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '最近修改人名称;新增时取创建人名称',
                                        `revised_time` datetime DEFAULT NULL COMMENT '更新时间',
                                        `tenant_id` bigint DEFAULT NULL COMMENT '租户id',
                                        PRIMARY KEY (`template_sku_id`),
                                        KEY `idx_listing_template_id` (`listing_template_id`),
                                        KEY `idx_template_skc_id` (`template_skc_id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='上架模板-SKU';




CREATE TABLE `product_template_temu_skc` (
                                             `temu_skc_id` bigint NOT NULL AUTO_INCREMENT COMMENT 'SKC模板id',
                                             `temu_spu_id` bigint NOT NULL COMMENT 'SPU模板id',
                                             `product_skc_id` bigint DEFAULT NULL COMMENT '商品skc id(可空)',
                                             `skc` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'SKC',
                                             `color` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '内部颜色',
                                             `platform_color` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '平台颜色',
                                             `combo_color_code` varchar(250) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '组合颜色编码(生成sellerSku用)',
                                             `color_code` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '颜色编码',
                                             `color_abbr_code` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '颜色缩写编码',
                                             `pictures` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'skc图片（多个用英文逗号分割）',
                                             `state` tinyint(1) DEFAULT '1' COMMENT 'SKC状态【1可用；0取消】',
                                             `article_number` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '货号',
                                             `combo` tinyint(1) DEFAULT '0' COMMENT '是否组合商品(1是,0否)',
                                             `allowed_update_unit` int DEFAULT '1' COMMENT '是否允许修改unit(1是0否)',
                                             `cb_price` decimal(10,2) DEFAULT NULL COMMENT '跨境价',
                                             `local_price` decimal(10,2) DEFAULT NULL COMMENT '本土价（供货价）',
                                             `purchase_price` decimal(10,2) DEFAULT NULL COMMENT '采购价格',
                                             `cost_price` decimal(10,2) DEFAULT NULL COMMENT '定价成本',
                                             `update_local_price` decimal(10,2) DEFAULT NULL COMMENT '更新供货价(新的价格)',
                                             `update_purchase_price` decimal(10,2) DEFAULT NULL COMMENT '更新采购价(新的价格)',
                                             `update_cost_price` decimal(10,2) DEFAULT NULL COMMENT '更新定价成本(新的价格)',
                                             `cost_price_update_state` int DEFAULT '0' COMMENT '成本价是否更新(1有更新；0没有)',
                                             `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否已删除;0未删除，1已删除',
                                             `creator_id` bigint NOT NULL COMMENT '创建人id',
                                             `creator_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '创建人',
                                             `created_time` datetime NOT NULL COMMENT '创建时间',
                                             `reviser_id` bigint DEFAULT NULL COMMENT '最近修改人id;新增时取创建人id',
                                             `reviser_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '最近修改人名称;新增时取创建人名称',
                                             `revised_time` datetime DEFAULT NULL COMMENT '更新时间',
                                             `tenant_id` bigint DEFAULT NULL COMMENT '租户id',
                                             PRIMARY KEY (`temu_skc_id`),
                                             KEY `idx_template_temu_skc_temu_spu_id` (`temu_spu_id`),
                                             KEY `idx_template_temu_skc_product_skc_id_index` (`product_skc_id`),
                                             KEY `idx_template_temu_skc_skc_index` (`skc`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='商品模板temu-SKC';




CREATE TABLE `product_template_temu_sku` (
                                             `temu_sku_id` bigint NOT NULL AUTO_INCREMENT COMMENT 'SKU模板id',
                                             `temu_skc_id` bigint NOT NULL COMMENT 'SKC模板id',
                                             `temu_spu_id` bigint NOT NULL COMMENT 'SPU模板id',
                                             `seller_sku` varchar(600) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '平台sku',
                                             `barcode` varchar(600) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '商品条码',
                                             `barcodes` json NULL COMMENT '商品条码(多个)',
                                             `article_number` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '货号',
                                             `country` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '站点编码',
                                             `country_name` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '站点名称',
                                             `stock_quantity` bigint DEFAULT NULL COMMENT '库存数量',
                                             `size_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '尺码名',
                                             `declared_price` json NULL COMMENT '申报价格',
                                             `recommended_price` json NULL COMMENT '建议零售价格',
                                             `manufacturer_recommended_price` json NULL COMMENT '制造商建议零售价格',
                                             `classified_attrs` json NULL COMMENT 'SKU分类关联属性组',
                                             `packing_items` json NULL COMMENT '包装清单',
                                             `reference_url` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '参考链接',
                                             `sensitive_config` json NULL COMMENT '敏感属性',
                                             `longest_edge` decimal(13,2) DEFAULT NULL COMMENT '最长边',
                                             `second_edge` decimal(13,2) DEFAULT NULL COMMENT '次长边',
                                             `shortest_edge` decimal(13,2) DEFAULT NULL COMMENT '最短边',
                                             `weight` decimal(13,2) DEFAULT NULL COMMENT '重量',
                                             `flag_frontend` tinyint(1) DEFAULT '1' COMMENT '是否到前台：1是，0否',
                                             `enable_state` tinyint(1) DEFAULT '1' COMMENT '是否启用【1启动；0禁用】',
                                             `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否已删除;0未删除，1已删除',
                                             `creator_id` bigint NOT NULL COMMENT '创建人id',
                                             `creator_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '创建人',
                                             `created_time` datetime NOT NULL COMMENT '创建时间',
                                             `reviser_id` bigint DEFAULT NULL COMMENT '最近修改人id;新增时取创建人id',
                                             `reviser_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '最近修改人名称;新增时取创建人名称',
                                             `revised_time` datetime DEFAULT NULL COMMENT '更新时间',
                                             `tenant_id` bigint DEFAULT NULL COMMENT '租户id',
                                             PRIMARY KEY (`temu_sku_id`),
                                             KEY `idx_template_temu_sku_temu_spu_id` (`temu_spu_id`),
                                             KEY `idx_template_temu_sku_temu_skc_id` (`temu_skc_id`),
                                             KEY `idx_template_temu_sku_temu_spu_id_index` (`temu_spu_id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='商品模板temu-SKU';



CREATE TABLE `product_template_temu_spu` (
                                             `temu_spu_id` bigint NOT NULL AUTO_INCREMENT COMMENT 'SPU模板id',
                                             `product_id` bigint NOT NULL COMMENT '商品id',
                                             `spu_code` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'spu编码',
                                             `product_name` varchar(600) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '商品名称',
                                             `product_name_en` varchar(600) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '商品英文名称',
                                             `business_type` tinyint(1) DEFAULT NULL COMMENT '运营模式: 2半托 3全托',
                                             `material_languages` json NULL COMMENT '多语言素材集合',
                                             `country_origin_place` json NULL COMMENT '对应的产地',
                                             `site` json NULL COMMENT '站点',
                                             `size_group_name` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '尺码组名称',
                                             `size_group_code` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '尺码组编码',
                                             `size_specification` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '尺码规格',
                                             `is_customized` tinyint(1) DEFAULT NULL COMMENT '商品类型：0-非定制商品 1-定制商品',
                                             `document_file` varchar(800) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '说明文件',
                                             `document_file_type` tinyint(1) DEFAULT NULL COMMENT '说明文件类型 1.更新说明文件 2.补充或修改[安装/使用说明]和[安全信息说明]',
                                             `unit_setting` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '单位设置',
                                             `generated_title_missing_fields` json NULL COMMENT '自动化标题字段缺失信息，存储为JSON数组，例如：["HOT_WORD","PRINT_ATTRIBUTE_EXTEND1"]',
                                             `generated_title_over_length` tinyint(1) DEFAULT '0' COMMENT '自动化标题是否过长：0-否，1-是',
                                             `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否已删除;0未删除，1已删除',
                                             `tenant_id` bigint DEFAULT NULL COMMENT '租户ID',
                                             `creator_id` bigint NOT NULL COMMENT '创建人id',
                                             `creator_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '创建人',
                                             `created_time` datetime NOT NULL COMMENT '创建时间',
                                             `reviser_id` bigint DEFAULT NULL COMMENT '最近修改人id;新增时取创建人id',
                                             `reviser_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '最近修改人名称;新增时取创建人名称',
                                             `revised_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                             PRIMARY KEY (`temu_spu_id`),
                                             KEY `idx_template_temu_spu_spu_code` (`spu_code`),
                                             KEY `idx_template_temu_spu_product_id_index` (`product_id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='商品模板temu-SPU';







CREATE TABLE `temu_category` (
                                 `temu_category_id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                 `category_id` bigint NOT NULL COMMENT 'temu类目ID',
                                 `parent_category_id` bigint DEFAULT NULL COMMENT '父类目ID',
                                 `category_name` varchar(128) DEFAULT NULL COMMENT '类目名称',
                                 `category_level` int NOT NULL COMMENT '类目层级',
                                 `category_type` tinyint(1) NOT NULL COMMENT '类目类型，0：未分类，1：服饰类',
                                 `leaf` tinyint(1) NOT NULL COMMENT '是否叶子节点',
                                 `hidden` tinyint(1) NOT NULL COMMENT '是否隐藏',
                                 `hidden_type` tinyint(1) NOT NULL COMMENT '隐藏类型，0：不隐藏，1：一般隐藏，2：老类目，3：废弃类目',
                                 `category_path_list` json NULL COMMENT '类目路径JSON字符串，表示从根类目到当前类目的完整路径',
                                 `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否已删除;0未删除，1已删除',
                                 `creator_id` bigint NOT NULL COMMENT '创建人id',
                                 `creator_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '创建人',
                                 `created_time` datetime DEFAULT NULL COMMENT '创建时间',
                                 `reviser_id` bigint DEFAULT NULL COMMENT '最近修改人id;新增时取创建人id',
                                 `reviser_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '最近修改人名称;新增时取创建人名称',
                                 `revised_time` datetime DEFAULT NULL COMMENT '更新时间',
                                 `tenant_id` bigint DEFAULT NULL COMMENT '租户id',
                                 PRIMARY KEY (`temu_category_id`),
                                 UNIQUE KEY `uniq_category_id` (`category_id`),
                                 KEY `idx_parent_id` (`parent_category_id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='TEMU类目表';





CREATE TABLE `temu_pull_select_status_log` (
                                               `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键自增 ID',
                                               `platform_product_id` bigint NOT NULL COMMENT '货品 id',
                                               `platform_skc_id` bigint NOT NULL COMMENT '货品 skcId',
                                               `select_status` int NOT NULL COMMENT '选品状态，枚举值参考文档：0-已弃用，1-待平台选品，14-待卖家修改，15-已修改，16-服饰可加色，2-待上传生产资料，3-待寄样，4-寄样中，5-待平台审版，6-审版不合格，7-平台核价中，8-待修改生产资料，9-核价未通过，10-待下首单，11-已下首单，12-已加入站点，13-已下架，17-已终止',
                                               `platform_sku_id_list` json NULL COMMENT 'sku id列表(Json数组)',
                                               `apply_jlt_status` tinyint DEFAULT NULL COMMENT '申诉 JIT 的状态(1-可申请；3-不可申请)',
                                               `suggest_close_jlt` tinyint DEFAULT NULL COMMENT '是否建议关闭 JIT 按钮，1-是，0-否',
                                               `creator_id` bigint NOT NULL COMMENT '创建人id',
                                               `creator_name` varchar(32) COLLATE utf8mb4_general_ci NOT NULL COMMENT '创建人名称',
                                               `created_time` datetime NOT NULL COMMENT '创建时间',
                                               `reviser_id` bigint DEFAULT NULL COMMENT '最近修改人id',
                                               `reviser_name` varchar(32) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '最近修改人名称',
                                               `revised_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最近更新时间',
                                               `is_deleted` tinyint(1) NOT NULL COMMENT '是否已删除;0未删除，1已删除',
                                               `tenant_id` bigint DEFAULT NULL COMMENT '租户id',
                                               PRIMARY KEY (`id`),
                                               KEY `idx_productId` (`platform_product_id`),
                                               KEY `idx_skcId` (`platform_skc_id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='Temu-拉取商品状态记录表';



CREATE TABLE `temu_sale_goods` (
                                   `sale_goods_id` bigint NOT NULL COMMENT '主键',
                                   `product_id` bigint DEFAULT NULL COMMENT '商品表ID',
                                   `platform_product_id` bigint DEFAULT NULL COMMENT '平台商品ID',
                                   `platform_id` bigint DEFAULT NULL COMMENT '平台ID',
                                   `channel_id` bigint DEFAULT NULL COMMENT '渠道ID',
                                   `product_name` varchar(600) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '商品名称',
                                   `product_name_en` varchar(600) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '商品英文名称',
                                   `business_type` tinyint(1) DEFAULT NULL COMMENT '运营模式: 2半托 3全托',
                                   `spu_code` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'spu编码',
                                   `shop_id` bigint DEFAULT NULL COMMENT '店铺ID',
                                   `shop_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '店铺名称',
                                   `currency_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '币种',
                                   `category_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '品类编码',
                                   `platform_category_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '平台品类唯一标识',
                                   `platform_category_name` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '平台品类名',
                                   `material_languages` json NULL COMMENT '多语言素材集合',
                                   `country_origin_place` json NULL COMMENT '对应的产地',
                                   `site` json NULL COMMENT '站点',
                                   `country_shipping_warehouse` json NULL COMMENT '站点和对应的发货仓',
                                   `size_template_name` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '尺码表名称',
                                   `size_template_code` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '尺码表编码',
                                   `is_customized` tinyint(1) DEFAULT NULL COMMENT '商品类型：0-非定制商品 1-定制商品',
                                   `document_file` varchar(800) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '说明文件',
                                   `document_file_type` tinyint(1) DEFAULT NULL COMMENT '说明文件类型 1.更新说明文件 2.补充或修改[安装/使用说明]和[安全信息说明]',
                                   `unit_setting` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '单位设置',
                                   `model_profile` json NULL COMMENT '模特信息',
                                   `delivery_promise` json NULL COMMENT '承诺发货时效',
                                   `freight_template` json NULL COMMENT '运费模版',
                                   `stocking_area` json NULL COMMENT '备货区域',
                                   `package_specs` json NULL COMMENT '包装规格',
                                   `publish_state` tinyint(1) DEFAULT '2' COMMENT '商品上架状态【1上架；2下架】',
                                   `update_state` int DEFAULT '0' COMMENT '是否更新【1有更新；0没有】',
                                   `cost_price_update_state` int DEFAULT '0' COMMENT '供货价是否更新【1有更新；0没有】',
                                   `system_update_platform_fail` int DEFAULT '0' COMMENT '系统更新平台失败(1是 0否)',
                                   `platform_sync_state` tinyint(1) DEFAULT '0' COMMENT '平台同步状态【0待同步;2同步中；1已同步；-1同步失败】',
                                   `publish_time` datetime DEFAULT NULL COMMENT '首次上架时间',
                                   `publish_user_id` bigint DEFAULT NULL COMMENT '首次发布人id',
                                   `publish_user_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '首次发布人名称',
                                   `latest_publish_time` datetime DEFAULT NULL COMMENT '最近上架时间',
                                   `latest_offline_user_id` bigint DEFAULT NULL COMMENT '最近下架人id',
                                   `latest_publish_user_id` bigint DEFAULT NULL COMMENT '最近上架人id',
                                   `latest_offline_user_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '最近下架人名称',
                                   `latest_publish_user_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '最近上架人名称',
                                   `latest_offline_time` datetime DEFAULT NULL COMMENT '最近下架时间',
                                   `price_confirm_time` datetime DEFAULT NULL COMMENT '价格确认时间',
                                   `country_add_time` datetime DEFAULT NULL COMMENT '加入站点时间',
                                   `is_history` int DEFAULT NULL COMMENT '是否历史数据【1：是】',
                                   `error_state` int DEFAULT '0' COMMENT '是否异常数据(1是0否)',
                                   `error_info` json NULL COMMENT '异常类型json数组[[{"errorType":  1, "errorMsg":  "SKU为空"}]',
                                   `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否已删除;0未删除，1已删除',
                                   `creator_id` bigint NOT NULL COMMENT '创建人id',
                                   `creator_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '创建人',
                                   `created_time` datetime DEFAULT NULL COMMENT '创建时间',
                                   `reviser_id` bigint DEFAULT NULL COMMENT '最近修改人id;新增时取创建人id',
                                   `reviser_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '最近修改人名称;新增时取创建人名称',
                                   `revised_time` datetime DEFAULT NULL COMMENT '更新时间',
                                   `tenant_id` bigint DEFAULT NULL COMMENT '租户id',
                                   PRIMARY KEY (`sale_goods_id`) USING BTREE,
                                   UNIQUE KEY `uniq_index` (`product_id`,`spu_code`,`shop_id`,`platform_product_id`) USING BTREE COMMENT '唯一标识索引',
                                   KEY `idx_sale_goods_product_id` (`product_id`) USING BTREE,
                                   KEY `idx_sale_goods_publish_state` (`publish_state`) USING BTREE,
                                   KEY `idx_sale_goods_filtering` (`product_id`,`product_name`,`publish_state`) USING BTREE,
                                   KEY `idx_goods_publish_time_index` (`publish_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='销售商品表-TEMU';





CREATE TABLE `temu_sale_skc` (
                                 `sale_skc_id` bigint NOT NULL AUTO_INCREMENT COMMENT '销售skc id',
                                 `sale_goods_id` bigint NOT NULL COMMENT '销售商品id',
                                 `product_skc_id` bigint DEFAULT NULL COMMENT '商品skc id',
                                 `skc` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'SKC',
                                 `color` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '内部颜色',
                                 `platform_color` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '平台颜色',
                                 `combo_color_code` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '组合颜色编码(生成sellerSku用)',
                                 `color_code` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '颜色编码',
                                 `color_abbr_code` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '颜色缩写编码',
                                 `pictures` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'skc图片（多个用英文逗号分割）',
                                 `state` tinyint(1) DEFAULT '1' COMMENT 'SKC状态【1可用；0取消】',
                                 `article_number` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '货号',
                                 `combo` tinyint(1) DEFAULT '0' COMMENT '是否组合商品(1是,0否)',
                                 `allowed_update_unit` int DEFAULT '1' COMMENT '是否允许修改unit(1是0否)',
                                 `cb_price` decimal(10,2) DEFAULT NULL COMMENT '跨境价',
                                 `local_price` decimal(10,2) DEFAULT NULL COMMENT '本土价（供货价）',
                                 `purchase_price` decimal(10,2) DEFAULT NULL COMMENT '采购价格',
                                 `cost_price` decimal(10,2) DEFAULT NULL COMMENT '定价成本',
                                 `update_local_price` decimal(10,2) DEFAULT NULL COMMENT '更新供货价(新的价格)',
                                 `update_purchase_price` decimal(10,2) DEFAULT NULL COMMENT '更新采购价(新的价格)',
                                 `update_cost_price` decimal(10,2) DEFAULT NULL COMMENT '更新定价成本(新的价格)',
                                 `cost_price_update_state` int DEFAULT '0' COMMENT '成本价是否更新(1有更新；0没有)',
                                 `platform_skc_id` bigint DEFAULT NULL COMMENT '平台SKC ID',
                                 `select_status` int DEFAULT NULL COMMENT '选品状态',
                                 `apply_jit_status` int DEFAULT NULL COMMENT '申诉JIT的状态(1-可申请；3-不可申请)',
                                 `suggest_close_jit` int DEFAULT NULL COMMENT '是否建议关闭JIT按钮(1是;0否)',
                                 `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否已删除;0未删除，1已删除',
                                 `creator_id` bigint NOT NULL COMMENT '创建人id',
                                 `creator_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '创建人',
                                 `created_time` datetime NOT NULL COMMENT '创建时间',
                                 `reviser_id` bigint DEFAULT NULL COMMENT '最近修改人id;新增时取创建人id',
                                 `reviser_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '最近修改人名称;新增时取创建人名称',
                                 `revised_time` datetime DEFAULT NULL COMMENT '更新时间',
                                 `tenant_id` bigint DEFAULT NULL COMMENT '租户id',
                                 PRIMARY KEY (`sale_skc_id`),
                                 KEY `idx_skc_sale_goods_id_index` (`sale_goods_id`),
                                 KEY `idx_sale_skc_skc` (`skc`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='销售SKC-TEMU';



CREATE TABLE `temu_sale_sku` (
                                 `sale_sku_id` bigint NOT NULL COMMENT '主键',
                                 `sale_skc_id` bigint DEFAULT NULL COMMENT '销售skc id',
                                 `sale_goods_id` bigint DEFAULT NULL COMMENT '销售商品ID',
                                 `product_id` bigint DEFAULT NULL COMMENT '商品表ID',
                                 `platform_product_id` bigint DEFAULT NULL COMMENT '平台商品ID',
                                 `seller_sku` varchar(600) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '平台sku',
                                 `shop_sku` varchar(600) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'shop sku',
                                 `product_skc_id` bigint DEFAULT NULL COMMENT '商品skcID',
                                 `barcode` varchar(600) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '商品条码',
                                 `barcodes` json NULL COMMENT '商品条码(多个)',
                                 `seller_sku_flat_id` bigint DEFAULT NULL COMMENT 'seller_sku平铺信息id',
                                 `sku_code` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'sku编码',
                                 `article_number` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '货号',
                                 `country` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '站点编码',
                                 `country_name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '站点名称',
                                 `stock_quantity` bigint DEFAULT NULL COMMENT '库存数量',
                                 `sku_warehouse_stock_quantity` json NULL COMMENT '仓库库存',
                                 `size_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '尺码名',
                                 `sale_price` decimal(13,2) DEFAULT NULL COMMENT '售价',
                                 `declared_price` decimal(13,2) DEFAULT NULL COMMENT '申报价格',
                                 `recommended_price` decimal(13,2) DEFAULT NULL COMMENT '建议零售价格',
                                 `manufacturer_recommended_price` decimal(13,2) DEFAULT NULL COMMENT '制造商建议零售价格',
                                 `classified_attrs` json NULL COMMENT 'SKU分类关联属性组',
                                 `packing_items` json NULL COMMENT '包装清单',
                                 `reference_url` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '参考链接',
                                 `sensitive_config` json NULL COMMENT '敏感属性',
                                 `longest_edge` decimal(13,2) DEFAULT NULL COMMENT '最长边',
                                 `second_edge` decimal(13,2) DEFAULT NULL COMMENT '次长边',
                                 `shortest_edge` decimal(13,2) DEFAULT NULL COMMENT '最短边',
                                 `weight` decimal(13,2) DEFAULT NULL COMMENT '重量',
                                 `platform_sku_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '平台SKU ID',
                                 `enable_state` tinyint(1) DEFAULT '1' COMMENT '是否启用【1启动；0禁用】',
                                 `shop_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '店铺名称',
                                 `brand_id` varchar(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '品牌ID',
                                 `brand_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '品牌名称',
                                 `platform_category_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '平台品类唯一标识',
                                 `platform_category_name` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '平台品类名',
                                 `delay_delivery_days` int DEFAULT NULL COMMENT '发货期',
                                 `publish_state` tinyint(1) DEFAULT '0' COMMENT 'SKU上架状态【1上架；2下架】',
                                 `latest_offline_time` datetime DEFAULT NULL COMMENT '最近下架时间',
                                 `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否已删除;0未删除，1已删除',
                                 `creator_id` bigint NOT NULL COMMENT '创建人id',
                                 `creator_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '创建人',
                                 `created_time` datetime NOT NULL COMMENT '创建时间',
                                 `reviser_id` bigint DEFAULT NULL COMMENT '最近修改人id;新增时取创建人id',
                                 `reviser_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '最近修改人名称;新增时取创建人名称',
                                 `revised_time` datetime DEFAULT NULL COMMENT '更新时间',
                                 `tenant_id` bigint DEFAULT NULL COMMENT '租户id',
                                 PRIMARY KEY (`sale_sku_id`) USING BTREE,
                                 KEY `idx_product_id` (`product_id`) USING BTREE,
                                 KEY `idx_sale_goods_id` (`sale_goods_id`) USING BTREE,
                                 KEY `idx_seller_sku` (`seller_sku`) USING BTREE,
                                 KEY `idx_skcid_country` (`product_skc_id`,`country`) USING BTREE,
                                 KEY `idx_sku_sale_skc_id_index` (`sale_skc_id`),
                                 KEY `idx_sku_revised_time_index` (`revised_time`),
                                 KEY `idx_sku_seller_sku_flat_id_index` (`seller_sku_flat_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='销售SKU表-TEMU';



CREATE TABLE `temu_skc_detailed_image` (
                                           `skc_detailed_image_id` bigint NOT NULL COMMENT 'skc详情图 id',
                                           `product_id` bigint NOT NULL COMMENT '商品id',
                                           `language_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '语言编码',
                                           `language_name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '语言名称',
                                           `skc` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'SKC',
                                           `color` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '内部颜色',
                                           `platform_color` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '平台颜色',
                                           `combo_color_code` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '组合颜色编码(生成sellerSku用)',
                                           `color_code` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '颜色编码',
                                           `color_abbr_code` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '颜色缩写编码',
                                           `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否已删除;0未删除，1已删除',
                                           `creator_id` bigint NOT NULL COMMENT '创建人id',
                                           `creator_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '创建人',
                                           `created_time` datetime NOT NULL COMMENT '创建时间',
                                           `reviser_id` bigint DEFAULT NULL COMMENT '最近修改人id;新增时取创建人id',
                                           `reviser_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '最近修改人名称;新增时取创建人名称',
                                           `revised_time` datetime DEFAULT NULL COMMENT '更新时间',
                                           `tenant_id` bigint DEFAULT NULL COMMENT '租户id',
                                           PRIMARY KEY (`skc_detailed_image_id`),
                                           KEY `idx_product_id` (`product_id`),
                                           KEY `idx_skc` (`skc`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='skc详情图';





ALTER TABLE `shop`
    ADD COLUMN `entity_code` varchar(32) DEFAULT NULL COMMENT '店铺所属主体字典值编码',
    ADD COLUMN `temu_site` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'tumu站点 多个站点逗号拼接' AFTER `entity_code`;





