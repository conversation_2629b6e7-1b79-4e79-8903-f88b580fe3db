ALTER TABLE `shop`
    ADD COLUMN `entity_code` varchar(32) DEFAULT NULL COMMENT '店铺所属主体字典值编码';

-- 添加标题字段缺失信息字段
ALTER TABLE `product_template_temu_spu`
    ADD COLUMN `generated_title_missing_fields` json NULL COMMENT '自动化标题字段缺失信息，存储为JSON数组，例如：["HOT_WORD","PRINT_ATTRIBUTE_EXTEND1"]' AFTER `unit_setting`,
    ADD COLUMN `generated_title_over_length` tinyint(1) DEFAULT '0' COMMENT '自动化标题是否过长：0-否，1-是' AFTER `generated_title_missing_fields`;


CREATE TABLE `temu_pull_select_status_log`
(
    `id`                   BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键自增 ID',
    `platform_product_id`  bigint          NOT NULL COMMENT '货品 id',
    `platform_skc_id`      bigint          NOT NULL COMMENT '货品 skcId',
    `select_status`        INT             NOT NULL COMMENT '选品状态，枚举值参考文档：0-已弃用，1-待平台选品，14-待卖家修改，15-已修改，16-服饰可加色，2-待上传生产资料，3-待寄样，4-寄样中，5-待平台审版，6-审版不合格，7-平台核价中，8-待修改生产资料，9-核价未通过，10-待下首单，11-已下首单，12-已加入站点，13-已下架，17-已终止',
    `platform_sku_id_list` JSON            NULL COMMENT 'sku id列表(Json数组)',
    `apply_jlt_status`     TINYINT         null COMMENT '申诉 JIT 的状态(1-可申请；3-不可申请)',
    `suggest_close_jlt`    TINYINT         null COMMENT '是否建议关闭 JIT 按钮，1-是，0-否',
    `creator_id`           bigint          NOT NULL COMMENT '创建人id',
    `creator_name`         varchar(32)     NOT NULL COMMENT '创建人名称',
    `created_time`         datetime        NOT NULL COMMENT '创建时间',
    `reviser_id`           bigint                   DEFAULT NULL COMMENT '最近修改人id',
    `reviser_name`         varchar(32)              DEFAULT NULL COMMENT '最近修改人名称',
    `revised_time`         datetime        NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最近更新时间',
    `is_deleted`           tinyint(1)      NOT NULL COMMENT '是否已删除;0未删除，1已删除',
    `tenant_id`            bigint                   DEFAULT NULL COMMENT '租户id',
    PRIMARY KEY (`id`),
    KEY `idx_productId` (`platform_product_id`),
    KEY `idx_skcId` (`platform_skc_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='Temu-拉取商品状态记录表';