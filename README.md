# Pop-Product 商品运营平台

[![Spring Boot](https://img.shields.io/badge/Spring%20Boot-3.3.5-brightgreen.svg)](https://spring.io/projects/spring-boot)
[![Kotlin](https://img.shields.io/badge/Kotlin-2.1.20-blue.svg)](https://kotlinlang.org/)
[![JDK](https://img.shields.io/badge/JDK-21-orange.svg)](https://openjdk.java.net/)
[![Gradle](https://img.shields.io/badge/Gradle-8.x-green.svg)](https://gradle.org/)

## 📋 项目简介

Pop-Product 是一个基于 Spring Boot 3.x、Kotlin 和 Jimmer ORM 构建的现代化商品运营平台，专为管理 AliExpress、Lazada、Temu 等电商平台的商品全生命周期而设计。

### 🎯 核心功能

- **商品管理**：完整的 SPU/SKC/SKU 管理体系
- **企划管理**：商品企划和开发流程，支持审批工作流
- **定价策略**：多平台定价策略和成本计算
- **图片管理**：商品图片管理
- **平台集成**：与主流电商平台的深度集成
- **配置管理**：类目映射、规则配置、店铺管理

## 🏗️ 系统架构

### 模块结构

```
pop-product/
├── pop-product-common/     # 共享模块：DTOs、枚举、常量、异常
├── pop-product-sdk/        # SDK模块：外部服务集成接口
└── pop-product-service/    # 服务模块：业务逻辑、控制器、数据层
```

### 技术栈

**核心技术**
- **运行环境**：JDK 21
- **开发语言**：Kotlin 2.1.20（支持 Java 互操作）
- **应用框架**：Spring Boot 3.3.5 + Spring Cloud
- **构建工具**：Gradle 8.x (Kotlin DSL)

**数据层**
- **数据库**：MySQL 8.3
- **ORM 框架**：Jimmer 0.9.99 + MyBatis-Plus 3.5.9
- **缓存**：Redis + Redisson 3.38.0
- **消息队列**：RabbitMQ

**核心依赖**
- **内部框架**：Blade 3.1.4
- **HTTP 客户端**：OpenFeign 4.x
- **工具库**：Google Guava 33.4.8-jre, Commons-io 2.18.0
- **Excel 处理**：EasyExcel 4.0.3, Apache POI 5.3.0
- **分页组件**：PageHelper 5.3.3
- **JSON 处理**：Jackson 2.18.0

## 🚀 快速开始

### 环境要求

- JDK 21+
- MySQL 8.3+
- Redis 6.0+
- RabbitMQ 3.8+
- Gradle 8.x

### 本地开发

```bash
# 克隆项目
git clone <repository-url>
cd pop-product

# 构建项目
./gradlew --parallel build
```

### 开发命令

```bash
# 构建项目
./gradlew --parallel build

# 跳过测试构建
./gradlew --parallel -x test build

# 清理并重新构建
./gradlew clean build

# 运行测试
./gradlew test

# 查看依赖版本
./gradlew :pop-product-service:dependencies --configuration runtimeClasspath
```

## 🔧 配置说明

**⚠️ 重要：新人必须先参考公司内部文档完成环境配置**

### Gradle 配置
- 配置公司内部 Gradle 仓库地址和访问凭证
- 设置 JDK 21 环境变量：`JAVA_HOME`
- 参考内部文档配置网络代理（如需要）

### IDEA 启动配置
**dev/qa/uat环境变量设置参考**：
```
args = [
    "--spring.cloud.nacos.discovery.register-enabled=false",
    "--spring.profiles.active=dev-ola",
    "--spring.config.import=https://jv-devops.oss-cn-hangzhou.aliyuncs.com/\${spring.profiles.active}-yunwei-config.yml,classpath:local-bootstrap.yml"
]
```

### 应用配置
- **主配置**：`application.yml` + Nacos 配置中心
- **本地配置**：参考内部文档获取配置文件模板
- **关键配置**：数据库、Redis、线程池、平台 API

## 🌐 平台集成

支持 AliExpress、Lazada、Temu 三大电商平台的完整商品生命周期管理，包括商品发布、状态同步、库存管理、类目映射等功能。

## 💻 开发规范

### 代码规范

- **语言优先级**：Kotlin 优先，Java 互操作
- **注释要求**：所有成员变量必须有中文注释
  ```kotlin
  /** 用户ID */
  var userId: String? = ""
  ```
- **日志规范**：使用 Blade 日志框架
  ```kotlin
  import team.aikero.blade.logging.core.annotation.Slf4j
  import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
  
  log.info { "处理类目 $categoryId" }
  ```

### 数据库操作

- **现有表**：MyBatis-Plus + `@TableName`
- **新表**：Jimmer + `@Entity`
- **BaseEntity 字段**：`creatorId`、`createdTime` 等由拦截器自动填充

### API 设计

- 统一响应：`DataResponse<T>`
- 分页：`PageReq` + `PageVo`
- 对象参数：使用 `@PostMapping`
- 验证注解：`@field:NotNull` + 中文注释


## 📝 开发须知

- **IDE**：IntelliJ IDEA + Kotlin 插件
- **日志**：Blade 日志框架，字符串模板记录
- **监控**：结构化日志 + 性能监控 + 错误跟踪

## 📄 许可证

本项目采用内部许可证，仅供公司内部使用。

---

**注意**：本项目为内部商用系统，包含敏感业务逻辑，请勿外泄。