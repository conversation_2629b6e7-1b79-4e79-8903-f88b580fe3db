// 解决多项目版本重复加载问题 见 https://docs.gradle.org/current/userguide/plugins.html#sec:subprojects_plugins_dsl
plugins {
    alias(commonLibs.plugins.kotlin.jvm) apply false
    alias(commonLibs.plugins.kapt) apply false
}

subprojects {

    configurations.all {
        resolutionStrategy.cacheChangingModulesFor(10, TimeUnit.MINUTES)
    }
}

/*
allprojects {
    configurations.all {
        resolutionStrategy {
            force("commons-io:commons-io:${libs.versions.commons.io.get()}")
        }
    }
}*/
